﻿/* *
 *
 * <AUTHOR>
 * @datetime  2022/02/22
 *
 * */

export default [
  {
    path: '/user',
    layout: false,
    routes: [
      {
        path: '/user/login',
        layout: false,
        name: 'login',
        component: './user/login',
      },
      {
        path: '/user',
        redirect: '/user/login',
      },
      {
        name: 'register-result',
        icon: 'smile',
        path: '/user/register-result',
        component: './user/register-result',
      },
      {
        name: 'register',
        icon: 'smile',
        path: '/user/register',
        component: './user/register',
      },
      {
        component: '404',
      },
    ],
  },
  {
    path: '/dashboard',
    name: 'dashboard',
    icon: 'dashboard',
    component: '@/layouts/TabsLayout',
    routes: [
      {
        path: '/dashboard',
        redirect: '/dashboard/analysis',
      },
      {
        name: 'analysis',
        icon: 'smile',
        path: '/dashboard/analysis',
        component: './dashboard/analysis',
        wrappers: ['@/components/KeepAlive'],
        KeepAlive: true,
        title: 'menu.dashboard.analysis',
      },
      {
        name: 'monitor',
        icon: 'smile',
        path: '/dashboard/monitor',
        component: './dashboard/monitor',
        wrappers: ['@/components/KeepAlive'],
        KeepAlive: true,
        title: 'menu.dashboard.monitor',
      },
      {
        name: 'workplace',
        icon: 'smile',
        path: '/dashboard/workplace',
        component: './dashboard/workplace',
        wrappers: ['@/components/KeepAlive'],
        KeepAlive: true,
        title: 'menu.dashboard.workplace',
      },
    ],
  },
  {
    name: 'testtool',
    icon: 'ToolOutlined',
    path: '/testtool',
    component: '@/layouts/TabsLayout',
    routes: [
      {
        path: '/testtool',
        redirect: '/testtool/routeQuery',
      },
      {
        name: 'routeQuery',
        icon: 'CreditCardOutlined',
        path: '/testtool/routeQuery',
        component: './testtool/routeQuery',
        wrappers: ['@/components/KeepAlive'],
        KeepAlive: true,
        title: 'menu.testtool.routeQuery',
      },
      {
        name: 'custquery',
        icon: 'CreditCardOutlined',
        path: '/testtool/custQuery',
        component: './testtool/custQuery',
        wrappers: ['@/components/KeepAlive'],
        KeepAlive: true,
        title: 'menu.testtool.custquery',
      },
      {
        name: 'accquery',
        icon: 'CreditCardOutlined',
        path: '/testtool/accquery',
        component: './testtool/accquery',
        wrappers: ['@/components/KeepAlive'],
        KeepAlive: true,
        title: 'menu.testtool.accquery',
      },
      {
        name: 'contquery',
        icon: 'CreditCardOutlined',
        path: '/testtool/contquery',
        component: './testtool/contquery',
        wrappers: ['@/components/KeepAlive'],
        KeepAlive: true,
        title: 'menu.testtool.contquery',
      },
      {
        name: 'open',
        icon: 'CreditCardOutlined',
        path: '/testtool/open',
        component: './testtool/open',
        wrappers: ['@/components/KeepAlive'],
        KeepAlive: true,
        title: 'menu.testtool.open',
      },
      {
        name: 'medquery',
        icon: 'CreditCardOutlined',
        path: '/testtool/medquery',
        component: './testtool/medquery',
        wrappers: ['@/components/KeepAlive'],
        KeepAlive: true,
        title: 'menu.testtool.medquery',
      },
      {
        name: 'queryAll',
        icon: 'CreditCardOutlined',
        path: '/testtool/queryAll',
        component: './testtool/queryAll',
        wrappers: ['@/components/KeepAlive'],
        KeepAlive: true,
        title: 'menu.testtool.cardQuery',
      },
      {
        name: 'limitSumInfo',
        icon: 'CreditCardOutlined',
        path: '/testtool/limitSumInfoquery',
        component: './testtool/limitSumInfoquery',
        wrappers: ['@/components/KeepAlive'],
        KeepAlive: true,
        title: 'menu.autotest.limitSumInfoquery',
      },
      {
        name: 'limitGloTrackBussNoQuery',
        icon: 'CreditCardOutlined',
        path: '/testtool/limitGloTrackBussNoQuery',
        component: './testtool/limitGloTrackBussNoQuery',
        wrappers: ['@/components/KeepAlive'],
        KeepAlive: true,
        title: 'menu.autotest.limitGloTrackBussNoQuery',
      },
      {
        name: 'updateHistoryQuery',
        icon: 'CreditCardOutlined',
        path: '/testtool/updateHistoryQuery',
        component: './testtool/updateHistoryQuery',
        wrappers: ['@/components/KeepAlive'],
        KeepAlive: true,
        title: 'menu.autotest.updateHistoryQuery',
      },
      {
        name: 'msgQuery',
        icon: 'CreditCardOutlined',
        path: '/testtool/msgQuery',
        component: './testtool/msgQuery',
        wrappers: ['@/components/KeepAlive'],
        KeepAlive: true,
        title: 'menu.testtool.msgQuery',
      },
      {
        name: '预留2',
        icon: 'CreditCardOutlined',
        path: '/testtool/medquery',
        component: './testtool/medquery',
        wrappers: ['@/components/KeepAlive'],
        KeepAlive: true,
        title: 'menu.testtool.yuliu2',
      },
      {
        name: '预留3',
        icon: 'CreditCardOutlined',
        path: '/testtool/medquery',
        component: './testtool/medquery',
        wrappers: ['@/components/KeepAlive'],
        KeepAlive: true,
        title: 'menu.testtool.yuliu3',
      },
      {
        name: 'complexRulesQuery',
        icon: 'CreditCardOutlined',
        path: '/testtool/complexRulesQuery',
        component: './testtool/complexRulesQuery',
        wrappers: ['@/components/KeepAlive'],
        KeepAlive: true,
        title: 'menu.testtool.complexRulesQuery',
      },
    ],
  },
  {
    name: 'tinytools',
    icon: 'ToolOutlined',
    path: '/tinytools',
    component: '@/layouts/TabsLayout',
    routes: [
      {
        path: '/tinytools',
        redirect: '/tinytools/mtinfo',
      },
      {
        name: 'mtinfo',
        icon: 'CreditCardOutlined',
        path: '/tinytools/mtinfo',
        component: './tinytools/mtinfo',
        wrappers: ['@/components/KeepAlive'],
        KeepAlive: true,
        title: 'menu.tinytools.mtinfo',
      },
      {
        name: 'encode',
        icon: 'CreditCardOutlined',
        path: '/tinytools/encode',
        component: './tinytools/encode',
        wrappers: ['@/components/KeepAlive'],
        KeepAlive: true,
        title: 'menu.tinytools.encode',
      },
      {
        name: 'dac',
        icon: 'CreditCardOutlined',
        path: '/tinytools/dac',
        component: './tinytools/dac',
        wrappers: ['@/components/KeepAlive'],
        KeepAlive: true,
        title: 'menu.tinytools.dac',
      },
      {
        name: 'pwdupdate',
        icon: 'CreditCardOutlined',
        path: '/tinytools/pwdupdate',
        component: './tinytools/pwdupdate',
        wrappers: ['@/components/KeepAlive'],
        KeepAlive: true,
        title: 'menu.tinytools.pwdupdate',
      },
      {
        name: 'cardAndPkGen',
        icon: 'CreditCardOutlined',
        path: '/tinytools/cardAndPkGen',
        component: './tinytools/cardAndPkGen',
        wrappers: ['@/components/KeepAlive'],
        KeepAlive: true,
        title: 'menu.tinytools.cardAndPkGen',
      },
      {
        name: 'jsonformat',
        icon: 'CreditCardOutlined',
        path: '/tinytools/jsonformat',
        component: './tinytools/jsonformat',
        wrappers: ['@/components/KeepAlive'],
        KeepAlive: true,
        title: 'menu.tinytools.jsonformat',
      },
    ],
  },
  {
    name: 'autotest',
    icon: 'CloudServerOutlined',
    path: '/autotest',
    component: '@/layouts/TabsLayout',
    routes: [
      {
        path: '/autotest',
        redirect: '/autotest/dataselect',
      },
      {
        name: 'dataselect',
        icon: 'CreditCardOutlined',
        path: '/autotest/dataselect',
        component: './autotest/dataselect',
        wrappers: ['@/components/KeepAlive'],
        KeepAlive: true,
        title: 'menu.autotest.dataselect',
      },
      {
        name: 'autotestsqlupload',
        icon: 'CreditCardOutlined',
        path: '/autotest/autotestsqlupload',
        component: './autotest/autotestsqlupload',
        wrappers: ['@/components/KeepAlive'],
        KeepAlive: true,
        title: 'menu.autotest.sqlupload',
      },
      {
        name: 'autotestsqlselect',
        icon: 'CreditCardOutlined',
        path: '/autotest/autotestsqlselect',
        component: './autotest/autotestsqlselect',
        wrappers: ['@/components/KeepAlive'],
        KeepAlive: true,
        title: 'menu.autotest.sqlselect',
      },
      {
        name: 'fileupload',
        icon: 'CreditCardOutlined',
        path: '/autotest/fileupload',
        component: './autotest/fileupload',
        wrappers: ['@/components/KeepAlive'],
        KeepAlive: true,
        title: 'menu.autotest.fileupload',
      },
      {
        name: 'fileOperation',
        icon: 'CreditCardOutlined',
        path: '/autotest/fileOperation',
        component: './autotest/fileOperation',
        wrappers: ['@/components/KeepAlive'],
        KeepAlive: true,
        title: 'menu.autotest.fileOperation',
      },
      {
        name: "autotestsvndata",
        icon: "CreditCardOutlined",
        path: "/autotest/autotestsvndata",
        component: "./autotest/autotestsvndata",
        wrappers: ["@/components/KeepAlive"],
        KeepAlive: true,
        title: "menu.autotest.svnfileOperation",
      },
    ],
  },
  {
    name: 'message',
    icon: 'ToolOutlined',
    path: '/message',
    component: '@/layouts/TabsLayout',
    routes: [
      {
        path: '/message',
        redirect: '/message/send',
      },
      {
        name: 'send',
        icon: 'CreditCardOutlined',
        path: '/message/send',
        component: './message/send',
        wrappers: ['@/components/KeepAlive'],
        KeepAlive: true,
        title: 'menu.message.send',
      },
    ],
  },
  {
    name: 'blog',
    icon: 'ToolOutlined',
    path: '/blog',
    component: '@/layouts/TabsLayout',
    routes: [
      {
        path: '/blog',
        redirect: '/blog/share',
      },
      {
        name: 'share',
        icon: 'CreditCardOutlined',
        path: '/blog/share',
        component: './blog/share',
        wrappers: ['@/components/KeepAlive'],
        KeepAlive: true,
        title: 'menu.blog.share',
      },
    ],
  },
  {
    name: 'account',
    icon: 'user',
    path: '/account',
    component: '@/layouts/TabsLayout',
    routes: [
      {
        path: '/account',
        redirect: '/account/center',
      },
      {
        name: 'center',
        icon: 'smile',
        path: '/account/center',
        component: './account/center',
        wrappers: ['@/components/KeepAlive'],
        KeepAlive: true,
        title: 'menu.account.center',
      },
      {
        name: 'settings',
        icon: 'smile',
        path: '/account/settings',
        component: './account/settings',
        wrappers: ['@/components/KeepAlive'],
        KeepAlive: true,
        title: 'menu.account.settings',
      },
    ],
  },
  {
    name: 'system',
    icon: 'BugOutlined',
    path: '/system',
    component: '@/layouts/TabsLayout',
    routes: [
      {
        path: '/',
        redirect: '/system/user',
      },
      {
        name: 'user',
        icon: 'PartitionOutlined',
        path: '/system/user',
        component: 'system/user/index',
        access: 'authorize',
        wrappers: ['@/components/KeepAlive'],
        KeepAlive: true,
        title: 'menu.title.user',
      },
      {
        name: 'menu',
        icon: 'PartitionOutlined',
        path: '/system/menu',
        component: 'system/menu/index',
        access: 'authorize',
        wrappers: ['@/components/KeepAlive'],
        KeepAlive: true,
        title: 'menu.title.menu',
      },
      {
        name: 'role',
        icon: 'PartitionOutlined',
        path: '/system/role',
        component: 'system/role/index',
        access: 'authorize',
        wrappers: ['@/components/KeepAlive'],
        KeepAlive: true,
        title: 'menu.title.role',
      },
      {
        name: 'dept',
        icon: 'PartitionOutlined',
        path: '/system/dept',
        component: 'system/dept/index',
        access: 'authorize',
        wrappers: ['@/components/KeepAlive'],
        KeepAlive: true,
        title: 'menu.title.dept',
      },
      {
        name: 'post',
        icon: 'PartitionOutlined',
        path: '/system/post',
        component: 'system/post/index',
        access: 'authorize',
        wrappers: ['@/components/KeepAlive'],
        KeepAlive: true,
        title: 'menu.title.post',
      },
      {
        name: 'dict',
        icon: 'PartitionOutlined',
        path: '/system/dict',
        component: 'system/dict/index',
        access: 'authorize',
        wrappers: ['@/components/KeepAlive'],
        KeepAlive: true,
        title: 'menu.title.dict',
      },
      {
        name: 'dictData',
        icon: 'PartitionOutlined',
        path: '/system/dictData/index/:id?',
        component: 'system/dictData/index',
        access: 'authorize',
        wrappers: ['@/components/KeepAlive'],
        KeepAlive: true,
        title: 'menu.title.dictData',
      },
      {
        name: 'config',
        icon: 'PartitionOutlined',
        path: '/system/config',
        component: 'system/config/index',
        access: 'authorize',
        wrappers: ['@/components/KeepAlive'],
        KeepAlive: true,
        title: 'menu.title.config',
      },
      {
        name: 'notice',
        icon: 'PartitionOutlined',
        path: '/system/notice',
        component: 'system/notice/index',
        access: 'authorize',
        wrappers: ['@/components/KeepAlive'],
        KeepAlive: true,
        title: 'menu.title.notice',
      },
      {
        name: 'log',
        icon: 'BugOutlined',
        path: '/system/log/',
        routes: [
          {
            path: '/',
            redirect: '/system/log/operlog',
          },
          {
            name: 'operlog',
            icon: 'PartitionOutlined',
            path: '/system/log/operlog',
            component: 'monitor/operlog',
            access: 'authorize',
            wrappers: ['@/components/KeepAlive'],
            KeepAlive: true,
            title: 'menu.title.operlog',
          },
          {
            name: 'loginInfo',
            icon: 'PartitionOutlined',
            path: '/system/log/logininfor',
            component: 'monitor/logininfor',
            access: 'authorize',
            wrappers: ['@/components/KeepAlive'],
            KeepAlive: true,
            title: 'menu.title.loginInfo',
          },
        ],
      },
    ],
  },
  {
    name: 'monitor',
    icon: 'BugOutlined',
    path: '/monitor',
    component: '@/layouts/TabsLayout',
    routes: [
      {
        path: '/',
        redirect: '/monitor/online',
      },
      {
        name: 'onlineUser',
        icon: 'PartitionOutlined',
        path: '/monitor/online',
        component: 'monitor/online',
        access: 'authorize',
        wrappers: ['@/components/KeepAlive'],
        KeepAlive: true,
        title: 'menu.title.onlineUser',
      },
      {
        name: 'job',
        icon: 'PartitionOutlined',
        path: '/monitor/job',
        component: 'monitor/job',
        access: 'authorize',
        wrappers: ['@/components/KeepAlive'],
        KeepAlive: true,
        title: 'menu.title.job',
      },
      {
        name: 'joblog',
        icon: 'PartitionOutlined',
        path: '/monitor/job-log/index/:jobId?',
        component: 'monitor/joblog',
        access: 'authorize',
        wrappers: ['@/components/KeepAlive'],
        KeepAlive: true,
        title: 'menu.title.joblog',
      },
      {
        name: 'druid',
        icon: 'PartitionOutlined',
        path: '/monitor/druid',
        component: 'monitor/druid',
        access: 'authorize',
        wrappers: ['@/components/KeepAlive'],
        KeepAlive: true,
        title: 'menu.title.druid',
      },
      {
        name: 'serverInfo',
        icon: 'PartitionOutlined',
        path: '/monitor/server',
        component: 'monitor/server',
        access: 'authorize',
        wrappers: ['@/components/KeepAlive'],
        KeepAlive: true,
        title: 'menu.title.serverInfo',
      },
      {
        name: 'cacheInfo',
        icon: 'PartitionOutlined',
        path: '/monitor/cache',
        component: 'monitor/cache',
        access: 'authorize',
        wrappers: ['@/components/KeepAlive'],
        KeepAlive: true,
        title: 'menu.title.cacheInfo',
      },
      {
        name: 'cacheList',
        icon: 'PartitionOutlined',
        path: '/monitor/cacheList',
        component: 'monitor/cacheList',
        access: 'authorize',
        wrappers: ['@/components/KeepAlive'],
        KeepAlive: true,
        title: 'menu.title.cacheList',
      },
    ],
  },
  {
    name: 'tool',
    icon: 'BugOutlined',
    path: '/tool',
    component: '@/layouts/TabsLayout',
    routes: [
      {
        path: '/',
        redirect: '/tool/gen',
      },
      {
        name: 'gen',
        icon: 'PartitionOutlined',
        path: '/tool/gen',
        component: 'tool/gen/index',
        access: 'authorize',
        wrappers: ['@/components/KeepAlive'],
        KeepAlive: true,
        title: 'menu.title.gen',
      },
      {
        name: 'design',
        icon: 'PartitionOutlined',
        path: '/tool/build',
        component: 'tool/builder',
        access: 'authorize',
        wrappers: ['@/components/KeepAlive'],
        KeepAlive: true,
        title: 'menu.title.design',
      },
      {
        name: 'swagger',
        icon: 'PartitionOutlined',
        path: '/tool/swagger',
        component: 'tool/swagger',
        access: 'authorize',
        wrappers: ['@/components/KeepAlive'],
        KeepAlive: true,
        title: 'menu.title.swagger',
      },
    ],
  },
  {
    name: 'TestTaskAutoWork',
    icon: 'ToolOutlined',
    path: '/TestTaskAutoWork',
    component: '@/layouts/TabsLayout',
    routes: [
      {
        path: '/TestTaskAutoWork',
        redirect: '/TestTaskAutoWork/TaskList',
      },
      {
        name: 'TaskList',
        icon: 'CreditCardOutlined',
        path: '/TestTaskAutoWork/TaskList',
        component: './TestTaskAutoWork/TaskList',
        wrappers: ['@/components/KeepAlive'],
        KeepAlive: true,
        title: 'menu.TestTaskAutoWork.TaskList',
      },
      {
        name: 'PostPcs',
        icon: 'CreditCardOutlined',
        path: '/TestTaskAutoWork/PostPcs',
        component: './TestTaskAutoWork/PostPcs',
        wrappers: ['@/components/KeepAlive'],
        KeepAlive: true,
        title: 'menu.TestTaskAutoWork.PostPcs',
      },
    ],
  },
  {
    path: '/',
    redirect: '/dashboard',
  },
  {
    component: './404',
  },
];