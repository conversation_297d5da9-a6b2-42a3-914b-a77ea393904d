/**
 * 在生产环境 代理是无法生效的，所以这里没有生产环境的配置
 * -------------------------------
 * The agent cannot take effect in the production environment
 * so there is no configuration of the production environment
 * For details, please see
 * https://pro.ant.design/docs/deploy
 */
export default {
  dev: {
    '/api/testtool/': {
      target: 'http://localhost:3002',
      changeOrigin: true,
      pathRewrite: { '^/api': '' },
    },
    '/api/': {
      target: 'http://localhost:8080',
      changeOrigin: true,
      pathRewrite: { '^/api': '' },
    },
    '/profile/avatar/': {
      target: 'http://localhost:8080',
      changeOrigin: true,
    },
    '/mac-service/T1/': {
      target: 'http://localhost:8081',
      changeOrigin: true,
      pathRewrite: { '^/mac-service/.*': '/macService' }, // 将所有mac-service路径重写为/macService
    },
    '/transaction-service/': {
      target: 'http://localhost:8081',
      changeOrigin: true,
      pathRewrite: { '^/transaction-service/.*': '/testService' }, // 将所有transaction-service路径重写为/online-service
    }
  },
  test: {
    '/api/': {
      target: 'http://localhost:8080',
      changeOrigin: true,
      pathRewrite: { '^': '' },
    },
  },
  pre: {
    '/api/': {
      target: 'your pre url',
      changeOrigin: true,
      pathRewrite: { '^': '' },
    },
  },
};
