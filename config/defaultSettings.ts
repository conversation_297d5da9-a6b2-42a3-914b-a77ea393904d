import { Settings as LayoutSettings } from '@ant-design/pro-layout';

const Settings: LayoutSettings & {
  pwa?: boolean;
  logo?: string;
  tabsLayout?: boolean;
  apiBasePath?: string;
} = {
  navTheme: 'dark',
  headerTheme: 'light',
  primaryColor: '#1890ff',
  layout: 'side',
  splitMenus: false,
  contentWidth: 'Fluid',
  fixedHeader: true,
  fixSiderbar: true,
  colorWeak: false,
  title: '自动化测试平台',
  pwa: false,
  logo:
    'data:image/svg+xml;base64,' +
    btoa(
      '<svg t="1729826633568" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="34609" width="200" height="200"><path d="M676.41 762.6c17.46 17.47 28.3 41.55 28.3 68.04 0 26.51-10.84 50.59-28.3 68.06-17.47 17.46-41.55 28.3-68.06 28.3-0.67 0-1.34 0-2.01-0.01-84.89-1.08-153.4-70.23-153.4-155.4V578.88c0 36.39 12.51 69.87 33.47 96.35 28.46 35.97 72.51 59.06 121.94 59.06 26.51 0 50.59 10.84 68.06 28.31z" fill="#4770DC" p-id="34610"></path><path d="M452.94 771.59c0 85.16 68.5 154.32 153.4 155.4-55.11-0.31-107.18-13.43-153.4-36.52-114.26-57.1-192.71-175.18-192.71-311.59V193.36c0-53 43.37-96.35 96.35-96.35 53 0 96.35 43.35 96.35 96.35v37.4H667.4c22.42 0 43.1 7.76 59.53 20.73 22.39 17.68 36.83 45.06 36.83 75.63 0 26.51-10.84 50.59-28.3 68.06-17.47 17.46-41.55 28.3-68.06 28.3H452.94v348.11z" fill="#6F94F4" p-id="34611"></path></svg>',
    ),
  iconfontUrl: '',
  tabsLayout: true,
  apiBasePath: '/api',
};

export default Settings;
