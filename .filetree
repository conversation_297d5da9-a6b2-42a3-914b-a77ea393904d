{"tree": [{"name": "项目文件", "rootPath": ".", "includeFiles": ["package.json", "tsconfig.json", ".eslintrc.js", ".stylelintrc.js", "README.md"]}, {"name": "源代码", "rootPath": "./src", "includeFiles": ["*.ts", "*.tsx", "*.js", "*.jsx", "*.less", "*.css"], "excludeFiles": [".umi/**/*", ".umi-production/**/*"]}, {"name": "组件", "rootPath": "./src/components", "includeFiles": ["**/*.tsx", "**/*.less"]}, {"name": "页面", "rootPath": "./src/pages", "includeFiles": ["**/*.tsx", "**/*.less"]}, {"name": "布局", "rootPath": "./src/layouts", "includeFiles": ["**/*.tsx", "**/*.less"]}, {"name": "工具函数", "rootPath": "./src/utils", "includeFiles": ["**/*.ts", "**/*.js"]}, {"name": "API服务", "rootPath": "./src/services", "includeFiles": ["**/*.ts"]}, {"name": "模型", "rootPath": "./src/models", "includeFiles": ["**/*.ts"]}, {"name": "配置文件", "rootPath": "./config", "includeFiles": ["**/*.ts", "**/*.js"]}, {"name": "Mo<PERSON>数据", "rootPath": "./mock", "includeFiles": ["**/*.ts", "**/*.js"]}]}