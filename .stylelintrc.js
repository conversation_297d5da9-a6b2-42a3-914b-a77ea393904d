module.exports = {
  extends: "stylelint-config-standard",
  customSyntax: "postcss-less",
  rules: {
    "selector-class-pattern": null,
    "no-descending-specificity": null,
    "font-family-no-missing-generic-family-keyword": null,
    "block-no-empty": null,
    "no-empty-source": null,
    "selector-pseudo-class-no-unknown": null,
    "selector-pseudo-element-no-unknown": null,
    "at-rule-no-unknown": null,
    "property-no-unknown": null,
    "declaration-block-no-duplicate-properties": null,
    "declaration-block-trailing-semicolon": null,
    "keyframe-declaration-no-important": null,
    "color-hex-length": null,
    "rule-empty-line-before": null,
    "selector-list-comma-newline-after": null,
    "no-eol-whitespace": null,
    "no-duplicate-selectors": null,
    "selector-pseudo-element-colon-notation": null,
    "no-missing-end-of-source-newline": null,
  },
  ignoreFiles: ["**/*.js", "**/*.jsx", "**/*.tsx", "**/*.ts"],
};
