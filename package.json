{"name": "ant-design-pro", "version": "5.0.0", "private": true, "description": "An out-of-box UI solution for enterprise applications", "scripts": {"analyze": "cross-env NODE_OPTIONS='--openssl-legacy-provider' ANALYZE=1 umi build", "build": "cross-env NODE_OPTIONS='--openssl-legacy-provider --max-old-space-size=4096 --no-deprecation' umi build", "deploy": "npm run build && npm run gh-pages", "dev": "npm run start:dev", "docker-hub:build": "docker build  -f Dockerfile.hub -t  ant-design-pro ./", "docker-prod:build": "docker-compose -f ./docker/docker-compose.yml build", "docker-prod:dev": "docker-compose -f ./docker/docker-compose.yml up", "docker:build": "docker-compose -f ./docker/docker-compose.dev.yml build", "docker:dev": "docker-compose -f ./docker/docker-compose.dev.yml up", "docker:push": "npm run docker-hub:build && npm run docker:tag && docker push antdesign/ant-design-pro", "docker:tag": "docker tag ant-design-pro antdesign/ant-design-pro", "gh-pages": "gh-pages -d dist", "i18n-remove": "pro i18n-remove --locale=zh-CN --write", "postinstall": "umi g tmp", "lint": "umi g tmp && npm run lint:js && npm run lint:style && npm run lint:prettier && npm run tsc", "lint-staged": "lint-staged", "lint-staged:js": "eslint --ext .js,.jsx,.ts,.tsx ", "lint:fix": "eslint --fix --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src && npm run lint:style", "lint:js": "eslint --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src", "lint:prettier": "prettier -c --write \"src/**/*\" --end-of-line auto", "lint:style": "stylelint --fix \"src/**/*.less\" --syntax less", "openapi": "umi open<PERSON>i", "precommit": "lint-staged", "prettier": "prettier -c --write \"src/**/*\"", "serve": "umi-serve", "start": "cross-env NODE_OPTIONS=--openssl-legacy-provider MOCK=none UMI_ENV=dev umi dev", "start:dev": "cross-env NODE_OPTIONS=--openssl-legacy-provider REACT_APP_ENV=dev MOCK=none UMI_ENV=dev umi dev", "start:no-mock": "cross-env MOCK=none UMI_ENV=dev umi dev", "start:no-ui": "cross-env UMI_UI=none UMI_ENV=dev umi dev", "start:pre": "cross-env REACT_APP_ENV=pre UMI_ENV=dev umi dev", "start:test": "cross-env REACT_APP_ENV=test MOCK=none UMI_ENV=dev umi dev", "pretest": "node ./tests/beforeTest", "test": "umi test", "test:all": "node ./tests/run-tests.js", "test:component": "umi test ./src/components", "tsc": "tsc --noEmit"}, "lint-staged": {"**/*.less": "stylelint --syntax less", "**/*.{js,jsx,ts,tsx}": "npm run lint-staged:js", "**/*.{js,jsx,tsx,ts,less,md,json}": ["prettier --write"]}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"], "dependencies": {"@ant-design/charts": "^0.9.13", "@ant-design/compatible": "^1.1.0", "@ant-design/icons": "^5.5.2", "@ant-design/pro-components": "^2.8.3", "@ant-design/pro-descriptions": "^1.12.7", "@ant-design/pro-field": "^1.x", "@ant-design/pro-form": "^1.74.7", "@ant-design/pro-layout": "^6.38.22", "@ant-design/pro-table": "^2.80.8", "@antv/data-set": "^0.11.8", "@antv/l7": "^2.22.3", "@antv/l7-maps": "^2.22.3", "@antv/l7-react": "^2.4.3", "@codemirror/lang-json": "^6.0.1", "@codemirror/language": "^6.11.0", "@codemirror/view": "^6.36.8", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.0.2", "@mui/material": "^7.0.2", "@types/d3": "^7.4.3", "@uiw/codemirror-theme-dracula": "^4.23.8", "@uiw/react-codemirror": "^4.23.8", "@umijs/route-utils": "^1.0.37", "ahooks": "^2.10.14", "antd": "^4.24.16", "bizcharts": "^3.5.10", "bizcharts-plugin-slider": "^2.1.1", "btoa": "^1.2.1", "captchapng3": "^1.0.7", "classnames": "^2.5.1", "cropperjs": "^1.6.2", "crypto-js": "^4.2.0", "d3": "^7.9.0", "dayjs": "^1.11.13", "esbuild": "0.8.57", "framer-motion": "^12.4.10", "g2": "^2.3.13", "gg-editor": "^2.0.4", "lodash": "^4.17.21", "lodash-decorators": "^6.0.1", "lodash.debounce": "^4.0.8", "moment": "^2.30.1", "numeral": "^2.0.6", "nzh": "^1.0.14", "omit.js": "^2.0.2", "qs": "^6.13.1", "query-string": "^9.1.1", "rc-field-form": "^2.7.0", "rc-util": "^5.44.3", "react": "^17.0.2", "react-activation": "^0.13.0", "react-cropper": "^2.3.3", "react-dev-inspector": "^1.9.0", "react-dom": "^17.0.2", "react-fittext": "^1.0.0", "react-helmet-async": "^1.3.0", "react-intl": "^5.25.1", "react-router": "^4.3.1", "react-sortable-hoc": "^2.0.0", "spark-md5": "^3.0.2", "store2": "^2.14.4", "swagger-ui-dist": "^5.18.2", "umi": "^3.5.43", "umi-request": "^1.4.0", "umi-serve": "^1.9.11"}, "devDependencies": {"@ant-design/pro-cli": "^2.1.5", "@antv/gl-matrix": "^2.7.1", "@antv/matrix-util": "^3.0.4", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.25.1", "@types/classnames": "^2.3.4", "@types/crypto-js": "^4.2.2", "@types/express": "^4.17.21", "@types/history": "^4.7.11", "@types/jest": "^26.0.24", "@types/lodash": "^4.17.14", "@types/qs": "^6.9.17", "@types/react": "^17.0.83", "@types/react-dom": "^17.0.26", "@types/react-helmet": "^6.1.11", "@types/spark-md5": "^3.0.5", "@typescript-eslint/eslint-plugin": "^8.31.1", "@typescript-eslint/parser": "^8.31.1", "@umijs/fabric": "^2.14.1", "@umijs/openapi": "^1.13.0", "@umijs/plugin-blocks": "^2.2.2", "@umijs/plugin-esbuild": "^1.4.2", "@umijs/plugin-openapi": "^1.3.3", "@umijs/preset-ant-design-pro": "^1.3.3", "@umijs/preset-dumi": "^1.1.54", "@umijs/preset-react": "^1.8.32", "@umijs/yorkie": "^2.0.5", "carlo": "^0.9.46", "cross-env": "^7.0.3", "cross-port-killer": "^1.4.0", "detect-installer": "^1.0.2", "enzyme": "^3.11.0", "eslint": "^9.24.0", "eslint-plugin-unicorn": "45.0.0", "express": "^4.21.2", "gh-pages": "^3.2.3", "jsdom-global": "^3.0.2", "lint-staged": "^10.5.4", "mockjs": "^1.1.0", "prettier": "^3.4.2", "pro-download": "1.0.1", "puppeteer-core": "^8.0.0", "stylelint": "^13.13.1", "typescript": "^4.9.5"}, "engines": {"node": ">=14.0.0"}, "gitHooks": {"commit-msg": "fabric verify-commit"}}