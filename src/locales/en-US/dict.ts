/* *
 *
 * <AUTHOR>
 * @datetime  2021/09/16
 * 
 * */

export default {  
  'system.DictType.dict_id': 'ID',
  'system.DictType.dict_name': 'Name',
  'system.DictType.dict_type': 'Type',
  'system.DictType.status': 'Status',
  'system.DictType.del_flag': 'Flag',
  'system.DictType.login_ip': 'IP',
  'system.DictType.login_date': 'Login Date',
  'system.DictType.create_by': 'Creator',
  'system.DictType.create_time': 'Create Time',
  'system.DictType.update_by': 'Updater',
  'system.DictType.update_time': 'Update Time',
  'system.DictType.remark': 'Remark',
	'system.DictType.modify': 'Edit',
  'system.DictData.dict_code': 'Code',
  'system.DictData.dict_sort': 'Sort',
  'system.DictData.dict_label': 'Label',
  'system.DictData.dict_value': 'Value',
  'system.DictData.dict_type': 'Type',
  'system.DictData.css_class': 'CSS',
  'system.DictData.list_class': 'List Class',
  'system.DictData.is_default': 'Default',
  'system.DictData.status': 'Status',
  'system.DictData.create_by': 'Creator',
  'system.DictData.create_time': 'Create Time',
  'system.DictData.update_by': 'Updater',
  'system.DictData.update_time': 'Update Time',
  'system.DictData.remark': 'Remark',
	'system.DictData.modify': 'Edit',
};
