
/* *
 *
 * <AUTHOR>
 * @datetime  2021/09/16
 * 
 * */

export default {
  'monitor.Job.job_id': 'ID',
  'monitor.Job.job_name': 'Name',
  'monitor.Job.job_group': 'Group',
  'monitor.Job.invoke_target': 'Invoke',
  'monitor.Job.cron_expression': 'Cron Exp',
  'monitor.Job.misfire_policy': 'Policy',
  'monitor.Job.concurrent': 'Concurrent',
  'monitor.Job.status': 'Status',
  'monitor.Job.del_flag': 'Flag',
  'monitor.Job.login_ip': 'IP',
  'monitor.Job.login_date': 'Login Date',
  'monitor.Job.create_by': 'Creator',
  'monitor.Job.create_time': 'Create Time',
  'monitor.Job.update_by': 'Updater',
  'monitor.Job.update_time': 'Update Time',
  'monitor.Job.remark': 'Remark',
	'monitor.Job.modify': 'Edit',
};
