export default {
  'autoTest.accquery.mediumNo': 'mediumNo',
  'autoTest.accquery.custNo': 'custNo',
  'autoTest.accquery.custNm': 'custNm',
  'autoTest.accquery.personalCertNo': 'personalCertNo',
  'autoTest.accquery.mobileNo': 'mobileNo',
  'autoTest.accquery.openAccInstNo': 'openAccInstNo',
  'autoTest.accquery.currCode': 'currCode',
  'autoTest.accquery.prodtContractName': 'prodtContractName',
  'autoTest.accquery.persDepAccTpCd': 'persDepAccTpCd',
  'autoTest.accquery.accBal': 'accBal',
  'autoTest.accquery.accAvalBal': 'accAvalBal',
  'autoTest.accquery.mediumTpCd': 'MediumTpCd',
  'autoTest.accquery.vendibiProdtNo': 'vendibiProdtNo',
  'autoTest.accquery.cardKindCd': 'CardKindCd',
  'autoTest.accquery.dcardCategNo': 'DcardCategNo',
  'autoTest.accquery.persDepAccKindCd': 'persDepAccKindCd',
  'autoTest.accquery.perCertTpCd': 'perCertTpCd',
  'autoTest.accquery.shardingId': 'shardingId',
  'autoTest.accquery.operation': 'Operation',
  'autoTest.accquery.dataTypeNo': 'dataTypeNo',
  'autoTest.accquery.relMediumNo': 'relMediumNo',
  'autoTest.accquery.useFlag': 'useFlag',
  'autoTest.accquery.remark': 'remark',
  'autoTest.accquery.id': 'id',
  'autoTest.accquery.dataDesc': 'dataDesc',
  'autoTest.accquery.createTime': 'createTime',
  'autoTest.accquery.updateTime': 'updateTime',

  'autoTest.accquery.lmtAmtCheckObjAttrbVal': 'lmtAmtCheckObjAttrbVal',
  'autoTest.accquery.sceneNo': 'sceneNo',
  'autoTest.accquery.sceneName': '限额场景名sceneName称',
  'autoTest.accquery.lmtAmtKindName': 'lmtAmtKindName',
  'autoTest.accquery.lmtAmtCheckObjSubtypeCd': 'lmtAmtCheckObjSubtypeCd',
  'autoTest.accquery.lmtAmtCtrlItemVal1': 'lmtAmtCtrlItemVal1',
  'autoTest.accquery.lmtAmtCtrlItemVal2': 'lmtAmtCtrlItemVal2',
  'autoTest.accquery.lmtAmtCtrlItemVal3': 'lmtAmtCtrlItemVal3',
  'autoTest.accquery.lmtAmtCtrlItemVal4': 'lmtAmtCtrlItemVal4',
  'autoTest.accquery.lmtAmtCtrlItemVal5': 'lmtAmtCtrlItemVal5',
  'autoTest.accquery.staticLmtAmtCtrlItemVal1': 'staticLmtAmtCtrlItemVal1',
  'autoTest.accquery.staticLmtAmtCtrlItemVal2': 'staticLmtAmtCtrlItemVal2',
  'autoTest.accquery.staticLmtAmtCtrlItemVal3': 'staticLmtAmtCtrlItemVal3',
  'autoTest.accquery.staticLmtAmtCtrlItemVal4': 'staticLmtAmtCtrlItemVal4',
  'autoTest.accquery.staticLmtAmtCtrlItemVal5': 'staticLmtAmtCtrlItemVal5',
  'autoTest.accquery.lmtAmtCtrlItemName1': 'lmtAmtCtrlItemName1',
  'autoTest.accquery.lmtAmtCtrlItemName2': 'lmtAmtCtrlItemName2',
  'autoTest.accquery.lmtAmtCtrlItemName3': 'lmtAmtCtrlItemName3',
  'autoTest.accquery.lmtAmtCtrlItemName4': 'lmtAmtCtrlItemName4',
  'autoTest.accquery.lmtAmtCtrlItemName5': 'lmtAmtCtrlItemName5',
  'autoTest.accquery.convtEquivaleRmb': 'convtEquivaleRmb',
  'autoTest.accquery.lmtAmtCtrlItemNo': 'lmtAmtCtrlItemNo',
  'autoTest.accquery.lmtAmtCtrlItemName': 'lmtAmtCtrlItemName',
  'autoTest.accquery.lmtAmtCtrlItemSumVal': 'lmtAmtCtrlItemSumVal',
  'autoTest.accquery.lmtAmtCtrlItemVal': 'lmtAmtCtrlItemVal',
  'autoTest.accquery.isCustomFlag': 'isCustomFlag',
  'autoTest.msgquery.globalBusiTrackNo': 'globalBusiTrackNo',
  'autoTest.msgquery.custNo': 'custNo',
  'autoTest.msgquery.msgCd': 'msgCd',
  'autoTest.msgquery.noticeMsgNo': 'noticeMsgNo',
  'autoTest.msgquery.accountingDate': 'accountingDate',
  'autoTest.msgquery.message': 'message',
};
