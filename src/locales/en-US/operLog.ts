
/* *
 *
 * <AUTHOR>
 * @datetime  2021/09/16
 * 
 * */

export default {
  'monitor.Operlog.detail': 'Detail',
  'monitor.Operlog.oper_id': 'ID',
  'monitor.Operlog.title': 'Title',
  'monitor.Operlog.business_type': 'Business',
  'monitor.Operlog.method': 'Method',
  'monitor.Operlog.request_method': 'Request',
  'monitor.Operlog.operator_type': 'Type',
  'monitor.Operlog.oper_name': 'Operator',
  'monitor.Operlog.dept_name': 'Dept Name',
  'monitor.Operlog.oper_url': 'URL',
  'monitor.Operlog.oper_ip': 'IP',
  'monitor.Operlog.oper_location': 'Location',
  'monitor.Operlog.oper_param': 'Param',
  'monitor.Operlog.json_result': 'Result',
  'monitor.Operlog.status': 'Status',
  'monitor.Operlog.error_msg': 'Message',
  'monitor.Operlog.oper_time': 'Time',
};
