/* *
 *
 * <AUTHOR>
 * @datetime  2021/09/16
 * 
 * */

export default {
  'system.Menu.menu_id': 'ID',
  'system.Menu.menu_name': 'Name',
  'system.Menu.parent_id': 'Parent ID',
  'system.Menu.order_num': 'Order',
  'system.Menu.path': 'Path',
  'system.Menu.component': 'Component',
  'system.Menu.is_frame': 'Is Frame',
  'system.Menu.is_cache': 'Cache',
  'system.Menu.menu_type': 'Type',
  'system.Menu.visible': 'Visible',
  'system.Menu.status': 'Status',
  'system.Menu.perms': 'Permission',
  'system.Menu.icon': 'Icon',
  'system.Menu.create_by': 'Creator',
  'system.Menu.create_time': 'Create Time',
  'system.Menu.update_by': 'Updater',
  'system.Menu.update_time': 'Update Time',
  'system.Menu.remark': 'Remark',
  'system.Menu.query': 'Query',
  'system.Menu.modify': 'Edit',
  'icon.selector.tab.direction': '方向性图标',
  'icon.selector.tab.suggestion': '提示建议性图标',
  'icon.selector.tab.editor': '编辑类图标',
  'icon.selector.tab.data': '数据类图标',
  'icon.selector.tab.logo': '品牌和标识',
  'icon.selector.tab.other': '网站通用图标',
};
