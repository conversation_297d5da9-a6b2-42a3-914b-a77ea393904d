
/* *
 *
 * <AUTHOR>
 * @datetime  2021/09/16
 * 
 * */

export default {
  'monitor.Operlog.detail': '操作日志详细信息',
  'monitor.Operlog.oper_id': '日志主键',
  'monitor.Operlog.title': '所属模块',
  'monitor.Operlog.business_type': '业务类型',
  'monitor.Operlog.method': '方法名称',
  'monitor.Operlog.request_method': '请求方式',
  'monitor.Operlog.operator_type': '操作类别',
  'monitor.Operlog.oper_name': '操作人员',
  'monitor.Operlog.dept_name': '部门名称',
  'monitor.Operlog.oper_url': '请求URL',
  'monitor.Operlog.oper_ip': '主机地址',
  'monitor.Operlog.oper_location': '操作地点',
  'monitor.Operlog.oper_param': '请求参数',
  'monitor.Operlog.json_result': '返回参数',
  'monitor.Operlog.status': '操作状态',
  'monitor.Operlog.error_msg': '错误消息',
  'monitor.Operlog.oper_time': '操作时间',
};
