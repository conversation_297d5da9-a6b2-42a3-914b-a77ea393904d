export default {
  'autoTest.accquery.mediumNo': '介质编号',
  'autoTest.accquery.custNo': '客户编号',
  'autoTest.accquery.custNm': '客户名称',
  'autoTest.accquery.personalCertNo': '证件号码',
  'autoTest.accquery.mobileNo': '手机号码',
  'autoTest.accquery.openAccInstNo': '开户机构号',
  'autoTest.accquery.currCode': '币种代码',
  'autoTest.accquery.prodtContractName': '产品合约名称',
  'autoTest.accquery.persDepAccTpCd': '个人存款账户类型代码',
  'autoTest.accquery.accBal': '账户余额',
  'autoTest.accquery.accAvalBal': '账户可用余额',
  'autoTest.accquery.mediumTpCd': '介质类型',
  'autoTest.accquery.vendibiProdtNo': '可售产品编码',
  'autoTest.accquery.cardKindCd': '卡品种',
  'autoTest.accquery.dcardCategNo': '借记卡类别',
  'autoTest.accquery.persDepAccKindCd': '个人存款账户种类代码',
  'autoTest.accquery.perCertTpCd': '个人证件类型代码',
  'autoTest.accquery.shardingId': '分片值',
  'autoTest.accquery.operation': '操作',
  'autoTest.accquery.dataTypeNo': '数据类型',
  'autoTest.accquery.relMediumNo': '关联介质编号',
  'autoTest.accquery.useFlag': '是否使用',
  'autoTest.accquery.remark': '备注',
  'autoTest.accquery.id': '序列号',
  'autoTest.accquery.dataDesc': '数据描述',
  'autoTest.accquery.createTime': '创建时间',
  'autoTest.accquery.updateTime': '更新时间',

  'autoTest.accquery.lmtAmtCheckObjAttrbVal': '限额检查对象',
  'autoTest.accquery.sceneNo': '限额场景编码',
  'autoTest.accquery.sceneName': '限额场景名称',
  'autoTest.accquery.lmtAmtKindName': '限额场景大类名称',
  'autoTest.accquery.lmtAmtCheckObjSubtypeCd': '累计账户类型',
  'autoTest.accquery.lmtAmtCtrlItemVal1': '限额控制项值1(单位:元)',
  'autoTest.accquery.lmtAmtCtrlItemVal2': '限额控制项值2(单位:元)',
  'autoTest.accquery.lmtAmtCtrlItemVal3': '限额控制项值3(单位:元)',
  'autoTest.accquery.lmtAmtCtrlItemVal4': '限额控制项值4(单位:元)',
  'autoTest.accquery.lmtAmtCtrlItemVal5': '限额控制项值5(单位:元)',
  'autoTest.accquery.staticLmtAmtCtrlItemVal1': '限额控制项值1-标数值(单位:元)',
  'autoTest.accquery.staticLmtAmtCtrlItemVal2': '限额控制项值2-标数值(单位:元)',
  'autoTest.accquery.staticLmtAmtCtrlItemVal3': '限额控制项值3-标数值(单位:元)',
  'autoTest.accquery.staticLmtAmtCtrlItemVal4': '限额控制项值4-标数值(单位:元)',
  'autoTest.accquery.staticLmtAmtCtrlItemVal5': '限额控制项值5-标数值(单位:元)',
  'autoTest.accquery.lmtAmtCtrlItemName1': '限额控制项名称1',
  'autoTest.accquery.lmtAmtCtrlItemName2': '限额控制项名称2',
  'autoTest.accquery.lmtAmtCtrlItemName3': '限额控制项名称3',
  'autoTest.accquery.lmtAmtCtrlItemName4': '限额控制项名称4',
  'autoTest.accquery.lmtAmtCtrlItemName5': '限额控制项名称5',
  'autoTest.accquery.convtEquivaleRmb': '等值人民币',
  'autoTest.accquery.lmtAmtCtrlItemNo': '限额控制项编码',
  'autoTest.accquery.lmtAmtCtrlItemName': '限额控制项名称',
  'autoTest.accquery.lmtAmtCtrlItemSumVal': '交易前已累计值',
  'autoTest.accquery.lmtAmtCtrlItemVal': '限额控制项标数值',
  'autoTest.accquery.isCustomFlag': '是否自定义限额',
  'autoTest.msgquery.globalBusiTrackNo': '全局业务跟踪号',
  'autoTest.msgquery.custNo': '客户编号',
  'autoTest.msgquery.msgCd': '通知消息类型',
  'autoTest.msgquery.noticeMsgNo': '通知消息编号',
  'autoTest.msgquery.accountingDate': '会计日期',
  'autoTest.msgquery.message': '原始数据',
};
