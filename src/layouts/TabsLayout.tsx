import KeepAliveTabs from '@/components/KeepAliveTabs';
import defaultSettings from '../../config/defaultSettings';
import { AliveScope } from 'react-activation';

const { tabsLayout } = defaultSettings;

/* *
 *
 * <AUTHOR>
 * @datetime  2022/02/22
 *
 * */

const TabsLayout: React.FC = (props) => {
  const renderTabs = () => {
    if(tabsLayout)
      return (
        <KeepAliveTabs />
      );
    else
      return null;
  }
  return (
    <AliveScope>
      <div>
        {renderTabs()}
        <div>{props.children}</div>
      </div>
    </AliveScope>
  );
};

export default TabsLayout;
