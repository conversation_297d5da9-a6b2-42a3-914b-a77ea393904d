import React, { useState, useEffect } from "react";
import { KeepAlive, useActivate, useUnactivate } from "react-activation";
import { storage } from "@/utils/storageUtils";
import {
  ProForm,
  ProFormText,
  ProFormSelect,
} from "@ant-design/pro-components";
import { Card, Typography, Collapse, Form, message, Spin } from "antd";
import { InfoCircleOutlined } from "@ant-design/icons";
import "./style.less";
import { dacCac } from "./service";

const { Paragraph } = Typography;

interface DacInfo {
  servRespDescInfo: string;
}
const DacReset: React.FC = () => {
  const [form] = Form.useForm();
  // 添加动画状态
  const [isVisible, setIsVisible] = useState(false);

  // 添加数据验证函数
  const validateMediumNo = (value: string) => {
    if (!value) return false;
    if (value.length > 50) return false; // 添加长度限制
    return /^[a-zA-Z0-9]+$/.test(value);
  };

  // 从localStorage初始化dac_response
  const [dacResInfo, setDacResInfo] = useState<DacInfo>(() => {
    const savedInfo = storage.get("dac_response");
    if (savedInfo) {
      return savedInfo;
    }
    return {
      servRespDescInfo: "",
    };
  });

  // 添加初始化 useEffect
  useEffect(() => {
    const savedSearch = storage.get("dac_search");
    if (savedSearch) {
      form.setFieldsValue(savedSearch);
    }
    const savedInfo = storage.get("dac_response");
    if (savedInfo) {
      setDacResInfo(savedInfo);
    }
    // 添加动画触发
    setTimeout(() => {
      setIsVisible(true);
    }, 100);
  }, [form]);

  // 监听 dacResInfo 变化
  useEffect(() => {
    if (dacResInfo && dacResInfo.servRespDescInfo) {
      storage.set("dac_response", dacResInfo, 30 * 60);
    }
  }, [dacResInfo]);

  // 修改 useActivate 的实现
  useActivate(() => {
    // 使用 setTimeout 确保在组件完全激活后再加载数据
    setTimeout(() => {
      const savedSearch = storage.get("dac_search");
      if (savedSearch) {
        form.setFieldsValue(savedSearch);
      }
      const savedInfo = storage.get("dac_response");
      if (savedInfo) {
        setDacResInfo(savedInfo);
      }
      setIsVisible(true);
    }, 0);
  });

   // 添加 useUnactivate 清理逻辑
   useUnactivate(() => {
    setLoading(false);
    setError(null);
    form.resetFields();
    setIsVisible(false);
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const collapseItems = [
    {
      key: "1",
      label: "返回结果",
      children: (
        <Paragraph copyable>{dacResInfo.servRespDescInfo || "暂无数据"}</Paragraph>
      ),
    },
  ];

  return (
    <KeepAlive>
      <div className={`app-container ${isVisible ? 'fade-in-up' : ''}`}>
        <div
          className="content-wrapper"
          style={{
            maxWidth: "100%", // 设置最大宽度为100%以贴近浏览器边角
            minWidth: "800px", // 设置最小宽度
            margin: "0 auto", // 水平居中
            padding: "0 16px", // 减小左右padding，使内容更贴近边缘
            width: "100%", // 占满可用宽度
            boxSizing: "border-box", // 添加盒模型属性
            WebkitBoxSizing: "border-box", // 添加浏览器前缀
            MozBoxSizing: "border-box", // 添加浏览器前缀
          }}
        >
          <div className={`card-container ${isVisible ? 'fade-in-up' : ''}`} style={{ animationDelay: '0.2s' }}>
            <Card
              title="重置DAC"
              bordered={false}
              className="input-section"
              style={{
                marginBottom: "24px",
                marginTop: "24px", // 减小顶部外边距
                height: "min-content",
                padding: "24px",
                backgroundColor: "#ffffff",
                borderRadius: "16px", // 确保圆角一致
                width: "100%", // 确保宽度100%
                boxSizing: "border-box", // 添加盒模型属性
                WebkitBoxSizing: "border-box", // 添加浏览器前缀
                MozBoxSizing: "border-box", // 添加浏览器前缀
              }}
            >
              <ProForm
                key={Date.now()} // 添加这一行，强制表单重新渲染
                form={form}
                onValuesChange={(changedValues, allValues) => {
                  // 设置30分钟后过期
                  storage.set("dac_search", allValues, 30 * 60);
                }}
                onFinish={async (values) => {
                  setLoading(true);
                  setError(null);
                  try {
                    // 检查必填字段
                    if (!values.mediumNo || !values.flag) {
                      message.error("请填写介质编号和是否同步重置子账户标识");
                      return false;
                    }

                    const requestData = {
                      mediumNo: values.mediumNo?.trim(), // 添加 trim 处理
                      flag: values.flag,
                      saccnoSeqNo: values.saccnoSeqNo?.trim(), // 添加 trim 处理
                    };
                    const res = await dacCac(requestData);

                    if (res.code === 200 && res.data) {
                      message.success("重置成功");
                      try {
                        const parsedData = JSON.parse(res.data);
                        const data = JSON.stringify(parsedData, null, 2);
                        if (!data) {
                          throw new Error("数据解析失败");
                        }

                        setDacResInfo({
                          servRespDescInfo: data || "",
                        });
                        storage.set(
                          "dac_response",
                          {
                            servRespDescInfo: data || "",
                          },
                          30 * 60
                        );
                      } catch (parseError) {
                        message.error("返回数据格式错误");
                        return false;
                      }
                    } else {
                      message.error(res.msg || "计算失败");
                    }
                    return true;
                  } catch (err: any) {
                    const errorMessage = err?.message || "计算失败";
                    setError(errorMessage);
                    message.error(errorMessage);
                    return false;
                  } finally {
                    setLoading(false);
                  }
                }}
                submitter={{
                  searchConfig: {
                    submitText: "提交",
                    resetText: "重置",
                  },
                  submitButtonProps: {
                    style: { minWidth: "80px" },
                  },
                  resetButtonProps: {
                    style: { minWidth: "80px" },
                    onClick: () => {
                      form.resetFields();
                      setDacResInfo({
                        servRespDescInfo: "",
                      });
                      storage.remove("dac_search");
                      storage.remove("dac_response");
                    },
                  },
                  render: (props, dom) => {
                    return (
                      <div
                        className="button-container"
                        style={{
                          display: "flex",
                          gap: "12px",
                          marginTop: "24px",
                          justifyContent: "flex-start", // 确保左对齐
                          marginLeft: "0", // 移除左边距，确保完全左对齐
                          paddingLeft: "0", // 移除左内边距
                        }}
                      >
                        {dom.map((btn, index) => (
                          <span key={index} style={{ marginRight: index === 0 ? '12px' : '0' }}>
                            {btn}
                          </span>
                        ))}
                      </div>
                    );
                  },
                }}
              >
                <ProForm.Group>
                  <ProFormText
                    name="mediumNo"
                    label="介质编号"
                    placeholder="请输入介质编号"
                    rules={[
                      { required: true, message: "请输入介质编号" },
                      { max: 50, message: "介质编号长度不能超过50个字符" },
                      {
                        pattern: /^[a-zA-Z0-9]+$/,
                        message: "介质编号只能包含数字和大小写字母",
                      },
                      {
                        validator: async (_, value) => {
                          if (value && !validateMediumNo(value)) {
                            throw new Error("介质编号格式不正确");
                          }
                        },
                      },
                    ]}
                    width="md"
                    fieldProps={{
                      className: "hover-input",
                      onChange: (e) => {
                        const value = e.target.value.replace(/[^a-zA-Z0-9]/g, "");
                        e.target.value = value;
                      },
                    }}
                  />
                  <ProFormSelect
                    name="flag"
                    label="是否同步重置子账户标识"
                    rules={[
                      { required: true, message: "请输入是否同步重置子账户标识" },
                    ]}
                    width="md"
                    options={[
                      { label: "0-否", value: "0" },
                      { label: "1-是", value: "1" },
                    ]}
                    fieldProps={{
                      className: "hover-input",
                    }}
                  />
                  <ProFormText
                    name="saccnoSeqNo"
                    label="子账号序号"
                    placeholder="请输入子账号序号,不输入则修改所有子账户"
                    width="md"
                    rules={[
                      { max: 20, message: "子账号序号长度不能超过20个字符" },
                      {
                        pattern: /^[0-9]+$/,
                        message: "子账号序号只能包含数字",
                      },
                    ]}
                    fieldProps={{
                      className: "hover-input",
                    }}
                  />
                </ProForm.Group>
              </ProForm>
            </Card>
          </div>

          <div className={`card-container ${isVisible ? 'fade-in-up' : ''}`} style={{ animationDelay: '0.4s' }}>
            <Card
              title="重置结果"
              bordered={false}
              className="result-section"
              style={{
                height: "min-content",
                padding: "24px",
                backgroundColor: "#ffffff",
                marginBottom: "24px",
                position: "relative", // 添加相对定位
                borderRadius: "16px", // 确保圆角一致
                width: "100%", // 确保宽度100%
                boxSizing: "border-box", // 添加盒模型属性
                WebkitBoxSizing: "border-box", // 添加浏览器前缀
                MozBoxSizing: "border-box", // 添加浏览器前缀
              }}
            >
              {loading ? (
                <div style={{ textAlign: "center", padding: "40px" }}>
                  <Spin size="large" tip="处理中..." />
                </div>
              ) : error ? (
                <div
                  style={{ textAlign: "center", padding: "20px", color: "red" }}
                >
                  <Typography.Text>{error}</Typography.Text>
                </div>
              ) : (
                <Collapse
                  defaultActiveKey={["1"]}
                  expandIcon={({ isActive }) => (
                    <InfoCircleOutlined rotate={isActive ? -90 : 90} />
                  )}
                  className="custom-collapse"
                >
                  {collapseItems.map((item) => (
                    <Collapse.Panel header={item.label} key={item.key}>
                      {item.children}
                    </Collapse.Panel>
                  ))}
                </Collapse>
              )}
            </Card>
          </div>
        </div>
      </div>
    </KeepAlive>
  );
};

export default DacReset;
