import React, { useState, useEffect } from "react";
import { KeepAlive, useActivate, useUnactivate } from "react-activation";
import { storage } from "@/utils/storageUtils";
import {
  ProForm,
  ProFormText,
} from "@ant-design/pro-components";
import { Card, Typography, Form, message, Spin, Row, Col } from "antd";
import "./style.less";
import { openCardAndPkOne } from "./service";
import type { OpenCardResult } from "./data.d";

const { Paragraph } = Typography;

interface OpenCardInfo {
  result: OpenCardResult | null;
}

const CardAndPkGen: React.FC = () => {
  const [form] = Form.useForm();
  // 添加动画状态
  const [isVisible, setIsVisible] = useState(false);

  // 添加数据验证函数
  const validateMediumNo = (value: string) => {
    if (!value) return false;
    if (value.length > 50) return false; // 添加长度限制
    return /^[a-zA-Z0-9]+$/.test(value);
  };

  // 证件类型映射表
  const certTypeMap: Record<string, string> = {
    "1010": "1010-居民身份证",
    "1011": "1011-临时居民身份证",
    "1020": "1020-军人身份证件",
    "1021": "1021-士兵证",
    "1022": "1022-军官证",
    "1023": "1023-文职干部证",
    "1024": "1024-军官退休证",
    "1025": "1025-文职干部退休证",
    "1030": "1030-武警身份证件",
    "1031": "1031-武警士兵证",
    "1032": "1032-警官证",
    "1033": "1033-武警文职干部证",
    "1034": "1034-武警军官退休证",
    "1035": "1035-武警文职干部退休证",
    "1040": "1040-户口簿",
    "1050": "1050-中国护照",
    "1051": "1051-外国护照",
    "1060": "1060-学生证",
    "1070": "1070-港澳居民来往内地通行证",
    "1071": "1071-往来港澳通行证",
    "1080": "1080-台湾居民来往大陆通行证",
    "1090": "1090-执行公务证",
    "1100": "1100-机动车驾驶证",
    "1110": "1110-社会保障卡",
    "1120": "1120-外国人居留证",
    "1121": "1121-外国人永久居留证",
    "1130": "1130-旅行证件",
    "1140": "1140-香港居民身份证",
    "1150": "1150-澳门居民身份证",
    "1160": "1160-台湾居民身份证",
    "1170": "1170-边民证",
    "1180": "1180-港澳台居民居住证",
    "1181": "1181-港澳居民居住证",
    "1182": "1182-台湾居民居住证",
    "1190": "1190-外国身份证",
    "1998": "1998-其他（原98类）",
    "1999": "1999-其他证件（个人）",
  };

  // 从localStorage初始化open_card_response
  const [openCardInfo, setOpenCardInfo] = useState<OpenCardInfo>(() => {
    const savedInfo = storage.get("open_card_response");
    if (savedInfo) {
      return savedInfo;
    }
    return {
      result: null,
    };
  });

  // 添加初始化 useEffect
  useEffect(() => {
    const savedSearch = storage.get("open_card_search");
    if (savedSearch) {
      form.setFieldsValue(savedSearch);
    }
    const savedInfo = storage.get("open_card_response");
    if (savedInfo) {
      setOpenCardInfo(savedInfo);
    }
    // 添加动画触发
    setTimeout(() => {
      setIsVisible(true);
    }, 100);
  }, [form]);

  // 监听 openCardInfo 变化
  useEffect(() => {
    if (openCardInfo && openCardInfo.result) {
      storage.set("open_card_response", openCardInfo, 30 * 60);
    }
  }, [openCardInfo]);

  // 修改 useActivate 的实现
  useActivate(() => {
    // 使用 setTimeout 确保在组件完全激活后再加载数据
    setTimeout(() => {
      const savedSearch = storage.get("open_card_search");
      if (savedSearch) {
        form.setFieldsValue(savedSearch);
      }
      const savedInfo = storage.get("open_card_response");
      if (savedInfo) {
        setOpenCardInfo(savedInfo);
      }
      setIsVisible(true);
    }, 0);
  });

  // 添加 useUnactivate 清理逻辑
  useUnactivate(() => {
    setLoading(false);
    setError(null);
    setIsVisible(false);
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 定义结果展示项
  const resultItems = [
    { label: "介质编号", key: "mediumNo" },
    { label: "活期折号", key: "pkNo" },
    { label: "客户名称", key: "custName" },
    { 
      label: "证件类型", 
      key: "perCertTpCd",
      // 添加格式化函数，用于证件类型的回显拼接
      format: (value: string) => certTypeMap[value] || value 
    },
    { label: "证件号码", key: "personalCertNo" },
    { label: "磁道信息", key: "sedMtInfo" },
    { label: "交易流水号", key: "globalBusiTrackNo" },
  ];

  return (
    <KeepAlive>
      <div className={`app-container ${isVisible ? 'fade-in-up' : ''}`}>
        <div
          className="content-wrapper"
          style={{
            maxWidth: "100%", // 设置最大宽度为100%以贴近浏览器边角
            minWidth: "800px", // 设置最小宽度
            margin: "0 auto", // 水平居中
            padding: "0 16px", // 减小左右padding，使内容更贴近边缘
            width: "100%", // 占满可用宽度
          }}
        >
          <div className={`card-container ${isVisible ? 'fade-in-up' : ''}`} style={{ animationDelay: '0.2s' }}>
            <Card
              title="开立卡折合一户"
              bordered={false}
              className="input-section"
              style={{
                marginBottom: "24px",
                marginTop: "24px",
                height: "min-content",
                padding: "24px",
                backgroundColor: "#ffffff",
                borderRadius: "16px", // 确保圆角一致
              }}
            >
              <ProForm
                form={form}
                onValuesChange={(changedValues, allValues) => {
                  // 设置30分钟后过期
                  storage.set("open_card_search", allValues, 30 * 60);
                }}
                onFinish={async (values) => {
                  setLoading(true);
                  setError(null);
                  try {
                    // 检查必填字段
                    if (!values.mediumNo) {
                      message.error("请填写介质编号");
                      return false;
                    }

                    const requestData = {
                      mediumNo: values.mediumNo?.trim(), // 添加 trim 处理
                    };
                    const res = await openCardAndPkOne(requestData);

                    if (res.code === 200 && res.data) {
                      message.success("开立成功");
                      try {
                        // 修改这里：将返回的JSON字符串解析为对象
                        const result = typeof res.data === 'string' ? JSON.parse(res.data) : res.data;
                        setOpenCardInfo({
                          result: result,
                        });
                        storage.set(
                          "open_card_response",
                          {
                            result: result,
                          },
                          30 * 60
                        );
                      } catch (parseError) {
                        message.error("返回数据格式错误");
                        return false;
                      }
                    } else {
                      message.error(res.msg || "开立失败");
                    }
                    return true;
                  } catch (err: any) {
                    const errorMessage = err?.message || "开立失败";
                    setError(errorMessage);
                    message.error(errorMessage);
                    return false;
                  } finally {
                    setLoading(false);
                  }
                }}
                submitter={{
                  searchConfig: {
                    submitText: "提交",
                    resetText: "重置",
                  },
                  submitButtonProps: {
                    style: { minWidth: "80px" },
                  },
                  resetButtonProps: {
                    style: { minWidth: "80px" },
                    onClick: () => {
                      form.resetFields();
                      setOpenCardInfo({
                        result: null,
                      });
                      storage.remove("open_card_search");
                      storage.remove("open_card_response");
                    },
                  },
                  render: (props, dom) => {
                    return (
                      <div
                        className="button-container"
                        style={{
                          display: "flex",
                          gap: "12px",
                          marginTop: "24px",
                          justifyContent: "flex-start", // 改为左对齐
                          marginLeft: "24px",  // 使用 marginLeft 替代 paddingLeft
                        }}
                      >
                        {dom.map((btn, index) => (
                          <span key={index} style={{ marginRight: index === 0 ? '12px' : '0' }}>
                            {btn}
                          </span>
                        ))}
                      </div>
                    );
                  },
                }}
              >
                <ProForm.Group>
                  <ProFormText
                    name="mediumNo"
                    label="介质编号"
                    placeholder="请输入介质编号"
                    rules={[
                      { required: true, message: "请输入介质编号" },
                      { max: 50, message: "介质编号长度不能超过50个字符" },
                      {
                        pattern: /^[a-zA-Z0-9]+$/,
                        message: "介质编号只能包含数字和大小写字母",
                      },
                      {
                        validator: async (_, value) => {
                          if (value && !validateMediumNo(value)) {
                            throw new Error("介质编号格式不正确");
                          }
                        },
                      },
                    ]}
                    width="md"
                    fieldProps={{
                      className: "hover-input",
                      onChange: (e) => {
                        const value = e.target.value.replace(/[^a-zA-Z0-9]/g, "");
                        e.target.value = value;
                      },
                    }}
                  />
                </ProForm.Group>
              </ProForm>
            </Card>
          </div>

          <div className={`card-container ${isVisible ? 'fade-in-up' : ''}`} style={{ animationDelay: '0.4s' }}>
            <Card
              title="开立结果"
              bordered={false}
              className="result-section"
              style={{
                height: "min-content",
                padding: "24px",
                backgroundColor: "#ffffff",
                marginBottom: "24px",
                position: "relative", // 添加相对定位
                borderRadius: "16px", // 确保圆角一致
              }}
            >
              {loading ? (
                <div style={{ textAlign: "center", padding: "40px" }}>
                  <Spin size="large" tip="处理中..." />
                </div>
              ) : error ? (
                <div
                  style={{ textAlign: "center", padding: "20px", color: "red" }}
                >
                  <Typography.Text>{error}</Typography.Text>
                </div>
              ) : openCardInfo.result ? (
                <div className="result-animation">
                  <Row gutter={[24, 16]}>
                    {resultItems.map((item, index) => (
                      <Col span={12} key={item.key}>
                        <Card 
                          size="small" 
                          title={item.label}
                          className="result-item-card"
                          style={{ 
                            borderRadius: "8px",
                            animation: `fadeInUp 0.5s ease-out forwards ${0.1 * index}s`
                          }}
                        >
                          <Paragraph copyable>
                            {item.format 
                              ? item.format(openCardInfo.result?.[item.key as keyof OpenCardResult] || "") 
                              : openCardInfo.result?.[item.key as keyof OpenCardResult] || "暂无数据"}
                          </Paragraph>
                        </Card>
                      </Col>
                    ))}
                  </Row>
                </div>
              ) : (
                <div style={{ textAlign: "center", padding: "40px" }}>
                  <Typography.Text type="secondary">暂无数据，请提交查询</Typography.Text>
                </div>
              )}
            </Card>
          </div>
        </div>
      </div>
    </KeepAlive>
  );
};

export default CardAndPkGen;