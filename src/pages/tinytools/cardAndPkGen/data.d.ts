/* *
 *
 * <AUTHOR>
 * @datetime  2024/12/15
 *
 * */

// 开立卡折合一户参数
export type OpenCardParams = {
  mediumNo: string;      // 介质编号
};

// 开立卡折合一户返回结果
export type OpenCardResult = {
  mediumNo: string;           // 介质编号
  pkNo: string;               // 折号
  custName: string;           // 客户名称
  perCertTpCd: string;        // 证件类型
  personalCertNo: string;     // 证件号码
  globalBusiTrackNo: string;  // 交易流水号
  sedMtInfo: string;          // 磁道信息
};