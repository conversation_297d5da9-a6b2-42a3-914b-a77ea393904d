import React, { useState, useEffect } from "react";
import { KeepAlive, useActivate, useUnactivate } from "react-activation";
import { storage } from "@/utils/storageUtils";
import { ProForm, ProFormText } from "@ant-design/pro-components";
import {
  Card,
  Typography,
  Collapse,
  Form,
  message,
  Spin,
} from "antd";
import { InfoCircleOutlined } from "@ant-design/icons";
import "./style.less";
import { pwdUpdate } from "./service";

const { Paragraph } = Typography;

interface PwdUpdateResponseInfo {
  mediumNo: string;
  encPwd: string;
  globalBusiTrackNo: string;
  servRespCd: string;
  servRespDescInfo: string;
}
const PwdUpdate: React.FC = () => {
  const [form] = Form.useForm();
  // 添加动画状态
  const [isVisible, setIsVisible] = useState(false);
  
  // 添加数据验证函数
  const validateMediumNo = (value: string) => {
    if (!value) return false;
    if (value.length > 50) return false; // 添加长度限制
    return /^[a-zA-Z0-9]+$/.test(value);
  };

  // 从localStorage初始化dac_response
  const [pwdUpdateResponseInfo, setPwdUpdateResponseInfo] = useState<PwdUpdateResponseInfo>(() => {
    const savedInfo = storage.get("pwdUpdate_ResponseInfo");
    if (savedInfo) {
      return savedInfo;
    }
    return {
      mediumNo: "",
      encPwd: "",
      globalBusiTrackNo: "",
      servRespCd: "",
      servRespDescInfo: "",
    };
  });

  // 添加初始化 useEffect
  useEffect(() => {
    const savedSearch = storage.get("pwdUpdate_requestInfo");
    if (savedSearch) {
      form.setFieldsValue(savedSearch);
    }
    const savedInfo = storage.get("pwdUpdate_ResponseInfo");
    if (savedInfo) {
      setPwdUpdateResponseInfo(savedInfo);
    }
    // 添加动画触发
    setTimeout(() => {
      setIsVisible(true);
    }, 100);
  }, [form]);

  // 监听 pwdUpdateResInfo 变化
  useEffect(() => {
    if (pwdUpdateResponseInfo && pwdUpdateResponseInfo.servRespDescInfo) {
      storage.set("pwdUpdate_ResponseInfo", pwdUpdateResponseInfo, 30 * 60);
    }
  }, [pwdUpdateResponseInfo]);

  // 修改 useActivate 的实现
  useActivate(() => {
    // 使用 setTimeout 确保在组件完全激活后再加载数据
    setTimeout(() => {
      const savedSearch = storage.get("pwdUpdate_requestInfo");
      if (savedSearch) {
        form.setFieldsValue(savedSearch);
      }
      const savedInfo = storage.get("pwdUpdate_ResponseInfo");
      if (savedInfo) {
        setPwdUpdateResponseInfo(savedInfo);
      }
      setIsVisible(true);
    }, 0);
  });

  // 添加 useUnactivate 清理逻辑
  useUnactivate(() => {
    setLoading(false);
    setError(null);
    form.resetFields();
    setIsVisible(false);
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const collapseItems = [
    {
      key: "1",
      label: "交易返回信息",
      children: (
        <Paragraph copyable>
          {pwdUpdateResponseInfo.servRespDescInfo || "暂无数据"}
        </Paragraph>
      ),
    },
    {
      key: "2",
      label: "介质号",
      children: (
        <Paragraph copyable>
          {pwdUpdateResponseInfo.mediumNo || "暂无数据"}
        </Paragraph>
      ),
    },
    {
      key: "3",
      label: "密码明文",
      children: (
        <Paragraph copyable>
          {pwdUpdateResponseInfo.encPwd || "暂无数据"}
        </Paragraph>
      ),
    },
    {
      key: "4",
      label: "交易流水号",
      children: (
        <Paragraph copyable>
          {pwdUpdateResponseInfo.globalBusiTrackNo || "暂无数据"}
        </Paragraph>
      ),
    },
    {
      key: "5",
      label: "交易返回码",
      children: (
        <Paragraph copyable>
          {pwdUpdateResponseInfo.servRespCd || "暂无数据"}
        </Paragraph>
      ),
    },
  ];

  return (
    <KeepAlive>
      <div className={`app-container ${isVisible ? 'fade-in-up' : ''}`}>
        <div
          className="content-wrapper"
          style={{
            maxWidth: "100%", // 设置最大宽度为100%以适应不同浏览器
            minWidth: "800px", // 设置最小宽度
            margin: "0 auto", // 水平居中
            padding: "0 16px", // 减小左右padding，使内容更贴近边缘
            width: "100%", // 占满可用宽度
            boxSizing: "border-box", // 添加盒模型属性
            WebkitBoxSizing: "border-box", // 添加浏览器前缀
            MozBoxSizing: "border-box", // 添加浏览器前缀
          }}
        >
          <div className={`card-container ${isVisible ? 'fade-in-up' : ''}`} style={{ animationDelay: '0.2s' }}>
            <Card
              title="修改介质密码"
              bordered={false}
              className="input-section"
              style={{
                marginBottom: "24px",
                marginTop: "24px", // 减小顶部外边距
                height: "min-content",
                padding: "24px",
                backgroundColor: "#ffffff",
                borderRadius: "16px", // 确保圆角一致
                width: "100%", // 确保宽度100%
                boxSizing: "border-box", // 添加盒模型属性
                WebkitBoxSizing: "border-box", // 添加浏览器前缀
                MozBoxSizing: "border-box", // 添加浏览器前缀
              }}
            >
              <ProForm
                key={Date.now()} // 添加这一行，强制表单重新渲染
                form={form}
                onValuesChange={(changedValues, allValues) => {
                  // 设置30分钟后过期
                  storage.set("pwdUpdate_requestInfo", allValues, 30 * 60);
                }}
                onFinish={async (values) => {
                  setLoading(true);
                  setError(null);
                  try {
                    // 检查必填字段
                    if (!values.mediumNo || !values.encPwd) {
                      message.error("请填写介质编号和明文密码");
                      return false;
                    }

                    const requestData = {
                      mediumNo: values.mediumNo?.trim(), // 添加 trim 处理
                      encPwd: values.encPwd,
                    };
                    const res = await pwdUpdate(requestData);

                    if (res.code === 200 && res.data) {
                      try {
                        const data = JSON.parse(res.data);
                        if (!data) {
                          throw new Error("数据解析失败");
                        }

                        setPwdUpdateResponseInfo({
                          mediumNo: data.mediumNo || "",
                          encPwd: data.encPwd || "",
                          globalBusiTrackNo: data.globalBusiTrackNo || "",
                          servRespCd: data.servRespCd || "",
                          servRespDescInfo: data.servRespDescInfo || "",
                        });
                        storage.set(
                          "mtinfo_mediaInfo",
                          {
                            mediumNo: data.mediumNo || "",
                            encPwd: data.encPwd || "",
                            globalBusiTrackNo: data.globalBusiTrackNo || "",
                            servRespCd: data.servRespCd || "",
                            servRespDescInfo: data.servRespDescInfo || "",
                          },
                          30 * 60
                        );
                      } catch (parseError) {
                        message.error("返回数据格式错误");
                        return false;
                      }
                    } else {
                      message.error(res.msg || "重置密码失败");
                    }
                    return true;
                  } catch (err: any) {
                    const errorMessage = err?.message || "重置密码失败";
                    setError(errorMessage);
                    message.error(errorMessage);
                    return false;
                  } finally {
                    setLoading(false);
                  }
                }}
                submitter={{
                  searchConfig: {
                    submitText: "提交",
                    resetText: "重置",
                  },
                  submitButtonProps: {
                    style: { minWidth: "80px" },
                  },
                  resetButtonProps: {
                    style: { minWidth: "80px" },
                    onClick: () => {
                      form.resetFields();
                      setPwdUpdateResponseInfo({
                        mediumNo: "",
                        encPwd: "",
                        globalBusiTrackNo: "",
                        servRespCd: "",
                        servRespDescInfo: "",
                      });
                      storage.remove("pwdUpdate_requestInfo");
                      storage.remove("pwdUpdate_ResponseInfo");
                    },
                  },
                  render: (props, dom) => {
                    return (
                      <div
                        className="button-container"
                        style={{
                          display: "flex",
                          gap: "12px",
                          marginTop: "24px",
                          justifyContent: "flex-start", // 改为左对齐
                        }}
                      >
                        {dom.map((btn, index) => (
                          <span key={index} style={{ marginRight: index === 0 ? '12px' : '0' }}>
                            {btn}
                          </span>
                        ))}
                      </div>
                    );
                  },
                }}
              >
                <ProForm.Group>
                  <ProFormText
                    name="mediumNo"
                    label="介质编号"
                    placeholder="请输入介质编号"
                    rules={[
                      { required: true, message: "请输入介质编号" },
                      { max: 50, message: "介质编号长度不能超过50个字符" },
                      {
                        pattern: /^[a-zA-Z0-9]+$/,
                        message: "介质编号只能包含数字和大小写字母",
                      },
                      {
                        validator: async (_, value) => {
                          if (value && !validateMediumNo(value)) {
                            throw new Error("介质编号格式不正确");
                          }
                        },
                      },
                    ]}
                    width="md"
                    fieldProps={{
                      className: "hover-input",
                      onChange: (e) => {
                        const value = e.target.value.replace(/[^a-zA-Z0-9]/g, "");
                        e.target.value = value;
                      },
                    }}
                  />
                  <ProFormText
                    name="encPwd"
                    label="加密密码"
                    placeholder="请输入加密密码明文"
                    rules={[
                      { required: true, message: "请输入加密密码明文" },
                      { max: 6, message: "加密密码长度不能超过6个字符" },
                      {
                        pattern: /^[0-9]+$/,
                        message: "加密密码只能为数字",
                      },
                    ]}
                    width="md"
                    fieldProps={{
                      className: "hover-input",
                      onChange: (e) => {
                        const value = e.target.value.replace(/[^0-9]/g, "");
                        e.target.value = value;
                      },
                    }}
                  />
                </ProForm.Group>
              </ProForm>
            </Card>
          </div>

          <div className={`card-container ${isVisible ? 'fade-in-up' : ''}`} style={{ animationDelay: '0.4s' }}>
            <Card
              title="操作结果"
              bordered={false}
              className="result-section"
              style={{
                height: "min-content",
                padding: "24px",
                backgroundColor: "#ffffff",
                marginBottom: "24px",
                position: "relative", // 添加相对定位
                borderRadius: "16px", // 确保圆角一致
                width: "100%", // 确保宽度100%
                boxSizing: "border-box", // 添加盒模型属性
                WebkitBoxSizing: "border-box", // 添加浏览器前缀
                MozBoxSizing: "border-box", // 添加浏览器前缀
              }}
            >
              {loading ? (
                <div style={{ textAlign: "center", padding: "40px" }}>
                  <Spin size="large" tip="处理中..." />
                </div>
              ) : error ? (
                <div
                  style={{ textAlign: "center", padding: "20px", color: "red" }}
                >
                  <Typography.Text>{error}</Typography.Text>
                </div>
              ) : (
                <Collapse
                  defaultActiveKey={["1"]}
                  expandIcon={({ isActive }) => (
                    <InfoCircleOutlined rotate={isActive ? -90 : 90} />
                  )}
                  className="custom-collapse"
                >
                  {collapseItems.map((item) => (
                    <Collapse.Panel header={item.label} key={item.key}>
                      {item.children}
                    </Collapse.Panel>
                  ))}
                </Collapse>
              )}
            </Card>
          </div>
        </div>
      </div>
    </KeepAlive>
  );
};

export default PwdUpdate;
