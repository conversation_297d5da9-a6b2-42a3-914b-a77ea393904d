.app-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding-top: 48px; // 改用 padding-top 替代 margin-top
  background: #fff;
  
  .main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 16px 12px 24px 12px;
    overflow: hidden;
    
    .editor-container {
      display: flex;
      gap: 16px;
      flex: 1;
      margin: 0 12px;

      .editor-section {
        flex: 1;
        display: flex;
        flex-direction: column;
        background: #fff;
        padding: 16px;
        border-radius: 6px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        
        h2 {
          color: #333;
          margin-bottom: 16px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          flex-shrink: 0;

          .button-group {
            display: flex;
            gap: 8px;

            .ant-btn {
              border-radius: 4px;

              &.format-button {
                background-color: #fff7e6;
                border-color: #ffe7ba;
                color: #d48806;
            
                &:hover {
                  background-color: #fff1d6;
                  border-color: #ffd666;
                }
              }
            
              &.tree-button {
                background-color: #fff0f6;
                border-color: #ffcee4;
                color: #eb2f96;
            
                &:hover {
                  background-color: #ffe6f0;
                  border-color: #ffadd2;
                }
              }
            
              &.copy-button {
                background-color: #f6ffed;
                border-color: #d9f7be;
                color: #52c41a;
            
                &:hover {
                  background-color: #efffde;
                  border-color: #b7eb8f;
                }
              }
              
              &.clear-button {
                background-color: #f9f0ff;
                border-color: #d3adf7;
                color: #722ed1;
            
                &:hover {
                  background-color: #efdbff;
                  border-color: #b37feb;
                }
              }
            }
          }
        }

        .cm-editor {
          height: calc(100vh - 248px) !important; // 调整编辑器高度
          border-radius: 4px;
          border: 1px solid #d9d9d9;

          .cm-scroller {
            overflow: auto !important;
          }

          .cm-content {
            white-space: pre-wrap !important;
            word-break: break-all !important;
            min-height: 100%;
          }
        }
      }
    }
  }
}