import { useState, useRef, useEffect } from 'react';
import { Layout, Button, message, Modal } from 'antd';
import CodeMirror from '@uiw/react-codemirror';
import { json } from '@codemirror/lang-json';
import { dracula } from '@uiw/codemirror-theme-dracula';
import * as d3 from 'd3';
import { FormatPainterOutlined, ApartmentOutlined, CopyOutlined } from '@ant-design/icons';
import { KeepAlive, useActivate, useUnactivate } from "react-activation";
import { storage } from "@/utils/storageUtils";
import './style.less';

const { Content } = Layout;

function App() {
  // 从localStorage初始化JSON输入和输出
  const [jsonInput, setJsonInput] = useState<string>(() => {
    const savedInput = storage.get("json_format_input");
    return savedInput || '';
  });
  
  const [jsonOutput, setJsonOutput] = useState<string>(() => {
    const savedOutput = storage.get("json_format_output");
    return savedOutput || '';
  });
  
  const [messageApi, contextHolder] = message.useMessage();
  const [isTreeVisible, setIsTreeVisible] = useState(false);

  // 添加初始化 useEffect
  useEffect(() => {
    const savedInput = storage.get("json_format_input");
    if (savedInput) {
      setJsonInput(savedInput);
    }
    const savedOutput = storage.get("json_format_output");
    if (savedOutput) {
      setJsonOutput(savedOutput);
    }
  }, []);

  // 监听 jsonInput 变化
  useEffect(() => {
    if (jsonInput) {
      storage.set("json_format_input", jsonInput, 30 * 60); // 30分钟过期
    }
  }, [jsonInput]);

  // 监听 jsonOutput 变化
  useEffect(() => {
    if (jsonOutput) {
      storage.set("json_format_output", jsonOutput, 30 * 60); // 30分钟过期
    }
  }, [jsonOutput]);

  // 修改 useActivate 的实现
  useActivate(() => {
    // 使用 setTimeout 确保在组件完全激活后再加载数据
    setTimeout(() => {
      const savedInput = storage.get("json_format_input");
      if (savedInput) {
        setJsonInput(savedInput);
      }
      const savedOutput = storage.get("json_format_output");
      if (savedOutput) {
        setJsonOutput(savedOutput);
      }
    }, 0);
  });

  // 添加 useUnactivate 清理逻辑
  useUnactivate(() => {
    setIsTreeVisible(false);
  });

  const handleFormat = () => {
    try {
      let processedInput = jsonInput.trim();
      if (processedInput.startsWith('"') && processedInput.endsWith('"')) {
        try {
          processedInput = JSON.parse(processedInput);
        } catch (e) {
          processedInput = jsonInput;
        }
      }

      const parsedJson = JSON.parse(processedInput);
      const formattedJson = JSON.stringify(parsedJson, null, 2);
      setJsonOutput(formattedJson);
      messageApi.success('JSON 格式化成功！');
    } catch (error) {
      messageApi.error('无效的 JSON 格式！');
    }
  };

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(jsonOutput);
      messageApi.success('复制成功！');
    } catch (error) {
      // 如果 clipboard API 不可用，尝试使用传统方法
      try {
        const textArea = document.createElement('textarea');
        textArea.value = jsonOutput;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        messageApi.success('复制成功！');
      } catch (fallbackError) {
        messageApi.error('复制失败！');
      }
    }
  };

  const svgRef = useRef<SVGSVGElement>(null);

  const processJsonData = (data: any): { key: string; value: any } => {
    if (typeof data !== 'object' || data === null) {
      return { key: '', value: data }
    }
  
    if (Array.isArray(data)) {
      return {
        key: '',
        value: data.map((item, index) => ({
          key: `[${index}]`,
          value: processJsonData(item).value,
          children: typeof item === 'object' && item !== null ? processJsonData(item).value : undefined
        }))
      }
    }
  
    return {
      key: '',
      value: Object.entries(data).map(([k, v]) => ({
        key: k,
        value: v,
        children: typeof v === 'object' && v !== null ? processJsonData(v).value : undefined
      }))
    }
  }

  const drawJsonTree = (data: any) => {
    if (!svgRef.current) return

    // 清除现有的图形
    d3.select(svgRef.current).selectAll('*').remove()

    // 处理数据结构
    const processedData = processJsonData(data).value
    const root = d3.hierarchy<{
      key: string;
      value: any;
      children: any;
    }>({
      key: 'root',
      value: processedData,
      children: processedData
    })

    // 设置树形图布局，调整节点间距
    const treeLayout = d3.tree<{
      key: string;
      value: any;
      children: any;
    }>().nodeSize([80, 400])
    treeLayout(root)

    // 调整叶子节点的位置，实现交错排列
    root.leaves().forEach((leaf: d3.HierarchyNode<{
      key: string;
      value: any;
      children: any;
    }>, i) => {
      if (i % 2 === 1) {
        (leaf as d3.HierarchyPointNode<any>).x += 40 // 偶数节点向下偏移
      }
    })

    // 获取 SVG 容器
    const svg = d3.select(svgRef.current)
    
    // 添加缩放功能
    const zoom = d3.zoom<SVGSVGElement, unknown>()
      .scaleExtent([0.1, 3])
      .on('zoom', (event) => {
        g.attr('transform', event.transform)
      })

    svg.call(zoom)

    // 创建主要绘图区域
    const g = svg.append('g')
      .attr('transform', 'translate(200, 100)')

    // 绘制连接线
    g.selectAll('.link')
      .data(root.links())
      .enter()
      .append('path')
      .attr('class', 'link')
      .attr('fill', 'none')
      .attr('stroke', '#4a90e2')
      .attr('stroke-width', 1.5)
      .attr('stroke-opacity', 0.7)
      .attr('d', d3.linkHorizontal<any, any>()
        .x((d: any) => d.y)
        .y((d: any) => d.x)
      )

    // 创建节点组
    const nodes = g.selectAll<SVGGElement, d3.HierarchyPointNode<any>>('.node')
      .data(root.descendants())
      .enter()
      .append('g')
      .attr('class', 'node')
      .attr('transform', function(d) {
        return `translate(${d.y},${d.x})`
      })

    // 添加节点矩形
    nodes.append('rect')
      .attr('x', -180)
      .attr('y', -20)
      .attr('width', 360)
      .attr('height', 40)
      .attr('rx', 8)
      .attr('ry', 8)
      .attr('fill', '#2a2a2a')
      .attr('stroke', '#4a90e2')
      .attr('stroke-width', 2)
      .attr('stroke-opacity', 0.8)

    // 添加节点文本
    nodes.append('text')
      .attr('dy', '0.31em')
      .attr('text-anchor', 'middle')
      .attr('fill', '#e6e6e6')
      .attr('font-size', '12px')
      .each(function(this: any, d: d3.HierarchyNode<{
        key: string;
        value: any;
        children: any;
      }>) {
        if (d.data.key === 'root') {
          d3.select(this).text('JSON Root')
          return
        }
  
        let text = d.data.key
        const value = d.data.value
  
        if (typeof value !== 'object') {
          const displayValue = typeof value === 'string' ? `"${value}"` : String(value)
          text += `: ${displayValue}`
        } else if (Array.isArray(value)) {
          text += ` [${value.length}]`
        } else if (value === null) {
          text += ': null'
        } else if (typeof value === 'object') {
          const keys = Object.keys(value)
          text += ` {${keys.length}}`
        }
  
        // 文本长度限制并添加省略号
        if (text.length > 30) {
          text = text.substring(0, 30) + '...'
        }
  
        d3.select(this).text(text)
      })

    // 自动调整视图
    const bounds = (g.node() as SVGGElement)?.getBBox()
    if (bounds) {
      const scale = Math.min(
        1000 / bounds.width,
        600 / bounds.height
      )
      const transform = d3.zoomIdentity
        .translate(
          (1200 - bounds.width * scale) / 2 - bounds.x * scale,
          (700 - bounds.height * scale) / 2 - bounds.y * scale
        )
        .scale(scale * 0.7)
      
      svg.transition()
        .duration(750)
        .call(zoom.transform, transform)
    }
  }

  const handleShowTree = () => {
    try {
      const jsonData = JSON.parse(jsonOutput)
      setIsTreeVisible(true)
      // 在下一个渲染周期绘制树形图
      setTimeout(() => {
        drawJsonTree(jsonData)
      }, 100)
    } catch (error) {
      messageApi.error('无效的 JSON 格式！')
    }
  }

  // 添加清除缓存的函数
  const handleClear = () => {
    setJsonInput('');
    setJsonOutput('');
    storage.remove("json_format_input");
    storage.remove("json_format_output");
    messageApi.success('已清除所有内容和缓存');
  };

  return (
    <KeepAlive>
      <Layout className="app-container">
        {contextHolder}
        <Content className="main-content">
          <div className="editor-container">
            <div className="editor-section">
              <h2>
                输入 JSON
                <div className="button-group">
                  <Button type="primary" onClick={handleFormat} className="format-button">
                    <FormatPainterOutlined /> 格式化
                  </Button>
                  <Button onClick={handleClear} className="clear-button">
                    清除
                  </Button>
                </div>
              </h2>
              <CodeMirror
                value={jsonInput}
                height="100%"
                theme={dracula}
                extensions={[json()]}
                onChange={(value) => setJsonInput(value)}
                style={{ fontSize: '14px' }}
                basicSetup={{
                  lineNumbers: true,
                  foldGutter: true,
                  highlightActiveLine: true,
                  dropCursor: true,
                  allowMultipleSelections: true,
                  indentOnInput: true,
                }}
              />
            </div>
            <div className="editor-section">
              <h2>
                格式化结果
                <div className="button-group">
                  <Button type="primary" onClick={handleShowTree} className="tree-button">
                    <ApartmentOutlined /> 树状图
                  </Button>
                  <Button type="primary" onClick={handleCopy} className="copy-button">
                    <CopyOutlined /> 一键复制
                  </Button>
                </div>
              </h2>
              <CodeMirror
                value={jsonOutput}
                height="100%"
                theme={dracula}
                extensions={[json()]}
                editable={true}
                onChange={(value) => setJsonOutput(value)}
                style={{ fontSize: '14px' }}
                basicSetup={{
                  lineNumbers: true,
                  foldGutter: true,
                  highlightActiveLine: true,
                  dropCursor: true,
                  allowMultipleSelections: true,
                  indentOnInput: true,
                }}
              />
            </div>
          </div>
        </Content>
        <Modal
          title="JSON 树状结构"
          open={isTreeVisible}
          onCancel={() => setIsTreeVisible(false)}
          width={1200}
          centered
          footer={null}
          bodyStyle={{
            height: '70vh',
            overflow: 'auto',
            background: '#1e1e1e',
            padding: '20px'
          }}
        >
          <svg
            ref={svgRef}
            style={{ width: '100%', height: '100%', background: '#1e1e1e' }}
          />
        </Modal>
      </Layout>
    </KeepAlive>
  );
}

export default App;
