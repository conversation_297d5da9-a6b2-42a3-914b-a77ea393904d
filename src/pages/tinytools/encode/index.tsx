import React, { useState, useEffect } from "react";
import { KeepAlive, useActivate, useUnactivate } from "react-activation";
import { storage } from "@/utils/storageUtils";
import {
  ProForm,
  ProFormText,
  ProFormSelect,
} from "@ant-design/pro-components";
import {
  Card,
  Typography,
  Collapse,
  Form,
  message,
  Spin,
} from "antd";
import { InfoCircleOutlined } from "@ant-design/icons";
import "./style.less";
import { encode } from "./service";

const { Paragraph } = Typography;

interface EncodeResonseInfo {
  encodeEncPwd: string;
}
const PwdEncode: React.FC = () => {
  const [form] = Form.useForm();
  // 添加状态来跟踪当前选择的加密方式
  const [encodeFlag, setEncodeFlag] = useState<string>("1"); // 默认为"1-介质参与加密"
  // 添加动画状态
  const [isVisible, setIsVisible] = useState(false);

  // 添加数据验证函数
  const validateMediumNo = (value: string) => {
    if (!value) return false;
    if (value.length > 50) return false; // 添加长度限制
    return /^[a-zA-Z0-9]+$/.test(value);
  };

  // 从localStorage初始化encodeResonseInfo
  const [encodeResonseInfo, setEncodeResonseInfo] = useState<EncodeResonseInfo>(() => {
    const savedInfo = storage.get("encode_responseInfo");
    if (savedInfo) {
      return savedInfo;
    }
    return {
      encodeEncPwd: "",
    };
  });

  // 添加初始化 useEffect
  useEffect(() => {
    const savedSearch = storage.get("encode_request");
    if (savedSearch) {
      form.setFieldsValue(savedSearch);
      // 初始化时设置encodeFlag
      if (savedSearch.encodeFlag) {
        setEncodeFlag(savedSearch.encodeFlag);
      }
    }
    const savedInfo = storage.get("encode_responseInfo");
    if (savedInfo) {
      setEncodeResonseInfo(savedInfo);
    }
    // 添加动画触发
    setTimeout(() => {
      setIsVisible(true);
    }, 100);
  }, [form]);

  // 监听 encodeResonseInfo 变化
  useEffect(() => {
    if (encodeResonseInfo) {
      storage.set("encode_responseInfo", encodeResonseInfo, 30 * 60);
    }
  }, [encodeResonseInfo]);

  // 当 encodeResonseInfo 更新时保存到localStorage,只保留30分钟
  useActivate(() => {
    // 使用 setTimeout 确保在组件完全激活后再加载数据
    setTimeout(() => {
      const savedSearch = storage.get("encode_request");
      if (savedSearch) {
        form.setFieldsValue(savedSearch);
        if (savedSearch.encodeFlag) {
          setEncodeFlag(savedSearch.encodeFlag);
        }
      }
      const savedInfo = storage.get("encode_responseInfo");
      if (savedInfo) {
        setEncodeResonseInfo(savedInfo);
      }
      setIsVisible(true);
    }, 0);
  });

  useUnactivate(() => {
    setLoading(false);
    setError(null);
    form.resetFields();
    setIsVisible(false);
  });
  
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const collapseItems = [
    {
      key: "1",
      label: "加密密码密文",
      children: (
        <Paragraph copyable>{encodeResonseInfo.encodeEncPwd || "暂无数据"}</Paragraph>
      ),
    },
  ];

  return (
    <KeepAlive>
      <div className={`app-container ${isVisible ? 'fade-in-up' : ''}`}>
        <div 
          className="content-wrapper" 
          style={{
            maxWidth: '100%',         // 设置最大宽度为100%以适应不同浏览器
            minWidth: '800px',        // 设置最小宽度
            margin: '0 auto',         // 水平居中
            padding: '0 16px',        // 减小左右padding，使内容更贴近边缘
            width: '100%',            // 占满可用宽度
            boxSizing: 'border-box',  // 添加盒模型属性
            WebkitBoxSizing: 'border-box', // 添加浏览器前缀
            MozBoxSizing: 'border-box',    // 添加浏览器前缀
          }}
        >
          <div className={`card-container ${isVisible ? 'fade-in-up' : ''}`} style={{ animationDelay: '0.2s' }}>
            <Card
              title="加密介质密码"
              bordered={false}
              className="input-section"
              style={{
                marginBottom: "24px",
                marginTop: "24px", // 减小顶部外边距
                height: "min-content",
                padding: "24px",
                backgroundColor: "#ffffff",
                borderRadius: "16px", // 确保圆角一致
                width: "100%", // 确保宽度100%
                boxSizing: "border-box", // 添加盒模型属性
                WebkitBoxSizing: "border-box", // 添加浏览器前缀
                MozBoxSizing: "border-box", // 添加浏览器前缀
              }}
            >
              <ProForm
                key={Date.now()} // 强制重新渲染
                form={form}
                onValuesChange={(changedValues, allValues) => {
                  // 设置30分钟后过期
                  storage.set("encode_request", allValues, 30 * 60);
                  
                  // 当加密方式改变时，更新状态
                  if ('encodeFlag' in changedValues) {
                    setEncodeFlag(changedValues.encodeFlag);
                    
                    // 当选择"5-介质不参与加密"时，清空介质编号字段
                    if (changedValues.encodeFlag === "5") {
                      form.setFieldsValue({ mediumNo: "" });
                      // 更新存储的数据
                      const updatedValues = { ...allValues, mediumNo: "" };
                      storage.set("encode_request", updatedValues, 30 * 60);
                    }
                  }
                }}
                onFinish={async (values) => {
                  setLoading(true);
                  setError(null);
                  try {
                    // 检查必填字段
                    const requiredFields = ['encPwd', 'encodeFlag', 'sysCode'];
                    if (values.encodeFlag === "1") {
                      requiredFields.push('mediumNo');
                    }
                    
                    const missingFields = requiredFields.filter(field => !values[field]);
                    if (missingFields.length > 0) {
                      message.error("请填写必输项");
                      return false;
                    }

                    const requestData = {
                      mediumNo: values.encodeFlag === "1" ? values.mediumNo?.trim() : "", // 当不需要介质编号时传空字符串
                      encPwd: values.encPwd,
                      encodeFlag: values.encodeFlag,
                      sysCode: values.sysCode,
                    };
                    const res = await encode(requestData);

                    if (res.code === 200 && res.data) {
                      try {
                        const data = JSON.parse(res.data);
                        if (!data) {
                          throw new Error('数据解析失败');
                        }

                        const responseInfo = {
                          encodeEncPwd: data
                        };

                        setEncodeResonseInfo(responseInfo);
                        storage.set("encode_responseInfo", responseInfo, 30 * 60);
                        message.success("获取加密密码成功");
                      } catch (parseError) {
                        message.error('返回数据格式错误');
                        return false;
                      }
                    } else {
                      message.error(res.msg || "获取加密密码失败");
                    }
                    return true;
                  } catch (err: any) {
                    const errorMessage = err?.message || '获取加密密码失败';
                    setError(errorMessage);
                    message.error(errorMessage);
                    return false;
                  } finally {
                    setLoading(false);
                  }
                }}
                submitter={{
                  searchConfig: {
                    submitText: "提交",
                    resetText: "重置"
                  },
                  submitButtonProps: {
                    style: { minWidth: '80px' }
                  },
                  resetButtonProps: {
                    style: { minWidth: '80px' },
                    onClick: () => {
                      form.resetFields();
                      setEncodeResonseInfo({
                        encodeEncPwd: "",
                      });
                      storage.remove("encode_request");
                      storage.remove("encode_responseInfo");
                    },
                  },
                  render: (props, dom) => {
                    return (
                      <div
                        className="button-container"
                        style={{
                          display: "flex",
                          gap: "12px",
                          marginTop: "24px",
                          justifyContent: "flex-start", // 确保左对齐
                          marginLeft: "0", // 移除左边距，确保完全左对齐
                          paddingLeft: "0", // 移除左内边距
                        }}
                      >
                        {dom.map((btn, index) => (
                          <span key={index} style={{ marginRight: index === 0 ? '12px' : '0' }}>
                            {btn}
                          </span>
                        ))}
                      </div>
                    );
                  }
                }}
              >
                <ProForm.Group>
                  <ProFormText
                    name="mediumNo"
                    label="介质编号"
                    placeholder="请输入介质编号"
                    rules={[
                      { 
                        required: encodeFlag === "1", 
                        message: "请输入介质编号" 
                      },
                      { max: 50, message: "介质编号长度不能超过50个字符" },
                      {
                        pattern: /^[a-zA-Z0-9]+$/,
                        message: "介质编号只能包含数字和大小写字母",
                      },
                      {
                        validator: async (_, value) => {
                          if (encodeFlag === "1" && value && !validateMediumNo(value)) {
                            throw new Error('介质编号格式不正确');
                          }
                          return Promise.resolve();
                        },
                      },
                    ]}
                    width="md"
                    fieldProps={{
                      className: "hover-input",
                      onChange: (e) => {
                        const value = e.target.value.replace(/[^a-zA-Z0-9]/g, "");
                        e.target.value = value;
                      },
                      disabled: encodeFlag === "5", // 当选择"5-介质不参与加密"时禁用介质编号输入框
                    }}
                  />
                  <ProFormText
                    name="encPwd"
                    label="加密密码"
                    placeholder="请输入加密密码明文"
                    rules={[
                      { required: true, message: "请输入加密密码明文" },
                      { max: 6, message: "加密密码长度不能超过6个字符" },
                      {
                        pattern: /^[0-9]+$/,
                        message: "加密密码只能为数字",
                      },
                    ]}
                    width="md"
                    fieldProps={{
                      className: "hover-input",
                      onChange: (e) => {
                        const value = e.target.value.replace(/[^0-9]/g, "");
                        e.target.value = value;
                      },
                    }}
                  />
                  <ProFormSelect
                    name="encodeFlag"
                    label="加密方式"
                    rules={[{ required: true, message: "请选择加密方式" }]}
                    width="md"
                    options={[
                      { label: "1-介质参与加密", value: "1" },
                      { label: "5-介质不参与加密", value: "5" },
                    ]}
                    fieldProps={{
                      className: "hover-input",
                    }}
                  />
                  <ProFormText
                    name="sysCode"
                    label="加密系统号"
                    placeholder="请输入加密系统号，例：柜面-99710640000"
                    width="md"
                    rules={[
                      { required: true, message: "请输入加密系统号" },
                    ]}
                    fieldProps={{
                      className: "hover-input",
                    }}
                  />
                </ProForm.Group>
              </ProForm>
            </Card>
          </div>

          <div className={`card-container ${isVisible ? 'fade-in-up' : ''}`} style={{ animationDelay: '0.4s' }}>
            <Card
              title="加密结果"
              bordered={false}
              className="result-section"
              style={{
                height: "min-content",
                padding: "24px",
                backgroundColor: "#ffffff",
                marginBottom: "24px",
                position: "relative", // 添加相对定位
                borderRadius: "16px", // 确保圆角一致
                width: "100%", // 确保宽度100%
                boxSizing: "border-box", // 添加盒模型属性
                WebkitBoxSizing: "border-box", // 添加浏览器前缀
                MozBoxSizing: "border-box", // 添加浏览器前缀
              }}
            >
              {loading ? (
                <div style={{ textAlign: "center", padding: "40px" }}>
                  <Spin size="large" tip="处理中..." />
                </div>
              ) : error ? (
                <div
                  style={{ textAlign: "center", padding: "20px", color: "red" }}
                >
                  <Typography.Text>{error}</Typography.Text>
                </div>
              ) : (
                <Collapse
                defaultActiveKey={["1"]}
                expandIcon={({ isActive }) => (
                  <InfoCircleOutlined rotate={isActive ? -90 : 90} />
                )}
                className="custom-collapse"
              >
                {collapseItems.map((item) => (
                  <Collapse.Panel header={item.label} key={item.key}>
                    {item.children}
                  </Collapse.Panel>
                ))}
              </Collapse>
            )}
          </Card>
          </div>
        </div>
      </div>
    </KeepAlive>
  );
};

export default PwdEncode;
