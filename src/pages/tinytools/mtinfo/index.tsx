import React, { useState, useEffect } from "react";
import moment from "moment";
import { KeepAlive, useActivate, useUnactivate } from "react-activation";
import { storage } from "@/utils/storageUtils";
import {
  ProForm,
  ProFormText,
  ProFormSelect,
} from "@ant-design/pro-components";
import {
  Card,
  Typography,
  Collapse,
  DatePicker,
  Form,
  message,
  Select,
  Spin,
} from "antd";
import { InfoCircleOutlined } from "@ant-design/icons";
import "./style.less";
import { mtInfoCac } from "./service";

const { Paragraph } = Typography;

interface MediaInfo {
  sedMtInfo: string;
  trdMtInfo: string;
  icCardMtInfo: string;
  icCardDataAreaInfo: string;
}

const MTinfoCac: React.FC = () => {
  const [form] = Form.useForm();
  // 添加动画状态
  const [isVisible, setIsVisible] = useState(false);

  // 添加数据验证函数
  const validateMediumNo = (value: string) => {
    if (!value) return false;
    if (value.length > 50) return false; // 添加长度限制
    return /^[a-zA-Z0-9]+$/.test(value);
  };

  // 从localStorage初始化mediaInfo
  const [mediaInfo, setMediaInfo] = useState<MediaInfo>(() => {
    const savedInfo = storage.get("mtinfo_mediaInfo");
    if (savedInfo) {
      return savedInfo;
    }
    return {
      sedMtInfo: "",
      trdMtInfo: "",
      icCardMtInfo: "",
      icCardDataAreaInfo: "",
    };
  });

  // 添加初始化 useEffect
  useEffect(() => {
    const savedSearch = storage.get("mtinfo_search");
    if (savedSearch) {
      form.setFieldsValue(savedSearch);
    }
    const savedInfo = storage.get("mtinfo_mediaInfo");
    if (savedInfo) {
      setMediaInfo(savedInfo);
    }
    // 添加动画触发
    setTimeout(() => {
      setIsVisible(true);
    }, 100);
  }, [form]);

  // 当mediaInfo更新时保存到localStorage,只保留30分钟
  useEffect(() => {
    if (mediaInfo && mediaInfo.sedMtInfo) {
      storage.set("mtinfo_mediaInfo", mediaInfo, 30 * 60);
    }
  }, [mediaInfo]);

  // 修改 useActivate 的实现
  useActivate(() => {
    // 使用 setTimeout 确保在组件完全激活后再加载数据
    setTimeout(() => {
      const savedSearch = storage.get("mtinfo_search");
      if (savedSearch) {
        form.setFieldsValue(savedSearch);
      }
      const savedInfo = storage.get("mtinfo_mediaInfo");
      if (savedInfo) {
        setMediaInfo(savedInfo);
      }
      setIsVisible(true);
    }, 0);
  });

   // 添加 useUnactivate 清理逻辑
   useUnactivate(() => {
    setLoading(false);
    setError(null);
    form.resetFields();
    setIsVisible(false);
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 1. 定义卡介质选项
  const cardMediumCodeOptions = [
    { value: "1", label: "1-磁条卡" },
    { value: "2", label: "2-复合卡" },
    { value: "3", label: "3-单芯片卡" },
    { value: "4", label: "4-虚拟卡" },
  ];

  // 2. 定义卡品种选项
  const cardKindCdOptions = [
    { value: "01", label: "01-储蓄卡" },
    { value: "02", label: "02-联名卡(储蓄卡)" },
    { value: "03", label: "03-联名卡(绿卡通)" },
    { value: "04", label: "04-认同卡(绿卡通)" },
    { value: "05", label: "05-认同卡(储蓄卡)" },
    { value: "06", label: "06-绿卡通卡" },
    { value: "07", label: "07-绿卡通副卡" },
    { value: "08", label: "08-小额支付卡" },
    { value: "09", label: "09-万事达卡" },
    { value: "13", label: "13-绿卡通(iverse网联)" },
  ];

  const collapseItems = [
    {
      key: "1",
      label: "二磁道信息",
      children: (
        <Paragraph copyable>{mediaInfo.sedMtInfo || "暂无数据"}</Paragraph>
      ),
    },
    {
      key: "2",
      label: "三磁道信息",
      children: (
        <Paragraph copyable>{mediaInfo.trdMtInfo || "暂无数据"}</Paragraph>
      ),
    },
    {
      key: "3",
      label: "芯片信息",
      children: (
        <Paragraph copyable>{mediaInfo.icCardMtInfo || "暂无数据"}</Paragraph>
      ),
    },
    {
      key: "4",
      label: "IC域信息",
      children: (
        <Paragraph copyable>
          {mediaInfo.icCardDataAreaInfo || "暂无数据"}
        </Paragraph>
      ),
    },
  ];

  return (
    <KeepAlive>
      <div className={`app-container ${isVisible ? 'fade-in-up' : ''}`}>
        <div
          className="content-wrapper"
          style={{
            maxWidth: "100%", // 设置最大宽度为100%以适应不同浏览器
            minWidth: "800px", // 设置最小宽度
            margin: "0 auto", // 水平居中
            padding: "0 16px", // 减小左右padding，使内容更贴近边缘
            width: "100%", // 占满可用宽度
            boxSizing: "border-box", // 添加盒模型属性
            WebkitBoxSizing: "border-box", // 添加浏览器前缀
            MozBoxSizing: "border-box", // 添加浏览器前缀
          }}
        >
          <div className={`card-container ${isVisible ? 'fade-in-up' : ''}`} style={{ animationDelay: '0.2s' }}>
            <Card
              title="计算介质磁道信息"
              bordered={false}
              className="input-section"
              style={{
                marginBottom: "24px",
                marginTop: "24px", // 减小顶部外边距
                height: "min-content",
                padding: "24px",
                backgroundColor: "#ffffff",
                borderRadius: "16px", // 确保圆角一致
                width: "100%", // 确保宽度100%
                boxSizing: "border-box", // 添加盒模型属性
                WebkitBoxSizing: "border-box", // 添加浏览器前缀
                MozBoxSizing: "border-box", // 添加浏览器前缀
              }}
            >
              <ProForm
                key={Date.now()} // 强制重新渲染
                form={form}
                onValuesChange={(changedValues, allValues) => {
                  // 设置30分钟后过期
                  storage.set("mtinfo_search", allValues, 30 * 60);
                }}
                onFinish={async (values) => {
                  setLoading(true);
                  setError(null);
                  try {
                    // 检查必填字段
                    if (!values.mediumNo || !values.flag) {
                      message.error("请填写介质编号和是否定制卡标识");
                      return false;
                    }

                    const requestData = {
                      mediumNo: values.mediumNo?.trim(), // 添加 trim 处理
                      flag: values.flag,
                      cardMediumCode: values.cardMediumCode,
                      cardKindCd: values.cardKindCd,
                      applyMakecardDt: values.applyMakecardDt
                        ? typeof values.applyMakecardDt === "string"
                          ? moment(values.applyMakecardDt).format("YYYYMMDD")
                          : values.applyMakecardDt.format("YYYYMMDD")
                        : undefined,
                    };
                    const res = await mtInfoCac(requestData);

                    if (res.code === 200 && res.data) {
                      try {
                        const data = JSON.parse(res.data);
                        if (!data) {
                          throw new Error("数据解析失败");
                        }

                        setMediaInfo({
                          sedMtInfo: data.sedMtInfo || "",
                          trdMtInfo: data.trdMtInfo || "",
                          icCardMtInfo: data.icCardMtInfo || "",
                          icCardDataAreaInfo: data.icCardDataAreaInfo || "",
                        });
                        storage.set(
                          "mtinfo_mediaInfo",
                          {
                            sedMtInfo: data.sedMtInfo || "",
                            trdMtInfo: data.trdMtInfo || "",
                            icCardMtInfo: data.icCardMtInfo || "",
                            icCardDataAreaInfo: data.icCardDataAreaInfo || "",
                          },
                          30 * 60
                        );
                        message.success("获取磁道信息成功");
                      } catch (parseError) {
                        message.error("返回数据格式错误");
                        return false;
                      }
                    } else {
                      message.error(res.msg || "获取磁道信息失败");
                    }
                    return true;
                  } catch (err: any) {
                    const errorMessage = err?.message || "获取磁道信息失败";
                    setError(errorMessage);
                    message.error(errorMessage);
                    return false;
                  } finally {
                    setLoading(false);
                  }
                }}
                submitter={{
                  searchConfig: {
                    submitText: "提交",
                    resetText: "重置",
                  },
                  submitButtonProps: {
                    style: { minWidth: "80px" },
                  },
                  resetButtonProps: {
                    style: { minWidth: "80px" },
                    onClick: () => {
                      form.resetFields();
                      setMediaInfo({
                        sedMtInfo: "",
                        trdMtInfo: "",
                        icCardMtInfo: "",
                        icCardDataAreaInfo: "",
                      });
                      storage.remove("mtinfo_search");
                      storage.remove("mtinfo_mediaInfo");
                    },
                  },
                  render: (props, dom) => {
                    return (
                      <div
                        className="button-container"
                        style={{
                          display: "flex",
                          gap: "12px",
                          marginTop: "24px",
                          justifyContent: "flex-start", // 确保左对齐
                          marginLeft: "0", // 移除左边距，确保完全左对齐
                          paddingLeft: "0", // 移除左内边距
                        }}
                      >
                        {dom.map((btn, index) => (
                          <span key={index} style={{ marginRight: index === 0 ? '12px' : '0' }}>
                            {btn}
                          </span>
                        ))}
                      </div>
                    );
                  },
                }}
              >
                <ProForm.Group>
                  <ProFormText
                    name="mediumNo"
                    label="介质编号"
                    placeholder="请输入介质编号"
                    rules={[
                      { required: true, message: "请输入介质编号" },
                      { max: 50, message: "介质编号长度不能超过50个字符" },
                      {
                        pattern: /^[a-zA-Z0-9]+$/,
                        message: "介质编号只能包含数字和大小写字母",
                      },
                      {
                        validator: async (_, value) => {
                          if (value && !validateMediumNo(value)) {
                            throw new Error("介质编号格式不正确");
                          }
                        },
                      },
                    ]}
                    width="md"
                    fieldProps={{
                      className: "hover-input",
                      onChange: (e) => {
                        const value = e.target.value.replace(/[^a-zA-Z0-9]/g, "");
                        e.target.value = value;
                      },
                    }}
                  />
                  <ProFormSelect
                    name="flag"
                    label="是否定制卡标识"
                    rules={[{ required: true, message: "请输入是否定制卡标识" }]}
                    width="md"
                    options={[
                      { label: "0-否", value: "0" },
                      { label: "1-是", value: "1" },
                      { label: "2-折/一本通", value: "2" },
                    ]}
                    fieldProps={{
                      className: "hover-input",
                    }}
                  />
                  <Form.Item
                    label="卡介质代码"
                    name="cardMediumCode"
                    style={{ width: "328px" }}
                  >
                    <Select placeholder="请输入卡介质代码" allowClear className="hover-input">
                      {cardMediumCodeOptions.map((option) => (
                        <Select.Option key={option.value} value={option.value}>
                          {option.label}
                        </Select.Option>
                      ))}
                    </Select>
                  </Form.Item>
                  <Form.Item
                    label="卡品种"
                    name="cardKindCd"
                    style={{ width: "328px" }}
                  >
                    <Select placeholder="请输入卡品种" allowClear className="hover-input">
                      {cardKindCdOptions.map((option) => (
                        <Select.Option key={option.value} value={option.value}>
                          {option.label}
                        </Select.Option>
                      ))}
                    </Select>
                  </Form.Item>
                  <Form.Item label="申请制卡日期" name="applyMakecardDt">
                    <DatePicker
                      placeholder="请选择申请制卡日期"
                      style={{ width: "328px" }}
                      className="hover-input"
                      format="YYYY-MM-DD"
                      showTime={false}
                      onChange={(date) => {
                        console.log("选择的日期:", date);
                        form.setFieldValue("applyMakecardDt", date);
                      }}
                    />
                  </Form.Item>
                </ProForm.Group>
              </ProForm>
            </Card>
          </div>

          <div className={`card-container ${isVisible ? 'fade-in-up' : ''}`} style={{ animationDelay: '0.4s' }}>
            <Card
              title="查询结果"
              bordered={false}
              className="result-section"
              style={{
                height: "min-content",
                padding: "24px",
                backgroundColor: "#ffffff",
                marginBottom: "24px",
                position: "relative", // 添加相对定位
                borderRadius: "16px", // 确保圆角一致
                width: "100%", // 确保宽度100%
                boxSizing: "border-box", // 添加盒模型属性
                WebkitBoxSizing: "border-box", // 添加浏览器前缀
                MozBoxSizing: "border-box", // 添加浏览器前缀
              }}
            >
              {loading ? (
                <div style={{ textAlign: "center", padding: "40px" }}>
                  <Spin size="large" tip="处理中..." />
                </div>
              ) : error ? (
                <div
                  style={{ textAlign: "center", padding: "20px", color: "red" }}
                >
                  <Typography.Text>{error}</Typography.Text>
                </div>
              ) : (
                <Collapse
                  defaultActiveKey={["1"]}
                  expandIcon={({ isActive }) => (
                    <InfoCircleOutlined rotate={isActive ? -90 : 90} />
                  )}
                  className="custom-collapse"
                >
                  {collapseItems.map((item) => (
                    <Collapse.Panel header={item.label} key={item.key}>
                      {item.children}
                    </Collapse.Panel>
                  ))}
                </Collapse>
              )}
            </Card>
          </div>
        </div>
      </div>
    </KeepAlive>
  );
};

export default MTinfoCac;
