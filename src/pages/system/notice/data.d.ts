/* *
 *
 * <AUTHOR>
 * @datetime  2021/09/16
 * 
 * */

export type NoticeType = {
  noticeId: number;
  noticeTitle: string;
  noticeType: string;
  noticeContent: string;
  status: string;
  createBy: string;
  createTime: Date;
  updateBy: string;
  updateTime: Date;
  remark: string;
};

export type NoticeListPagination = {
  total: number;
  pageSize: number;
  current: number;
};

export type NoticeListData = {
  list: NoticeType[];
  pagination: Partial<NoticeListPagination>;
};

export type NoticeListParams = {
  noticeId?: string;
  noticeTitle?: string;
  noticeType?: string;
  noticeContent?: string;
  status?: string;
  createBy?: string;
  createTime?: string;
  updateBy?: string;
  updateTime?: string;
  remark?: string;
  pageSize?: string;
  currentPage?: string;
  filter?: string;
  sorter?: string;
};
