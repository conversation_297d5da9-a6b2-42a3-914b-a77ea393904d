/* *
 *
 * <AUTHOR>
 * @datetime  2021/09/16
 *
 *  该文件定义了与用户相关的 TypeScript 类型
 *
 * */

// UserType 定义了用户的数据结构
export type UserType = {
  userId: number; // 用户ID
  deptId: number; // 部门ID
  userName: string; // 用户账号
  nickName: string; // 用户昵称
  userType: string; // 用户类型
  email: string; // 用户邮箱
  phonenumber: string; // 手机号码
  sex: string; // 性别
  avatar: string; // 头像
  password: string; // 密码
  status: string; // 状态
  delFlag: string; // 删除标记
  loginIp: string; // 登录IP
  loginDate: Date; // 登录时间
  createBy: string; // 创建人
  createTime: Date; // 创建时间
  updateBy: string; // 更新人
  updateTime: Date; // 更新时间
  remark: string; // 备注
  admin: boolean; // 是否是管理员
  params: any; // 其他参数
  postIds: any; // 岗位ID列表
  roleId: number; // 角色ID
  roleIds: []; // 角色ID列表
  roles: []; // 角色列表
  authorizeEnv: [], // 授权环境
  searchValue: string; // 搜索值
};

// UserListPagination 定义了用户列表的分页信息
export type UserListPagination = {
  total: number; // 总数
  pageSize: number; // 每页大小
  current: number; // 当前页码
};

// UserListData 定义了用户列表的数据结构
export type UserListData = {
  list: UserType[]; // 用户列表
  pagination: Partial<UserListPagination>; // 分页信息
};

// UserListParams 定义了用户列表的请求参数
export type UserListParams = {
  userId?: string;
  deptId?: string;
  userName?: string;
  nickName?: string;
  userType?: string;
  email?: string;
  phonenumber?: string;
  sex?: string;
  avatar?: string;
  password?: string;
  status?: string;
  delFlag?: string;
  loginIp?: string;
  loginDate?: string;
  createBy?: string;
  createTime?: string;
  updateBy?: string;
  updateTime?: string;
  remark?: string;
  pageSize?: string;
  currentPage?: string;
  filter?: string;
  sorter?: string;
};
