import { PlusOutlined, DeleteOutlined } from "@ant-design/icons";
import type { FormInstance } from "antd";
import { Button, message, Modal, Row, Col } from "antd";
import React, { useState, useRef, useEffect } from "react";
import { useIntl, FormattedMessage, useAccess } from "umi";
import { FooterToolbar } from "@ant-design/pro-layout";
import type { ProColumns, ActionType } from "@ant-design/pro-table";
import ProTable from "@ant-design/pro-table";
import WrapContent from "@/components/WrapContent"; // 引入 WrapContent 组件，用于包裹内容区域，提供统一的样式
import Card from "antd/es/card";
import type { UserType, UserListParams } from "./data.d";
import {
  getUserList,
  getUser,
  removeUser,
  addUser,
  updateUser,
  exportUser,
  updateUserPwd,
} from "./service"; // 引入用户相关的服务函数，这些函数负责与后端 API 交互
import UpdateForm from "./components/edit"; // 引入编辑用户信息的表单组件
import { getDict } from "../dict/service"; // 引入字典服务，用于获取例如性别、状态等字典数据
import ResetPwd from "./components/ResetPwd"; // 引入重置密码组件
import { getTreeList as getDeptTreeList } from "../dept/service"; // 引入部门服务，用于获取部门树结构数据
import DeptTree from "./components/DeptTree"; // 引入部门树组件
import type { DataNode } from "antd/lib/tree";
import { getPostList } from "../post/service"; // 引入岗位服务，用于获取岗位列表
import { getRoleList } from "../role/service"; // 引入角色服务，用于获取角色列表

/* *
 *
 * <AUTHOR>
 * @datetime  2021/09/16
 *
 *  该文件是用户管理页面的主入口，负责展示用户列表，并提供添加、编辑、删除和导出用户的功能
 *
 * */

/**
 * 添加节点
 *
 * @param fields 用户信息
 */
const handleAdd = async (fields: UserType) => {
  const hide = message.loading("正在添加"); // 显示 loading 提示
  try {
    const resp = await addUser({ ...fields }); // 调用 addUser 服务函数，添加用户
    hide(); // 隐藏 loading 提示
    if (resp.code === 200) {
      message.success("添加成功"); // 添加成功提示
    } else {
      message.error(resp.msg); // 添加失败提示
    }
    return true;
  } catch (error) {
    hide(); // 隐藏 loading 提示
    message.error("添加失败请重试！"); // 添加失败提示
    return false;
  }
};

/**
 * 更新节点
 *
 * @param fields 用户信息
 */
const handleUpdate = async (fields: UserType) => {
  const hide = message.loading("正在配置"); // 显示 loading 提示
  try {
    const resp = await updateUser(fields); // 调用 updateUser 服务函数，更新用户信息
    hide(); // 隐藏 loading 提示
    if (resp.code === 200) {
      message.success("配置成功"); // 更新成功提示
    } else {
      message.error(resp.msg); // 更新失败提示
    }
    return true;
  } catch (error) {
    hide(); // 隐藏 loading 提示
    message.error("配置失败请重试！"); // 更新失败提示
    return false;
  }
};

/**
 * 删除节点
 *
 * @param selectedRows 选中的用户列表
 */
const handleRemove = async (selectedRows: UserType[]) => {
  const hide = message.loading("正在删除"); // 显示 loading 提示
  if (!selectedRows) return true;
  try {
    const resp = await removeUser(
      selectedRows.map((row) => row.userId).join(","),
    ); // 调用 removeUser 服务函数，删除用户
    hide(); // 隐藏 loading 提示
    if (resp.code === 200) {
      message.success("删除成功，即将刷新"); // 删除成功提示
    } else {
      message.error(resp.msg); // 删除失败提示
    }
    return true;
  } catch (error) {
    hide(); // 隐藏 loading 提示
    message.error("删除失败，请重试"); // 删除失败提示
    return false;
  }
};

/**
 * 删除单个节点
 *
 * @param selectedRow 选中的用户
 */
const handleRemoveOne = async (selectedRow: UserType) => {
  const hide = message.loading("正在删除"); // 显示 loading 提示
  if (!selectedRow) return true;
  try {
    const params = [selectedRow.userId];
    const resp = await removeUser(params.join(",")); // 调用 removeUser 服务函数，删除单个用户
    hide(); // 隐藏 loading 提示
    if (resp.code === 200) {
      message.success("删除成功，即将刷新"); // 删除成功提示
    } else {
      message.error(resp.msg); // 删除失败提示
    }
    return true;
  } catch (error) {
    hide(); // 隐藏 loading 提示
    message.error("删除失败，请重试"); // 删除失败提示
    return false;
  }
};

/**
 * 导出数据
 *
 */
const handleExport = async () => {
  const hide = message.loading("正在导出"); // 显示 loading 提示
  try {
    await exportUser(); // 调用 exportUser 服务函数，导出用户数据
    hide(); // 隐藏 loading 提示
    message.success("导出成功"); // 导出成功提示
    return true;
  } catch (error) {
    hide(); // 隐藏 loading 提示
    message.error("导出失败，请重试"); // 导出失败提示
    return false;
  }
};

const UserTableList: React.FC = () => {
  const formTableRef = useRef<FormInstance>(); // 表单的 ref，用于访问表单实例

  const [modalVisible, setModalVisible] = useState<boolean>(false); // 控制编辑/添加用户模态框的显示
  const [resetPwdModalVisible, setResetPwdModalVisible] =
    useState<boolean>(false); // 控制重置密码模态框的显示

  const actionRef = useRef<ActionType>(); // ProTable 的 actionRef，用于触发 ProTable 的操作，例如刷新表格
  const [currentRow, setCurrentRow] = useState<UserType>(); // 当前选中的用户，用于编辑或查看详情
  const [selectedRowsState, setSelectedRows] = useState<UserType[]>([]); // 选中的用户列表，用于批量操作

  const [selectDept, setSelectDept] = useState<any>({ id: 0 }); // 选中的部门，用于筛选用户列表

  const [sexOptions, setSexOptions] = useState<any>({}); // 性别选项，从字典服务获取
  const [statusOptions, setStatusOptions] = useState<any>({}); // 状态选项，从字典服务获取

  const [postIds, setPostIds] = useState<string[]>(); // 选中的岗位ID列表，用于编辑用户时设置岗位
  const [postList, setPostList] = useState<string[]>(); // 岗位列表，从岗位服务获取
  const [roleIds, setRoleIds] = useState<string[]>(); // 选中的角色ID列表，用于编辑用户时设置角色
  const [roleList, setRoleList] = useState<string[]>(); // 角色列表，从角色服务获取
  const [deptTree, setDeptTree] = useState<DataNode[]>(); // 部门树数据，从部门服务获取
  const [authorizeEnv, setAuthorizeEnv] = useState<string[]>([]); // 授权环境，用于编辑用户时设置授权环境
  const access = useAccess(); // 权限控制，用于判断当前用户是否有权限进行某些操作

  /** 国际化配置 */
  const intl = useIntl(); // 国际化实例，用于翻译文本

  useEffect(() => {
    // 获取性别字典
    getDict("sys_user_sex").then((res) => {
      if (res.code === 200) {
        const opts: Record<string, string> = {};
        res.data.forEach((item: any) => {
          opts[item.dictValue] = item.dictLabel;
        });
        setSexOptions(opts);
      }
    });
    // 获取状态字典
    getDict("sys_normal_disable").then((res) => {
      if (res.code === 200) {
        const opts: Record<string, string> = {};
        res.data.forEach((item: any) => {
          opts[item.dictValue] = item.dictLabel;
        });
        setStatusOptions(opts);
      }
    });
  }, []);

  // 定义表格的列
  const columns: ProColumns<UserType>[] = [
    {
      title: (
        <FormattedMessage id="system.User.user_id" defaultMessage="编号" />
      ), // 表格列标题，使用 FormattedMessage 组件进行国际化
      dataIndex: "userId", // 数据索引，对应数据源中的字段
      valueType: "textarea", // 值类型，这里是文本域
      hideInSearch: true, // 在搜索表单中隐藏该列
    },
    {
      title: (
        <FormattedMessage id="system.User.dept_id" defaultMessage="部门ID" />
      ),
      dataIndex: "deptId",
      valueType: "text",
    },
    {
      title: (
        <FormattedMessage
          id="system.User.user_name"
          defaultMessage="用户账号"
        />
      ),
      dataIndex: "userName",
      valueType: "text",
    },
    {
      title: (
        <FormattedMessage
          id="system.User.nick_name"
          defaultMessage="用户昵称"
        />
      ),
      dataIndex: "nickName",
      valueType: "text",
      hideInSearch: true, // 在搜索表单中隐藏该列
    },
    // {
    //   title: <FormattedMessage id="system.User.email" defaultMessage="用户邮箱" />,
    //   dataIndex: 'email',
    //   valueType: 'text',
    // },
    {
      title: (
        <FormattedMessage
          id="system.User.authorize_env"
          defaultMessage="授权环境"
        />
      ),
      dataIndex: "authorizeEnv",
      valueType: "text",
      hideInSearch: true, // 在搜索表单中隐藏该列
    },
    {
      title: (
        <FormattedMessage
          id="system.User.phonenumber"
          defaultMessage="手机号码"
        />
      ),
      dataIndex: "phonenumber",
      valueType: "text",
    },
    {
      title: (
        <FormattedMessage id="system.User.status" defaultMessage="帐号状态" />
      ),
      dataIndex: "status",
      valueType: "select", // 值类型为下拉选择框
      valueEnum: statusOptions, // 下拉选择框的选项，这里使用状态字典
    },
    {
      title: (
        <FormattedMessage
          id="pages.searchTable.titleOption"
          defaultMessage="操作"
        />
      ),
      dataIndex: "option",
      width: "220px",
      valueType: "option", // 值类型为操作列
      render: (_, record) => [
        // 渲染操作按钮
        <Button
          type="link"
          size="small"
          key="edit"
          hidden={!access.hasPerms("system:user:edit")} // 根据权限控制按钮是否显示
          onClick={() => {
            // 获取用户详情，并设置岗位和角色信息
            const fetchUserInfo = async (userId: number) => {
              const res = await getUser(userId); // 调用 getUser 服务函数，获取用户详情
              setPostIds(res.postIds);
              setPostList(
                res.posts.map((item: any) => {
                  return {
                    value: item.postId,
                    label: item.postName,
                  };
                }),
              );
              setRoleIds(res.roleIds);
              setRoleList(
                res.roles.map((item: any) => {
                  return {
                    value: item.roleId,
                    label: item.roleName,
                  };
                }),
              );
              setAuthorizeEnv(res.authorizeEnv || []);
            };
            fetchUserInfo(record.userId);
            // 获取部门树
            getDeptTreeList({}).then((treeData) => {
              setDeptTree(treeData);
            });
            setModalVisible(true); // 显示编辑用户的模态框
            setCurrentRow(record); // 设置当前选中的用户
          }}
        >
          <FormattedMessage id="pages.searchTable.edit" defaultMessage="编辑" />
        </Button>,
        <Button
          type="link"
          size="small"
          danger
          key="batchRemove"
          hidden={!access.hasPerms("system:user:remove")} // 根据权限控制按钮是否显示
          onClick={async () => {
            Modal.confirm({
              // 弹出确认框
              title: "删除",
              content: "确定删除该项吗？",
              okText: "确认",
              cancelText: "取消",
              onOk: async () => {
                const success = await handleRemoveOne(record); // 调用 handleRemoveOne 函数删除单个用户
                if (success) {
                  if (actionRef.current) {
                    actionRef.current.reload(); // 刷新表格
                  }
                }
              },
            });
          }}
        >
          <FormattedMessage
            id="pages.searchTable.delete"
            defaultMessage="删除"
          />
        </Button>,
        <Button
          type="link"
          size="small"
          key="resetpwd"
          hidden={!access.hasPerms("system:user:edit")} // 根据权限控制按钮是否显示
          onClick={() => {
            setResetPwdModalVisible(true); // 显示重置密码的模态框
            setCurrentRow(record); // 设置当前选中的用户
          }}
        >
          <FormattedMessage
            id="system.User.reset.password"
            defaultMessage="密码重置"
          />
        </Button>,
      ],
    },
  ];

  return (
    <WrapContent>
      {/* 使用 Row 和 Col 组件进行布局 */}
      <Row gutter={[16, 24]}>
        {/* <Col lg={6} md={24}>
          <Card>
            <DeptTree
              onSelect={async (value: any) => {
                setSelectDept(value);
                if (actionRef.current) {
                  formTableRef?.current?.submit();
                }
              }}
            />
          </Card>
        </Col> */}
        <Col lg={24} md={24}>
          {/* 使用 ProTable 组件展示用户列表 */}
          <ProTable<UserType>
            headerTitle={intl.formatMessage({
              id: "pages.searchTable.title",
              defaultMessage: "信息",
            })}
            actionRef={actionRef}
            formRef={formTableRef}
            rowKey="userId"
            key="userList"
            search={{
              labelWidth: 120,
            }}
            toolBarRender={() => [
              // 渲染工具栏按钮
              <Button
                type="primary"
                key="add"
                hidden={!access.hasPerms("system:user:add")} // 根据权限控制按钮是否显示
                onClick={async () => {
                  // 添加用户时，需要先选择部门
                  if (selectDept.id === "" || selectDept.id == null) {
                    message.warning("请选择左侧父级节点");
                  } else {
                    // 获取部门树
                    getDeptTreeList({}).then((treeData) => {
                      setDeptTree(treeData);
                      setCurrentRow(undefined);
                      setModalVisible(true); // 显示添加用户的模态框
                    });
                    // 获取岗位列表
                    getPostList().then((res) => {
                      if (res.code === 200) {
                        setPostList(
                          res.rows.map((item: any) => {
                            return {
                              value: item.postId,
                              label: item.postName,
                            };
                          }),
                        );
                      }
                    });
                    // 获取角色列表
                    getRoleList().then((res) => {
                      if (res.code === 200) {
                        setRoleList(
                          res.rows.map((item: any) => {
                            return {
                              value: item.roleId,
                              label: item.roleName,
                            };
                          }),
                        );
                      }
                    });
                  }
                }}
              >
                <PlusOutlined />{" "}
                <FormattedMessage
                  id="pages.searchTable.new"
                  defaultMessage="新建"
                />
              </Button>,
              <Button
                type="primary"
                key="remove"
                hidden={
                  selectedRowsState?.length === 0 ||
                  !access.hasPerms("system:user:remove")
                } // 根据权限控制按钮是否显示
                onClick={async () => {
                  const success = await handleRemove(selectedRowsState); // 调用 handleRemove 函数批量删除用户
                  if (success) {
                    setSelectedRows([]); // 清空选中的用户列表
                    actionRef.current?.reloadAndRest?.(); // 刷新表格并重置
                  }
                }}
              >
                <DeleteOutlined />
                <FormattedMessage
                  id="pages.searchTable.delete"
                  defaultMessage="删除"
                />
              </Button>,
              <Button
                type="primary"
                key="export"
                hidden={!access.hasPerms("system:user:export")} // 根据权限控制按钮是否显示
                onClick={async () => {
                  handleExport(); // 调用 handleExport 函数导出用户数据
                }}
              >
                <PlusOutlined />
                <FormattedMessage
                  id="pages.searchTable.export"
                  defaultMessage="导出"
                />
              </Button>,
            ]}
            request={(params) =>
              // 请求用户列表数据
              getUserList({
                ...params,
                deptId: selectDept.id,
              } as UserListParams).then((res) => {
                return {
                  data: res.rows,
                  total: res.total,
                  success: true,
                };
              })
            }
            columns={columns} // 表格列配置
            rowSelection={{
              // 行选择配置
              onChange: (_, selectedRows) => {
                setSelectedRows(selectedRows); // 更新选中的用户列表
              },
            }}
          />
        </Col>
      </Row>
      {selectedRowsState?.length > 0 && (
        // 底部工具栏，显示已选择的项目数和批量操作按钮
        <FooterToolbar
          extra={
            <div>
              <FormattedMessage
                id="pages.searchTable.chosen"
                defaultMessage="已选择"
              />
              <a style={{ fontWeight: 600 }}>{selectedRowsState.length}</a>
              <FormattedMessage
                id="pages.searchTable.item"
                defaultMessage="项"
              />
            </div>
          }
        >
          <Button
            key="remove"
            hidden={!access.hasPerms("system:user:remove")} // 根据权限控制按钮是否显示
            onClick={async () => {
              Modal.confirm({
                // 弹出确认框
                title: "删除",
                content: "确定删除该项吗？",
                okText: "确认",
                cancelText: "取消",
                onOk: async () => {
                  const success = await handleRemove(selectedRowsState); // 调用 handleRemove 函数批量删除用户
                  if (success) {
                    setSelectedRows([]); // 清空选中的用户列表
                    actionRef.current?.reloadAndRest?.(); // 刷新表格并重置
                  }
                },
              });
            }}
          >
            <FormattedMessage
              id="pages.searchTable.batchDeletion"
              defaultMessage="批量删除"
            />
          </Button>
        </FooterToolbar>
      )}
      {/* 编辑/添加用户信息的表单 */}
      <UpdateForm
        onSubmit={async (values) => {
          let success = false;
          if (values.userId) {
            success = await handleUpdate({ ...values } as UserType); // 更新用户信息
          } else {
            success = await handleAdd({ ...values } as UserType); // 添加用户
          }
          if (success) {
            setModalVisible(false); // 隐藏模态框
            setCurrentRow(undefined); // 清空当前选中的用户
            if (actionRef.current) {
              actionRef.current.reload(); // 刷新表格
            }
          }
        }}
        onCancel={() => {
          setModalVisible(false); // 隐藏模态框
          setCurrentRow(undefined); // 清空当前选中的用户
        }}
        visible={modalVisible} // 控制模态框的显示
        values={currentRow || {}} // 表单的初始值，这里是当前选中的用户或空对象
        sexOptions={sexOptions} // 性别选项
        statusOptions={statusOptions} // 状态选项
        posts={postList || []} // 岗位列表
        postIds={postIds || []} // 选中的岗位ID列表
        roles={roleList || []} // 角色列表
        roleIds={roleIds || []} // 选中的角色ID列表
        depts={deptTree || []} // 部门树数据
        authorizeEnv={authorizeEnv || []} // 授权环境选项
      />

      {/* 重置密码的表单 */}
      <ResetPwd
        onSubmit={async (value: any) => {
          const success = await updateUserPwd(
            value.login_password,
            value.confirm_password,
            currentRow?.userId,
          ); // 调用 updateUserPwd 函数更新用户密码
          if (success) {
            setResetPwdModalVisible(false); // 隐藏模态框
            setSelectedRows([]); // 清空选中的用户列表
            setCurrentRow(undefined); // 清空当前选中的用户
            message.success("密码重置成功。"); // 提示密码重置成功
          }
        }}
        onCancel={() => {
          setResetPwdModalVisible(false); // 隐藏模态框
          setSelectedRows([]); // 清空选中的用户列表
          setCurrentRow(undefined); // 清空当前选中的用户
        }}
        resetPwdModalVisible={resetPwdModalVisible} // 控制模态框的显示
        values={currentRow || {}} // 表单的初始值，这里是当前选中的用户或空对象
      />
    </WrapContent>
  );
};

export default UserTableList;

/**
 * WrapContent 组件
 *
 * 该组件用于包裹内容区域，提供统一的样式。
 *
 * props:
 *   - children: React.ReactNode - 需要包裹的内容
 */
