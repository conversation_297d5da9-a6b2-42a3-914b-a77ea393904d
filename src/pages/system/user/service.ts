import { downLoadXlsx } from "@/utils/downloadfile";
import request from "@/utils/request";
import type { UserType, UserListParams } from "./data.d";

/* *
 *
 * <AUTHOR>
 * @datetime  2021/09/16
 *
 * 该文件包含了用户管理相关的所有 API 服务函数
 *
 * */

/**
 * 查询用户信息列表
 *
 * @param params 查询参数，包括分页、过滤和排序信息
 * @returns 返回用户列表数据，包括分页信息
 */
export async function getUserList(params?: UserListParams) {
  const queryString = new URLSearchParams(params).toString();
  return request(`/system/user/list?${queryString}`, {
    data: params,
    method: "GET",
    headers: {
      "Content-Type": "application/json;charset=UTF-8",
    },
  });
}

/**
 * 查询用户信息详细
 *
 * @param userId 用户ID
 * @returns 返回用户详细信息
 */
export function getUser(userId: number) {
  return request(`/system/user/${userId}`, {
    method: "GET",
  });
}

/**
 * 新增用户信息
 *
 * @param params 用户信息
 * @returns 返回添加结果
 */
export async function addUser(params: UserType) {
  return request("/system/user", {
    method: "POST",
    data: params,
  });
}

/**
 * 修改用户信息
 *
 * @param params 用户信息
 * @returns 返回修改结果
 */
export async function updateUser(params: UserType) {
  return request("/system/user", {
    method: "PUT",
    data: params,
  });
}

/**
 * 删除用户信息
 *
 * @param ids 用户ID列表，多个ID用逗号分隔
 * @returns 返回删除结果
 */
export async function removeUser(ids: string) {
  return request(`/system/user/${ids}`, {
    method: "DELETE",
    headers: {
      "Content-Type": "application/json;charset=UTF-8",
    },
  });
}

/**
 * 导出用户信息
 *
 * @param params 导出参数
 * @returns 返回导出的文件
 */
export function exportUser(params?: UserListParams) {
  return downLoadXlsx(
    `/system/user/export`,
    { params },
    `user_${new Date().getTime()}.xlsx`,
  );
}

/**
 * 更新用户个人信息
 *
 * @param data 用户个人信息
 * @returns 返回更新结果
 */
export function updateUserProfile(data: API.CurrentUser) {
  return request("/system/user/profile", {
    method: "put",
    data: data,
  });
}

/**
 * 用户密码重置
 *
 * @param oldPassword 旧密码
 * @param newPassword 新密码
 * @param userId 用户ID
 * @returns 返回重置结果
 */
export function updateUserPwd(
  oldPassword: string,
  newPassword: string,
  userId?: number,
) {
  const data = {
    oldPassword,
    newPassword,
    userId,
  };
  return request("/system/user/profile/updatePwd", {
    method: "put",
    params: data,
  });
}

/**
 * 用户头像上传
 *
 * @param data 头像文件数据
 * @returns 返回上传结果
 */
export function uploadAvatar(data: any) {
  return request("/system/user/profile/avatar", {
    method: "post",
    data: data,
  });
}
