@import (reference) '~antd/es/style/themes/index';

.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: auto;
  background: @layout-body-background;
}

.lang {
  width: 100%;
  height: 40px;
  line-height: 44px;
  text-align: right;
  :global(.ant-dropdown-trigger) {
    margin-right: 24px;
  }
}

.content {
  flex: 1;
  padding: 32px 0;
  
  // 添加输入框样式
  :global {
    // 外层容器可以保持圆角
    .ant-input-affix-wrapper {
      border-radius: 10px !important;
      
      // 确保内部输入区域为直角
      .ant-input {
        border-radius: 0 !important;
      }
    }
    
    // 普通输入框外层为圆角
    .ant-input {
      border-radius: 10px !important;
    }
    
    // 选择器外层为圆角
    .ant-select-selector {
      border-radius: 10px !important;
    }
    
    // 密码输入框外层为圆角，内部为直角
    .ant-input-password {
      border-radius: 10px !important;

      // 确保密码输入框内部为直角
      .ant-input {
        border-radius: 0 !important;
      }
    }
    
    // 全局设置所有表单元素的内部输入区域为直角
    input {
      border-radius: 0 !important;
    }
    
    // 针对所有表单控件的输入区域强制设置为直角
    .ant-form-item-control-input {
      .ant-input {
        border-radius: 0 !important;
      }
    }
    
    // 验证码输入框样式
    .code-input-wrapper {
      .ant-form-item-control-input, 
      .ant-form-item-control-input-content {
        height: 40px !important;
      }
      
      .ant-input-affix-wrapper {
        border-radius: 10px !important;

        // 确保内部输入区域为直角
        .ant-input {
          border-radius: 0 !important;
        }
      }
      
      // 覆盖默认的圆角设置，确保输入框内部为直角
      .ant-input {
        border-radius: 0 !important;
        height: 40px !important;
        line-height: 40px !important;
        font-size: 16px !important;
      }
      
      .ant-pro-field-pro-form-text {
        .ant-input {
          border-radius: 0 !important;
        }
      }
      
      input {
        border-radius: 0 !important;
      }
    }
    
    // 验证码区域样式
    .captcha-row {
      display: flex !important;
      align-items: center !important;
      flex-wrap: nowrap !important;
      margin-bottom: 24px !important;
      
      .captcha-col {
        flex: 3;
        display: flex;
        align-items: center;
        
        .ant-form-item {
          margin-bottom: 0 !important;
          width: 100%;
        }
      }
      
      .captcha-img-col {
        flex: 2;
        max-width: 120px;
        display: flex;
        align-items: center;
        height: 40px; // 与输入框高度保持一致
        
        .ant-image {
          display: flex;
          align-items: center;
          height: 100%;
          
          img {
            vertical-align: middle;
            max-height: 38px;
          }
        }
      }
    }
  }
}

@media (min-width: @screen-md-min) {
  .container {
    background-image: url('https://gw.alipayobjects.com/zos/rmsportal/TVYTbAXWheQpRcWDaDMu.svg');
    background-repeat: no-repeat;
    background-position: center 110px;
    background-size: 100%;
  }

  .content {
    padding: 32px 0 24px;
  }
}

.icon {
  margin-left: 8px;
  color: rgba(0, 0, 0, 0.2);
  font-size: 24px;
  vertical-align: middle;
  cursor: pointer;
  transition: color 0.3s;

  &:hover {
    color: @primary-color;
  }
}
