import {
  AlipayCircleOutlined,
  LockOutlined,
  MobileOutlined,
  TaobaoCircleOutlined,
  UserOutlined,
  TranslationOutlined,
  GoldOutlined,
  WeiboCircleOutlined,
} from "@ant-design/icons";
import {
  Alert,
  Col,
  message,
  Row,
  Tabs,
  Image,
  Select,
  Modal,
  Form,
} from "antd";
import React, { useEffect, useState } from "react";
import {
  ProFormCaptcha,
  ProFormCheckbox,
  ProFormText,
  ProFormSelect,
  LoginForm,
} from "@ant-design/pro-form";
import { useIntl, history, FormattedMessage, SelectLang, useModel } from "umi";
import Footer from "@/components/Footer";
import {
  getCaptchaImage,
  getFakeCaptcha,
  login,
  register,
} from "@/services/login";
import defaultSettings from "../../../../config/defaultSettings";

import styles from "./index.less";
import { clearSessionToken, setSessionToken } from "@/access";

const LoginMessage: React.FC<{
  content: string;
}> = ({ content }) => (
  <Alert
    style={{
      marginBottom: 24,
    }}
    message={content}
    type="error"
    showIcon
  />
);

const Login: React.FC = () => {
  const [userLoginState, setUserLoginState] = useState<any>({});
  const [type, setType] = useState<string>("account");
  const { initialState, setInitialState } = useModel("@@initialState");
  const [form] = Form.useForm();

  const [captchaCode, setCaptchaCode] = useState<string>("");
  const [uuid, setUuid] = useState<string>("");

  const intl = useIntl();

  const fetchUserInfo = async () => {
    const userInfo = await initialState?.fetchUserInfo?.();
    if (userInfo) {
      await setInitialState((s) => ({
        ...s,
        currentUser: userInfo,
      }));
    }
  };
  const getCaptchaCode = async () => {
    const response = await getCaptchaImage();
    const imgdata = `data:image/png;base64,${response.img}`;
    setCaptchaCode(imgdata);
    setUuid(response.uuid);
  };

  const handleSubmit = async (values: API.LoginParams) => {
    try {
      // 登录
      const response = await login({ ...values, uuid });
      if (response.code === 200) {
        const defaultLoginSuccessMessage = intl.formatMessage({
          id: "pages.login.success",
          defaultMessage: "登录成功！",
        });
        const current = new Date();
        const expireTime = current.setTime(
          current.getTime() + 1000 * 12 * 60 * 60
        );
        setSessionToken(response.token, response.refresh_token, expireTime);
        message.success(defaultLoginSuccessMessage);
        localStorage.setItem("currentEnv", "DEV1");

        await fetchUserInfo();
        /** 此方法会跳转到 redirect 参数所在的位置 */
        if (!history) return;

        const { query } = history.location;
        const { redirect } = query as { redirect: string };
        history.push(redirect || "/");
        return;
      } else {
        console.log("login failed");
        clearSessionToken();
        // 如果失败去设置用户错误信息
        setUserLoginState({
          status: "error",
          type: "account",
          massage: response.msg,
        });
        message.error(response.msg);
        getCaptchaCode();
      }
    } catch (error) {
      clearSessionToken();
      const defaultLoginFailureMessage = intl.formatMessage({
        id: "pages.login.failure",
        defaultMessage: "登录失败，请重试！",
      });
      message.error(defaultLoginFailureMessage);
      getCaptchaCode();
    }
  };

  // 注册处理函数
  const handleRegister = async (values: API.RegisterParams) => {
    try {
      console.log("handleRegister values:", values); // 添加这行日志
      const response = await register({ ...values, uuid });
      console.log("register response:", response);
      if (response.code === 200) {
        message.success("注册成功！");
        setType("account");
        // 设置登录表单的初始值为注册时的用户名
        form.setFieldsValue({
          username: values.username,
          password: "", // 清空密码字段
          code: "", // 清空验证码字段
        });
        getCaptchaCode(); // 刷新验证码
      } else {
        message.error(response.msg);
        getCaptchaCode();
      }
    } catch (error) {
      console.error("register error:", error); // 添加错误日志
      message.error("注册失败，请重试！");
      getCaptchaCode();
    }
  };

  const { status, type: loginType, massage } = userLoginState;

  useEffect(() => {
    getCaptchaCode();
  }, []);

  return (
    <div className={styles.container}>
      <div className={styles.lang} data-lang>
        {SelectLang && <SelectLang />}
      </div>
      <div className={styles.content}>
        <LoginForm
          form={form}
          // logo={<img alt="logo" src="/logo.svg" />}
          logo={<img alt="logo" src={defaultSettings.logo} />}
          title="自动化测试平台2.0"
          subTitle={intl.formatMessage({
            id: "pages.layouts.userLayout.title",
          })}
          initialValues={{
            autoLogin: true,
          }}
          submitter={{
            searchConfig: {
              submitText: type === "register" ? "注册" : "登录",
            },
          }}
          onFinish={async (values) => {
            console.log("form submit:", type, values); // 添加这行日志
            if (type === "register") {
              await handleRegister(values as API.RegisterParams);
            } else {
              await handleSubmit(values as API.LoginParams);
            }
          }}
          // actions={[
          //   <FormattedMessage
          //     key="loginWith"
          //     id="pages.login.loginWith"
          //     defaultMessage="其他登录方式"
          //   />,
          //   <AlipayCircleOutlined key="AlipayCircleOutlined" className={styles.icon} />,
          //   <TaobaoCircleOutlined key="TaobaoCircleOutlined" className={styles.icon} />,
          //   <WeiboCircleOutlined key="WeiboCircleOutlined" className={styles.icon} />,
          // ]}
        >
          <Tabs activeKey={type} onChange={setType}>
            <Tabs.TabPane
              key="account"
              tab={intl.formatMessage({
                id: "pages.login.accountLogin.tab",
                defaultMessage: "账户密码登录",
              })}
            />
            <Tabs.TabPane
              key="register"
              tab={intl.formatMessage({
                id: "pages.login.registerAccount",
                defaultMessage: "注册",
              })}
            />
          </Tabs>

          {status === "error" && loginType === "account" && (
            <LoginMessage content={massage} />
          )}
          {type === "account" && (
            <>
              <ProFormText
                name="username"
                initialValue=""
                fieldProps={{
                  size: "large",
                  prefix: <UserOutlined className={styles.prefixIcon} />,
                }}
                placeholder={intl.formatMessage({
                  id: "pages.login.username.placeholder",
                  defaultMessage: "",
                })}
                rules={[
                  {
                    required: true,
                    message: (
                      <FormattedMessage
                        id="pages.login.username.required"
                        defaultMessage="请输入用户名!"
                      />
                    ),
                  },
                ]}
              />
              <ProFormText.Password
                name="password"
                initialValue=""
                fieldProps={{
                  size: "large",
                  prefix: <LockOutlined className={styles.prefixIcon} />,
                }}
                placeholder={intl.formatMessage({
                  id: "pages.login.password.placeholder",
                  defaultMessage: "密码: admin123",
                })}
                rules={[
                  {
                    required: true,
                    message: (
                      <FormattedMessage
                        id="pages.login.password.required"
                        defaultMessage="请输入密码！"
                      />
                    ),
                  },
                ]}
              />
              <Row className="captcha-row">
                <Col className="captcha-col">
                  <ProFormText
                    className="code-input-wrapper"
                    name="code"
                    fieldProps={{
                      size: "large",
                      style: { borderRadius: 0 },
                      className: "captcha-input",
                    }}
                    placeholder={intl.formatMessage({
                      id: "pages.login.code.placeholder",
                      defaultMessage: "请输入验证码",
                    })}
                    rules={[
                      {
                        required: true,
                        message: (
                          <FormattedMessage
                            id="pages.searchTable.updateForm.ruleName.nameRules"
                            defaultMessage="请输入验证码"
                          />
                        ),
                      },
                    ]}
                  />
                </Col>
                <Col className="captcha-img-col">
                  <Image
                    src={captchaCode}
                    alt="验证码"
                    style={{
                      display: "inline-block",
                      cursor: "pointer",
                      paddingLeft: "10px",
                      width: "100%",
                      height: "auto",
                    }}
                    preview={false}
                    onClick={() => getCaptchaCode()}
                  />
                </Col>
              </Row>
            </>
          )}

          {status === "error" && loginType === "mobile" && (
            <LoginMessage content="验证码错误" />
          )}
          {type === "register" && (
            <>
              <ProFormText
                name="username" // 修改为 username，而不是 userName
                fieldProps={{
                  size: "large",
                  prefix: <UserOutlined className={styles.prefixIcon} />,
                }}
                placeholder="请输入用户账号"
                rules={[
                  {
                    required: true,
                    message: "请输入用户账号！",
                  },
                  {
                    pattern: /^[a-z0-9]{1,20}$/,
                    message: "只能输入英文小写和数字，且不超过20位！",
                  },
                ]}
              />
              <ProFormText
                name="nickName"
                fieldProps={{
                  size: "large",
                  prefix: <TranslationOutlined className={styles.prefixIcon} />,
                }}
                placeholder="用户昵称请实名"
                rules={[
                  {
                    required: true,
                    message: "请输入用户姓名，否则管理员有权删除未知账户！",
                  },
                  {
                    pattern: /^[\u4e00-\u9fa5]{2,10}$/,
                    message: "只能输入2-10个汉字！",
                  },
                ]}
              />
              <ProFormSelect
                name="deptId"
                fieldProps={{
                  size: "large",
                }}
                placeholder="请选择所属组别"
                options={[
                  { label: "集成测试组", value: 1 },
                  { label: "个人存款组", value: 2 },
                  { label: "结算账户组", value: 3 },
                  { label: "公共任务组", value: 4 },
                  { label: "技术支撑组", value: 5 },
                  { label: "应用分析组", value: 6 },
                  { label: "业务支撑组", value: 7 },
                  { label: "应用基础组", value: 8 },
                  { label: "其他", value: 9 },
                ]}
                rules={[
                  {
                    required: true,
                    message: "请选择所属组别！",
                  },
                ]}
              />
              <ProFormText
                name="phonenumber"
                fieldProps={{
                  size: "large",
                  prefix: <MobileOutlined className={styles.prefixIcon} />,
                }}
                placeholder="请输入手机号码"
                rules={[
                  {
                    required: true,
                    message: "请输入手机号码！",
                  },
                  {
                    pattern: /^1[3-9]\d{9}$/,
                    message: "手机号码格式不正确！",
                  },
                ]}
              />
              <ProFormText.Password
                name="password"
                fieldProps={{
                  size: "large",
                  prefix: <LockOutlined className={styles.prefixIcon} />,
                }}
                placeholder="请输入密码"
                rules={[
                  {
                    required: true,
                    message: "请输入密码！",
                  },
                ]}
              />
              <Row className="captcha-row">
                <Col className="captcha-col">
                  <ProFormText
                    className="code-input-wrapper"
                    name="code"
                    fieldProps={{
                      size: "large",
                      style: { borderRadius: 0 },
                      className: "captcha-input",
                    }}
                    placeholder="请输入验证码"
                    rules={[
                      {
                        required: true,
                        message: "请输入验证码！",
                      },
                    ]}
                  />
                </Col>
                <Col className="captcha-img-col">
                  <Image
                    src={captchaCode}
                    alt="验证码"
                    style={{
                      display: "inline-block",
                      cursor: "pointer",
                      paddingLeft: "10px",
                      width: "100%",
                      height: "auto",
                    }}
                    preview={false}
                    onClick={() => getCaptchaCode()}
                  />
                </Col>
              </Row>
            </>
          )}
          <div
            style={{
              marginBottom: 24,
            }}
          >
            <ProFormCheckbox noStyle name="autoLogin">
              <FormattedMessage
                id="pages.login.rememberMe"
                defaultMessage="自动登录"
              />
            </ProFormCheckbox>
            <a
              style={{
                float: "right",
              }}
              onClick={() => {
                Modal.info({
                  title: "提示",
                  content: "公测期间请联系管理员重置密码",
                  okText: "确定",
                });
              }}
            >
              <FormattedMessage
                id="pages.login.forgotPassword"
                defaultMessage="忘记密码"
              />
            </a>
          </div>
        </LoginForm>
      </div>
      <Footer />
    </div>
  );
};

export default Login;
