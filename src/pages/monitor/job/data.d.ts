
/* *
 *
 * <AUTHOR>
 * @datetime  2021/09/16
 * 
 * */

export type JobType = {
  jobId: number;
  jobName: string;
  jobGroup: string;
  invokeTarget: string;
  cronExpression: string;
  misfirePolicy: string;
  concurrent: string;
  nextValidTime: string;
  status: string;
  createBy: string;
  createTime: Date;
  updateBy: string;
  updateTime: Date;
  remark: string;
};

export type JobListPagination = {
  total: number;
  pageSize: number;
  current: number;
};

export type JobListData = {
  list: JobType[];
  pagination: Partial<JobListPagination>;
};

export type JobListParams = {
  jobId?: string;
  jobName?: string;
  jobGroup?: string;
  invokeTarget?: string;
  cronExpression?: string;
  misfirePolicy?: string;
  concurrent?: string;
  status?: string;
  createBy?: string;
  createTime?: string;
  updateBy?: string;
  updateTime?: string;
  remark?: string;
  pageSize?: string;
  currentPage?: string;
  filter?: string;
  sorter?: string;
};
