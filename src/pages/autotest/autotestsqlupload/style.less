@import '~antd/es/style/themes/default.less';

.container {
  padding: 20px;
  animation: fadeInUp 0.5s ease-out;
}

.card {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border-radius: 16px !important;  // 添加 !important
  transition: all 0.3s;
  overflow: hidden;

  &:hover {
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
  }

  :global {
    .ant-card-body {
      padding: 24px;
      border-radius: 16px;
    }

    .ant-card-head {
      border-bottom: 1px solid #f0f0f0;
      border-radius: 16px 16px 0 0 !important;  // 添加 !important
    }

    // 添加以下规则
    .ant-card {
      border-radius: 16px !important;
    }
  }
}

// 表单容器样式
.formContainer {
  background-color: #fff;
  border-radius: 12px;
  padding: 20px;
  box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.03);
}

.titleContainer {
  animation: slideInLeft 0.5s ease-out;
}

.formItem {
  animation: fadeIn 0.5s ease-out;
  margin-bottom: 16px;
}

// 添加水平布局的表单项容器
.horizontalFormItems {
  display: flex;
  justify-content: space-between;
  animation: fadeIn 0.5s ease-out;
  margin-bottom: 16px;
}

// 半宽的表单项
.formItemHalf {
  width: 48%;
  animation: fadeIn 0.5s ease-out;
}

.textArea {
  border-radius: 8px !important;
  resize: none;
  transition: all 0.3s;
  width: 100% !important;
  display: block;

  &:hover, &:focus {
    border-color: @primary-color !important;
    box-shadow: 0 0 0 2px fade(@primary-color, 20%) !important;
  }
}

// 添加新的全局样式规则
:global {
  .ant-input {
    border-radius: 8px !important;
  }

  .ant-input-textarea {
    border-radius: 8px !important;
  }

  .ant-form-item-control-input-content {
    .ant-input-textarea {
      textarea {
        border-radius: 8px !important;
      }
    }
  }

  .ant-form-item-control-input {
    width: 100% !important;
  }

  .ant-form-item-control-input-content {
    width: 100% !important;

    .ant-input-textarea {
      width: 100% !important;

      textarea {
        width: 100% !important;
        border-radius: 8px !important;
      }
    }
  }

  .custom-textarea {
    width: 100% !important;
  }
}

// 圆角按钮样式
.roundedButton {
  border-radius: 8px;
  transition: all 0.3s;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }
}

// 成功状态按钮（SQL正确时）
.successButton {
  background-color: #e8f5e9 !important;  // 浅绿色背景
  border-color: #c8e6c9 !important;      // 浅绿色边框
  color: #2e7d32 !important;            // 绿色文字

  &:hover {
    background-color: #2e7d32 !important; // 鼠标悬浮时变为深绿色
    border-color: #2e7d32 !important;
    color: #ffffff !important;           // 文字变为白色
  }

  &:focus {
    background-color: #e8f5e9 !important;
    border-color: #c8e6c9 !important;
    color: #2e7d32 !important;
  }
}

// 禁用状态按钮（SQL错误时）
.disabledButton {
  background-color: #f5f5f5 !important;  // 灰色背景
  border-color: #d9d9d9 !important;      // 灰色边框
  color: #bdbdbd !important;            // 灰色文字
  cursor: not-allowed;

  &:hover {
    background-color: #f5f5f5 !important;
    border-color: #d9d9d9 !important;
    color: #bdbdbd !important;
    transform: none !important;
    box-shadow: none !important;
  }
}

// 圆角选择器样式
.roundedSelect {
  :global {
    .ant-select-selector {
      border-radius: 8px !important;
    }

    // 确保清除图标正常显示和工作
    .ant-select-clear {
      opacity: 0.3;

      &:hover {
        opacity: 1;
      }
    }
  }
}

// SQL输入框特殊样式
.sqlTextArea {
  font-style: italic;
  color: #1890ff;
  font-family: 'Consolas', 'Monaco', monospace;
  width: 100% !important;

  &::placeholder {
    font-style: normal;
    color: rgba(0, 0, 0, 0.25);
  }
}

.sqlInputWrapper {
  position: relative;
  width: 100%;
  display: block;
}

.buttonContainer {
  display: flex;
  justify-content: center;
  margin-top: 24px;
  animation: fadeInUp 0.5s ease-out;
}

// 添加动画关键帧
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

// 添加一些响应式样式
@media screen and (max-width: @screen-md) {
  .container {
    padding: 10px;
  }
}