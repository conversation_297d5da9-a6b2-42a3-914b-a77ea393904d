import React, { useState, useEffect } from 'react';
import { Card, Form, Input, Button, Select, message, Spin, Typography, Space } from 'antd';
import { UploadOutlined, ReloadOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import { useIntl, FormattedMessage } from 'umi';
import WrapContent from '@/components/WrapContent';
import styles from './style.less';
import { uploadSql } from './service';

const { TextArea } = Input;
const { Option } = Select;
const { Title, Text } = Typography;

const AutoTestSqlUpload: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState<boolean>(false);
  const [submitLoading, setSubmitLoading] = useState<boolean>(false);
  const [sqlContent, setSqlContent] = useState<string>('');
  const [sqlError, setSqlError] = useState<string | null>(null);

  const intl = useIntl();

  // 分片值选项
  const shardingOptions = ['agg', 'bto1', 'bto2', 'dcdp', '0001', '0002', '0003', '0004', '0005', '0006', '0007', '0008'];

  // SQL启用标识选项
  const sqlStatusOptions = [
    { label: '0-未启用', value: '0' },
    { label: '1-已启用', value: '1' },
  ];

  // SQL格式校验
  const validateSql = (sql: string): boolean => {
    if (!sql) return true;

    // 简单SQL语法检查
    const sqlLower = sql.toLowerCase();

    // 检查基本SQL关键字
    const hasSelect = sqlLower.includes('select');
    const hasFrom = sqlLower.includes('from');
    const hasSet = sqlLower.includes('set');
    const hasWhere = sqlLower.includes('where');
    const hasInto = sqlLower.includes('into');
    const hasValues = sqlLower.includes('values');
    const hasInsert = sqlLower.includes('insert');
    const hasUpdate = sqlLower.includes('update');
    const hasDelete = sqlLower.includes('delete');

    // 检查SQL语句结构
    if (hasSelect && !hasFrom) {
      setSqlError('SELECT语句缺少FROM子句');
      return false;
    }
    if (hasUpdate) {
      if (!hasSet) {
        setSqlError('UPDATE语句缺少SET子句');
        return false;
      }
      if (!hasWhere) {
        setSqlError('UPDATE语句缺少WHERE子句');
        return false;
      }
    }
    if (hasDelete) {
      if (!hasFrom) {
        setSqlError('DELETE语句缺少FROM子句');
        return false;
      }
      if (!hasWhere) {
        setSqlError('DELETE语句缺少WHERE子句');
        return false;
      }
    }
    if (hasInsert) {
      if (!hasInto) {
        setSqlError('INSERT语句缺少INTO子句');
        return false;
      }
      if (!hasValues) {
        setSqlError('INSERT语句缺少VALUES子句');
        return false;
      }
    }

    // 检查括号是否匹配
    const openParenCount = (sql.match(/\(/g) || []).length;
    const closeParenCount = (sql.match(/\)/g) || []).length;
    if (openParenCount !== closeParenCount) {
      setSqlError('SQL语句中的括号不匹配');
      return false;
    }

    // 检查引号是否匹配
    const singleQuoteCount = (sql.match(/'/g) || []).length;
    if (singleQuoteCount % 2 !== 0) {
      setSqlError('SQL语句中的单引号不匹配');
      return false;
    }

    // 检查分号结尾
    if (!sqlLower.trim().endsWith(';')) {
      setSqlError('SQL语句应以分号结尾');
      return false;
    }
    setSqlError(null);
    return true;
  };

  // 处理SQL内容变化
  const handleSqlChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    let value = e.target.value;

    // 自动替换中文逗号和引号
    value = value.replace(/，/g, ',').replace(/（/g, '(').replace(/）/g, ')')
    .replace(/“/g, '"').replace(/”/g, '"')
    .replace(/‘/g, "'").replace(/’/g, "'").replace(/；/g, ";");

    setSqlContent(value);
    validateSql(value);
    form.setFieldsValue({ sqlContent: value });
  };

  // 提交表单
  const handleSubmit = async (values: any) => {
    // 提交前再次验证SQL
    if (!validateSql(values.sqlContent)) {
      message.error('SQL格式有误，请修正后再提交！');
      return;
    }

    try {
      setSubmitLoading(true);
      const response = await uploadSql({
        sqlContent: values.sqlContent,
        remark: values.remark,
        shardId: values.shardId,
        isUsed: values.isUsed,
      });

      if (response.code === 200) {
        message.success('SQL上传成功！');
        // form.resetFields();
        // setSqlContent('');
        // setSqlError(null);
      } else {
        message.error(response.msg || '上传失败，请重试！');
      }
    } catch (error) {
      console.error('上传SQL出错:', error);
      message.error('上传失败，请检查网络连接！');
    } finally {
      setSubmitLoading(false);
    }
  };

  // 重置表单
  const handleReset = () => {
    form.resetFields();
    setSqlContent('');
    setSqlError(null);
    message.info('表单已重置');
  };

  // 添加处理初始渲染的 useEffect
  useEffect(() => {
    // 强制在首次渲染后重新计算布局
    const timer = setTimeout(() => {
      // 触发窗口的 resize 事件可以帮助 Ant Design 组件重新计算布局
      window.dispatchEvent(new Event('resize'));
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  return (
    <WrapContent>
      <div className={styles.container}>
        <Card
          title={
            <div className={styles.titleContainer}>
              <Title level={4}>
                <FormattedMessage id="pages.searchTable.autoTestUploadTitle" defaultMessage="自动化SQL语句上传" />
              </Title>
            </div>
          }
          bordered={false}
          className={`${styles.card} custom-card`}  // 添加额外的类名
        >
          <Spin spinning={loading}>
            <div className={styles.formContainer}>
              <Form
                form={form}
                layout="vertical"
                onFinish={handleSubmit}
                initialValues={{
                  sqlStatus: '0',
                  shardingValue: 'agg',
                }}
              >
                <div className={styles.formItem}>
                  <Form.Item
                    label="自动化备份SQL语句"
                    name="sqlContent"
                    rules={[{ required: true, message: '请输入SQL语句!' }]}
                    help={sqlError && <Text type="danger"><ExclamationCircleOutlined /> {sqlError}</Text>}
                    validateStatus={sqlError ? 'error' : undefined}
                    style={{ width: '100%' }}
                  >
                    <div className={styles.sqlInputWrapper}>
                      <TextArea
                        rows={5}
                        placeholder="请在此输入完整的SQL语句..."
                        className={`${styles.textArea} ${styles.sqlTextArea} custom-textarea`}
                        value={sqlContent}
                        onChange={handleSqlChange}
                        style={{ width: '100%' }}
                      />
                    </div>
                  </Form.Item>
                </div>

                <div className={styles.formItem}>
                  <Form.Item
                    label="备注"
                    name="remark"
                    rules={[{ required: true, message: '请输入备注信息!' }]}
                  >
                    <TextArea
                      rows={4}
                      placeholder="请输入备注信息..."
                      className={styles.textArea}
                    />
                  </Form.Item>
                </div>

                <div className={styles.horizontalFormItems}>
                  <div className={styles.formItemHalf}>
                    <Form.Item
                      label="分片值"
                      name="shardId"
                      rules={[{ required: true, message: '请选择分片值!' }]}
                    >
                      <Select
                        placeholder="请选择分片值"
                        className={styles.roundedSelect}
                        allowClear
                      >
                        {shardingOptions.map(option => (
                          <Option key={option} value={option}>
                            {option}
                          </Option>
                        ))}
                      </Select>
                    </Form.Item>
                  </div>

                  <div className={styles.formItemHalf}>
                    <Form.Item
                      label="SQL启用标识"
                      name="isUsed"
                      rules={[{ required: true, message: '请选择SQL启用标识!' }]}
                    >
                      <Select
                        placeholder="请选择SQL启用标识"
                        className={styles.roundedSelect}
                        allowClear
                      >
                        {sqlStatusOptions.map(option => (
                          <Option key={option.value} value={option.value}>
                            {option.label}
                          </Option>
                        ))}
                      </Select>
                    </Form.Item>
                  </div>
                </div>

                <div className={styles.buttonContainer}>
                  <Form.Item>
                    <Space size="middle">
                      <Button
                        type="primary"
                        htmlType="submit"
                        icon={<UploadOutlined />}
                        loading={submitLoading}
                        disabled={!!sqlError}
                        className={`${styles.roundedButton} ${sqlError ? styles.disabledButton : styles.successButton}`}
                      >
                        上传SQL
                      </Button>
                      <Button
                        type="default"
                        icon={<ReloadOutlined />}
                        onClick={handleReset}
                        className={styles.roundedButton}
                      >
                        重置
                      </Button>
                    </Space>
                  </Form.Item>
                </div>
              </Form>
            </div>
          </Spin>
        </Card>
      </div>
    </WrapContent>
  );
};

export default AutoTestSqlUpload;