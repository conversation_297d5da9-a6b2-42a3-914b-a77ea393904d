@import '~antd/es/style/themes/default.less';

.file-manager-container {
  width: 100%;
  background-color: #f0f2f5;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
  transition: all 0.3s;
  height: calc(100vh - 220px); /* 设置固定高度 */
  display: flex;
  flex-direction: column;
  
  .fixed-top-section {
    background: #fff;
    border-radius: 8px;
    margin-bottom: 16px;
    flex-shrink: 0;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  }
  
  .breadcrumb-container {
    padding: 12px 16px;
    background: #fff;
    border-radius: 8px 8px 0 0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    
    .ant-breadcrumb {
      a {
        cursor: pointer;
        color: #1890ff;
        transition: color 0.3s;
        
        &:hover {
          color: #40a9ff;
        }
      }
    }
  }
  
  .action-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: #fff;
    border-radius: 0 0 8px 8px;
    border-top: 1px solid #f0f0f0;
    
    .upload-container {
      display: flex;
      align-items: center;
      margin-left: 8px;
      
      .ant-upload-list {
        margin-left: 16px;
      }
    }
    
    .left-buttons {
      display: flex;
      align-items: center;
      
      button {
        transition: all 0.3s;
        
        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }
      }
    }
    
    .right-buttons {
      display: flex;
      align-items: center;
      
      button {
        transition: all 0.3s;
        
        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }
      }
    }
  }
  
  .scrollable-content {
    flex: 1;
    overflow-y: auto;
    background: #fff;
    border-radius: 8px;
    margin-top: 16px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  }
  
  .file-list {
    background: #fff;
    border-radius: 8px;
    
    .file-item {
      display: flex;
      align-items: center;
      padding: 16px;
      border-bottom: 1px solid #f0f0f0;
      cursor: pointer;
      transition: all 0.3s;
      
      &:hover {
        background-color: #f5f5f5;
        transform: translateX(4px);
      }
      
      &:last-child {
        border-bottom: none;
      }
      
      .file-icon {
        font-size: 24px;
        margin-right: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        
        // 文件夹图标样式
        .anticon-folder {
          font-size: 30px;
          color: #1890ff;
          
          &:before {
            background-color: rgba(24, 144, 255, 0.1);
            border-radius: 8px;
            padding: 4px;
          }
        }
        
        // 文件图标样式
        .anticon-file,
        .anticon-file-text,
        .anticon-file-image,
        .anticon-file-excel,
        .anticon-file-word,
        .anticon-file-pdf,
        .anticon-file-zip {
          font-size: 26px;
          color: #52c41a;
        }
        
        .anticon-file-pdf {
          color: #f5222d;
        }
        
        .anticon-file-excel {
          color: #52c41a;
        }
        
        .anticon-file-word {
          color: #1890ff;
        }
        
        .anticon-file-image {
          color: #faad14;
        }
        
        .anticon-file-zip {
          color: #722ed1;
        }
      }
      
      .file-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        
        .file-name {
          color: rgba(0, 0, 0, 0.85);
          font-weight: 500;
          margin-bottom: 4px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          max-width: 500px;
        }
        
        .file-meta {
          display: flex;
          color: rgba(0, 0, 0, 0.45);
          font-size: 12px;
          
          .file-size {
            margin-right: 16px;
          }
          
          .file-date {
            white-space: nowrap;
          }
        }
      }
      
      .file-actions {
        opacity: 0;
        transition: opacity 0.3s;
        margin-left: 16px;
        
        .ant-btn {
          color: #1890ff;
          
          &:hover {
            color: #40a9ff;
            background-color: rgba(24, 144, 255, 0.1);
          }
        }
      }
      
      &:hover .file-actions {
        opacity: 1;
      }
    }
  }
  
  .empty-folder {
    padding: 48px 0;
    text-align: center;
    color: rgba(0, 0, 0, 0.45);
    background: #fff;
    border-radius: 8px;
    
    .ant-empty-image {
      height: 100px;
    }
  }
}

// 添加淡入动画
.fade-in-up {
  animation: fadeInUp 0.5s ease-out forwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 上传进度模态框样式
.ant-modal-upload-progress {
  .ant-modal-content {
    border-radius: 8px;
    overflow: hidden;
  }
  
  .ant-modal-header {
    background-color: #f6f8fa;
  }
  
  .ant-modal-body {
    padding: 24px;
  }
  
  .ant-progress-text {
    font-size: 16px;
    font-weight: 500;
  }
  
  .ant-progress-bg {
    background-color: #1890ff;
    transition: all 0.3s ease;
  }
}

// 响应式调整
@media screen and (max-width: 768px) {
  .file-manager-container {
    padding: 16px;
    height: calc(100vh - 180px);
    
    .file-item {
      .file-name {
        max-width: 200px;
      }
      
      .file-meta {
        flex-direction: column;
        
        .file-size {
          margin-right: 0;
          margin-bottom: 4px;
        }
      }
    }
    
    .action-bar {
      flex-direction: column;
      align-items: flex-start;
      
      .right-buttons {
        margin-top: 12px;
        align-self: flex-end;
      }
    }
  }
} 