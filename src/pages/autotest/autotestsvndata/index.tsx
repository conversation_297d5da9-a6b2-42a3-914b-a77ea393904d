import React, { useState, useEffect, useRef } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON>b,
  <PERSON>ton,
  List,
  Empty,
  message,
  Upload,
  Tooltip,
  Modal,
  Spin,
  Progress,
  Alert,
} from "antd";
import {
  FolderOutlined,
  FileOutlined,
  UploadOutlined,
  DownloadOutlined,
  ArrowLeftOutlined,
  HomeOutlined,
  FileImageOutlined,
  FileExcelOutlined,
  FileWordOutlined,
  FilePdfOutlined,
  FileZipOutlined,
  FileTextOutlined,
  ReloadOutlined,
} from "@ant-design/icons";
import type { UploadProps } from "antd";
import { PageContainer } from "@ant-design/pro-layout";
import {
  getFileList,
  downloadFile,
  uploadFile,
  checkFileUploadStatus,
  uploadChunk,
  mergeChunks,
} from "./service";
import type { FileItem, PathHistory, UploadProgress } from "./data";
import {
  sliceFile,
  formatFileSize as formatFileSizeUtil,
  safeDecodeFileName,
  isLargeFile,
} from "@/utils/fileUtils";
import {
  createUploadProgress,
  getUploadProgress,
  updateChunkProgress,
  removeUploadProgress,
} from "@/utils/uploadStorage";
import "./style.less";

const FILE_ICONS: Record<string, any> = {
  default: <FileOutlined />,
  jpg: <FileImageOutlined />,
  jpeg: <FileImageOutlined />,
  png: <FileImageOutlined />,
  gif: <FileImageOutlined />,
  bmp: <FileImageOutlined />,
  xls: <FileExcelOutlined />,
  xlsx: <FileExcelOutlined />,
  doc: <FileWordOutlined />,
  docx: <FileWordOutlined />,
  pdf: <FilePdfOutlined />,
  zip: <FileZipOutlined />,
  rar: <FileZipOutlined />,
  txt: <FileTextOutlined />,
};

// 上传进度模态框组件
interface UploadProgressModalProps {
  visible: boolean;
  progress: number;
  fileName: string;
  fileHash?: string;
  totalChunks?: number;
  currentPath?: string;
  onCancel: () => void;
  onMerge?: (
    fileHash: string,
    fileName: string,
    folder: string,
    totalChunks: number
  ) => Promise<void>;
  merging?: boolean;
}

const UploadProgressModal: React.FC<UploadProgressModalProps> = ({
  visible,
  progress,
  fileName,
  fileHash,
  totalChunks,
  currentPath,
  onCancel,
  onMerge,
  merging = false,
}) => {
  const [isMerging, setIsMerging] = useState<boolean>(merging);

  // 处理合并按钮点击
  const handleMergeClick = async () => {
    if (fileHash && fileName && currentPath && totalChunks && onMerge) {
      setIsMerging(true);
      try {
        await onMerge(fileHash, fileName, currentPath, totalChunks);
        message.success("文件合并成功");
      } catch (error: any) {
        message.error(`文件合并失败: ${error.message || "未知错误"}`);
      } finally {
        setIsMerging(false);
      }
    } else {
      message.error("缺少必要的合并信息");
    }
  };

  // 判断是否显示合并按钮
  const showMergeButton =
    progress === 100 && !isMerging && fileHash && fileName && totalChunks;

  return (
    <Modal
      title="文件上传进度"
      open={visible}
      footer={null}
      closable={false}
      maskClosable={false}
      centered
    >
      <div style={{ padding: "20px 0" }}>
        <p style={{ marginBottom: 16 }}>正在处理: {fileName}</p>
        <Progress
          percent={Math.round(progress)}
          status={isMerging ? "active" : progress < 100 ? "active" : "success"}
        />
        <div style={{ marginTop: 16, textAlign: "right" }}>
          {showMergeButton && (
            <Button
              type="primary"
              onClick={handleMergeClick}
              loading={isMerging}
              style={{ marginRight: 8 }}
            >
              {isMerging ? "正在合并..." : "完成并合并文件"}
            </Button>
          )}
          <Button onClick={onCancel} disabled={isMerging}>
            {progress < 100 ? "上传中..." : isMerging ? "合并中..." : "关闭"}
          </Button>
        </div>
      </div>
    </Modal>
  );
};

// 上传并发数限制
const MAX_CONCURRENT_UPLOADS = 3;

const SVNFileManager: React.FC = () => {
  const [currentPath, setCurrentPath] = useState<string>("/");
  const [fileList, setFileList] = useState<FileItem[]>([]);
  const [pathHistory, setPathHistory] = useState<PathHistory[]>([
    { name: "根目录", path: "/" },
  ]);
  const [isVisible, setIsVisible] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [uploading, setUploading] = useState<boolean>(false);
  // 上传进度状态
  const [uploadModalVisible, setUploadModalVisible] = useState<boolean>(false);
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [currentUploadFile, setCurrentUploadFile] = useState<string>("");
  const [uploadCancelled, setUploadCancelled] = useState<boolean>(false);
  const [confirmModal, setConfirmModal] = useState<any>(null);
  const [uploadFileInfo, setUploadFileInfo] = useState<{
    fileHash: string;
    totalChunks: number;
  } | null>(null);
  const [isMerging, setIsMerging] = useState<boolean>(false);

  // 获取文件图标
  const getFileIcon = (fileName: string) => {
    if (!fileName) return FILE_ICONS.default;

    const extension = fileName.toLowerCase().split(".").pop() || "";
    return FILE_ICONS[extension] || FILE_ICONS.default;
  };

  // 格式化文件大小
  const formatFileSize = (size?: number) => {
    return formatFileSizeUtil(size);
  };

  // 判断是否为文件夹
  const isFolder = (item: FileItem): boolean => {
    return Boolean(item.directory);
  };

  // 加载文件列表
  const fetchFileList = async (path: string) => {
    setLoading(true);
    try {
      const res = await getFileList({ path });
      if (res.code === 200) {
        // 解析文件列表数据，并处理中文文件名
        const formattedData = (res.data || []).map((item) => {
          // 使用安全解码函数处理文件名
          const decodedName = safeDecodeFileName(item.name || "");

          return {
            ...item,
            isDirectory: Boolean(item.directory),
            name: decodedName,
          };
        });

        // 对文件列表进行排序：文件夹在上，文件在下，按名称排序
        const sortedData = formattedData.sort((a, b) => {
          // 首先按照是否为目录排序
          if ((a.directory ? true : false) !== (b.directory ? true : false)) {
            return a.directory ? -1 : 1;
          }
          // 然后按照名称排序（不区分大小写）
          return a.name.toLowerCase().localeCompare(b.name.toLowerCase());
        });

        console.log("格式化后的文件列表数据:", sortedData);
        setFileList(sortedData);
      } else {
        message.error(res.msg || "获取文件列表失败");
      }
    } catch (error) {
      console.error("获取文件列表失败:", error);
      message.error("获取文件列表失败");
    } finally {
      setLoading(false);
    }
  };

  // 初始加载和路径变化时重新加载文件列表
  useEffect(() => {
    fetchFileList(currentPath);
    // 添加动画，在组件首次渲染时显示
    setIsVisible(true);
  }, [currentPath]);

  // 处理文件或文件夹点击事件
  const handleItemClick = (item: FileItem) => {
    console.log("点击的项目:", item);

    if (isFolder(item)) {
      // 如果是文件夹，导航到该文件夹
      console.log("点击了文件夹，准备导航");
      const newPath = item.path;
      const newHistory = [...pathHistory, { name: item.name, path: newPath }];
      setPathHistory(newHistory);
      setCurrentPath(newPath);
    } else {
      // 如果是文件，提示是否下载
      console.log("点击了文件，询问是否下载");
      Modal.confirm({
        title: "下载文件",
        content: `是否下载文件 ${item.name}？`,
        onOk: () => handleDownload(item),
      });
    }
  };

  // 处理返回上级目录
  const handleGoBack = () => {
    if (pathHistory.length > 1) {
      const newHistory = [...pathHistory];
      newHistory.pop(); // 移除当前路径
      const previousItem = newHistory[newHistory.length - 1];

      setPathHistory(newHistory);
      setCurrentPath(previousItem.path);
    }
  };

  // 处理刷新当前目录
  const handleRefresh = () => {
    fetchFileList(currentPath);
    message.success("正在刷新目录...");
  };

  // 导航到特定路径
  const navigateTo = (index: number) => {
    if (index < pathHistory.length) {
      const newHistory = pathHistory.slice(0, index + 1);
      setPathHistory(newHistory);
      setCurrentPath(newHistory[index].path);
    }
  };

  // 处理文件下载
  const handleDownload = async (file: FileItem) => {
    if (isFolder(file)) {
      message.info("文件夹不能直接下载，请进入文件夹查看文件");
      return;
    }

    try {
      const res = await downloadFile(file.path);
      if (res.code === 200) {
        message.success("文件下载成功");
      } else {
        message.error(res.msg || "文件下载失败");
      }
    } catch (error) {
      console.error("文件下载失败:", error);
      message.error("文件下载失败");
    }
  };

  // 处理文件上传
  const handleUpload: UploadProps["customRequest"] = async (options) => {
    const { file, onSuccess, onError, onProgress } = options;

    if (!file) {
      message.error("请选择文件");
      return;
    }

    // 设置上传状态
    setUploading(true);
    setUploadCancelled(false);
    setUploadFileInfo(null); // 重置之前的文件信息

    try {
      const fileObj = file as File;
      const fileSize = fileObj.size;

      // 对于超大文件给予警告提示
      if (fileSize > 100 * 1024 * 1024) {
        // 大于100MB
        const modal = Modal.confirm({
          title: "大文件上传提示",
          content: (
            <div>
              <Alert
                message="大文件上传警告"
                description={`您选择的文件大小为${formatFileSize(fileSize)}，上传可能需要较长时间。建议将大文件拆分后上传或使用其他传输方式。`}
                type="warning"
                showIcon
                style={{ marginBottom: 16 }}
              />
              <p>是否继续上传？</p>
            </div>
          ),
          onOk: async () => {
            // 确认上传，先关闭对话框
            modal.destroy();
            // 然后开始上传
            await processUpload(fileObj, onSuccess, onError, onProgress);
          },
          onCancel: () => {
            setUploading(false); // 确保取消时重置上传状态
            message.info("已取消上传");
          },
        });
        setConfirmModal(modal);
      } else {
        await processUpload(fileObj, onSuccess, onError, onProgress);
      }
    } catch (error: any) {
      console.error("文件上传失败:", error);
      message.error(`文件上传失败: ${error.message || "未知错误"}`);
      onError?.(error as Error);

      // 出错时重置上传状态
      setUploading(false);
      setUploadModalVisible(false);
    }
  };

  // 处理上传逻辑，从handleUpload中提取出来
  const processUpload = async (
    fileObj: File,
    onSuccess?: (response: any, xhr: XMLHttpRequest) => void,
    onError?: (error: Error) => void,
    onProgress?: (event: { percent: number }) => void
  ) => {
    try {
      // 准备上传进度显示
      setCurrentUploadFile(fileObj.name);
      setUploadProgress(0);
      setUploadModalVisible(true);

      const updateProgress = (percent: number) => {
        setUploadProgress(percent);
        onProgress?.({ percent });
      };

      // 根据文件大小决定上传方式
      if (isLargeFile(fileObj.size)) {
        await handleChunkUpload(fileObj, updateProgress);
        onSuccess?.({ success: true }, new XMLHttpRequest());
      } else {
        // 小文件使用普通上传
        const res = await uploadFile(currentPath, fileObj);
        if (res.code === 200) {
          updateProgress(100);
          setTimeout(() => {
            message.success("文件上传成功");
            fetchFileList(currentPath);
          }, 500);
          onSuccess?.(res, new XMLHttpRequest());
        } else {
          message.error(res.msg || "文件上传失败");
          onError?.(new Error(res.msg || "文件上传失败"));
        }
      }
    } catch (error: any) {
      console.error("处理上传过程出错:", error);
      onError?.(new Error(`处理上传过程出错: ${error.message}`));
      throw error;
    }
  };

  // 处理手动合并文件
  const handleManualMerge = async (
    fileHash: string,
    fileName: string,
    folder: string,
    totalChunks: number
  ) => {
    setIsMerging(true);
    try {
      await finalizeUpload(
        fileHash,
        fileName,
        folder,
        totalChunks,
        (percent) => {
          // 将原始99%-100%的范围映射到0%-100%
          const mappedPercent = (percent - 99) * 100;
          setUploadProgress(100 + mappedPercent * 0.01); // 保持100%显示，但内部有细微变化
        }
      );

      // 合并成功，更新文件列表
      message.success("文件合并成功");
      fetchFileList(folder);

      // 关闭上传对话框
      setTimeout(() => {
        setUploadModalVisible(false);
      }, 500);
    } catch (error: any) {
      message.error(`文件合并失败: ${error.message || "未知错误"}`);
    } finally {
      // 重置所有上传相关状态
      setIsMerging(false);
      setUploading(false); // 重置上传按钮状态
      setUploadFileInfo(null);
    }
  };

  // 处理分片上传（修改为并发上传，但不自动合并）
  const handleChunkUpload = async (
    file: File,
    onProgress?: (percent: number) => void
  ) => {
    // 定义上传结果类型
    interface ChunkUploadResult {
      success: boolean;
      index?: number;
      error?: Error;
      cancelled?: boolean;
    }

    // 切分文件并计算哈希
    const { chunks, fileHash, fileName, totalChunks } = await sliceFile(file);
    console.log(`开始上传文件: ${fileName}, 总分片数: ${totalChunks}`);

    // 保存文件信息以便后续合并使用
    setUploadFileInfo({
      fileHash,
      totalChunks,
    });

    try {
      // 检查文件上传状态
      const checkResult = await checkFileUploadStatus(
        fileHash,
        fileName,
        currentPath,
        totalChunks
      );

      // 如果文件已存在，则无需上传
      if (checkResult.data && !checkResult.data.shouldUpload) {
        message.success("文件已存在，无需重新上传");
        fetchFileList(currentPath);
        return;
      }

      // 已上传的分片
      const uploadedChunks = checkResult.data?.uploadedChunks || [];
      console.log(`已上传的分片: ${uploadedChunks.length}/${totalChunks}`);

      // 创建或获取上传进度
      let progress = getUploadProgress(fileHash);
      if (!progress) {
        progress = createUploadProgress(fileHash, fileName, uploadedChunks);
      } else {
        // 更新已上传的分片
        uploadedChunks.forEach((index: number) => {
          if (!progress?.uploadedChunks.includes(index)) {
            progress?.uploadedChunks.push(index);
          }
        });
      }

      // 需要上传的分片
      const uploadTasks = [];
      for (let i = 0; i < totalChunks; i++) {
        // 跳过已上传的分片
        if (uploadedChunks.includes(i)) continue;

        uploadTasks.push({
          index: i,
          blob: chunks[i].chunk,
        });
      }

      // 如果没有需要上传的分片，就不进行后续上传，但不自动合并
      if (uploadTasks.length === 0) {
        console.log("所有分片已上传，可以进行合并");
        onProgress?.(100);
        return;
      }

      // 显示进度
      let completedTasks = uploadedChunks.length;
      const totalTasks = totalChunks;

      if (onProgress) {
        onProgress((completedTasks / totalTasks) * 100);
      }

      // 追踪上传状态
      const uploadingState = {
        completed: completedTasks,
        total: totalTasks,
        cancelled: false,
      };

      // 创建上传单个分片的函数
      const uploadSingleChunk = async (
        index: number,
        blob: Blob
      ): Promise<ChunkUploadResult> => {
        // 如果已取消上传，则不继续
        if (uploadCancelled || uploadingState.cancelled) {
          return { success: false, cancelled: true };
        }

        let retries = 0;
        let uploadSuccess = false;

        while (
          !uploadSuccess &&
          retries < 3 &&
          !uploadCancelled &&
          !uploadingState.cancelled
        ) {
          try {
            // 上传单个分片
            await uploadChunk(fileHash, index, totalChunks, blob);
            uploadSuccess = true;

            // 更新上传进度
            updateChunkProgress(fileHash, index, 100);

            // 原子操作更新已完成任务数
            uploadingState.completed++;

            if (onProgress) {
              onProgress(
                (uploadingState.completed / uploadingState.total) * 100
              );
            }

            return { success: true, index };
          } catch (error: any) {
            retries++;
            console.error(`上传分片${index}失败，第${retries}次重试...`, error);

            if (retries >= 3) {
              message.error(`分片${index}上传失败: ${error.message}`);
              return {
                success: false,
                error: new Error(
                  `分片${index}上传失败，已达到最大重试次数: ${error.message}`
                ),
              };
            }

            // 失败后等待更长时间再重试
            const waitTime = 2000 * retries; // 2秒 * 重试次数
            message.warning(`等待${waitTime / 1000}秒后重试分片${index}...`);
            await new Promise((resolve) => setTimeout(resolve, waitTime));
          }
        }

        // 如果被取消
        if (uploadCancelled || uploadingState.cancelled) {
          return { success: false, cancelled: true };
        }

        return { success: false };
      };

      // 并发上传分片，但限制最大并发数
      const tasksQueue = [...uploadTasks];
      const inProgress = new Set();
      const results = [];

      // 监听取消上传
      const cancelListener = () => {
        if (uploadCancelled) {
          uploadingState.cancelled = true;
          inProgress.clear();
          tasksQueue.length = 0;
        }
      };

      // 定期检查是否取消
      const cancelCheckInterval = setInterval(cancelListener, 1000);

      try {
        while (tasksQueue.length > 0 || inProgress.size > 0) {
          // 填充并发任务
          while (
            tasksQueue.length > 0 &&
            inProgress.size < MAX_CONCURRENT_UPLOADS
          ) {
            const task = tasksQueue.shift()!;
            const taskPromise = uploadSingleChunk(task.index, task.blob)
              .then((result) => {
                inProgress.delete(taskPromise);
                return result;
              })
              .catch((error) => {
                inProgress.delete(taskPromise);
                return { success: false, error };
              });

            inProgress.add(taskPromise);
            results.push(taskPromise);

            // 添加短暂延迟，避免同时发起太多请求
            await new Promise((resolve) => setTimeout(resolve, 100));
          }

          // 如果并发队列满了，等待一个任务完成
          if (
            inProgress.size >= MAX_CONCURRENT_UPLOADS ||
            tasksQueue.length === 0
          ) {
            await Promise.race([...inProgress]);
          }

          // 检查是否已取消
          if (uploadCancelled || uploadingState.cancelled) {
            clearInterval(cancelCheckInterval);
            throw new Error("上传已被用户取消");
          }
        }

        clearInterval(cancelCheckInterval);

        // 等待所有上传任务完成并检查结果
        const uploadResults = await Promise.all(results);
        const failedTasks = uploadResults.filter(
          (r: ChunkUploadResult) => !r.success && !r.cancelled
        );

        // 如果有失败的任务
        if (failedTasks.length > 0) {
          const errors = failedTasks
            .map((t: ChunkUploadResult) => t.error?.message || "未知错误")
            .join("; ");
          throw new Error(`部分分片上传失败: ${errors}`);
        }

        // 添加明确的日志
        console.log(`所有分片已上传完成，等待用户点击合并按钮: ${fileName}`);

        // 更新进度到100%，但不自动合并
        if (onProgress) {
          onProgress(100);
        }
      } catch (error: any) {
        console.error("文件上传失败:", error);
        message.error(
          `文件${uploadCancelled ? "上传取消" : "上传失败"}: ${error.message}`
        );
        throw error;
      }
    } catch (error: any) {
      console.error("文件上传失败:", error);
      message.error(
        `文件${uploadCancelled ? "上传取消" : "上传失败"}: ${error.message}`
      );
      throw error;
    }
  };

  // 提取合并文件的逻辑为独立函数
  const finalizeUpload = async (
    fileHash: string,
    fileName: string,
    folder: string,
    totalChunks: number,
    onProgress?: (percent: number) => void
  ) => {
    try {
      console.log(
        `开始最终检查和合并文件: ${fileName}, 哈希: ${fileHash}, 分片数: ${totalChunks}`
      );

      // 在合并前再次检查所有分片状态
      const verifyResult = await checkFileUploadStatus(
        fileHash,
        fileName,
        folder,
        totalChunks
      );

      const uploadedAfterVerify = verifyResult.data?.uploadedChunks || [];
      console.log(
        `验证后的分片数量: ${uploadedAfterVerify.length}/${totalChunks}`
      );

      if (uploadedAfterVerify.length !== totalChunks) {
        throw new Error(
          `部分分片未上传成功(${uploadedAfterVerify.length}/${totalChunks})，请重试`
        );
      }

      // 更新进度到99%（表示准备合并）
      if (onProgress) {
        onProgress(99);
      }

      // 合并文件分片
      console.log(`确认所有分片已上传，开始合并文件: ${fileName}`);
      const mergeResult = await mergeChunks(
        fileHash,
        fileName,
        folder,
        totalChunks
      );

      if (mergeResult.code === 200) {
        console.log(`文件合并成功: ${fileName}`);
        // 更新进度到100%
        if (onProgress) {
          onProgress(100);
        }

        // 清理上传进度记录
        removeUploadProgress(fileHash);

        // 显示成功消息并刷新列表
        setTimeout(() => {
          message.success("文件上传成功");
          fetchFileList(folder);
        }, 500);
      } else {
        throw new Error(mergeResult.msg || "文件合并失败");
      }
    } catch (error: any) {
      console.error("合并文件失败:", error);
      message.error(`文件合并失败: ${error.message}`);
      throw error;
    }
  };

  // 渲染文件或文件夹图标
  const renderFileIcon = (item: FileItem) => {
    if (isFolder(item)) {
      return (
        <span className="file-icon">
          <FolderOutlined />
        </span>
      );
    }

    return <span className="file-icon">{getFileIcon(item.name)}</span>;
  };

  // 自定义按钮样式
  const buttonStyle = {
    borderRadius: "8px", // 更圆的边框
  };

  const uploadButtonStyle = {
    ...buttonStyle,
    backgroundColor: "#FFF9C4", // 淡黄色
    borderColor: "#FFC107",
    color: "#000",
  };

  const refreshButtonStyle = {
    ...buttonStyle,
    backgroundColor: "#FFECEF", // 淡粉色
    borderColor: "#FFC0CB",
    color: "#000",
    marginLeft: "8px", // 与返回上级按钮的间距
  };

  // 取消上传
  const handleCancelUpload = () => {
    if (uploadProgress < 100 || isMerging) {
      Modal.confirm({
        title: "取消操作",
        content: `确定要取消${isMerging ? "合并" : "上传"}吗？`,
        onOk: () => {
          setUploadCancelled(true);
          setUploadModalVisible(false);
          setIsMerging(false);
          setUploading(false); // 确保上传状态被重置
          message.info(`已取消${isMerging ? "合并" : "上传"}`);
        },
      });
    } else {
      // 如果已经上传完成且没在合并，直接关闭对话框
      setUploadModalVisible(false);
      setUploading(false); // 确保上传状态被重置
    }
  };

  // 组件卸载时清理确认对话框
  useEffect(() => {
    return () => {
      if (confirmModal) {
        confirmModal.destroy();
      }
    };
  }, [confirmModal]);

  return (
    <PageContainer>
      <div
        className={`file-manager-container ${isVisible ? "fade-in-up" : ""}`}
      >
        {/* 顶部固定区域 */}
        <div className="fixed-top-section">
          {/* 面包屑导航 */}
          <div className="breadcrumb-container">
            <Breadcrumb>
              {pathHistory.map((item, index) => (
                <Breadcrumb.Item key={item.path}>
                  <a onClick={() => navigateTo(index)}>
                    {index === 0 ? <HomeOutlined /> : null} {item.name}
                  </a>
                </Breadcrumb.Item>
              ))}
            </Breadcrumb>
          </div>

          {/* 操作栏 */}
          <div className="action-bar">
            <div className="left-buttons">
              <Button
                icon={<ArrowLeftOutlined />}
                onClick={handleGoBack}
                disabled={pathHistory.length <= 1}
                style={buttonStyle}
              >
                返回上级
              </Button>

              <Button
                icon={<ReloadOutlined />}
                onClick={handleRefresh}
                style={refreshButtonStyle}
              >
                刷新目录
              </Button>
            </div>

            <div className="right-buttons">
              <Upload
                customRequest={handleUpload}
                showUploadList={false}
                multiple={false}
              >
                <Button
                  type="primary"
                  icon={<UploadOutlined />}
                  loading={uploading}
                  style={uploadButtonStyle}
                >
                  上传文件
                </Button>
              </Upload>
            </div>
          </div>
        </div>

        {/* 可滚动的文件列表区域 */}
        <div className="scrollable-content">
          <Spin spinning={loading}>
            {fileList.length > 0 ? (
              <div className="file-list">
                <List
                  dataSource={fileList}
                  renderItem={(item) => (
                    <div
                      className="file-item"
                      onClick={() => handleItemClick(item)}
                    >
                      {renderFileIcon(item)}
                      <div className="file-info">
                        <span className="file-name">{item.name}</span>
                        <div className="file-meta">
                          {!isFolder(item) && (
                            <span className="file-size">
                              {formatFileSize(item.size)}
                            </span>
                          )}
                          {item.lastModified && (
                            <span className="file-date">
                              {item.lastModified}
                            </span>
                          )}
                        </div>
                      </div>
                      {!isFolder(item) && (
                        <div className="file-actions">
                          <Tooltip title="下载">
                            <Button
                              type="text"
                              icon={<DownloadOutlined />}
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDownload(item);
                              }}
                            />
                          </Tooltip>
                        </div>
                      )}
                    </div>
                  )}
                />
              </div>
            ) : (
              <div className="empty-folder">
                <Empty
                  description="当前文件夹为空"
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                />
              </div>
            )}
          </Spin>
        </div>

        {/* 上传进度模态框 */}
        <UploadProgressModal
          visible={uploadModalVisible}
          progress={uploadProgress}
          fileName={currentUploadFile}
          fileHash={uploadFileInfo?.fileHash}
          totalChunks={uploadFileInfo?.totalChunks}
          currentPath={currentPath}
          onCancel={handleCancelUpload}
          onMerge={handleManualMerge}
          merging={isMerging}
        />
      </div>
    </PageContainer>
  );
};

export default SVNFileManager;
