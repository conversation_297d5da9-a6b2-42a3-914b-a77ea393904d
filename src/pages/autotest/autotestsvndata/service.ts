import request from "@/utils/request";
import type { FileListParams, FileListResponse, UploadFileResponse } from "./data";
import { safeDecodeFileName } from "@/utils/fileUtils";

// 获取文件列表
export async function getFileList(params: FileListParams) {
  return request<FileListResponse>("/svn/file/list", {
    method: "GET",
    params,
    headers: {
      "Content-Type": "application/json;charset=UTF-8",
    },
  });
}

// 下载文件
export async function downloadFile(filePath: string) {
  try {
    // 使用项目的request工具，设置responseType为blob
    const res = await request(`/svn/file/download`, {
      method: "GET",
      params: { filePath },
      responseType: "blob",
      getResponse: true,
    });

    // 检查响应
    if (!res || !res.data) {
      throw new Error("下载失败: 未收到有效响应");
    }

    // 从路径中获取文件名
    const pathParts = filePath.split('/');
    let fileName = pathParts[pathParts.length - 1];

    // 获取文件名（从响应头）
    const contentDisposition = res.response.headers.get("content-disposition");
    if (contentDisposition) {
      // 先尝试获取filename*=utf-8''格式的文件名（RFC 5987）
      const filenameStarMatch = /filename\*=([^']*)'[^']*'([^;]*)/.exec(contentDisposition);
      if (filenameStarMatch && filenameStarMatch[2]) {
        // 对URL编码的文件名进行解码
        try {
          fileName = decodeURIComponent(filenameStarMatch[2]);
        } catch (e) {
          console.error("解码文件名失败", e);
        }
      } else {
        // 回退到普通filename形式
        const filenameMatch = /filename=["]?([^"]*)["]?/.exec(contentDisposition);
        if (filenameMatch && filenameMatch[1]) {
          fileName = filenameMatch[1];
          
          // 使用安全的解码函数
          fileName = safeDecodeFileName(fileName);
        }
      }
    }

    // 创建Blob对象
    const blob = new Blob([res.data], {
      // 尝试从响应头获取正确的MIME类型
      type: res.response.headers.get('content-type') || 'application/octet-stream'
    });

    // 创建下载链接
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.setAttribute("download", fileName);

    // 触发下载
    document.body.appendChild(link);
    link.click();

    // 清理
    setTimeout(() => {
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    }, 100);

    return { code: 200, msg: "下载成功" };
  } catch (error) {
    console.error("下载文件失败:", error);
    return { code: 500, msg: "下载失败" };
  }
}

// 上传文件
export async function uploadFile(folder: string, file: File) {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('folder', folder);

  return request<UploadFileResponse>('/svn/file/upload', {
    method: 'POST',
    data: formData,
    headers: {
      // 不需要设置 Content-Type，浏览器会自动设置为 multipart/form-data
    },
    timeout: 60000, // 增加超时时间到60秒
  });
} 

// 检查文件上传状态
export async function checkFileUploadStatus(fileHash: string, fileName: string, folder: string, totalChunks: number) {
  let retries = 0;
  const maxRetries = 2;
  
  while (retries <= maxRetries) {
    try {
      console.log(`检查文件状态: ${fileName}, 哈希: ${fileHash}, 尝试次数: ${retries + 1}`);
      
      const response = await request('/svn/file/check', {
        method: 'GET',
        params: { fileHash, fileName, folder, totalChunks },
        timeout: 10000, // 10秒超时
      });
      
      console.log('检查文件状态返回:', response);
      return response;
    } catch (error: any) {
      retries++;
      console.error(`检查文件状态失败(尝试${retries}/${maxRetries}):`, error);
      
      if (retries > maxRetries) {
        return {
          code: 500,
          msg: `检查文件状态失败: ${error.message || '未知错误'}`,
          data: { uploadedChunks: [], shouldUpload: true }
        };
      }
      
      // 等待后重试
      await new Promise(resolve => setTimeout(resolve, 1000 * retries));
    }
  }
  
  // 默认返回（不应该到达这里）
  return {
    code: 500,
    msg: '检查文件状态失败: 超过最大重试次数',
    data: { uploadedChunks: [], shouldUpload: true }
  };
}

// 上传文件分片
export async function uploadChunk(fileHash: string, chunkIndex: number, totalChunks: number, chunkFile: Blob) {
  const formData = new FormData();
  formData.append('file', chunkFile);
  formData.append('fileHash', fileHash);
  formData.append('chunkIndex', chunkIndex.toString());
  formData.append('totalChunks', totalChunks.toString());

  return request('/svn/file/upload/chunk', {
    method: 'POST',
    data: formData,
    timeout: 60000, // 增加超时到60秒
    getResponse: true, // 获取完整响应
    headers: {
      // 不设置Content-Type，让浏览器自动处理
      'X-Requested-With': 'XMLHttpRequest',
    },
    errorHandler: (error: any) => {
      console.error(`分片上传错误[${chunkIndex}/${totalChunks}]:`, error);
      // 返回一个包含更多信息的错误对象
      const errorResponse = {
        code: error.response?.status || 500,
        msg: `分片[${chunkIndex}]上传失败: ${error.message || '未知错误'}`,
        data: null,
        success: false
      };
      
      // 如果错误消息中包含"Too many open files"，给出更具体的错误提示
      if (error.message && error.message.includes('Too many open files')) {
        errorResponse.msg = `分片[${chunkIndex}]上传失败: 服务器打开的文件数量过多，请稍后重试或联系管理员增加系统文件句柄限制`;
      }
      
      return errorResponse;
    }
  }).then(res => {
    const { data } = res;
    // 成功上传分片
    return data;
  }).catch(err => {
    // 处理网络错误
    console.error(`分片[${chunkIndex}]上传网络错误:`, err);
    throw new Error(`分片[${chunkIndex}]上传失败: ${err.message || '网络错误'}`);
  });
}

// 合并文件分片
export async function mergeChunks(fileHash: string, fileName: string, folder: string, totalChunks: number) {
  let retries = 0;
  const maxRetries = 2;
  
  while (retries <= maxRetries) {
    try {
      console.log(`开始合并文件(尝试${retries + 1}/${maxRetries + 1}): ${fileName}, 哈希: ${fileHash}, 总分片数: ${totalChunks}`);
      
      const response = await request('/svn/file/upload/merge', {
        method: 'POST',
        params: { fileHash, fileName, folder, totalChunks },
        timeout: 120000, // 增加合并操作超时时间到2分钟，避免大文件合并超时
      });
      
      console.log('合并文件返回:', response);
      return response;
    } catch (error: any) {
      retries++;
      console.error(`合并文件失败(尝试${retries}/${maxRetries}):`, error);
      
      if (retries > maxRetries) {
        return {
          code: 500,
          msg: `合并文件失败: ${error.message || '未知错误'}`,
          data: null
        };
      }
      
      // 等待后重试
      await new Promise(resolve => setTimeout(resolve, 2000 * retries));
    }
  }
  
  // 默认返回（不应该到达这里）
  return {
    code: 500,
    msg: '合并文件失败: 超过最大重试次数',
    data: null
  };
}