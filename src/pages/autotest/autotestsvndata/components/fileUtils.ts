import SparkMD5 from 'spark-md5';

const CHUNK_SIZE = 1 * 1024 * 1024; // 1MB chunks

/**
 * 计算文件Hash值
 * @param file 文件对象
 * @returns Promise<string> 计算得到的哈希值
 */
export const calculateHash = async (file: File): Promise<string> => {
  return new Promise((resolve) => {
    const spark = new SparkMD5.ArrayBuffer();
    const reader = new FileReader();
    
    reader.onload = (e) => {
      spark.append(e.target?.result as ArrayBuffer);
      resolve(spark.end());
    };
    
    reader.readAsArrayBuffer(file);
  });
};

/**
 * 创建文件分片
 * @param file 文件对象
 * @returns Blob[] 分片数组
 */
export const createFileChunks = (file: File): Blob[] => {
  const chunks: Blob[] = [];
  let start = 0;
  
  while (start < file.size) {
    const chunk = file.slice(start, start + CHUNK_SIZE);
    chunks.push(chunk);
    start += CHUNK_SIZE;
  }
  
  return chunks;
}; 