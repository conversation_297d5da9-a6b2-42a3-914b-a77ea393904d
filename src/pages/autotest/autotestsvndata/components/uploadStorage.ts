import { UploadProgress } from '../data';

const STORAGE_KEY = 'svn_file_upload_progress';

/**
 * 保存上传进度信息到localStorage
 * @param progress 上传进度信息
 */
export const saveUploadProgress = (progress: UploadProgress): void => {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(progress));
  } catch (error) {
    console.error('保存上传进度失败:', error);
  }
};

/**
 * 获取指定文件的上传进度
 * @param fileHash 文件哈希值
 * @returns UploadProgress | null 上传进度或null
 */
export const getUploadProgress = (fileHash: string): UploadProgress | null => {
  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (!stored) return null;
    
    const progress = JSON.parse(stored) as UploadProgress;
    return progress.fileHash === fileHash ? progress : null;
  } catch (error) {
    console.error('获取上传进度失败:', error);
    return null;
  }
};

/**
 * 清除指定文件的上传进度
 * @param fileHash 文件哈希值
 */
export const clearUploadProgress = (fileHash: string): void => {
  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (stored) {
      const progress = JSON.parse(stored) as UploadProgress;
      if (progress.fileHash === fileHash) {
        localStorage.removeItem(STORAGE_KEY);
      }
    }
  } catch (error) {
    console.error('清除上传进度失败:', error);
  }
}; 