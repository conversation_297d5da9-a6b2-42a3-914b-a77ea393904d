export interface FileItem {
  name: string;
  path: string;
  directory: boolean;
  size?: number;
  lastModified?: string;
}

export interface FileListParams {
  path: string;
}

export interface FileListResponse {
  code: number;
  msg: string;
  data: FileItem[];
}

export interface UploadFileResponse {
  code: number;
  msg: string;
  data: string;
}

export interface PathHistory {
  name: string;
  path: string;
} 

// 分片上传相关接口
export interface ChunkInfo {
  chunk: Blob;
  hash: string;
  index: number;
  percentage: number;
}

export interface FileStatus {
  uploading: boolean;
  fileName: string;
  fileSize: number;
  chunks: ChunkInfo[];
  progress: number;
}

export interface FileStatusResponse {
  shouldUpload: boolean;
  uploadedChunks: number[];
}

export interface UploadProgress {
  fileHash: string;
  fileName: string;
  chunkProgress: { [key: number]: number };
  uploadedChunks: number[];
}

// 上传状态类型
export enum UploadState {
  PENDING = 'pending',
  UPLOADING = 'uploading',
  PAUSED = 'paused',
  SUCCESS = 'success',
  ERROR = 'error',
  CANCELLED = 'cancelled'
}

// 上传错误类型
export interface UploadError {
  code: string;
  message: string;
  details?: any;
}

// 分片上传配置
export interface ChunkUploadConfig {
  chunkSize: number;  // 分片大小（字节）
  retryCount: number; // 重试次数
  retryDelay: number; // 重试延迟（毫秒）
  concurrent: boolean; // 是否并发上传
  maxConcurrent?: number; // 最大并发数
}

// 默认配置
export const DEFAULT_CHUNK_CONFIG: ChunkUploadConfig = {
  chunkSize: 1 * 1024 * 1024, // 1MB
  retryCount: 3,
  retryDelay: 2000, // 2秒
  concurrent: false, // 默认串行上传
  maxConcurrent: 2 // 最大并发数（当concurrent为true时有效）
};

// 文件上传请求和响应
export interface UploadChunkRequest {
  fileHash: string;
  chunkIndex: number;
  totalChunks: number;
  chunk: Blob;
}

export interface UploadChunkResponse {
  code: number;
  msg: string;
  data?: any;
}

export interface MergeChunksRequest {
  fileHash: string;
  fileName: string;
  folder: string;
  totalChunks: number;
}

export interface MergeChunksResponse {
  code: number;
  msg: string;
  data?: any;
} 