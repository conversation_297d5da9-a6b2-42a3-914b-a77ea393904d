import React, { useState, useEffect } from "react";
import {
  Modal,
  Form,
  Input,
  Select,
  DatePicker,
  message,
  Row,
  Col,
  Typography,
} from "antd";
import { ExclamationCircleOutlined } from "@ant-design/icons";
import type { SqlQueryData } from "../data.d";
import moment from "moment";
import styles from "../style.less";

const { TextArea } = Input;
const { Option } = Select;
const { Text } = Typography;

interface EditModalProps {
  visible: boolean;
  onCancel: () => void;
  onOk: (values: any) => Promise<void>;
  editingRecord: SqlQueryData | null;
  confirmLoading: boolean; // 添加确认加载状态属性
}

const EditModal: React.FC<EditModalProps> = ({
  visible,
  onCancel,
  onOk,
  editingRecord,
  confirmLoading, // 接收确认加载状态
}) => {
  const [form] = Form.useForm();
  const [sqlError, setSqlError] = useState<string | null>(null);
  const [sqlContent, setSqlContent] = useState<string>("");

  // 分片值选项
  const shardIdOptions = [
    "0001",
    "0002",
    "0003",
    "0004",
    "0005",
    "0006",
    "0007",
    "0008",
  ];

  // SQL启用标识选项
  const isUsedOptions = [
    { label: "0-未启用", value: "0" },
    { label: "1-已启用", value: "1" },
  ];

  useEffect(() => {
    if (visible && editingRecord) {
      form.setFieldsValue({
        ...editingRecord,
        updateTime: editingRecord.updateTime
          ? moment(editingRecord.updateTime)
          : null,
      });
      // 初始化SQL内容状态
      setSqlContent(editingRecord.sqlContent || "");
      // 初始校验SQL
      validateSql(editingRecord.sqlContent || "");
    }
  }, [visible, editingRecord, form]);

  // SQL格式校验
  const validateSql = (sql: string): boolean => {
    if (!sql) return true;

    // 简单SQL语法检查
    const sqlLower = sql.toLowerCase();

    // 检查基本SQL关键字
    const hasSelect = sqlLower.includes("select");
    const hasFrom = sqlLower.includes("from");
    const hasSet = sqlLower.includes("set");
    const hasWhere = sqlLower.includes("where");
    const hasInto = sqlLower.includes("into");
    const hasValues = sqlLower.includes("values");
    const hasInsert = sqlLower.includes("insert");
    const hasUpdate = sqlLower.includes("update");
    const hasDelete = sqlLower.includes("delete");

    // 检查SQL语句结构
    if (hasSelect && !hasFrom) {
      setSqlError("SELECT语句缺少FROM子句");
      return false;
    }
    if (hasUpdate) {
      if (!hasSet) {
        setSqlError("UPDATE语句缺少SET子句");
        return false;
      }
      if (!hasWhere) {
        setSqlError("UPDATE语句缺少WHERE子句");
        return false;
      }
    }
    if (hasDelete) {
      if (!hasFrom) {
        setSqlError("DELETE语句缺少FROM子句");
        return false;
      }
      if (!hasWhere) {
        setSqlError("DELETE语句缺少WHERE子句");
        return false;
      }
    }
    if (hasInsert) {
      if (!hasInto) {
        setSqlError("INSERT语句缺少INTO子句");
        return false;
      }
      if (!hasValues) {
        setSqlError("INSERT语句缺少VALUES子句");
        return false;
      }
    }

    // 检查括号是否匹配
    const openParenCount = (sql.match(/\(/g) || []).length;
    const closeParenCount = (sql.match(/\)/g) || []).length;
    if (openParenCount !== closeParenCount) {
      setSqlError("SQL语句中的括号不匹配");
      return false;
    }

    // 检查引号是否匹配
    const singleQuoteCount = (sql.match(/'/g) || []).length;
    if (singleQuoteCount % 2 !== 0) {
      setSqlError("SQL语句中的单引号不匹配");
      return false;
    }

    // 检查分号结尾
    if (!sqlLower.trim().endsWith(";")) {
      setSqlError("SQL语句应以分号结尾");
      return false;
    }
    setSqlError(null);
    return true;
  };

  // 处理SQL内容变化
  const handleSqlChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    let value = e.target.value;

    // 自动替换中文逗号和引号
    value = value
      .replace(/，/g, ",")
      .replace(/（/g, "(")
      .replace(/）/g, ")")
      .replace(/“/g, '"')
      .replace(/”/g, '"')
      .replace(/‘/g, "'")
      .replace(/’/g, "'")
      .replace(/；/g, ";");

    setSqlContent(value);
    validateSql(value);
    form.setFieldsValue({ sqlContent: value });
  };

  const handleOk = async () => {
    try {
      const values = await form.validateFields();

      // 提交前再次验证SQL
      if (!validateSql(values.sqlContent)) {
        message.error("SQL格式有误，请修正后再提交！");
        return;
      }

      // 处理日期格式
      if (values.updateTime) {
        values.updateTime = values.updateTime.format("YYYY-MM-DD HH:mm:ss");
      }
      await onOk(values);
    //   form.resetFields();
    //   setSqlContent("");
    //   setSqlError(null);
    } catch (error) {
      message.error("表单验证失败，请检查输入");
    }
  };

  const handleCancel = () => {
    form.resetFields();
    setSqlContent("");
    setSqlError(null);
    onCancel();
  };

  return (
    <Modal
      title="编辑SQL信息"
      visible={visible}
      onOk={handleOk}
      onCancel={handleCancel}
      width={800}
      destroyOnClose
      className={styles.editModal}
      bodyStyle={{
        maxHeight: "calc(90vh - 200px)",
        overflowY: "auto",
        padding: "24px"
      }}
      centered
      okButtonProps={{ disabled: !!sqlError }}
      confirmLoading={confirmLoading}
    >
      <Form form={form} layout="vertical" className={styles.editModalForm}>
        <Form.Item name="id" label="序号" hidden>
          <Input disabled />
        </Form.Item>

        <Form.Item
          name="sqlContent"
          label="自动化备份SQL语句"
          rules={[{ required: true, message: "请输入自动化备份SQL语句" }]}
          help={
            sqlError && (
              <Text type="danger">
                <ExclamationCircleOutlined /> {sqlError}
              </Text>
            )
          }
          validateStatus={sqlError ? "error" : undefined}
        >
          <div className={styles.sqlInputWrapper}>
            <TextArea
              rows={6}
              placeholder="请在此输入完整的SQL语句"
              className={`${styles.textArea} ${styles.sqlTextArea}`}
              value={sqlContent}
              onChange={handleSqlChange}
              autoSize={{ minRows: 6, maxRows: 12 }}
            />
          </div>
        </Form.Item>

        <Form.Item
          name="remark"
          label="备注"
          rules={[{ required: true, message: "请输入备注" }]}
        >
          <TextArea
            rows={2}
            placeholder="请输入备注"
            className={styles.textArea}
          />
        </Form.Item>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="shardId"
              label="分片值"
              rules={[{ required: true, message: "请选择分片值" }]}
            >
              <Select
                placeholder="请选择分片值"
                className={styles.roundedSelect}
              >
                {shardIdOptions.map((option) => (
                  <Option key={option} value={option}>
                    {option}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="isUsed"
              label="启用标识"
              rules={[{ required: true, message: "请选择启用标识" }]}
            >
              <Select
                placeholder="请选择启用标识"
                className={styles.roundedSelect}
              >
                {isUsedOptions.map((option) => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Modal>
  );
};

export default EditModal;
