import request from '@/utils/request';
import type { SqlQueryParams, SqlQueryData } from './data.d';

// 查询SQL列表
export async function sqlQueryList(params: Partial<SqlQueryParams>) {
  // 设置默认值
  const defaultParams: SqlQueryParams = {
    id: '',
    remark: '',
    uploadBy: '',
    updateTimeStart: '',
    updateTimeEnd: '',
    current: 1,
    pageSize: 10,
    ...params  // 使用传入的参数覆盖默认值
  };
  
  // 将所有参数转换为字符串类型
  const requestBody: Record<string, string> = {};
  Object.keys(defaultParams).forEach(key => {
    let value = defaultParams[key as keyof SqlQueryParams];
    
    if (value !== undefined && value !== null) {
      requestBody[key] = value.toString();
    } else {
      requestBody[key] = '';
    }
  });
  
  // 添加日志，帮助调试
  console.log('发送请求体:', requestBody);
  
  try {
    const response = await request('/common/dataProcess/selectSql/list', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json;charset=UTF-8',
      },
      data: requestBody,
    });
    
    console.log('请求响应:', response);
    return response;
  } catch (error) {
    console.error('请求错误:', error);
    throw error;
  }
}

// 查询SQL详情
export async function sqlQueryDetail(params: { id: string | number }) {
  return request(`/common/dataProcess/selectSql/${params.id}`, {
    method: 'GET',
    params,
    headers: {
        'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

// 更新SQL
export async function updateSqlQuery(data: SqlQueryData) {
  return request('/common/dataProcess/selectSql/update', {
    method: 'POST',
    data,
  });
}

// 删除SQL
export async function deleteSqlQuery(params: { id: string | number }) {
  return request(`/common/dataProcess/selectSql/delete/${params.id}`, {
    method: 'DELETE',
    data: params,
    headers: {
        'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}