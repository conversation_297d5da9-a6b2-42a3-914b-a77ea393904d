export interface SqlQueryData {
  id: string;
  sqlContent: string;
  remark: string;
  shardId: string;
  isUsed: string;
  uploadBy: string;
  updateTime: string;
  isPerson: string;
}

export interface SqlQueryParams {
  id?: string;
  remark?: string;
  uploadBy?: string;
  updateTimeStart?: string;  // 确保这个字段名称与后端API一致
  updateTimeEnd?: string;    // 确保这个字段名称与后端API一致
  current?: number;
  pageSize?: number;
  [key: string]: any;  // 添加索引签名，允许额外的属性
}