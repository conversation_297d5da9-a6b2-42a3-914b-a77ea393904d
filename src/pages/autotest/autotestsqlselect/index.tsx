import React, { useState, useRef, useEffect } from "react";
import { But<PERSON>, message, Row, Col, Popconfirm, Card, Typography } from 'antd';
import {
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
  ReloadOutlined,
} from "@ant-design/icons";
import { useIntl, FormattedMessage } from "umi";
import type { ProColumns, ActionType } from "@ant-design/pro-table";
import ProTable from "@ant-design/pro-table";
import type { FormInstance } from "antd";
import WrapContent from "@/components/WrapContent";
import { KeepAlive } from "react-activation";
import { storage } from "@/utils/storageUtils";
import type { SqlQueryData } from "./data.d";
import {
  sqlQueryList,
  sqlQueryDetail,
  updateSqlQuery,
  deleteSqlQuery,
} from "./service";
import EditModal from "./components/EditModal";
import moment from "moment";
import styles from "./style.less";
const { Title } = Typography;

interface FormValueType {
  remark?: string;
  uploadBy?: string;
  updateTime?: [moment.Moment, moment.Moment] | null; // 修改为 Moment 对象数组
  updateTimeStart?: string;
  updateTimeEnd?: string;
}

const AutoTestSqlSelect: React.FC = () => {
  const formTableRef = useRef<FormInstance>();
  const actionRef = useRef<ActionType>();
  const [formValues, setFormValues] = useState<FormValueType>({});
  const [tableData, setTableData] = useState<SqlQueryData[]>([]);
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const [isFirstRequest, setIsFirstRequest] = useState(true);

  // 编辑模态框相关状态
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [editingRecord, setEditingRecord] = useState<SqlQueryData | null>(null);
  const [confirmLoading, setConfirmLoading] = useState(false); // 添加确认加载状态

  const intl = useIntl();

  // 搜索表单状态初始化
  const [searchForm, setSearchForm] = useState(() => {
    const savedSearch = storage.get("sqlquery_search");
    return savedSearch || {};
  });

  // 加载缓存数据的函数
  const loadCacheData = () => {
    try {
      const savedData = storage.get("sqlquery_data");
      const savedSearch = storage.get("sqlquery_search");

      if (savedData?.records && savedSearch) {
        const records = Array.isArray(savedData.records)
          ? savedData.records
          : [];

        // 处理日期回显
        const formValues = { ...savedSearch };

        // 如果有日期范围，转换为 moment 对象
        if (savedSearch.updateTimeStart && savedSearch.updateTimeEnd) {
          formValues.updateTime = [
            moment(savedSearch.updateTimeStart, "YYYYMMDD"),
            moment(savedSearch.updateTimeEnd, "YYYYMMDD"),
          ];
        }

        // 设置表单值
        setFormValues(savedSearch);
        if (formTableRef.current) {
          formTableRef.current.setFieldsValue(savedSearch || {});
        }
        setTableData(records);
      }
    } catch (error) {
      console.error("加载缓存数据失败:", error);
    }
    setIsInitialLoad(false);
  };

  // 初始化数据
  useEffect(() => {
    loadCacheData();
    return () => {
      setIsFirstRequest(true);
      setIsInitialLoad(true);
    };
  }, []);

  // 处理重置
  const handleReset = (form: FormInstance | undefined) => {
    // 重置所有状态
    setFormValues({});
    setTableData([]);
    setSearchForm({});

    // 重置表单
    setTimeout(() => {
      form?.resetFields();
      formTableRef.current?.resetFields();

      // 清除本地存储
      storage.set("sqlquery_data", null, 0);
      storage.set("sqlquery_search", null, 0);
    }, 0);
  };

  // 处理编辑按钮点击
  const handleEditClick = async (record: SqlQueryData) => {
    try {
      const res = await sqlQueryDetail({ id: record.id });
      if (res.code === 200) {
        const detailData =
          typeof res.data === "string" ? JSON.parse(res.data) : res.data;
        setEditingRecord(detailData);
        setEditModalVisible(true);
      }
    } catch (error) {
      message.error("获取详情失败");
    }
  };

  // 处理删除按钮点击
  const handleDeleteClick = async (id: string) => {
    try {
      const res = await deleteSqlQuery({ id });
      if (res.code === 200) {
        message.success("删除成功");
        actionRef.current?.reload();
      } else {
        message.error(res.msg || "删除失败");
      }
    } catch (error) {
      message.error("删除失败");
    }
  };

  // 处理模态框确认
  const handleEditModalOk = async (values: any) => {
    try {
      setConfirmLoading(true); // 设置加载状态为true

      const processedValues = {
        ...values,
        isUsed: values.isUsed?.split("-")[0] || values.isUsed,
      };
      const res = await updateSqlQuery(processedValues);
      if (res.code === 200) {
        message.success("更新成功");
        setEditModalVisible(false);
        setEditingRecord(null);
        actionRef.current?.reload();
      } else {
        message.error(res.msg || "更新失败");
      }
    } catch (error) {
      message.error("更新失败");
    } finally {
      setConfirmLoading(false); // 无论成功或失败，都设置加载状态为false
    }
  };

  // 处理模态框取消
  const handleEditModalCancel = () => {
    setEditModalVisible(false);
    setEditingRecord(null);
  };

  // 处理请求
  const handleRequest = async (params: any) => {
    const { current = 1, pageSize = 10, ...searchParams } = params;

    // 处理日期范围
    let updateTimeStart = "";
    let updateTimeEnd = "";

    // 检查日期参数的格式和存在性
    console.log("原始日期参数:", searchParams.updateTime);

    if (searchParams.updateTime) {
      // 处理不同类型的日期输入
      if (
        Array.isArray(searchParams.updateTime) &&
        searchParams.updateTime.length === 2
      ) {
        // 数组形式的日期范围
        if (searchParams.updateTime[0] && searchParams.updateTime[1]) {
          updateTimeStart = moment(searchParams.updateTime[0]).format(
            "YYYYMMDD"
          );
          updateTimeEnd = moment(searchParams.updateTime[1]).format("YYYYMMDD");
        }
      } else if (typeof searchParams.updateTime === "string") {
        // 字符串形式的日期范围，尝试解析
        try {
          const dateRange = JSON.parse(searchParams.updateTime);
          if (Array.isArray(dateRange) && dateRange.length === 2) {
            updateTimeStart = moment(dateRange[0]).format("YYYYMMDD");
            updateTimeEnd = moment(dateRange[1]).format("YYYYMMDD");
          }
        } catch (e) {
          console.error("日期解析错误:", e);
        }
      }
    }

    const requestParams = {
      current: parseInt(current.toString()),
      pageSize: parseInt(pageSize.toString()),
      id: searchParams.id || "",
      remark: searchParams.remark || "",
      uploadBy: searchParams.uploadBy || "",
      updateTimeStart,
      updateTimeEnd,
    };

    // 添加日志，帮助调试
    console.log("请求参数:", requestParams);

    try {
      // 只有在非首次请求时才更新缓存
      if (!isFirstRequest) {
        setSearchForm({
          ...requestParams,
          current: parseInt(current),
          pageSize: parseInt(pageSize),
        });
        storage.set("sqlquery_search", requestParams, 30 * 60);
      }
      setIsFirstRequest(false);

      const res = await sqlQueryList(requestParams);
      // 添加日志，帮助调试
      console.log("响应结果:", res);

      if (res.code === 200) {
        const parsedData =
          typeof res.data === "string" ? JSON.parse(res.data) : res.data;
        const records = Array.isArray(parsedData.records)
          ? parsedData.records
          : [];
        const total = parsedData.total || 0;

        // 只有在非首次请求时才更新缓存
        if (!isFirstRequest) {
          storage.set("sqlquery_data", parsedData, 30 * 60);
        }
        setTableData(records);
        return {
          data: records,
          total: total,
          success: true,
        };
      } else {
        message.error(res.msg || "查询失败");
      }
      return {
        data: [],
        total: 0,
        success: false,
      };
    } catch (error) {
      console.error("查询失败:", error);
      return {
        data: [],
        total: 0,
        success: false,
      };
    }
  };

  const columns: ProColumns<SqlQueryData>[] = [
    {
      title: (
        <FormattedMessage id="autoTest.sqlquery.id" defaultMessage="序号" />
      ),
      dataIndex: "id",
      valueType: "text",
      fixed: "left",
      width: 80,
      align: "center",
      hideInSearch: false,
      fieldProps: {
        style: {
          width: "100%",
          borderRadius: "8px",
        },
        placeholder: "请输入序号",
      },
    },
    {
      title: (
        <FormattedMessage
          id="autoTest.sqlquery.uploadBy"
          defaultMessage="用户名"
        />
      ),
      dataIndex: "uploadBy",
      valueType: "text",
      hideInSearch: false,
      width: 120,
      align: "center",
      fieldProps: {
        style: {
          width: "100%",
          borderRadius: "8px",
        },
        placeholder: "请输入用户名",
      },
    },
    {
      title: (
        <FormattedMessage
          id="autoTest.sqlquery.sqlContent"
          defaultMessage="SQL语句"
        />
      ),
      dataIndex: "sqlContent",
      valueType: "textarea",
      hideInSearch: true,
      ellipsis: true,
      width: 300,
      align: "center",
    },
    {
      title: (
        <FormattedMessage id="autoTest.sqlquery.remark" defaultMessage="备注" />
      ),
      dataIndex: "remark",
      valueType: "textarea",
      hideInSearch: false,
      ellipsis: true,
      width: 180,
      align: "center",
      fieldProps: {
        style: {
          width: "100%",
          borderRadius: "8px",
        },
        placeholder: "请输入备注",
      },
    },
    {
      title: (
        <FormattedMessage
          id="autoTest.sqlquery.shardId"
          defaultMessage="分片值"
        />
      ),
      dataIndex: "shardId",
      valueType: "select",
      valueEnum: {
        "0001": "0001",
        "0002": "0002",
        "0003": "0003",
        "0004": "0004",
        "0005": "0005",
        "0006": "0006",
        "0007": "0007",
        "0008": "0008",
      },
      hideInSearch: true,
      width: 80,
      align: "center",
    },
    {
      title: (
        <FormattedMessage
          id="autoTest.sqlquery.isUsed"
          defaultMessage="启用标识"
        />
      ),
      dataIndex: "isUsed",
      valueType: "select",
      valueEnum: {
        "0": "0-未启用",
        "1": "1-已启用",
      },
      hideInSearch: true,
      width: 100,
      align: "center",
    },
    {
      title: (
        <FormattedMessage
          id="autoTest.sqlquery.updateTime"
          defaultMessage="更新时间"
        />
      ),
      dataIndex: "updateTime",
      valueType: "dateRange", // 使用dateRange类型
      hideInSearch: false,
      width: 160,
      align: "center",
      fieldProps: {
        style: { width: "100%", borderRadius: "8px" },
        placeholder: ["开始日期", "结束日期"],
        format: "YYYY-MM-DD",
        allowClear: true,
        onChange: (dates: moment.Moment[] | null, dateStrings: string[]) => {
          console.log("日期变更:", dates, dateStrings);
          if (dates && dates.length === 2) {
            // 可以在这里手动设置表单值，但通常 ProTable 会自动处理
            const updateTimeStart = dates[0].format("YYYYMMDD");
            const updateTimeEnd = dates[1].format("YYYYMMDD");
            console.log("转换后的日期:", updateTimeStart, updateTimeEnd);
          }
        },
        ranges: {
          今天: [moment().startOf("day"), moment().endOf("day")],
          本周: [moment().startOf("week"), moment().endOf("week")],
          本月: [moment().startOf("month"), moment().endOf("month")],
        },
      },
      // 添加转换函数，处理表格显示
      render: (_, record) => {
        // 格式化表格中显示的日期
        return record.updateTime
          ? moment(record.updateTime).format("YYYY-MM-DD HH:mm:ss")
          : "-";
      },
    },
    {
      title: (
        <FormattedMessage
          id="autoTest.sqlquery.operation"
          defaultMessage="操作"
        />
      ),
      dataIndex: "option",
      valueType: "option",
      fixed: "right", // 添加这一行，固定在右侧
      width: 120,
      align: "center",
      render: (_, record) => [
        <Button
          type="link"
          size="small"
          key="edit"
          icon={<EditOutlined />}
          disabled={record.isPerson === "0"}
          onClick={() => handleEditClick(record)}
        >
          编辑
        </Button>,
        <Popconfirm
          key="delete"
          title="确定要删除这条记录吗？"
          onConfirm={() => handleDeleteClick(record.id)}
          okText="确定"
          cancelText="取消"
          disabled={record.isPerson === "0"}
        >
          <Button
            type="link"
            size="small"
            danger
            icon={<DeleteOutlined />}
            disabled={record.isPerson === "0"}
          >
            删除
          </Button>
        </Popconfirm>,
      ],
    },
  ];

  // 修改 index.tsx 文件中的 Card 组件使用方式
  return (
    <KeepAlive
      saveScrollPosition={false}
      when={() => true}
      cacheKey="sqlquery"
      onActivate={() => {
        setIsFirstRequest(true);
        requestAnimationFrame(() => {
          loadCacheData();
        });
      }}
    >
      <WrapContent>
        <div className={styles.container}>
          <Card
            title={
              <div className={styles.titleContainer}>
                <Title level={4}>
                  <FormattedMessage
                    id="pages.searchTable.autoTestSearchTitle"
                    defaultMessage="自动化SQL查询表格"
                  />
                </Title>
              </div>
            }
            bordered={false}
            className={styles.card}
            style={{ borderRadius: '16px', overflow: 'hidden' }}
          >
            <div style={{ borderRadius: '16px', overflow: 'hidden' }}>
              <Row gutter={[16, 24]}>
                <Col lg={24} md={24}>
                  <ProTable<SqlQueryData>
                    headerTitle={false}
                    actionRef={actionRef}
                    formRef={formTableRef}
                    rowKey="id"
                    key="sqlQueryList"
                    form={{
                      ignoreRules: false,
                      onReset: () => {
                        handleReset(formTableRef.current);
                      },
                      onValuesChange: (_, values: FormValueType) => {
                        setFormValues(values);
                      },
                      initialValues: searchForm,
                    }}
                    search={{
                      labelWidth: 100,
                      searchGutter: 16, // 控制搜索项之间的间距
                      span: {
                        xs: 24,  // 超小屏幕一行一个
                        sm: 24,  // 小屏幕一行一个
                        md: 12,  // 中等屏幕一行两个
                        lg: 8,   // 大屏幕一行三个
                        xl: 6,   // 超大屏幕一行四个
                        xxl: 6   // 特大屏幕一行四个
                      },
                      defaultCollapsed: false,
                      optionRender: ({ searchText, form }) => [
                        <Button
                          type="primary"
                          key="search"
                          style={{ borderRadius: "8px", marginRight: 8 }}
                          icon={<SearchOutlined />}
                          onClick={() => {
                            setIsFirstRequest(false);
                            const formValues = form?.getFieldsValue();
                            console.log("表单提交值:", formValues);

                            if (
                              formValues?.updateTime &&
                              Array.isArray(formValues.updateTime) &&
                              formValues.updateTime.length === 2
                            ) {
                              formValues.updateTimeStart =
                                formValues.updateTime[0].format("YYYYMMDD");
                              formValues.updateTimeEnd =
                                formValues.updateTime[1].format("YYYYMMDD");
                            }

                            form?.submit();
                          }}
                        >
                          {searchText}
                        </Button>,
                        <Button
                          key="reset"
                          style={{ borderRadius: "8px" }}
                          icon={<ReloadOutlined />}
                          onClick={() => {
                            handleReset(form);
                            form?.setFieldsValue({});
                          }}
                        >
                          重置
                        </Button>,
                      ],
                    }}
                    pagination={{
                      defaultPageSize: 10,
                      showSizeChanger: true,
                      showQuickJumper: true,
                      current:
                        parseInt(storage.get("sqlquery_search")?.current) || 1,
                      pageSize:
                        parseInt(storage.get("sqlquery_search")?.pageSize) || 10,
                      onChange: (page, pageSize) => {
                        const currentSearchParams =
                          formTableRef.current?.getFieldsValue();
                        const params = {
                          ...currentSearchParams,
                          current: page,
                          pageSize: pageSize,
                        };
                        handleRequest(params).then((result) => {
                          if (result.success) {
                            storage.set(
                              "sqlquery_search",
                              {
                                ...storage.get("sqlquery_search"),
                                current: page,
                                pageSize: pageSize,
                              },
                              30 * 60
                            );
                          }
                        });
                      },
                    }}
                    request={async (params) => {
                      console.log("ProTable请求参数:", params);

                      if (isFirstRequest) {
                        const savedData = storage.get("sqlquery_data");
                        setIsFirstRequest(false);
                        return {
                          data: savedData?.records || [],
                          success: true,
                          total: savedData?.total || 0,
                          current:
                            parseInt(storage.get("sqlquery_search")?.current) ||
                            1,
                        };
                      }

                      try {
                        const result = await handleRequest(params);
                        console.log("请求结果:", result);
                        return result;
                      } catch (error) {
                        console.error("请求处理错误:", error);
                        return {
                          data: [],
                          success: false,
                          total: 0,
                        };
                      }
                    }}
                    dataSource={tableData}
                    scroll={{ x: 1500 }}
                    columnEmptyText="-"
                    columns={columns}
                  />
                </Col>
              </Row>
            </div>
          </Card>
        </div>
        <EditModal
          visible={editModalVisible}
          onCancel={handleEditModalCancel}
          onOk={handleEditModalOk}
          editingRecord={editingRecord}
          confirmLoading={confirmLoading}
        />
      </WrapContent>
    </KeepAlive>
  );
};

export default AutoTestSqlSelect;
