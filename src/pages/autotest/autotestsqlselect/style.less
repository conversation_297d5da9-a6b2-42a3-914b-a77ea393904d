@import '~antd/es/style/themes/default.less';

// 添加容器动画样式
.container {
  padding: 20px;
  animation: fadeInUp 0.5s ease-out;
}

// 添加卡片样式
.card {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border-radius: 16px !important; // 增加 !important 提高优先级
  transition: all 0.3s;
  overflow: hidden;

  &:hover {
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
  }

  // 确保Card内部元素也有圆角
  :global {
    .ant-card-body {
      padding: 24px;
      border-radius: 16px !important; // 增加 !important 提高优先级
    }

    .ant-card-head {
      border-bottom: 1px solid #f0f0f0;
      border-radius: 16px 16px 0 0 !important; // 增加 !important 提高优先级
    }

    // 添加卡片内容区域的圆角保护
    .ant-card-bordered {
      border-radius: 16px !important;
    }

    // 确保卡片容器也有圆角
    .ant-card {
      border-radius: 16px !important;
    }
  }
}

// 标题容器样式
.titleContainer {
  animation: slideInLeft 0.5s ease-out;
}

// 表单项动画
.formItem {
  animation: fadeIn 0.5s ease-out;
  margin-bottom: 16px;
}

// 按钮容器动画
.buttonContainer {
  display: flex;
  justify-content: center;
  margin-top: 24px;
  animation: fadeInUp 0.5s ease-out;
}

// 添加动画关键帧
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

// 编辑模态框样式
.editModal {
  :global {
    .ant-modal-content {
      border-radius: 8px;
      overflow: hidden;
    }

    .ant-modal-header {
      background-color: #f5f5f5;
      padding: 16px 24px;
    }

    .ant-modal-body {
      padding: 24px;
      // 添加滚动条样式
      &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
      }

      &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;
      }

      &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
      }
    }

    .ant-modal-footer {
      border-top: 1px solid #f0f0f0;
      padding: 10px 24px;
    }

    // 确保模态框垂直居中
    .ant-modal {
      top: 50%;
      transform: translateY(-50%);
      margin: 0 auto;
      padding-bottom: 0;
    }
  }
}

.editModalForm {
  :global {
    .ant-select-selector {
      border-radius: 8px !important;
    }

    .ant-select-dropdown {
      border-radius: 8px !important;
    }

    .ant-input {
      border-radius: 8px !important;
    }

    .ant-picker {
      border-radius: 8px !important;
    }

    // 减少表单项间距
    .ant-form-item {
      margin-bottom: 16px;
    }

    // 减少表单项标签和控件的间距
    .ant-form-item-label > label {
      height: 28px;
    }
  }

  .sqlInputWrapper {
    width: 100%;
    min-height: 120px; // 设置最小高度
    position: relative;
  }

  .textArea {
    width: 100%;
    resize: vertical; // 允许垂直方向调整大小
    min-height: 120px; // 设置最小高度
    font-family: 'Courier New', monospace; // 使用等宽字体
    line-height: 1.5;
    padding: 8px 12px;
    border-radius: 6px;

    &.sqlTextArea {
      font-size: 14px;
      background-color: #fafafa;
      border: 1px solid #d9d9d9;

      &:hover {
        border-color: #40a9ff;
      }

      &:focus {
        border-color: #40a9ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        outline: none;
      }
    }
  }
}

.roundedSelect {
  width: 100%;
  border-radius: 6px;
}

// 文本区域样式
.textArea {
  border-radius: 8px;
  resize: none;
  transition: all 0.3s;

  &:hover, &:focus {
    border-color: @primary-color;
    box-shadow: 0 0 0 2px fade(@primary-color, 20%);
  }
}

// 圆角选择器样式
.roundedSelect {
  :global {
    .ant-select-selector {
      border-radius: 8px !important;
    }

    // 确保清除图标正常显示和工作
    .ant-select-clear {
      opacity: 0.3;

      &:hover {
        opacity: 1;
      }
    }
  }
}

:global {
  // 表格样式优化
  .ant-pro-table {
    .ant-pro-table-search {
      margin-bottom: 16px;
      padding: 24px 24px 0;
      background-color: #fff;
      border-radius: 12px !important; // 增加 !important 提高优先级
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
    }

    .ant-card {
      border-radius: 12px !important; // 增加 !important 提高优先级
      overflow: hidden;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
    }

    .ant-table-container {
      border-radius: 12px !important; // 增加 !important 提高优先级
      overflow: hidden;
    }

    // 添加表格包装器的圆角
    .ant-table-wrapper {
      border-radius: 12px !important;
      overflow: hidden;
    }

    // 添加表格本身的圆角
    .ant-table {
      border-radius: 12px !important;
      overflow: hidden;
    }

    .ant-table-thead > tr > th {
      background-color: #fafafa;
    }

    .ant-pagination {
      margin: 16px 0;
    }

    .ant-form-item-control-input {
      min-height: 32px;
    }

    .ant-form-item {
      margin-bottom: 24px;
    }
  }

  // 按钮样式
  .ant-btn {
    border-radius: 4px;

    &.ant-btn-primary {
      background-color: @primary-color;
      border-color: @primary-color;

      &:hover, &:focus {
        background-color: lighten(@primary-color, 10%);
        border-color: lighten(@primary-color, 10%);
      }
    }
  }

  // 表单项样式
  .ant-form-item-label > label {
    color: rgba(0, 0, 0, 0.85);
    font-weight: 500;
  }

  // 搜索表单样式
  .ant-pro-table-search {
    .ant-form-item {
      margin-bottom: 16px !important;
    }

    .ant-form-item-label {
      padding-bottom: 8px !important;
    }
  }
}

// SQL输入框特殊样式
.sqlTextArea {
  font-style: italic;
  color: #1890ff; // 使用蓝色作为SQL文本颜色
  font-family: 'Consolas', 'Monaco', monospace; // 使用等宽字体更适合代码显示

  &::placeholder {
    font-style: normal;
    color: rgba(0, 0, 0, 0.25);
  }
}

.sqlInputWrapper {
  position: relative;
}

.textArea {
  border-radius: 8px;
  resize: none;
  transition: all 0.3s;

  &:hover, &:focus {
    border-color: @primary-color;
    box-shadow: 0 0 0 2px fade(@primary-color, 20%);
  }
}