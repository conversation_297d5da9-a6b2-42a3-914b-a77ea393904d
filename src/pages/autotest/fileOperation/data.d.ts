// 修改 FileInfo 接口以匹配后端返回的数据结构
export interface FileInfo {
  fileName: string;
  filePath: string;
  size: number; // 前端使用，对应后端的 size
  lastModified: string; // 前端使用，对应后端的 lastModified
}

// 添加后端实际返回的数据结构接口
export interface FileInfoResponse {
  fileName: string;
  filePath: string;
  size: number;
  lastModified: string;
}

export interface FileQueryParams {
  fileName?: string;
  startDate?: string;
  endDate?: string;
  pageSize?: number;
  current?: number;
}

// 修改为与后端返回结构匹配的接口
export interface FileQueryResponse {
  code: number;
  msg: string;
  data: string | FileInfoResponse[]; // 可能是字符串或对象数组
}