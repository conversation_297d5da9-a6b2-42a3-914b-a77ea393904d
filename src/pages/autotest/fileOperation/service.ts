import request from "@/utils/request";
import type { FileQueryParams, FileQueryResponse } from "./data";

// 查询文件列表
export async function getFileList(params?: FileQueryParams) {
  try {
    const response = await request<FileQueryResponse>("/file/upload/list", {
      method: "GET",
      params,
      headers: {
        "Content-Type": "application/json;charset=UTF-8",
      },
    });

    // 处理返回的数据
    if (response && response.code === 200) {
      let dataArray;

      // 如果data是字符串，尝试解析JSON
      if (typeof response.data === "string") {
        try {
          // 解析JSON字符串
          dataArray = JSON.parse(response.data);
          console.log("解析后的数据结构:", dataArray);

          // 处理双重嵌套的数组 [[...]]
          if (Array.isArray(dataArray) && dataArray.length > 0) {
            if (Array.isArray(dataArray[0])) {
              dataArray = dataArray[0];
            }
          }
        } catch (e) {
          console.error("解析JSON字符串失败:", e);
          dataArray = [];
        }
      }
      // 如果已经是数组
      else if (Array.isArray(response.data)) {
        dataArray = response.data;
      }

      // 确保dataArray是数组后再处理
      if (Array.isArray(dataArray)) {
        // 打印第一个数据项，帮助调试
        if (dataArray.length > 0) {
          console.log("数据项示例:", dataArray[0]);
        }

        const formattedData = dataArray.map((item: any) => ({
          fileName: item.fileName,
          filePath: item.filePath,
          size: item.size || 0, // 使用size字段映射到fileSize
          lastModified: item.lastModified, // 使用lastModified字段映射到uploadTime
        }));

        console.log("格式化后的数据:", formattedData);

        return {
          code: response.code,
          msg: response.msg,
          data: formattedData,
        };
      }
    }

    return response;
  } catch (error) {
    console.error("API请求错误:", error);
    throw error;
  }
}

// 删除文件
export async function deleteFile(fileName: string) {
  return request("/file/upload/delete", {
    method: "DELETE",
    params: { fileName },
    headers: {
      "Content-Type": "application/json;charset=UTF-8",
    },
  });
}

// 修改下载文件方法
// export async function downloadFile(fileName: string) {
//   try {
//     // 创建下载链接
//     const downloadUrl = `/testtool/upload/download?fileName=${encodeURIComponent(fileName)}`;

//     // 使用fetch获取文件内容
//     const response = await fetch(downloadUrl);

//     // 检查响应状态
//     if (!response.ok) {
//       throw new Error(`下载失败: ${response.status} ${response.statusText}`);
//     }

//     // 获取文件名
//     let downloadFileName = fileName;
//     const contentDisposition = response.headers.get('content-disposition');
//     if (contentDisposition) {
//       const fileNameMatch = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/.exec(contentDisposition);
//       if (fileNameMatch && fileNameMatch[1]) {
//         downloadFileName = fileNameMatch[1].replace(/['"]/g, '');
//       }
//     }

//     // 将响应转换为blob
//     const blob = await response.blob();

//     // 创建下载链接
//     const url = window.URL.createObjectURL(blob);
//     const link = document.createElement('a');
//     link.href = url;
//     link.setAttribute('download', downloadFileName);

//     // 触发下载
//     document.body.appendChild(link);
//     link.click();

//     // 清理
//     setTimeout(() => {
//       document.body.removeChild(link);
//       window.URL.revokeObjectURL(url);
//     }, 100);

//     return { code: 200, msg: '下载成功' };
//   } catch (error) {
//     console.error('下载文件失败:', error);
//     return { code: 500, msg: '下载失败' };
//   }
// }

// 下载文件方法
export async function downloadFile(fileName: string) {
  try {
    // 使用项目的request工具，设置responseType为blob
    const res = await request(`/file/upload/download`, {
      method: "GET",
      params: { fileName },
      responseType: "blob",
      getResponse: true,
    });

    // 检查响应
    if (!res || !res.data) {
      throw new Error("下载失败: 未收到有效响应");
    }

    // 获取文件名
    let downloadFileName = fileName;
    const contentDisposition = res.response.headers.get("content-disposition");
    if (contentDisposition) {
      // const fileNameMatch = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/.exec(
      //   contentDisposition,
      // );
      // if (fileNameMatch && fileNameMatch[1]) {
      //   downloadFileName = fileNameMatch[1].replace(/['"]/g, "");
      // }

      // 先尝试获取filename*=utf-8''格式的文件名（RFC 5987）
      const filenameStarMatch = /filename\*=([^']*)'[^']*'([^;]*)/.exec(contentDisposition);
      if (filenameStarMatch && filenameStarMatch[2]) {
        // 对URL编码的文件名进行解码
        try {
          downloadFileName = decodeURIComponent(filenameStarMatch[2]);
        } catch (e) {
          console.error("解码文件名失败", e);
        }
      } else {
        // 回退到普通filename形式
        const filenameMatch = /filename=["]?([^"]*)["]?/.exec(contentDisposition);
        if (filenameMatch && filenameMatch[1]) {
          downloadFileName = filenameMatch[1];
          
          // 尝试对可能的URL编码进行解码
          try {
            downloadFileName = decodeURIComponent(downloadFileName);
          } catch (e) {
            // 解码失败则使用原始文件名
            console.error("解码普通文件名失败", e);
          }
        }
      }
    }

    // 创建Blob对象
    const blob = new Blob([res.data]);

    // 创建下载链接
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.setAttribute("download", downloadFileName);

    // 触发下载
    document.body.appendChild(link);
    link.click();

    // 清理
    setTimeout(() => {
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    }, 100);

    return { code: 200, msg: "下载成功" };
  } catch (error) {
    console.error("下载文件失败:", error);
    return { code: 500, msg: "下载失败" };
  }
}

// 执行测试1
export async function executeTest1(fileName: string) {
  return request("/file/upload/test1", {
    method: "POST",
    data: { fileName },
    headers: {
      "Content-Type": "application/json;charset=UTF-8",
    },
  });
}

// 执行测试2
export async function executeTest2(fileName: string) {
  return request("/file/upload/test2", {
    method: "POST",
    data: { fileName },
    headers: {
      "Content-Type": "application/json;charset=UTF-8",
    },
  });
}

// 执行测试3
export async function executeTest3(fileName: string) {
  return request("/file/upload/test3", {
    method: "POST",
    data: { fileName },
    headers: {
      "Content-Type": "application/json;charset=UTF-8",
    },
  });
}
