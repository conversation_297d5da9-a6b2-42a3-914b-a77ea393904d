@import '~antd/es/style/themes/default.less';

.app-container {
  padding: 24px;
  background-color: #fff;
  border-radius: 2px;

  // 初始状态整个容器不可见，但不使用opacity: 0
  &.fade-in-up {
    animation: fadeInUp 0.5s ease-out forwards;
  }

  .ant-pro-table {
    .ant-pro-table-search {
      margin-bottom: 16px;
      padding: 24px 24px 0;
      background: #fafafa;
      border-radius: 2px;

      // 修改输入框为外边框圆角，内边框直角
      .ant-form-item-control-input-content {
        .ant-input, .ant-picker {
          border-radius: 0 !important; // 内边框改为直角
        }

        .ant-input-affix-wrapper, .ant-picker {
          border-radius: 8px !important; // 外边框改为圆角
        }
      }

      // 修改按钮为圆角
      .ant-btn {
        border-radius: 12px !important;
      }
    }

    .ant-table-wrapper {
      background: #fff;

      .ant-table-cell {
        text-align: center !important;

        .ant-btn-link {
          padding: 4px 8px;
          transition: all 0.3s;

          &:hover {
            background: rgba(0, 0, 0, 0.025);
          }
        }
      }
    }
  }

  .file-detail-modal {
    .ant-modal-body {
      max-height: 500px;
      overflow-y: auto;

      pre {
        margin: 0;
        padding: 16px;
        background: #f5f5f5;
        border-radius: 2px;
      }
    }
  }
}

// 统一的动画定义（移除重复定义）
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.card-container {
  // 移除opacity: 0，避免初始隐藏后再显示
  transition: all 0.3s ease;

  &.fade-in-up {
    animation: fadeInUp 0.5s ease-out forwards;
  }

  // 添加以下代码移除任何可能的渐变边框
  border: none !important;

  // 移除可能用于创建渐变边框的伪元素效果
  &::before, &::after {
    display: none !important;
    content: none !important;
    background: none !important;
  }
}

.custom-table {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border-radius: 16px !important;
  height: fit-content;
  overflow: visible;
  background-color: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  position: relative;

  // 添加下载按钮样式
  .download-button {
    color: #1890ff;
    &:hover {
      color: #40a9ff;
      background: rgba(24, 144, 255, 0.1);
    }
  }
}

@media screen and (max-width: @screen-md) {
  .app-container {
    padding: 12px;

    .ant-pro-table-search {
      padding: 16px 16px 0;
    }
  }
}

// 如果渐变边框是在其他选择器中定义的，也需检查类似这样的选择器
.input-section, .custom-card, .animation-card {
  border: none !important;

  &::before, &::after {
    display: none !important;
    content: none !important;
    background: none !important;
  }
}