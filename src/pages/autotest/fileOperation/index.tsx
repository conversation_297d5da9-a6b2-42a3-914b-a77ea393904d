import React, { useRef, useState, useEffect } from 'react';
import { But<PERSON>, message, Modal, Tooltip, Dropdown, Menu } from 'antd';
import { DeleteOutlined, MoreOutlined, DownloadOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import type { FormInstance } from 'antd';
import WrapContent from '@/components/WrapContent';
import type { FileInfo, FileQueryParams } from './data';
import { getFileList, deleteFile, executeTest1, executeTest2, executeTest3, downloadFile } from './service';
import './style.less';

const FileOperation: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const formRef = useRef<FormInstance>();
  // 添加动画状态
  const [isVisible, setIsVisible] = useState(false);

  // 修改1: 移除setTimeout延迟
  useEffect(() => {
    // 立即设置可见性，避免延迟导致的跳动
    setIsVisible(true);
  }, []);

  // 处理文件大小显示
  const formatFileSize = (size: number) => {
    if (size < 1024) return `${size} B`;
    if (size < 1024 * 1024) return `${(size / 1024).toFixed(2)} KB`;
    if (size < 1024 * 1024 * 1024) return `${(size / 1024 / 1024).toFixed(2)} MB`;
    return `${(size / 1024 / 1024 / 1024).toFixed(2)} GB`;
  };

  // 日期格式化为YYYYMMDD
  const formatDateToYYYYMMDD = (dateString: string) => {
    if (!dateString) return undefined;
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}${month}${day}`;
  };

  // 处理下载文件的函数
  const handleDownload = async (fileName: string) => {
    try {
      const result = await downloadFile(fileName);
      if (result.code === 200) {
        message.success('开始下载文件');
      } else {
        message.error(result.msg || '文件下载失败');
      }
    } catch (error) {
      message.error('文件下载失败');
    }
  };

  // 处理删除文件
  const handleDelete = async (fileName: string) => {
    try {
      const res = await deleteFile(fileName);
      if (res.code === 200) {
        message.success('删除成功');
        actionRef.current?.reload();
      } else {
        message.error(res.msg || '删除失败');
      }
    } catch (error) {
      message.error('删除失败');
    }
  };

  // 处理测试1请求
  const handleTest1 = async (fileName: string) => {
    try {
      const result = await executeTest1(fileName);
      if (result.code === 200) {
        message.success('测试1执行成功');
      } else {
        message.error(result.msg || '测试1执行失败');
      }
    } catch (error) {
      message.error('测试1执行失败');
    }
  };

  // 处理测试2请求
  const handleTest2 = async (fileName: string) => {
    try {
      const result = await executeTest2(fileName);
      if (result.code === 200) {
        message.success('测试2执行成功');
      } else {
        message.error(result.msg || '测试2执行失败');
      }
    } catch (error) {
      message.error('测试2执行失败');
    }
  };

  // 处理测试3请求
  const handleTest3 = async (fileName: string) => {
    try {
      const result = await executeTest3(fileName);
      if (result.code === 200) {
        message.success('测试3执行成功');
      } else {
        message.error(result.msg || '测试3执行失败');
      }
    } catch (error) {
      message.error('测试3执行失败');
    }
  };

  const columns: ProColumns<FileInfo>[] = [
    {
      title: '文件名',
      dataIndex: 'fileName',
      ellipsis: true,
      valueType: 'text',
      align: 'center',
      width: 300,
      fieldProps: {
        style: {
          width: "280px",
          borderRadius: "8px",
        },
        placeholder: "请输入文件名，可模糊查询",
      },
      formItemProps: {
        rules: [
          {
            required: false,
            message: '请输入文件名',
          },
        ],
      },
    },
    {
      title: '文件路径',
      dataIndex: 'filePath',
      ellipsis: true,
      valueType: 'text',
      hideInSearch: true,
      align: 'center',
      width: 300,
    },
    {
      title: '文件大小',
      dataIndex: 'size',
      hideInSearch: true,
      align: 'center',
      width: 120,
      render: (_, record) => formatFileSize(record.size),
    },
    {
      title: '上传时间',
      dataIndex: 'lastModified',
      valueType: 'dateRange',
      align: 'center',
      width: 180,
      render: (_, record) => record.lastModified,
      search: {
        transform: (value) => {
          return {
            startDate: value[0],
            endDate: value[1],
          };
        },
      },
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      width: 180, // 增加宽度以容纳新按钮
      fixed: 'right',
      align: 'center',
      render: (_, record) => [
        <Dropdown
          key="more"
          overlay={
            <Menu>
              <Menu.Item key="test1" onClick={() => handleTest1(record.fileName)}>
                测试1
              </Menu.Item>
              <Menu.Item key="test2" onClick={() => handleTest2(record.fileName)}>
                测试2
              </Menu.Item>
              <Menu.Item key="test3" onClick={() => handleTest3(record.fileName)}>
                测试3
              </Menu.Item>
            </Menu>
          }
          placement="bottomRight"
        >
          <Button type="link" icon={<MoreOutlined />}>
            更多
          </Button>
        </Dropdown>,
        <Tooltip title="下载" key="download">
          <Button
            type="link"
            icon={<DownloadOutlined />}
            onClick={() => handleDownload(record.fileName)}
          />
        </Tooltip>,
        <Tooltip title="删除" key="delete">
          <Button
            type="link"
            danger
            icon={<DeleteOutlined />}
            onClick={() => {
              Modal.confirm({
                title: '确认删除',
                content: `是否确认删除文件 ${record.fileName}？`,
                onOk: () => handleDelete(record.fileName),
              });
            }}
          />
        </Tooltip>,
      ],
    },
  ];

  return (
    <WrapContent>
      <div className={`app-container ${isVisible ? 'fade-in-up' : ''}`} style={{ width: '100%' }}>
        <div className={`card-container ${isVisible ? 'fade-in-up' : ''}`}>
          <ProTable<FileInfo>
            headerTitle="文件查询"
            actionRef={actionRef}
            formRef={formRef}
            rowKey="filePath"
            search={{
              labelWidth: 120,
            }}
            // 取消自动加载数据，改为手动触发
            manualRequest={true}
            request={async (params) => {
              const { current, pageSize, fileName, startDate, endDate } = params;

              // 转换日期格式为YYYYMMDD
              const formattedStartDate = formatDateToYYYYMMDD(startDate);
              const formattedEndDate = formatDateToYYYYMMDD(endDate);

              const queryParams: FileQueryParams = {
                current,
                pageSize,
                fileName,
                startDate: formattedStartDate,
                endDate: formattedEndDate,
              };

              try {
                const res = await getFileList(queryParams);

                // 检查返回的数据结构
                console.log('API返回数据:', res);

                // 处理后端返回的数据结构，将字段名映射到组件期望的字段名
                let formattedData: FileInfo[] = [];

                // 如果service层已经处理过数据，直接使用
                if (res.data && Array.isArray(res.data) && res.data.length > 0 && 'size' in res.data[0]) {
                  formattedData = res.data as FileInfo[];
                  console.log('使用service层处理后的数据');
                }
                // 否则自行处理
                else if (res.data) {
                  let dataArray;

                  // 如果是字符串，尝试解析JSON
                  if (typeof res.data === 'string') {
                    try {
                      // 解析JSON字符串
                      dataArray = JSON.parse(res.data);
                      console.log('解析后的数据结构:', dataArray);

                      // 处理双重嵌套的数组 [[...]]
                      if (Array.isArray(dataArray) && dataArray.length > 0) {
                        if (Array.isArray(dataArray[0])) {
                          dataArray = dataArray[0];
                        }
                      }
                    } catch (e) {
                      console.error('解析JSON字符串失败:', e);
                      dataArray = [];
                    }
                  }
                  // 如果已经是数组
                  else if (Array.isArray(res.data)) {
                    dataArray = res.data;
                  }

                  // 确保dataArray是数组后再处理
                  if (Array.isArray(dataArray)) {
                    formattedData = dataArray.map((item: any) => ({
                      fileName: item.fileName,
                      filePath: item.filePath,
                      size: item.size || 0, // 使用size字段映射到fileSize
                      lastModified: item.lastModified, // 使用lastModified字段映射到uploadTime
                    }));
                  }
                }

                console.log('格式化后的数据:', formattedData);

                return {
                  data: formattedData,
                  success: res.code === 200,
                  total: formattedData.length,
                };
              } catch (error) {
                console.error('查询文件列表出错:', error);
                message.error('查询文件列表失败');
                return {
                  data: [],
                  success: false,
                  total: 0,
                };
              }
            }}
            columns={columns}
            className="custom-table"
          />
        </div>
      </div>
    </WrapContent>
  );
};

export default FileOperation;