import React from "react";
import { Modal, Form, Input, Row, Col } from "antd";
import type { AccQueryData } from "../data.d";
import { ProFormText, ProFormSelect } from "@ant-design/pro-form";
import "../style.less";

interface EditModalProps {
  visible: boolean;
  onCancel: () => void;
  onOk: (values: any) => Promise<void>;
  editingRecord: AccQueryData | null;
}

const EditModal: React.FC<EditModalProps> = ({
  visible,
  onCancel,
  onOk,
  editingRecord,
}) => {
  const [form] = Form.useForm();

  const commonFieldProps = {
    style: { 
      borderRadius: '8px',
      overflow: 'hidden'
    }
  };

  // 处理表单提交
  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      await onOk(values);
      form.resetFields();
    } catch (error) {
      console.error("表单验证失败:", error);
    }
  };

  // 当 editingRecord 改变时，更新表单值
  React.useEffect(() => {
    if (editingRecord) {
      form.setFieldsValue(editingRecord);
    }
  }, [editingRecord, form]);

  return (
    <Modal
      title="编辑自动化数据"
      visible={visible}
      onOk={handleOk}
      onCancel={() => {
        form.resetFields();
        onCancel();
      }}
      width={800}
      destroyOnClose
      style={{ 
        borderRadius: "16px",
        overflow: "hidden"
      }}
      bodyStyle={{ 
        borderRadius: "16px",
        overflow: "hidden"
      }}
      modalRender={modal => (
        <div style={{ borderRadius: '16px', overflow: 'hidden' }}>
          {modal}
        </div>
      )}
    >
      <Form 
        form={form} 
        layout="vertical" 
        initialValues={editingRecord || {}}
        className="edit-modal-form"
      >
        <Row gutter={16}>
          <Col span={12}>
            <ProFormText
              name="id"
              label="序列号"
              width="xl"
              placeholder="请输入序列号"
              disabled
              fieldProps={commonFieldProps}
              rules={[
                {
                  required: true,
                  message: "请输入序列号！",
                },
              ]}
            />
          </Col>
          <Col span={12}>
            <ProFormText
              name="mediumNo"
              label="介质号"
              width="xl"
              placeholder="请输入介质号"
              disabled
              fieldProps={commonFieldProps}
              rules={[
                {
                  required: true,
                  message: "请输入介质号！",
                },
              ]}
            />
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={12}>
            <ProFormText
              name="custNo"
              label="客户编号"
              width="xl"
              disabled
              fieldProps={commonFieldProps}
            />
          </Col>
          <Col span={12}>
            <ProFormText
              name="relMediumNo"
              label="关联介质编号"
              width="xl"
              disabled
              fieldProps={commonFieldProps}
            />
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={12}>
            <ProFormSelect
              name="shardingId"
              label="分片值"
              width="xl"
              placeholder="请选择分片值"
              disabled
              fieldProps={{
                ...commonFieldProps,
                style: {
                  ...commonFieldProps.style,
                  width: '100%'
                }
              }}
              options={[
                {
                  value: "0001",
                  label: "0001",
                },
                {
                  value: "0002",
                  label: "0002",
                },
                {
                  value: "0003",
                  label: "0003",
                },
                {
                  value: "0004",
                  label: "0004",
                },
                {
                  value: "0005",
                  label: "0005",
                },
                {
                  value: "0006",
                  label: "0006",
                },
                {
                  value: "0007",
                  label: "0007",
                },
                {
                  value: "0008",
                  label: "0008",
                },
              ]}
            />
          </Col>
          <Col span={12}>
            <ProFormSelect
              name="useFlag"
              label="是否使用"
              width="xl"
              placeholder="请选择是否使用"
              initialValue="1"  // 添加这一行，强制设置初始值为"1"
              options={[
                {
                  value: "0",
                  label: "0-未使用",
                },
                {
                  value: "1",
                  label: "1-已使用",
                },
              ]}
              fieldProps={{
                ...commonFieldProps,
                style: {
                  ...commonFieldProps.style,
                  width: '100%'
                },
                optionItemRender: (item: { value: string; label: string }) => {
                  const useFlagMap: { [key: string]: string } = {
                    "0": "未使用",
                    "1": "已使用",
                  };
                  return `${item.value}-${useFlagMap[item.value] || item.label}`;
                },
              }}
            />
          </Col>
        </Row>
        <Form.Item name="remark" label="备注" >
          <Input.TextArea 
            rows={3}
            style={{ borderRadius: '8px' }}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default EditModal;
