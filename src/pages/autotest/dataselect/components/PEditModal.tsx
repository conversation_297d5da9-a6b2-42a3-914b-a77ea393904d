import React from "react";
import { Modal, Form, Input, Row, Col } from "antd";
import type { AccQueryData } from "../data.d";
import { ProFormText } from "@ant-design/pro-form";
import "../style.less";

interface PEditModalProps {
  visible: boolean;
  onCancel: () => void;
  onOk: (values: any) => Promise<void>;
  selectedRecords: AccQueryData[];
}

const PEditModal: React.FC<PEditModalProps> = ({
  visible,
  onCancel,
  onOk,
  selectedRecords,
}) => {
  const [form] = Form.useForm();

  const commonFieldProps = {
    style: {
      borderRadius: "8px",
      overflow: "hidden",
    },
  };

  // 处理表单提交
  const handleOk = async () => {
    try {
        const values = await form.validateFields();
    
        // 构造提交的数据结构
        const submitData = {
          items: selectedRecords.map(record => ({
            id: record.id,
            mediumNo: record.mediumNo
          })),
          remark: values.remark
        };
    
        await onOk(submitData);
        form.resetFields();
    } catch (error) {
      console.error("表单验证失败:", error);
    }
  };

  return (
    <Modal
      title="批量使用数据"
      visible={visible}
      onOk={handleOk}
      onCancel={() => {
        form.resetFields();
        onCancel();
      }}
      width={800}
      destroyOnClose
      style={{
        borderRadius: "16px",
        overflow: "hidden",
        top: 20, // 设置距离顶部的距离
      }}
      bodyStyle={{
        borderRadius: "16px",
        overflow: "hidden",
        maxHeight: "calc(100vh - 200px)", // 设置最大高度
        overflowY: "auto", // 添加垂直滚动
        paddingRight: 16, // 添加右侧内边距，避免滚动条遮挡内容
      }}
      modalRender={(modal) => (
        <div style={{ borderRadius: "16px", overflow: "hidden" }}>{modal}</div>
      )}
    >
      <Form
        form={form}
        layout="vertical"
        className="edit-modal-form batch-edit-form" // 添加新的类名
      >
        {selectedRecords.map((record, index) => (
          <Row gutter={16} key={record.id}>
            <Col span={12}>
              <ProFormText
                name={`id_${record.id}`}
                label={`序列号`}
                width="xl"
                initialValue={record.id}
                disabled
                fieldProps={commonFieldProps}
              />
            </Col>
            <Col span={12}>
              <ProFormText
                name={`mediumNo_${record.id}`}
                label={`介质编号`}
                width="xl"
                initialValue={record.mediumNo}
                disabled
                fieldProps={commonFieldProps}
              />
            </Col>
          </Row>
        ))}
        <Form.Item
          name="remark"
          label="备注"
          rules={[
            {
              required: true,
              message: "请输入备注！",
            },
          ]}
        >
          <Input.TextArea
            rows={3}
            style={{ borderRadius: "8px" }}
            placeholder="请输入备注"
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default PEditModal;
