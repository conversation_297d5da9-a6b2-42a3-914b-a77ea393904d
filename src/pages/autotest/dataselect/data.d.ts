export type AccQueryData = {
    id: string;
    dataTypeNo: string;  // 数据类型代码
    mediumNo: string;
    custNo: string;
    relMediumNo: string;
    shardingId: string;
    useFlag: string;
    userName: string;  // 添加使用人字段
    remark: string;
    isPerson: string;
};

// AccListPagination 定义了账户列表的分页信息
export type AccListPagination = {
    total: number; // 总数
    pageSize: number; // 每页大小
    current: number; // 当前页码
};

// AccListData 定义了账户列表的数据结构
export type AccListData = {
    list: AccQueryData[]; // 用户列表
    pagination: Partial<AccListPagination>; // 分页信息
};

// AccListParams 定义了账户列表的请求参数
export type AccListParams = {
    dataTypeNo: string;
    mediumNo: string;
    custNo: string;
    relMediumNo: string;
    shardingId: string;
    useFlag: string;
    remark: string;
    current: number;       // 改为必填
    pageSize: number;      // 改为必填
    id?: string | number; // 添加 id 字段
  };

// AccExportParams 定义了导出功能的请求参数
export type AccExportParams = {
    list: AccQueryData[]; // 直接使用选中的数据对象数组
};

export type BatchUpdateItem = {
    id: string;
    mediumNo: string;
};

export type BatchUpdateParams = {
    items: BatchUpdateItem[];
    remark: string;
};
