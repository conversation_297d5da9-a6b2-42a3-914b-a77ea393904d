import request from '@/utils/request';
import { downLoadXlsx } from '@/utils/downloadfile';
import type { AccListParams, AccExportParams, AccQueryData, BatchUpdateParams } from './data.d';

// 查询账户列表
export async function accQueryList(params: Partial<AccListParams>) {
  // 设置默认值
  const defaultParams: AccListParams = {
    dataTypeNo: '',
    mediumNo: '',
    custNo: '',
    relMediumNo: '',
    shardingId: '',
    useFlag: '',
    remark: '',
    current: 1,
    pageSize: 10,
    ...params  // 使用传入的参数覆盖默认值
  };

  // 将所有参数转换为字符串类型
  const requestBody: Record<string, string> = {};
  Object.keys(defaultParams).forEach(key => {
    const value = defaultParams[key as keyof AccListParams];
    requestBody[key] = value?.toString() || '';
  });
  // return request('/testtool/dataSelection/list', {
  return request('/common/dataProcess/selectTestENVLayoutData', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    data: requestBody,
  });
}

// 更新数据
export async function updateAccQuery(params: AccQueryData) {
  // return request('/testtool/dataSelection/update', {
  return request('/common/dataProcess/update', {
    method: 'POST',
    data: params,
  });
}

// 导出账户数据
export function exportAccQuery(params: AccExportParams) {
  return downLoadXlsx(
    `/common/dataProcess/export`,
    {
      method: 'POST',
      data: params.list, // 直接传递数据数组
    },
    `account_${new Date().getTime()}.xlsx`,
  );
}


export async function accQuery(params: { id: string | number }) {
  // return request(`/testtool/dataSelection/${params.id}`, {
  return request(`/common/dataProcess/${params.id}`, {
    method: 'GET',
    params: params,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

// 获取数据类型代码字典
export async function getDict() {
  return request(`/common/dataProcess/dict/`, {
  // return request(`/testtool/dataProcess/dict/`, {
    method: 'GET',
  });
}

export async function updateBatchAcc(params: BatchUpdateParams) {
  return request('/common/dataProcess/batchUpdate', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    data: params,
  });
}
