.edit-modal-form {
  :global {
    .ant-select-selector {
      border-radius: 8px !important;
    }
      
    .ant-select-dropdown {
      border-radius: 8px !important;
    }
  }
}
  
// 添加批量编辑模态框的样式
.batch-edit-form {
  :global {
    // 自定义滚动条样式
    ::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }
  
    ::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }
  
    ::-webkit-scrollbar-thumb {
      background: #888;
      border-radius: 3px;
    }
  
    ::-webkit-scrollbar-thumb:hover {
      background: #555;
    }
  
    // 表单项样式
    .ant-form-item {
      margin-bottom: 16px !important;
    }
  
    // 文本框样式
    .ant-input {
      border-radius: 8px !important;
    }
  
    // 禁用输入框样式
    .ant-input[disabled] {
      color: rgba(0, 0, 0, 0.65) !important;
      background-color: #f5f5f5 !important;
    }
  }
}
  
:global {
  .custom-select-dropdown {
    .ant-select-item-option {
      padding: 5px 12px !important;
        
      &:hover,
      &.ant-select-item-option-active {
        background-color: #e6f7ff !important;
          
        .ant-select-item-option-content {
          position: relative !important; // 改为相对定位
          display: block !important;
          background-color: #e6f7ff !important;
          padding: 4px 12px !important;
          white-space: normal !important;
          height: auto !important;
          word-break: break-all !important;
          border-radius: 4px !important;
          width: 100% !important; // 使用100%宽度
          z-index: 1 !important;
        }
      }
    }
  
    .ant-select-item-option-content {
      white-space: nowrap !important;
      overflow: hidden !important;
      text-overflow: ellipsis !important;
      transition: all 0.3s !important;
  
      // 添加弹性布局以支持两端对齐
      display: flex !important;
      justify-content: space-between !important;
      align-items: center !important;
  
      // 确保内容不会被截断
      > div {
        width: 100% !important;
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
      }
  
      // 确保数字部分不会换行
      span:last-child {
        margin-left: 8px !important;
        flex-shrink: 0 !important;
      }
    }
  }
  
  .ant-select-dropdown {
    padding: 4px 0 !important;
  
    .rc-virtual-list-holder-inner {
      padding: 0 !important;
    }
  
    .ant-select-item {
      padding: 5px 12px !important;
    }
  
    .ant-select-item-option-active {
      background-color: #e6f7ff !important;
    }
  }
  
  // 移除之前的搜索结果样式，使用统一的样式
  .ant-select-dropdown-search,
  .ant-select-dropdown-menu {
    .ant-select-item-option {
      &:hover,
      &.ant-select-item-option-active {
        .ant-select-item-option-content {
          position: relative !important;
          width: 100% !important;
        }
      }
    }
  }
  
  // 添加搜索表单的样式
  .ant-form {
    padding: 24px 24px 0 24px !important;
  }
  
  .ant-form-item {
    margin-bottom: 24px !important;
    padding-right: 32px !important;  // 增加右侧间距
  
    // 调整标签样式
    .ant-form-item-label {
      padding-bottom: 8px !important;
      label {
        height: auto !important;
        white-space: normal !important;  // 允许标签文字换行
        line-height: 1.5 !important;
      }
    }
  
    // 调整输入框容器样式
    .ant-form-item-control {
      flex: 1 !important;
      max-width: 100% !important;
    }
  
    // 确保输入框不会溢出
    .ant-form-item-control-input {
      min-width: 0 !important;
      width: 100% !important;
    }
  
    // 调整所有输入框和选择框的宽度
    .ant-input,
    .ant-select {
      width: 100% !important;  // 使用100%宽度
      max-width: 300px !important;  // 但限制最大宽度
    }
  }
  
  // 响应式布局调整
  @media screen and (max-width: 1200px) {
    .ant-form-item {
      padding-right: 24px !important;
    }
  }
  
  @media screen and (max-width: 768px) {
    .ant-form-item {
      padding-right: 16px !important;
    }
  }
  
  @media screen and (max-width: 575px) {
    .ant-form-item {
      padding-right: 0 !important;
    }
  }
}
:global {
  // 修改搜索表单的样式
  .search-form {
    .ant-form {
      padding: 24px 24px 0 24px !important;
      display: flex !important;
      flex-wrap: wrap !important;
      gap: 16px !important;  // 使用 gap 来控制间距
    }
  
    .ant-form-item {
      margin: 0 !important;  // 移除默认边距
      flex: 1 1 calc(16.666% - 16px) !important;  // 使用 calc 计算宽度
      min-width: 220px !important;  // 设置最小宽度
      max-width: 300px !important;  // 设置最大宽度
  
      // 调整标签样式
      .ant-form-item-label {
        padding-bottom: 8px !important;
          
        label {
          height: auto !important;
          white-space: normal !important;
          line-height: 1.5 !important;
        }
      }
  
      // 调整输入框容器样式
      .ant-form-item-control {
        width: 100% !important;
      }
  
      // 调整所有输入框和选择框的样式
      .ant-input,
      .ant-select {
        width: 100% !important;
      }
  
      // 调整下拉菜单的样式
      .ant-select-dropdown {
        max-width: 90vw !important;  // 限制下拉菜单最大宽度
      }
    }
  
    // 响应式布局调整
    @media screen and (max-width: 1600px) {
      .ant-form-item {
        flex: 1 1 calc(20% - 16px) !important;
      }
    }
  
    @media screen and (max-width: 1200px) {
      .ant-form-item {
        flex: 1 1 calc(25% - 16px) !important;
      }
    }
  
    @media screen and (max-width: 992px) {
      .ant-form-item {
        flex: 1 1 calc(33.333% - 16px) !important;
      }
    }
  
    @media screen and (max-width: 768px) {
      .ant-form-item {
        flex: 1 1 calc(50% - 16px) !important;
      }
    }
  
    @media screen and (max-width: 576px) {
      .ant-form-item {
        flex: 1 1 100% !important;
        max-width: 100% !important;
      }
    }
  }
}