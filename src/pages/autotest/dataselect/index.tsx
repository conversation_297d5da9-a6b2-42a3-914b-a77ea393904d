import { PlusOutlined } from "@ant-design/icons";
import WrapContent from "@/components/WrapContent";
import { KeepAlive } from "react-activation";
import type { FormInstance } from "antd";
import { Button, message, Row, Col } from "antd";
import { useIntl, FormattedMessage, useAccess } from "umi";
import React, { useState, useRef, useEffect } from "react";
import type { ProColumns, ActionType } from "@ant-design/pro-table";
import ProTable from "@ant-design/pro-table";
import type { AccQueryData, BatchUpdateParams } from "./data.d";
import { accQueryList, exportAccQuery, updateAccQuery, accQuery, getDict, updateBatchAcc } from "./service";
import { storage } from "@/utils/storageUtils";
import EditModal from './components/EditModal';
import PEditModal from './components/PEditModal';

interface FormValueType {
    dataTypeNo?: string;
    mediumNo?: string;
    custNo?: string;
    relMediumNo?: string;
    shardingId?: string;
    useFlag?: string;
    remark?: string;
}

const AutoTestQueryList: React.FC = () => {
  const formTableRef = useRef<FormInstance>();
  const actionRef = useRef<ActionType>();
  const [selectedRowsState, setSelectedRows] = useState<AccQueryData[]>([]);
  const [formValues, setFormValues] = useState<FormValueType>({});
  const [tableData, setTableData] = useState<AccQueryData[]>([]);
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const [isFirstRequest, setIsFirstRequest] = useState(true);
  const [pEditModalVisible, setPEditModalVisible] = useState(false);

  const access = useAccess();
  const intl = useIntl();

  // 添加字典数据状态
  const [dataTypeNoOptions, setDataTypeNoOptions] = useState<{
    value: string;
    label: React.ReactNode;
    usableNum: number;
    rawData: {
      dataTypeNo: string;
      dataTypeName: string;
      usableNum: number;
    };
  }[]>([]);
  // 获取字典数据
  const loadDictData = async () => {
    try {
      console.log('获取字典数据');
      const res = await getDict();
      if (res.code === 200) {
        // 解析 JSON 字符串
        const dictData = JSON.parse(res.data);
        storage.set("dict_data", dictData, 30 * 60);  // 缓存原始字典数据，用于表格渲染
        const options = dictData.map((item: any) => ({
          value: item.dataTypeNo,
          label: (
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <span>{`${item.dataTypeNo}-${item.dataTypeName}`}</span>
              <span style={{ color: '#f9a825' }}>{item.usableNum}</span>
            </div>
          ),
          usableNum: item.usableNum,  // 添加 num 属性
          rawData: item
        }));
        setDataTypeNoOptions(options);
      }
    } catch (error) {
      console.error('获取字典数据失败:', error);
    }
  };
  // 在组件加载时获取字典数据
  useEffect(() => {
    loadDictData();
  }, []);

  // 搜索表单状态初始化
  const [searchForm, setSearchForm] = useState(() => {
    const savedSearch = storage.get("dataselect_search");
    return savedSearch || {};
  });

  // 添加编辑模态框相关状态
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [editingRecord, setEditingRecord] = useState<AccQueryData | null>(null);

  // 加载缓存数据的函数
  const loadCacheData = () => {
    try {
      const savedData = storage.get("dataselect_data");
      const savedSearch = storage.get("dataselect_search");
      console.log('初始化数据:', savedData, savedSearch);

      if (savedData?.records && savedSearch) {
        const records = Array.isArray(savedData.records) ? savedData.records : [];
        // 先设置表单值
        setFormValues(savedSearch);
        if (formTableRef.current) {
          formTableRef.current.setFieldsValue(savedSearch || {});
        }
        setTableData(records);
      }
    } catch (error) {
      console.error('加载缓存数据失败:', error);
    }
    setIsInitialLoad(false);
  };

  // 初始化数据
  useEffect(() => {
    loadCacheData();
    return () => {
      setIsFirstRequest(true);
      setIsInitialLoad(true);
    };
  }, []);

  // 处理重置
  const handleReset = (form: FormInstance | undefined) => {
    // 重置所有状态
    setFormValues({});
    setSelectedRows([]);
    setTableData([]);
    setSearchForm({});

    // 重置表单 - 使用 setTimeout 确保状态更新后再重置表单
    setTimeout(() => {
      form?.resetFields();
      formTableRef.current?.resetFields();

      // 清除表格选中状态
      if (actionRef.current?.clearSelected) {
        actionRef.current.clearSelected();
      }

      // 清除本地存储
      storage.set("dataselect_data", null, 0);
      storage.set("dataselect_search", null, 0);
    }, 0);
  };

  // 处理导出
  const handleExport = async () => {
    if (selectedRowsState.length === 0) {
      message.warning("请选择数据进行导出");
      return;
    }
    const hide = message.loading("正在导出");
    try {
      await exportAccQuery({
        list: selectedRowsState,
      });
      hide();
      message.success("导出成功");
      return true;
    } catch (error) {
      hide();
      message.error("导出失败，请重试");
      return false;
    }
  };

  // 处理编辑按钮点击
  const handleEditClick = async (record: AccQueryData) => {
    try {
      // 这里假设有一个根据 id 查询详情的接口
      const res = await accQuery({ id: record.id });
      if (res.code === 200) {
        const detailData = typeof res.data === 'string' ? JSON.parse(res.data) : res.data;
        // setEditingRecord(detailData);
        setEditingRecord({
          ...detailData,
          useFlag: "1"
        });
        setEditModalVisible(true);
      }
    } catch (error) {
      message.error('获取详情失败');
    }
  };

  // 处理模态框确认
  const handleEditModalOk = async (values: any) => {
    try {
      // 这里需要调用更新接口
      const processedValues = {
        ...values,
        useFlag: values.useFlag?.split('-')[0] || values.useFlag,
        id: editingRecord?.id
      };
      const res = await updateAccQuery(processedValues);
      if (res.code === 200) {
        message.success('更新成功');
        setEditModalVisible(false);
        setEditingRecord(null);
        actionRef.current?.reload();
      }
    } catch (error) {
      message.error('更新失败');
    }
  };

  // 处理模态框取消
  const handleEditModalCancel = () => {
    setEditModalVisible(false);
    setEditingRecord(null);
  };

  // 3. 添加批量处理函数
  const handlePEditModalOk = async (values: BatchUpdateParams) => {
    try {
      const res = await updateBatchAcc(values);
    if (res.code === 200) {
      message.success('批量更新成功');
      setPEditModalVisible(false);
      // 清空选中的行
      setSelectedRows([]);
      // 刷新表格数据
      actionRef.current?.reload();
    } else {
      message.error(res.msg || '批量更新失败');
    }
    } catch (error) {
      message.error('批量更新失败，请重试');
    }
  };

  const handlePEditModalCancel = () => {
    setPEditModalVisible(false);
  };

  // 处理请求
  const handleRequest = async (params: any) => {
    const { current = 1, pageSize = 10, ...searchParams } = params;
    const requestParams = {
      current: parseInt(current),  // 确保 current 是数字类型
      pageSize: parseInt(pageSize),
      dataTypeNo: searchParams.dataTypeNo
        ? searchParams.dataTypeNo.split('-')[0]
        : '',
      mediumNo: searchParams.mediumNo || '',
      custNo: searchParams.custNo || '',
      relMediumNo: searchParams.relMediumNo || '',
      shardingId: searchParams.shardingId || '',
      useFlag: searchParams.useFlag
        ? searchParams.useFlag.split("-")[0]
        : '',
      remark: searchParams.remark || '',
    };

    try {
      // 只有在非首次请求时才更新缓存
      if (!isFirstRequest) {
        setSearchForm({
          ...requestParams,
          current: parseInt(current),  // 确保缓存中的 current 也是正确的
          pageSize: parseInt(pageSize)
        });
        storage.set("dataselect_search", requestParams, 30 * 60);
      }
      setIsFirstRequest(false);

      const res = await accQueryList(requestParams);
      if (res.code === 200) {
        const parsedData = typeof res.data === 'string' ? JSON.parse(res.data) : res.data;
        const records = Array.isArray(parsedData.records) ? parsedData.records : [];
        const total = parsedData.total || 0;

        // 只有在非首次请求时才更新缓存
        if (!isFirstRequest) {
          storage.set("dataselect_data", parsedData, 30 * 60);
        }
        setTableData(records);
        return {
          data: records,
          total: total,
          success: true
        };
      } else {
        message.error(res.msg || '查询失败');
      }
      return {
        data: [],
        total: 0,
        success: false
      };
    } catch (error) {
      console.error('查询失败:', error);
      return {
        data: [],
        total: 0,
        success: false
      };
    }
  };

  const columns: ProColumns<AccQueryData>[] = [
    {
      title: (
        <FormattedMessage
          id="autoTest.accquery.dataTypeNo"
          defaultMessage="数据类型"
        />
      ),
      dataIndex: "dataTypeNo",
      valueType: "select",  // 改为 select 类型
      valueEnum: Object.fromEntries(
        dataTypeNoOptions.map(option => [
          option.value,
          option.value // 简化为只显示值
        ])
      ),
      // 使用 render 函数来自定义单元格显示
      render: (_, record) => {
        // 查找匹配的选项
        const option = dataTypeNoOptions.find(opt => opt.value === record.dataTypeNo);
        if (option && option.rawData) {
          return `${option.rawData.dataTypeNo}-${option.rawData.dataTypeName}`;
        }

        // 如果在当前选项中找不到，尝试从缓存中查找
        const dictData = storage.get("dict_data") || [];
        const dictItem = dictData.find((item: any) => item.dataTypeNo === record.dataTypeNo);
        if (dictItem) {
          return (
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <span>{`${dictItem.dataTypeNo}-${dictItem.dataTypeName}`}</span>
              <span style={{ color: '#f9a825' }}>{dictItem.usableNum}</span>
            </div>
          );
        }

        return record.dataTypeNo || '-';
      },
      fieldProps: {
        options: dataTypeNoOptions,  // 使用options来提供选项
        // 添加以下配置来调整下拉菜单的样式
        listHeight: 256,          // 下拉列表的高度
        popupMatchSelectWidth: 400,  // 下拉菜单的宽度（像素）
        showSearch: true,  // 启用搜索功能
        filterOption: (input: string, option?: { label: any; value: string }) => {
          if (!option) return false;
          // 由于 label 现在是 ReactNode，需要获取文本内容进行比较
          const labelText = option.label?.props?.children[0]?.props?.children || '';
          const inputLower = input.toLowerCase();
          return labelText.toLowerCase().includes(inputLower);
        },
        style: {
          width: '300px',  // 修改宽度
          borderRadius: "8px", // 添加圆角
        },
        dropdownStyle: {
          maxWidth: '800px',  // 下拉框最大宽度
          whiteSpace: 'normal',  // 允许文本换行
          wordBreak: 'break-all'  // 在任意字符间断行
        },
        dropdownClassName: 'custom-select-dropdown', // 添加这一行
        placeholder: "请选择数据类型",
      },
      ellipsis: true,  // 添加这一行，使用省略号
      width: 120,      // 添加这一行，限制列宽
      align: 'center',  // 添加这一行，实现居中对齐
    },
    {
      title: (
        <FormattedMessage
          id="autoTest.accquery.mediumNo"
          defaultMessage="介质编号"
        />
      ),
      dataIndex: "mediumNo",
      valueType: "textarea",
      hideInSearch: false,
      width: 100,      // 添加这一行，限制列宽
      align: 'center',  // 添加这一行，实现居中对齐
      fieldProps: {
        style: {
          width: "220px",
          borderRadius: "8px", // 添加圆角样式
        }, // 设置搜索框宽度
        placeholder: "请输入介质编号",
      },
    },
    {
      title: (
        <FormattedMessage
          id="autoTest.accquery.custNo"
          defaultMessage="客户编号"
        />
      ),
      dataIndex: "custNo",
      valueType: "textarea",
      hideInSearch: false,
      width: 100,      // 添加这一行，限制列宽
      align: 'center',  // 添加这一行，实现居中对齐
      fieldProps: {
        style: {
          width: "220px",
          borderRadius: "8px", // 添加圆角样式
        }, // 设置搜索框宽度
        placeholder: "请输入客户编号",
      },
    },
    {
      title: (
        <FormattedMessage
          id="autoTest.accquery.custNm"
          defaultMessage="客户名称"
        />
      ),
      dataIndex: "custNm",
      valueType: "textarea",
      hideInSearch: true,
      ellipsis: true,  // 添加这一行，使用省略号
      width: 60,      // 添加这一行，限制列宽
      align: 'center',  // 添加这一行，实现居中对齐
      fieldProps: {
        placeholder: "请输入客户名称",
      },
    },
    {
      title: (
        <FormattedMessage
          id="autoTest.accquery.perCertTpCd"
          defaultMessage="个人证件类型代码"
        />
      ),
      dataIndex: "perCertTpCd",
      valueType: "select",
      valueEnum: {
        "1010": "1010-居民身份证",
        "1011": "1011-临时居民身份证",
        "1020": "1020-军人身份证件",
        "1021": "1021-士兵证",
        "1022": "1022-军官证",
        "1023": "1023-文职干部证",
        "1024": "1024-军官退休证",
        "1025": "1025-文职干部退休证",
        "1030": "1030-武警身份证件",
        "1031": "1031-武警士兵证",
        "1032": "1032-警官证",
        "1033": "1033-武警文职干部证",
        "1034": "1034-武警军官退休证",
        "1035": "1035-武警文职干部退休证",
        "1040": "1040-户口簿",
        "1050": "1050-中国护照",
        "1051": "1051-外国护照",
        "1060": "1060-学生证",
        "1070": "1070-港澳居民来往内地通行证",
        "1071": "1071-往来港澳通行证",
        "1080": "1080-台湾居民来往大陆通行证",
        "1090": "1090-执行公务证",
        "1100": "1100-机动车驾驶证",
        "1110": "1110-社会保障卡",
        "1120": "1120-外国人居留证",
        "1121": "1121-外国人永久居留证",
        "1130": "1130-旅行证件",
        "1140": "1140-香港居民身份证",
        "1150": "1150-澳门居民身份证",
        "1160": "1160-台湾居民身份证",
        "1170": "1170-边民证",
        "1180": "1180-港澳台居民居住证",
        "1181": "1181-港澳居民居住证",
        "1182": "1182-台湾居民居住证",
        "1190": "1190-外国身份证",
        "1998": "1998-其他（原98类）",
        "1999": "1999-其他证件（个人）",
      },
      hideInSearch: true,
      width: 100,      // 添加这一行，限制列宽
      align: 'center',  // 添加这一行，实现居中对齐
    },
    {
      title: (
        <FormattedMessage
          id="autoTest.accquery.personalCertNo"
          defaultMessage="证件号码"
        />
      ),
      dataIndex: "personalCertNo",
      valueType: "textarea",
      hideInSearch: true,
      width: 100,      // 添加这一行，限制列宽
      align: 'center',  // 添加这一行，实现居中对齐
    },
    {
      title: (
        <FormattedMessage
          id="autoTest.accquery.relMediumNo"
          defaultMessage="关联介质编号"
        />
      ),
      dataIndex: "relMediumNo",
      valueType: "textarea",
      hideInSearch: false,
      width: 120,      // 添加这一行，限制列宽
      align: 'center',  // 添加这一行，实现居中对齐
      fieldProps: {
        style: {
          width: "220px",
          borderRadius: "8px", // 添加圆角样式
        }, // 设置搜索框宽度
        placeholder: "请输入关联介质编号",
      },
    },
    {
      title: (
        <FormattedMessage
          id="autoTest.accquery.dataDesc"
          defaultMessage="数据描述"
        />
      ),
      dataIndex: "dataDesc",
      valueType: "textarea",
      hideInSearch: true,
      ellipsis: true,  // 添加这一行，使用省略号
      width: 180,      // 添加这一行，限制列宽
      align: 'center',  // 添加这一行，实现居中对齐
    },
    {
      title: (
        <FormattedMessage
          id="autoTest.accquery.shardingId"
          defaultMessage="分片值"
        />
      ),
      dataIndex: "shardingId",
      valueType: "select",
      valueEnum: {
        "0001": "0001",
        "0002": "0002",
        "0003": "0003",
        "0004": "0004",
        "0005": "0005",
        "0006": "0006",
        "0007": "0007",
        "0008": "0008",
      },
      fieldProps: {
        // 添加以下配置来调整下拉菜单的样式
        listHeight: 256,          // 下拉列表的高度
        popupMatchSelectWidth: 400,  // 下拉菜单的宽度（像素）
        style: {
          width: '300px',  // 修改宽度
          borderRadius: "8px", // 添加圆角
        },
        dropdownStyle: {
          maxWidth: '500px',  // 下拉框最大宽度
          whiteSpace: 'normal',  // 允许文本换行
          wordBreak: 'break-all'  // 在任意字符间断行
        },
        placeholder: "请选择分片值",
      },
      width: 40,      // 添加这一行，限制列宽
      align: 'center',  // 添加这一行，实现居中对齐
      fixed: 'left',  // 添加这一行，固定在右侧
    },
    {
      title: (
        <FormattedMessage
          id="autoTest.accquery.useFlag"
          defaultMessage="是否使用"
        />
      ),
      dataIndex: "useFlag",
      valueType: "select",
      valueEnum: {
        "0": "0-未使用",
        "1": "1-已使用",
      },
      width: 60,      // 添加这一行，限制列宽
      align: 'center',  // 添加这一行，实现居中对齐
      fieldProps: {
        // 添加以下配置来调整下拉菜单的样式
        listHeight: 256,          // 下拉列表的高度
        popupMatchSelectWidth: 400,  // 下拉菜单的宽度（像素）
        style: {
          width: '220px',  // 与介质号保持一致
          borderRadius: "8px", // 添加圆角
        },
        dropdownStyle: {
          maxWidth: '500px',  // 下拉框最大宽度
          whiteSpace: 'normal',  // 允许文本换行
          wordBreak: 'break-all'  // 在任意字符间断行
        },
        placeholder: "请选择是否使用标志",
      },
    },
    {
      title: (
        <FormattedMessage
          id="autoTest.accquery.userName"
          defaultMessage="使用人"
        />
      ),
      dataIndex: "userName",
      valueType: "textarea",
      hideInSearch: true,
      ellipsis: true,  // 使用省略号
      width: 80,      // 限制列宽
      align: 'center',  // 居中对齐
    },
    {
      title: (
        <FormattedMessage
          id="autoTest.accquery.remark"
          defaultMessage="备注"
        />
      ),
      dataIndex: "remark",
      valueType: "textarea",
      hideInSearch: false,
      ellipsis: true,  // 添加这一行，使用省略号
      width: 180,      // 添加这一行，限制列宽
      align: 'center',  // 添加这一行，实现居中对齐
      fieldProps: {
        style: {
          width: "220px",
          borderRadius: "8px", // 添加圆角样式
        }, // 设置搜索框宽度
        placeholder: "请输入备注",
      },
    },
    {
      title: (
        <FormattedMessage
          id="autoTest.accquery.createTime"
          defaultMessage="创建时间"
        />
      ),
      dataIndex: "createTime",
      valueType: "textarea",
      hideInSearch: true,
      width: 80,      // 添加这一行，限制列宽
      align: 'center',  // 添加这一行，实现居中对齐
    },
    {
      title: (
        <FormattedMessage
          id="autoTest.accquery.updateTime"
          defaultMessage="更新时间"
        />
      ),
      dataIndex: "updateTime",
      valueType: "textarea",
      hideInSearch: true,
      width: 80,      // 添加这一行，限制列宽
      align: 'center',  // 添加这一行，实现居中对齐
    },
    {
      title: (
        <FormattedMessage
          id="autoTest.accquery.id"
          defaultMessage="序列号"
        />
      ),
      dataIndex: "id",
      valueType: "textarea",
      hideInSearch: true,
      ellipsis: true,  // 添加这一行，使用省略号
      width: 60,      // 添加这一行，限制列宽
      align: 'center',  // 添加这一行，实现居中对齐
    },
    {
      title: (
        <FormattedMessage
          id="autoTest.accquery.operation"
          defaultMessage="操作"
        />
      ),
      dataIndex: "option",
      width: "40px",
      valueType: "option",
      fixed: 'right',  // 添加这一行，固定在右侧
      align: 'center',  // 添加这一行，实现居中对齐
      render: (_, record) => [
        <Button type="link" size="small" key="resetpwd" hidden={true}>
          <FormattedMessage
            id="autoTest.accquery.operation"
            defaultMessage="操作"
          />
        </Button>,
        <Button
          type="link"
          size="small"
          key="edit"
          disabled={record.isPerson === "0"}  // 改用 disabled 来控制按钮状态
          onClick={() => {
            // 编辑操作
            handleEditClick(record);
          }}
        >
          <FormattedMessage id="pages.searchTable.edit" defaultMessage="编辑" />
        </Button>,
        <Button
          type="link"
          size="small"
          danger
          key="batchRemove"
          hidden={true}
        >
          <FormattedMessage
            id="pages.searchTable.delete"
            defaultMessage="删除"
          />
        </Button>,
      ],
    },
  ];

  return (
    <KeepAlive
      saveScrollPosition={false}
      when={() => true}
      cacheKey="accquery"
      onActivate={() => {
        setIsFirstRequest(true);
        // 使用 requestAnimationFrame 确保在下一帧渲染
        requestAnimationFrame(() => {
          loadCacheData();
        });
      }}
    >
      <WrapContent>
        <Row gutter={[16, 24]}>
          <Col lg={24} md={24}>
            <ProTable<AccQueryData>
              headerTitle={intl.formatMessage({
                id: "pages.searchTable.title",
                defaultMessage: "信息",
              })}
              actionRef={actionRef}
              formRef={formTableRef}
              form={{
                ignoreRules: false,
                onReset: () => {
                  handleReset(formTableRef.current);
                },
                onValuesChange: (_, values: FormValueType) => {
                  setFormValues(values);
                },
                // 添加初始值
                initialValues: searchForm,
              }}
              rowKey={(record) => record.id}  // 修改为使用 mediumNo 作为唯一标识
              key="accList"
              search={{
                labelWidth: 150,
                span: {
                  xs: 24,
                  sm: 24,
                  md: 12,
                  lg: 12,
                  xl: 8,
                  xxl: 6,
                },
                layout: "vertical",
                defaultCollapsed: false,
                optionRender: ({ searchText, form }) => [
                  <Button
                    type="primary"
                    key="search"
                    style={{ borderRadius: "8px" }}
                    onClick={() => {
                      setIsFirstRequest(false);
                      form?.submit();
                    }}
                  >
                    {searchText}
                  </Button>,
                  <Button
                    key="reset"
                    style={{ marginLeft: 8, borderRadius: "8px" }}
                    onClick={() => {
                      handleReset(form);
                      // 强制更新表单状态
                      form?.setFieldsValue({});
                    }}
                  >
                    重置
                  </Button>,
                ],
              }}
              toolBarRender={() => [
                <Button
                  type="primary"
                  key="batchUse"
                  onClick={() => {
                    if (selectedRowsState.length === 0) {
                      message.warning("请选择要处理的数据");
                      return;
                    }
                    setPEditModalVisible(true);
                  }}
                  style={{ borderRadius: "8px" }}
                >
                  <PlusOutlined />
                  <FormattedMessage
                    id="pages.searchTable.batchUse"
                    defaultMessage="批量使用数据"
                  />
                </Button>,
                <Button
                  type="primary"
                  key="export"
                  onClick={handleExport}
                  style={{ borderRadius: "8px" }}
                  disabled={true}
                  // disabled={!access.hasEnvPerms(
                  //   localStorage.getItem("currentEnv") as string
                  // )}
                >
                  <PlusOutlined />
                  <FormattedMessage
                    id="pages.searchTable.export"
                    defaultMessage="导出"
                  />
                </Button>,
              ]}
              pagination={{
                defaultPageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                current: parseInt(storage.get("dataselect_search")?.current) || 1,
                pageSize: parseInt(storage.get("dataselect_search")?.pageSize) || 10,
                onChange: (page, pageSize) => {
                  const currentSearchParams = formTableRef.current?.getFieldsValue();
                  const params = {
                    ...currentSearchParams,
                    current: page,
                    pageSize: pageSize
                  };
                  // 直接调用 handleRequest 而不是使用 reload
                  handleRequest(params).then((result) => {
                    if (result.success) {
                      // 更新分页信息到缓存
                      storage.set("dataselect_search", {
                        ...storage.get("dataselect_search"),
                        current: page,
                        pageSize: pageSize
                      }, 30 * 60);
                    }
                  });
                }
              }}
              request={async (params) => {
                // 如果有缓存数据且是首次加载，直接返回缓存数据
                if (isFirstRequest) {
                  const savedData = storage.get("dataselect_data");
                  setIsFirstRequest(false);
                  return {
                    data: savedData?.records || [],
                    success: true,
                    total: savedData?.total || 0,
                    current: parseInt(storage.get("dataselect_search")?.current) || 1
                    // data: tableData,
                    // success: true,
                    // total: savedData?.total || tableData.length,
                    // current: parseInt(storage.get("dataselect_search")?.current) || 1
                  };
                }
                // 否则执行正常的请求逻辑
                return handleRequest(params);
              }}
              dataSource={tableData}
              rowSelection={{
                type: 'checkbox',
                preserveSelectedRowKeys: true,
                getCheckboxProps: (record) => ({
                  name: record.id,  // 使用 mediumNo 作为 checkbox 的 name
                  disabled: record.isPerson === "0",  // 添加 disabled 属性，当 isPerson === "0" 时禁用选择框
                  style: record.isPerson === "0" ? { cursor: 'not-allowed' } : undefined,  // 添加鼠标样式
                }),
                selectedRowKeys: selectedRowsState.map(row => row.id),  // 使用 mediumNo 作为选中的 key
                onChange: (selectedRowKeys, selectedRows) => {
                  // 过滤掉不可选的行
                  const filteredRows = selectedRows.filter(row => row.isPerson !== "0");
                  setSelectedRows(filteredRows);
                },
                selections: [
                  {
                    key: 'clear',
                    text: '清空选择',
                    onSelect: () => {
                      setSelectedRows([]);
                    },
                  },
                  {
                    key: 'all',
                    text: '全选所有可选项',
                    onSelect: () => {
                      // 只选择可选的行（isPerson !== "0"）
                      const availableRows = tableData.filter(row => row.isPerson !== "0");
                      setSelectedRows(availableRows);
                    },
                  },
                ],
              }}
              scroll={{ x: 3500 }}
              columnEmptyText="-"
              columns={columns}
            />
          </Col>
        </Row>
        <EditModal
          visible={editModalVisible}
          onCancel={handleEditModalCancel}
          onOk={handleEditModalOk}
          editingRecord={editingRecord}
        />
        <PEditModal
          visible={pEditModalVisible}
          onCancel={handlePEditModalCancel}
          onOk={handlePEditModalOk}
          selectedRecords={selectedRowsState}
        />
      </WrapContent>
    </KeepAlive>
  );
};

export default AutoTestQueryList;