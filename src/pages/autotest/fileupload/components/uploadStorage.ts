export interface UploadProgress {
    fileHash: string;
    fileName: string;
    chunkProgress: { [key: number]: number };
    uploadedChunks: number[];
  }
  
  const STORAGE_KEY = 'file_upload_progress';
  
  export const saveUploadProgress = (progress: UploadProgress): void => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(progress));
    } catch (error) {
      console.error('Error saving upload progress:', error);
    }
  };
  
  export const getUploadProgress = (fileHash: string): UploadProgress | null => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (!stored) return null;
      
      const progress = JSON.parse(stored) as UploadProgress;
      return progress.fileHash === fileHash ? progress : null;
    } catch (error) {
      console.error('Error getting upload progress:', error);
      return null;
    }
  };
  
  export const clearUploadProgress = (fileHash: string): void => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (stored) {
        const progress = JSON.parse(stored) as UploadProgress;
        if (progress.fileHash === fileHash) {
          localStorage.removeItem(STORAGE_KEY);
        }
      }
    } catch (error) {
      console.error('Error clearing upload progress:', error);
    }
  };