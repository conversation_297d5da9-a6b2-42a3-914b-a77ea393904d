import SparkMD5 from 'spark-md5';

const CHUNK_SIZE = 1 * 1024 * 1024; // 1MB chunks

export const calculateHash = async (file: File): Promise<string> => {
  return new Promise((resolve) => {
    const spark = new SparkMD5.ArrayBuffer();
    const reader = new FileReader();
    
    reader.onload = (e) => {
      spark.append(e.target?.result as ArrayBuffer);
      resolve(spark.end());
    };
    
    reader.readAsArrayBuffer(file);
  });
};

export const createFileChunks = (file: File): Blob[] => {
  const chunks: Blob[] = [];
  let start = 0;
  
  while (start < file.size) {
    const chunk = file.slice(start, start + CHUNK_SIZE);
    chunks.push(chunk);
    start += CHUNK_SIZE;
  }
  
  return chunks;
};