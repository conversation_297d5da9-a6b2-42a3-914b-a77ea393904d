import request from '@/utils/request';
import { ChunkInfo } from './data.d';

interface ApiResponse<T> {
    code: number;
    msg: string;
    data: T;
    error: boolean;
    success: boolean;
}

interface FileStatusResponse {
shouldUpload: boolean;
uploadedChunks: number[];
}

/**
 * 检查文件状态
 *
 * @param fileHash 文件哈希值
 * @param fileName 文件名
 * @returns 返回文件上传状态信息
 */
export async function checkFileStatus(
  fileHash: string,
  fileName: string
): Promise<ApiResponse<FileStatusResponse>> {
  return request(`/testtool/upload/check`, {
    method: 'GET',
    params: { fileHash, fileName },
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * 上传文件分片
 *
 * @param chunk 分片信息
 * @param fileName 文件名
 * @param totalChunks 总分片数
 * @returns void
 */
export async function uploadChunk(
  chunk: ChunkInfo,
  fileName: string,
  totalChunks: number
): Promise<void> {
  const formData = new FormData();
  // 确保这里的 chunk.chunk 是 Blob 类型
  formData.append('chunk', chunk.chunk, fileName); // 添加第三个参数作为文件名
  formData.append('hash', chunk.hash);
//   formData.append('fileName', fileName);
  formData.append('index', chunk.index.toString());
//   formData.append('totalChunks', totalChunks.toString());

  return request(`/testtool/upload/chunk`, {
    method: 'POST',
    data: formData,
    requestType: 'form', // 确保使用 form 提交方式
  });
}

/**
 * 合并文件分片
 *
 * @param fileName 文件名
 * @param fileHash 文件哈希值
 * @param totalChunks 总分片数
 * @returns void
 */
export async function mergeChunks(
  fileName: string,
  fileHash: string,
  totalChunks: number
): Promise<void> {
  return request(`/testtool/upload/merge`, {
    method: 'POST',
    data: {
      fileName,
      fileHash,
      totalChunks,
    },
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}