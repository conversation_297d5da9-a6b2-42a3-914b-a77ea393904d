.app-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  overflow: auto;
  width: 100%;
  max-width: 1900px !important;
  margin: 0 auto !important;
  padding: 0 16px;
  background-color: #f5f5f5;
  margin-top: 48px !important;
  opacity: 0;

  &.fade-in-up {
    animation: fadeInUp 0.5s ease-out forwards;
  }
}

/* 添加进入动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.card-container {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border-radius: 16px !important;
  height: fit-content;
  overflow: visible;
  background-color: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  position: relative;
  border: 4px solid transparent;

  &::before {
    content: '';
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    border-radius: 16px;
    z-index: -1;
    background:

      linear-gradient(
        to right,
        #fff8e1 10%,
        #ffe082 25%,
        #c8e6c9 50%,
        #ffd6e7 75%,
        #2e7d32 100%
      );
    mask:

      linear-gradient(#fff 0 0) content-box,
      linear-gradient(#fff 0 0);
    mask-composite: exclude;
    -webkit-mask:


      linear-gradient(#fff 0 0) content-box,
      linear-gradient(#fff 0 0);
    -webkit-mask-composite: destination-out;
    padding: 4px;
  }

  &:hover::before {
    animation: borderGlow 2s linear infinite;
  }
}

@keyframes borderGlow {
  0% {
    background-position: 0% center;
  }
  100% {
    background-position: 200% center;
  }
}

.input-section {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border-radius: 16px !important;
  height: fit-content;
  overflow: visible;
  background-color: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  position: relative;
  border: 4px solid transparent;

  &::before {
    content: '';
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    border-radius: 16px;
    z-index: -1;
    background:

      linear-gradient(
        to right,
        #fff8e1 10%,
        #ffe082 25%,
        #c8e6c9 50%,
        #ffd6e7 75%,
        #2e7d32 100%
      );
    mask:

      linear-gradient(#fff 0 0) content-box,
      linear-gradient(#fff 0 0);
    mask-composite: exclude;
    -webkit-mask:


      linear-gradient(#fff 0 0) content-box,
      linear-gradient(#fff 0 0);
    -webkit-mask-composite: destination-out;
    padding: 4px;
  }

  &:hover::before {
    animation: borderGlow 2s linear infinite;
  }
}

.result-animation {
}
