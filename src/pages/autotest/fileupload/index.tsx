import React, { useState, useCallback, useEffect } from 'react';
import { Upload, Progress, Card, message } from 'antd';
import { InboxOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import { FileStatus } from './data.d';
import { calculateHash, createFileChunks } from './components/fileUtils';
import { uploadChunk, mergeChunks, checkFileStatus } from './service';
import { saveUploadProgress, getUploadProgress, clearUploadProgress } from './components/uploadStorage';
import './style.less';

const { Dragger } = Upload;

const FileUpload: React.FC = () => {
  const [fileStatus, setFileStatus] = useState<FileStatus | null>(null);
  // 添加动画状态
  const [isVisible, setIsVisible] = useState(false);

  // 修改1: 移除setTimeout延迟
  useEffect(() => {
    // 立即设置可见性，避免延迟导致的跳动
    setIsVisible(true);
  }, []);

  const updateChunkProgress = useCallback((index: number, percentage: number, fileHash: string) => {
    setFileStatus(prev => {
      if (!prev) return null;
      const newChunks = [...prev.chunks];
      newChunks[index].percentage = percentage;
      const progress = (newChunks.reduce((acc, chunk) => acc + chunk.percentage, 0) / newChunks.length);

      saveUploadProgress({
        fileHash,
        fileName: prev.fileName,
        chunkProgress: newChunks.reduce((acc, chunk, idx) => ({
          ...acc,
          [idx]: chunk.percentage
        }), {}),
        uploadedChunks: newChunks
          .map((chunk, idx) => chunk.percentage === 100 ? idx : -1)
          .filter(idx => idx !== -1)
      });

      return {
        ...prev,
        chunks: newChunks,
        progress,
      };
    });
  }, []);

  const handleUpload = async (file: File) => {
    try {
      setFileStatus({
        uploading: true,
        fileName: file.name,
        fileSize: file.size,
        chunks: [],
        progress: 0,
      });

      const fileHash = await calculateHash(file);
      const chunks = createFileChunks(file);

      console.log('fileHash', fileHash);
      console.log('chunks', chunks);
      const response = await checkFileStatus(fileHash, file.name);
      const { shouldUpload, uploadedChunks } = response.data;
      console.log('shouldUpload', shouldUpload);
      console.log('uploadedChunks', uploadedChunks);

      if (!shouldUpload) {
        message.success('文件已存在，上传完成！');
        return;
      }

      const savedProgress = getUploadProgress(fileHash);
      const chunkInfos = chunks.map((chunk, index) => ({
        chunk,
        hash: `${fileHash}-${index}`,
        index,
        percentage: savedProgress?.chunkProgress[index] || 0,
      }));

      setFileStatus(prev => prev ? { ...prev, chunks: chunkInfos } : null);

      const chunksToUpload = chunkInfos.filter(
        (_, index) => !uploadedChunks.includes(index)
      );

      const concurrentLimit = 3;
      const uploadQueue = async (chunks: typeof chunkInfos) => {
        const results = [];
        for (let i = 0; i < chunks.length; i += concurrentLimit) {
          const uploadPromises = chunks
            .slice(i, i + concurrentLimit)
            .map(async (chunkInfo) => {
              if (uploadedChunks.includes(chunkInfo.index)) {
                updateChunkProgress(chunkInfo.index, 100, fileHash);
                return;
              }

              await uploadChunk(chunkInfo, file.name, chunks.length);
              updateChunkProgress(chunkInfo.index, 100, fileHash);
            });

          results.push(...await Promise.all(uploadPromises));
        }
        return results;
      };

      await uploadQueue(chunksToUpload);
      await mergeChunks(file.name, fileHash, chunks.length);

      clearUploadProgress(fileHash);
      message.success('文件上传成功！');
    } catch (error) {
      message.error('上传失败，请重试。');
      console.error('Upload error:', error);
    } finally {
      setFileStatus(prev => prev ? { ...prev, uploading: false } : null);
    }
  };

  return (
    <div className={`app-container ${isVisible ? 'fade-in-up' : ''}`} style={{
      padding: '16px',
      minWidth: '800px',
      maxWidth: '1400px',
      margin: '0 auto',
      marginTop: "48px",
      width: '100%',
      boxSizing: 'border-box',
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      position: 'relative',
      overflow: 'hidden',
    }}>
      <PageContainer
        title="文件上传"
        subTitle="支持大文件分片上传"
        style={{
          background: '#fff',
          borderRadius: '16px',
          overflow: 'hidden',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
          width: '100%',
          maxWidth: '100%',
          WebkitBoxSizing: 'border-box',
          MozBoxSizing: 'border-box',
          boxSizing: 'border-box',
        }}
      >
        <div className={`card-container ${isVisible ? 'fade-in-up' : ''}`}>
          <Card
            style={{
              margin: '16px',
              borderRadius: '12px',
              border: '1px solid #f0f0f0',
              width: 'calc(100% - 32px)',
              WebkitBoxSizing: 'border-box',
              MozBoxSizing: 'border-box',
              boxSizing: 'border-box',
            }}
            className="input-section"
          >
            <Dragger
              name="file"
              multiple={false}
              showUploadList={false}
              beforeUpload={(file) => {
                handleUpload(file);
                return false;
              }}
              disabled={fileStatus?.uploading}
              style={{
                padding: '40px 0',
                background: '#fafafa',
                border: '2px dashed #e8e8e8',
                borderRadius: '8px',
                transition: 'all 0.3s ease',
                width: '100%',
              }}
            >
              <p className="ant-upload-drag-icon">
                <InboxOutlined style={{ fontSize: '48px', color: '#1890ff' }} />
              </p>
              <p className="ant-upload-text" style={{ fontSize: '16px', fontWeight: 'bold', margin: '16px 0' }}>
                点击或拖拽文件到此区域上传
              </p>
              <p className="ant-upload-hint" style={{ color: '#666' }}>
                支持大文件自动分片上传，支持断点续传
              </p>
            </Dragger>

            {fileStatus && (
              <div className={`result-animation ${isVisible ? 'fade-in-up' : ''}`} style={{ marginTop: 24 }}>
                <p style={{ margin: '8px 0' }}>文件名：{fileStatus.fileName}</p>
                <p style={{ margin: '8px 0' }}>文件大小：{(fileStatus.fileSize / (1024 * 1024)).toFixed(2)} MB</p>
                <Progress
                  percent={Math.round(fileStatus.progress)}
                  status={fileStatus.uploading ? 'active' : 'success'}
                  strokeColor={{
                    '0%': '#108ee9',
                    '100%': '#87d068',
                  }}
                  style={{ marginTop: '16px' }}
                />
              </div>
            )}
          </Card>
        </div>
      </PageContainer>
    </div>
  );
};

export default FileUpload;