import React, { useState, useEffect } from "react";
import { Spin, message, Modal, Form, Input, Button } from "antd";
import { useIntl } from "umi";
import WrapContent from "@/components/WrapContent";
import MainModules from "./components/MainModules";
import TestPoints from "./components/TestPoints";
import ForumContent from "./components/ForumContent";
import {
  getModules,
  getTestPoints,
  getForumPosts,
  likePost,
  addComment,
  createModule,
  createTestPoint,
  updateTestPoint,
  ModuleItem,
  TestPointItem,
  ForumPost,
  Comment,
} from "./service";
import styles from "./style.less";

const TestPointsForum: React.FC = () => {
  const intl = useIntl();
  const [loading, setLoading] = useState<boolean>(true);
  const [modules, setModules] = useState<ModuleItem[]>([]);
  const [selectedModule, setSelectedModule] = useState<string | null>(null);
  const [testPoints, setTestPoints] = useState<TestPointItem[]>([]);
  const [selectedTestPoint, setSelectedTestPoint] = useState<string | null>(
    null
  );
  const [forumPosts, setForumPosts] = useState<ForumPost[]>([]);
  const [isModalVisible, setIsModalVisible] = useState<boolean>(false);

  // 添加新模态框状态
  const [isAddModuleModalVisible, setIsAddModuleModalVisible] =
    useState<boolean>(false);
  const [isAddTestPointModalVisible, setIsAddTestPointModalVisible] =
    useState<boolean>(false);
  const [addModuleForm] = Form.useForm();
  const [addTestPointForm] = Form.useForm();
  const [createLoading, setCreateLoading] = useState<boolean>(false);

  // 获取模块数据
  const fetchModules = async () => {
    try {
      // 实际项目中，替换为真实API调用
      const response = await getModules();
      setModules(response.data || []);
      if (response.data && response.data.length > 0) {
        setSelectedModule(response.data[0].moduleId);
      }
    } catch (error) {
      message.error(
        intl.formatMessage({
          id: "app.request.failed",
          defaultMessage: "请求失败，请重试",
        })
      );
    }
  };

  // 获取测试要点数据
  const fetchTestPoints = async (moduleId: string) => {
    if (!moduleId) return;

    try {
      setLoading(true);
      // 实际项目中，替换为真实API调用
      const response = await getTestPoints(moduleId);

      // 处理后端返回的测试要点数据，将isPinned字段从数字转换为布尔值
      const testPointsWithPin = (response.data || []).map((item) => ({
        ...item,
        isPinned: item.isPinned === 1 || item.isPinned === true, // 处理后端返回的isPinned字段
      }));

      // 对测试要点进行排序：置顶项在前，其他项按testPointId排序
      const sortedTestPoints = testPointsWithPin.sort((a, b) => {
        // 置顶项排在前面
        if (a.isPinned && !b.isPinned) return -1;
        if (!a.isPinned && b.isPinned) return 1;

        // 如果都是置顶或都不是置顶，按testPointId排序
        return a.testPointId.localeCompare(b.testPointId);
      });

      // 添加调试日志
      console.log("获取到的测试要点数据:", sortedTestPoints);

      setTestPoints(sortedTestPoints);

      // 如果有测试要点，默认选中第一个
      if (sortedTestPoints.length > 0) {
        setSelectedTestPoint(sortedTestPoints[0].testPointId);
      } else {
        setSelectedTestPoint(null);
        setForumPosts([]);
      }
    } catch (error) {
      message.error(
        intl.formatMessage({
          id: "app.request.failed",
          defaultMessage: "请求失败，请重试",
        })
      );
    } finally {
      setLoading(false);
    }
  };

  // 获取论坛帖子数据
  const fetchForumPosts = async (testPointId: string) => {
    if (!testPointId) return;

    try {
      setLoading(true);
      // 实际项目中，替换为真实API调用
      const response = await getForumPosts(testPointId);
      setForumPosts(response.data || []);
    } catch (error) {
      message.error(
        intl.formatMessage({
          id: "app.request.failed",
          defaultMessage: "请求失败，请重试",
        })
      );
    } finally {
      setLoading(false);
    }
  };

  // 初始加载模块数据
  useEffect(() => {
    fetchModules();
  }, []);

  // 当选中的模块改变时，加载该模块的测试要点
  useEffect(() => {
    if (selectedModule) {
      fetchTestPoints(selectedModule);
    }
  }, [selectedModule]);

  // 当选中的测试要点改变时，加载该测试要点的论坛帖子
  useEffect(() => {
    if (selectedTestPoint) {
      fetchForumPosts(selectedTestPoint);
    }
  }, [selectedTestPoint]);

  // 处理模块点击
  const handleModuleClick = (moduleId: string) => {
    setSelectedModule(moduleId);
    setSelectedTestPoint(null);
  };

  // 处理测试要点点击
  const handleTestPointClick = (testPointId: string) => {
    setSelectedTestPoint(testPointId);
    // 先显示模态框并进入加载状态，然后再加载数据
    setIsModalVisible(true);
    setLoading(true);
    fetchForumPosts(testPointId).finally(() => {
      setLoading(false);
    });
  };

  // 处理测试要点置顶
  const handleTestPointPin = (testPointId: string, isPinned: boolean) => {
    console.log("置顶参数:", { testPointId, isPinned, selectedModule }); // 调试日志

    // 严格验证参数
    if (!testPointId) {
      console.error("测试要点ID为空");
      message.error("操作失败: 测试要点ID无效");
      return;
    }

    if (!selectedModule) {
      console.error("未选择模块");
      message.error("操作失败: 请先选择一个模块");
      return;
    }

    // 找到当前测试要点的完整数据
    const testPoint = testPoints.find(
      (item) => item.testPointId === testPointId
    );
    if (!testPoint) {
      console.error("找不到测试要点:", testPointId);
      message.error("操作失败: 找不到指定的测试要点");
      return;
    }

    // 显示加载状态
    setLoading(true);

    // 调用API更新置顶状态
    updateTestPoint({
      testPointId: testPointId,
      isPinned: !isPinned, // 取反当前状态 (isPinned是当前状态，我们需要切换它)
      moduleId: selectedModule,
    })
      .then((response) => {
        if (response.code === 200) {
          // API调用成功后更新本地状态
          const updatedTestPoints = testPoints.map((item) =>
            item.testPointId === testPointId
              ? { ...item, isPinned: !isPinned }
              : item
          );
          setTestPoints(updatedTestPoints);
          message.success(isPinned ? "已取消置顶" : "已置顶");
        } else {
          message.error(response.msg || "操作失败");
        }
      })
      .catch((error) => {
        console.error("置顶操作失败:", error);
        message.error("置顶操作失败，请重试");
      })
      .finally(() => {
        setLoading(false);
      });
  };

  // 处理测试要点编辑
  const handleTestPointEdit = (testPointId: string, newName: string) => {
    const updatedTestPoints = testPoints.map((item) =>
      item.testPointId === testPointId ? { ...item, name: newName } : item
    );
    setTestPoints(updatedTestPoints);
    message.success("测试要点已更新");
  };

  // 处理发布新帖子
  const handlePostSubmit = async (content: string) => {
    if (!selectedTestPoint) return;

    try {
      // 实际项目中，替换为真实API调用
      await new Promise((resolve) => setTimeout(resolve, 500));

      // 模拟新帖子
      const newPost: ForumPost = {
        id: `post-${Date.now()}`,
        content,
        author: "当前用户",
        createTime: new Date().toISOString(),
        testPointId: selectedTestPoint,
        likes: 0,
        isLiked: false,
        comments: [],
      };

      // 更新帖子列表，将新帖子添加到列表开头
      setForumPosts((prev) => [newPost, ...prev]);

      message.success(
        intl.formatMessage({
          id: "pages.blog.postSuccess",
          defaultMessage: "发布成功",
        })
      );
    } catch (error) {
      message.error(
        intl.formatMessage({
          id: "pages.blog.postFailed",
          defaultMessage: "发布失败，请重试",
        })
      );
    }
  };

  // 处理点赞/取消点赞
  const handleLikePost = async (postId: string, isLiked: boolean) => {
    try {
      const response = await likePost(postId, !isLiked);

      if (response.code === 200) {
        setForumPosts((prevPosts) =>
          prevPosts.map((post) =>
            post.id === postId
              ? {
                  ...post,
                  likes: response.data.likes,
                  isLiked: response.data.isLiked,
                }
              : post
          )
        );
      }
    } catch (error) {
      message.error("操作失败，请重试");
    }
  };

  // 处理评论
  const handleAddComment = async (params: {
    content: string;
    postId: string;
    parentId?: string;
  }) => {
    try {
      const response = await addComment(params);

      if (response.code === 200) {
        setForumPosts((prevPosts) =>
          prevPosts.map((post) => {
            if (post.id === params.postId) {
              // 添加新评论到评论列表
              const updatedComments = [...(post.comments || []), response.data];
              return {
                ...post,
                comments: updatedComments,
              };
            }
            return post;
          })
        );
        return response.data;
      }
      return null;
    } catch (error) {
      message.error("评论失败，请重试");
      return null;
    }
  };

  // 添加新的测试模块
  const handleAddModule = () => {
    setIsAddModuleModalVisible(true);
    addModuleForm.resetFields();
  };

  // 提交新的测试模块
  const handleAddModuleSubmit = async () => {
    try {
      const values = await addModuleForm.validateFields();
      setCreateLoading(true);

      // 调用创建模块API
      const response = await createModule(values);

      if (response.code === 200) {
        // 模拟新模块
        const newModule: ModuleItem = {
          moduleId: `module-${Date.now()}`,
          name: values.name,
          description: values.description || "",
          // icon: "AppstoreOutlined", // 使用默认图标
        };

        // 更新模块列表
        setModules([...modules, newModule]);
        message.success("测试模块创建成功");
        setIsAddModuleModalVisible(false);
        addModuleForm.resetFields();

        // 选择新创建的模块
        setSelectedModule(newModule.moduleId);
      } else {
        message.error(response.msg || "创建失败，请重试");
      }
    } catch (error) {
      console.error("Form validation failed:", error);
    } finally {
      setCreateLoading(false);
    }
  };

  // 添加新的测试要点
  const handleAddTestPoint = () => {
    if (!selectedModule) {
      message.warning("请先选择一个测试模块");
      return;
    }
    setIsAddTestPointModalVisible(true);
    addTestPointForm.resetFields();
  };

  // 提交新的测试要点
  const handleAddTestPointSubmit = async () => {
    if (!selectedModule) return;

    try {
      const values = await addTestPointForm.validateFields();
      setCreateLoading(true);

      // 调用创建测试要点API
      const response = await createTestPoint({
        ...values,
        moduleId: selectedModule,
      });

      if (response.code === 200) {
        // 模拟新测试要点
        const newTestPoint: TestPointItem = {
          testPointId: `testpoint-${Date.now()}`,
          name: values.name,
          description: values.description || "",
          moduleId: selectedModule,
          isPinned: false,
        };

        // 更新测试要点列表
        setTestPoints([...testPoints, newTestPoint]);
        message.success("测试要点创建成功");
        setIsAddTestPointModalVisible(false);
        addTestPointForm.resetFields();

        // 选择新创建的测试要点
        setSelectedTestPoint(newTestPoint.testPointId);
      } else {
        message.error(response.msg || "创建失败，请重试");
      }
    } catch (error) {
      console.error("Form validation failed:", error);
    } finally {
      setCreateLoading(false);
    }
  };

  // 当前选中的测试要点名称和描述
  const selectedTestPointData = React.useMemo(() => {
    const selected = testPoints.find(
      (tp) => tp.testPointId === selectedTestPoint
    );
    return {
      name: selected?.name || "",
      description: selected?.description || "",
    };
  }, [testPoints, selectedTestPoint]);

  return (
    <WrapContent>
      <div className={styles.container}>
        {loading && selectedModule && !modules.length ? (
          <div className={styles.loadingContainer}>
            <Spin size="large" />
          </div>
        ) : (
          <div className={styles.forumLayout}>
            <div className={styles.leftSidebar}>
              <MainModules
                modules={modules}
                selectedModule={selectedModule}
                onModuleClick={handleModuleClick}
                onAddModule={handleAddModule}
              />
            </div>
            <div className={styles.content}>
              {selectedModule && (
                <TestPoints
                  testPoints={testPoints}
                  selectedTestPoint={selectedTestPoint}
                  onTestPointClick={handleTestPointClick}
                  onTestPointPin={handleTestPointPin}
                  onTestPointEdit={handleTestPointEdit}
                  onAddTestPoint={handleAddTestPoint}
                />
              )}
              {/* 添加测试要点数据调试信息 */}
              {process.env.NODE_ENV === "development" && (
                <div style={{ display: "none" }}>
                  {console.log("渲染的测试要点数据:", testPoints)}
                </div>
              )}
            </div>
          </div>
        )}

        {/* 论坛内容模态框 */}
        <Modal
          title={`测试论坛 - ${selectedTestPointData.name}`}
          open={isModalVisible}
          onCancel={() => setIsModalVisible(false)}
          footer={null}
          width="80%"
          bodyStyle={{
            maxHeight: "calc(90vh - 130px)",
            overflow: "auto",
            borderRadius: "8px",
          }}
          style={{
            borderRadius: "12px",
            overflow: "hidden",
          }}
          destroyOnClose
        >
          {selectedTestPointData.description && (
            <div
              style={{
                background: "#f5f5f5",
                padding: "12px 16px",
                borderRadius: "6px",
                marginBottom: "16px",
                fontSize: "14px",
                color: "rgba(0, 0, 0, 0.65)",
              }}
            >
              <div style={{ fontWeight: "bold", marginBottom: "8px" }}>
                测试要点描述：
              </div>
              <div>{selectedTestPointData.description}</div>
            </div>
          )}
          <ForumContent
            posts={forumPosts}
            loading={loading}
            onPostSubmit={handlePostSubmit}
            testPointName={selectedTestPointData.name}
          />
        </Modal>

        {/* 添加测试模块模态框 */}
        <Modal
          title="添加测试模块"
          open={isAddModuleModalVisible}
          onCancel={() => setIsAddModuleModalVisible(false)}
          footer={[
            <Button
              key="cancel"
              onClick={() => setIsAddModuleModalVisible(false)}
              style={{ borderRadius: "6px" }}
            >
              取消
            </Button>,
            <Button
              key="submit"
              type="primary"
              loading={createLoading}
              onClick={handleAddModuleSubmit}
              style={{ borderRadius: "6px" }}
            >
              创建
            </Button>,
          ]}
          destroyOnClose
          bodyStyle={{
            padding: "24px",
            borderRadius: "8px",
          }}
          style={{
            borderRadius: "8px",
            overflow: "hidden",
          }}
        >
          <Form form={addModuleForm} layout="vertical" name="addModuleForm">
            <Form.Item
              name="name"
              label="模块名称"
              rules={[{ required: true, message: "请输入模块名称" }]}
            >
              <Input
                placeholder="请输入模块名称"
                style={{ borderRadius: "6px" }}
              />
            </Form.Item>
            <Form.Item name="description" label="模块描述">
              <Input.TextArea
                placeholder="请输入模块描述（选填）"
                rows={3}
                style={{ borderRadius: "6px" }}
              />
            </Form.Item>
          </Form>
        </Modal>

        {/* 添加测试要点模态框 */}
        <Modal
          title="添加测试要点"
          open={isAddTestPointModalVisible}
          onCancel={() => setIsAddTestPointModalVisible(false)}
          footer={[
            <Button
              key="cancel"
              onClick={() => setIsAddTestPointModalVisible(false)}
              style={{ borderRadius: "6px" }}
            >
              取消
            </Button>,
            <Button
              key="submit"
              type="primary"
              loading={createLoading}
              onClick={handleAddTestPointSubmit}
              style={{ borderRadius: "6px" }}
            >
              创建
            </Button>,
          ]}
          destroyOnClose
          bodyStyle={{
            padding: "24px",
            borderRadius: "8px",
          }}
          style={{
            borderRadius: "8px",
            overflow: "hidden",
          }}
        >
          <Form
            form={addTestPointForm}
            layout="vertical"
            name="addTestPointForm"
          >
            <Form.Item
              name="name"
              label="测试要点名称"
              rules={[{ required: true, message: "请输入测试要点名称" }]}
            >
              <Input
                placeholder="请输入测试要点名称"
                style={{ borderRadius: "6px" }}
              />
            </Form.Item>
            <Form.Item name="description" label="测试要点描述">
              <Input.TextArea
                placeholder="请输入测试要点描述（选填）"
                rows={3}
                style={{ borderRadius: "6px" }}
              />
            </Form.Item>
          </Form>
        </Modal>
      </div>
    </WrapContent>
  );
};

export default TestPointsForum;
