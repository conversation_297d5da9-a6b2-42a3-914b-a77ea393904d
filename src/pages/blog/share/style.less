@import '~antd/es/style/themes/default.less';

// 全局容器样式
.container {
  min-height: 100vh;
  padding: 24px;
  animation: fadeIn 0.5s ease-out;
}

// 加载状态容器
.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
}

// 论坛布局
.forumLayout {
  display: flex;
  gap: 24px;
  min-height: calc(100vh - 200px);
  align-items: stretch;
}

// 左侧边栏
.leftSidebar {
  width: 280px;
  flex-shrink: 0;
  animation: slideInLeft 0.5s ease-out;
  display: flex;
  flex-direction: column;
}

// 内容区域
.content {
  flex: 1;
  display: flex;
  flex-direction: column;
  animation: fadeIn 0.6s ease-out;
  min-height: calc(100vh - 150px);
}

// 测试要点容器
.testPointsContainer {
  margin-bottom: 24px;
  animation: slideInDown 0.5s ease-out;
}

// 论坛内容容器
.forumContainer {
  flex: 1;
  animation: fadeInUp 0.7s ease-out;
}

// 模块容器
.modulesContainer {
  animation: fadeIn 0.5s ease-out;
}

// 模块项容器
.moduleItem {
  position: relative;
  margin-bottom: 12px;
  transition: all 0.3s;
  border-radius: 8px;
  overflow: hidden;
  animation: fadeInUp 0.3s ease-out;
  animation-fill-mode: both;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
  
  &:nth-child(1) { animation-delay: 0.05s; }
  &:nth-child(2) { animation-delay: 0.1s; }
  &:nth-child(3) { animation-delay: 0.15s; }
  &:nth-child(4) { animation-delay: 0.2s; }
  &:nth-child(5) { animation-delay: 0.25s; }
  &:nth-child(6) { animation-delay: 0.3s; }
  &:nth-child(7) { animation-delay: 0.35s; }
  &:nth-child(8) { animation-delay: 0.4s; }
  &:nth-child(9) { animation-delay: 0.45s; }
}

// 高亮条动画
.highlightBar {
  animation: fadeIn 0.3s ease-out;
}

// 已选中的模块
.selectedModule {
  border-left: 4px solid @primary-color;
  background-color: fade(@primary-color, 5%);
  
  &:after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    height: 100%;
    width: 3px;
    background: linear-gradient(to bottom, transparent, @primary-color, transparent);
  }
}

// 测试要点列表
.testPointsList {
  border-radius: 8px;
  overflow: hidden;
  height: 100%;
  
  .ant-list-empty-text {
    padding: 16px;
  }
}

// 测试要点项样式
.testPointItem {
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 8px;
  transition: all 0.3s;
  border-left: 3px solid transparent;
  cursor: pointer;
  animation: fadeInUp 0.3s ease-out;
  animation-fill-mode: both;
  
  &:hover {
    background-color: fade(@primary-color, 5%);
    transform: translateX(4px);
  }
  
  &:nth-child(1) { animation-delay: 0.05s; }
  &:nth-child(2) { animation-delay: 0.1s; }
  &:nth-child(3) { animation-delay: 0.15s; }
  &:nth-child(4) { animation-delay: 0.2s; }
  &:nth-child(5) { animation-delay: 0.25s; }
  &:nth-child(6) { animation-delay: 0.3s; }
  &:nth-child(7) { animation-delay: 0.35s; }
  &:nth-child(8) { animation-delay: 0.4s; }
  &:nth-child(9) { animation-delay: 0.45s; }
  &:nth-child(10) { animation-delay: 0.5s; }
}

// 选中的测试要点项
.selectedTestPointItem {
  background-color: fade(@primary-color, 8%);
  border-left: 3px solid @primary-color;
  
  &:hover {
    background-color: fade(@primary-color, 12%);
  }
}

// 测试要点内容
.testPointContent {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  position: relative;
}

// 测试要点名称与编辑按钮容器
.nameWithEditButton {
  display: flex;
  align-items: center;
  flex: 1;
  gap: 4px;
}

// 测试要点名称
.testPointName {
  flex: 1;
  font-size: 14px;
  line-height: 1.5;
}

// 编辑按钮
.editButton {
  opacity: 0.7;
  transition: all 0.3s;
  color: @primary-color;
  margin-left: 4px;
  
  &:hover {
    opacity: 1;
    transform: scale(1.1);
  }
}

// 编辑容器
.editingContainer {
  display: flex;
  align-items: center;
  width: 100%;
  
  .ant-btn {
    padding: 0 6px;
    margin: 0 2px;
  }
}

// 编辑输入框
.editInput {
  border-radius: 6px !important;
  
  :global {
    .ant-input {
      border-radius: 6px;
    }
  }
}

// 置顶标签
.pinnedTag {
  margin-right: 8px;
  font-size: 12px;
}

// 卡片样式
.card {
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s;
  margin-bottom: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
  
  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  }
}

// 帖子列表容器
.postsContainer {
  animation: fadeIn 0.5s ease-out;
}

// 帖子项包装器
.postItemWrapper {
  animation: fadeInUp 0.5s ease-out;
  animation-fill-mode: both;
  
  &:nth-child(1) { animation-delay: 0.05s; }
  &:nth-child(2) { animation-delay: 0.1s; }
  &:nth-child(3) { animation-delay: 0.15s; }
  &:nth-child(4) { animation-delay: 0.2s; }
  &:nth-child(5) { animation-delay: 0.25s; }
  &:nth-child(6) { animation-delay: 0.3s; }
  &:nth-child(7) { animation-delay: 0.35s; }
  &:nth-child(8) { animation-delay: 0.4s; }
}

// 论坛帖子项样式
.postItem {
  padding: 16px;
  margin-bottom: 16px;
  border-radius: 8px;
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s;
  
  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    transform: translateY(-2px);
  }
}

// 帖子标题样式
.postTitle {
  margin-bottom: 8px;
  color: @heading-color;
  font-weight: 500;
}

// 帖子内容样式
.postContent {
  margin-bottom: 12px;
  color: @text-color;
  line-height: 1.6;
  font-size: 20px !important;
  font-weight: 500 !important;
  font-style: italic !important;
}

// 帖子元信息样式
.postMeta {
  display: flex;
  justify-content: space-between;
  color: @text-color-secondary;
  font-size: 12px;
}

// 帖子作者信息样式
.postAuthor {
  display: flex;
  align-items: center;
  gap: 8px;
}

// 发布表单样式
.postForm {
  margin-bottom: 24px;
  padding: 16px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  animation: fadeInUp 0.5s ease-out;
}

// 空状态容器
.emptyContainer {
  padding: 40px;
  text-align: center;
  color: @text-color-secondary;
}

// 添加动画关键帧
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

// 搜索框样式
.searchInput {
  :global {
    .ant-input, .ant-input-affix-wrapper {
      border-radius: 8px 0 0 8px !important;
      input {
        border-radius: 0 !important;
      }
    }
    .ant-input-search-button {
      border-radius: 0 8px 8px 0 !important;
    }
  }
}

// 自定义滚动条样式
.modulesContainer, .testPointsList, .card {
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f0f2f5;
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 3px;
    
    &:hover {
      background: #999;
    }
  }
}

// 确保测试要点列表滚动条可见
.testPointsList {
  overflow-y: auto !important;
  max-height: 100%;
  padding-right: 4px;
}

// 响应式调整
@media (max-width: @screen-md) {
  .forumLayout {
    flex-direction: column;
  }
  
  .leftSidebar {
    width: 100%;
    margin-bottom: 24px;
  }
}

// 点赞按钮样式
.likeButton {
  color: @text-color-secondary;
  transition: all 0.3s;
  
  &:hover {
    color: @primary-color;
    transform: scale(1.1);
  }
  
  &.liked {
    color: @primary-color;
  }
}

// 评论容器样式
.commentsContainer {
  margin-top: 8px;
  border-top: 1px solid #f0f0f0;
  padding-top: 8px;
}

// 评论列表样式
.commentsList {
  margin-top: 8px;
  max-height: 300px;
  overflow-y: auto;
  
  &::-webkit-scrollbar {
    width: 4px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f0f2f5;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 2px;
    
    &:hover {
      background: #999;
    }
  }
}

// 评论项样式
.commentItem {
  padding: 8px;
  border-radius: 4px;
  margin-bottom: 8px;
  background-color: #f9f9f9;
  
  &:hover {
    background-color: #f0f0f0;
  }
  
  &.replyComment {
    margin-left: 24px;
    border-left: 2px solid @primary-color;
  }
}

// 评论作者样式
.commentAuthor {
  font-weight: 500;
  margin-right: 8px;
}

// 评论时间样式
.commentTime {
  font-size: 12px;
  color: @text-color-secondary;
}

// 评论内容样式
.commentContent {
  margin: 4px 0;
}

// 评论操作样式
.commentActions {
  display: flex;
  justify-content: flex-end;
  
  > span {
    margin-left: 16px;
    color: @text-color-secondary;
    cursor: pointer;
    
    &:hover {
      color: @primary-color;
    }
  }
}

// 评论表单样式修改
.replyForm {
  margin-top: 8px;
  width: 100%;
  
  .ant-input, .ant-btn {
    border-radius: 8px;
  }
  
  > div {
    width: 100%;
  }
}

// 评论编辑器容器
.commentEditorContainer {
  position: relative;
  width: 100%;
  margin-bottom: 8px;
}

// 评论文本域样式
.commentTextArea {
  width: 100%;
  border-radius: 8px !important;
  padding-bottom: 40px !important;
  
  &:hover, &:focus {
    border-color: @primary-color !important;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }
  
  :global {
    .ant-input {
      border-radius: 8px !important;
    }
  }
}

// 发送按钮容器
.sendButtonContainer {
  position: absolute;
  bottom: 8px;
  right: 8px;
  z-index: 1;
}

// 评论数量徽标样式
.commentBadge {
  :global {
    .ant-badge-count {
      background-color: @primary-color;
    }
  }
}

// 点赞数量样式
.likeCount {
  margin-left: 4px;
}

// 圆角按钮样式
.roundButton {
  border-radius: 8px !important;
  padding: 0 16px !important;
  transition: all 0.3s;
  
  &:hover {
    transform: scale(1.05);
  }
}

// 圆角外部输入框，内部直角
.roundOuterInput {
  border-radius: 8px !important;
  overflow: hidden;
  
  // 确保内部输入框为直角
  :global {
    .ant-input {
      border-radius: 0 !important;
    }
  }
}

// 回复目标提示样式
.replyTarget {
  font-size: 12px;
  color: @primary-color;
  margin-bottom: 4px;
}

// 修改帖子操作区域样式
.postActions {
  display: flex;
  margin-top: 8px;
  
  > span {
    display: flex;
    align-items: center;
    margin-right: 16px;
    cursor: pointer;
    color: @text-color-secondary;
    transition: all 0.3s;
    
    &:hover {
      color: @primary-color;
    }
    
    .anticon {
      margin-right: 4px;
    }
  }
} 