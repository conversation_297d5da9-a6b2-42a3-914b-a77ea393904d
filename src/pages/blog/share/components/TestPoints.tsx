import React, { useState } from "react";
import {
  Card,
  Typo<PERSON>,
  Button,
  Tooltip,
  List,
  Tag,
  Input,
  message,
} from "antd";
import {
  PlusOutlined,
  ExperimentOutlined,
  StarFilled,
  VerticalAlignTopOutlined,
  EditOutlined,
  CheckOutlined,
  CloseOutlined,
} from "@ant-design/icons";
import { FormattedMessage } from "umi";
import styles from "../style.less";

const { Title, Text } = Typography;
const { Search } = Input;

export interface TestPointItemProps {
  testPointId: string; // 修改为testPointId与后端一致
  name: string;
  moduleId: string;
  isPinned?: boolean;
  description?: string;
}

interface TestPointsProps {
  testPoints: TestPointItemProps[];
  selectedTestPoint: string | null;
  onTestPointClick: (testPointId: string) => void;
  onTestPointPin?: (testPointId: string, isPinned: boolean) => void;
  onTestPointEdit?: (testPointId: string, newName: string) => void;
  onAddTestPoint: () => void;
}

const TestPoints: React.FC<TestPointsProps> = ({
  testPoints,
  selectedTestPoint,
  onTestPointClick,
  onTestPointPin,
  onTestPointEdit,
  onAddTestPoint,
}) => {
  const [searchValue, setSearchValue] = useState("");
  const [testPointsData, setTestPointsData] =
    useState<TestPointItemProps[]>(testPoints);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editingName, setEditingName] = useState<string>("");
  const [hoveredId, setHoveredId] = useState<string | null>(null);

  // 编辑框引用
  const editingRef = React.useRef<HTMLDivElement>(null);

  // 处理页面点击事件，点击编辑区域外时保存
  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        editingId &&
        editingRef.current &&
        !editingRef.current.contains(event.target as Node)
      ) {
        if (editingName.trim()) {
          saveEditing();
        } else {
          cancelEditing();
        }
      }
    };

    // 添加全局点击事件监听
    document.addEventListener("mousedown", handleClickOutside);

    // 清理函数
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [editingId, editingName]);

  // 处理搜索
  const handleSearch = (value: string) => {
    setSearchValue(value);
  };

  // 处理置顶/取消置顶
  const handlePinToggle = (testPointId: string, currentPinned: boolean) => {
    // 添加调试信息和验证
    console.log("TestPoints组件调用:", { testPointId, currentPinned });

    // 确保testPointId不为undefined或空
    if (!testPointId) {
      console.error("测试要点组件内ID为空");
      message.error("操作失败：测试要点ID无效");
      return;
    }

    if (onTestPointPin) {
      // 调用父组件提供的处理函数，传递当前状态，不需要在这里取反
      // 因为父组件会处理取反逻辑
      onTestPointPin(testPointId, currentPinned);
    } else {
      // 如果没有传入外部处理函数，在本地处理
      const updatedTestPoints = testPointsData.map((item) =>
        item.testPointId === testPointId
          ? { ...item, isPinned: !currentPinned }
          : item
      );
      setTestPointsData(updatedTestPoints);
    }
  };

  // 开始编辑
  const startEditing = (testPointId: string, name: string) => {
    setEditingId(testPointId);
    setEditingName(name);
  };

  // 取消编辑
  const cancelEditing = () => {
    setEditingId(null);
    setEditingName("");
  };

  // 保存编辑
  const saveEditing = () => {
    if (!editingId || !editingName.trim()) {
      message.error("测试要点名称不能为空");
      return;
    }

    // 如果有外部回调，使用外部回调
    if (onTestPointEdit) {
      onTestPointEdit(editingId, editingName.trim());
    } else {
      // 本地更新数据
      const updatedTestPoints = testPointsData.map((item) =>
        item.testPointId === editingId
          ? { ...item, name: editingName.trim() }
          : item
      );
      setTestPointsData(updatedTestPoints);

      // 在实际项目中，这里应该调用API来更新数据
      message.success("测试要点名称已更新");
    }

    // 重置编辑状态
    setEditingId(null);
    setEditingName("");
  };

  // 如果数据变化，更新状态
  React.useEffect(() => {
    setTestPointsData(testPoints);
  }, [testPoints]);

  // 过滤并排序测试要点
  const filteredAndSortedTestPoints = React.useMemo(() => {
    // 先过滤
    const filtered = testPointsData.filter(
      (item) =>
        !searchValue ||
        item.name.toLowerCase().includes(searchValue.toLowerCase())
    );

    // 再排序：先置顶的，再按名称
    return [...filtered].sort((a, b) => {
      // 置顶排在前面
      if ((a.isPinned && b.isPinned) || (!a.isPinned && !b.isPinned)) {
        return a.name.localeCompare(b.name);
      }
      return a.isPinned ? -1 : 1;
    });
  }, [testPointsData, searchValue]);

  return (
    <Card
      className={styles.card}
      title={
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <div style={{ display: "flex", alignItems: "center" }}>
            <Title level={4} style={{ margin: 0 }}>
              <FormattedMessage
                id="pages.blog.testPoints"
                defaultMessage="测试要点"
              />
            </Title>
            <Tooltip
              title={
                <FormattedMessage
                  id="pages.blog.addTestPoint"
                  defaultMessage="添加测试要点"
                />
              }
            >
              <Button
                type="primary"
                shape="circle"
                icon={<PlusOutlined />}
                size="small"
                style={{ marginLeft: 8 }}
                onClick={onAddTestPoint}
              />
            </Tooltip>
          </div>
          <Search
            placeholder="搜索测试要点"
            allowClear
            onSearch={handleSearch}
            onChange={(e) => handleSearch(e.target.value)}
            style={{ width: 200 }}
            className={styles.searchInput}
          />
        </div>
      }
      extra={null}
      bodyStyle={{
        height: "calc(100vh - 250px)",
        overflow: "auto",
        overflowX: "hidden",
        padding: "0 12px 0 0",
      }}
    >
      {filteredAndSortedTestPoints.length === 0 ? (
        <div className={styles.emptyContainer}>
          <ExperimentOutlined style={{ fontSize: 48, opacity: 0.3 }} />
          <p>
            <FormattedMessage
              id="pages.blog.noTestPoints"
              defaultMessage="暂无测试要点"
            />
          </p>
        </div>
      ) : (
        <div
          style={{ height: "100%", position: "relative", paddingRight: "6px" }}
        >
          <List
            className={styles.testPointsList}
            itemLayout="horizontal"
            dataSource={filteredAndSortedTestPoints}
            style={{ overflowY: "auto", height: "100%" }}
            renderItem={(testPoint) => (
              <List.Item
                className={`${styles.testPointItem} ${selectedTestPoint === testPoint.testPointId ? styles.selectedTestPointItem : ""}`}
                actions={[
                  <Tooltip
                    key="pin"
                    title={testPoint.isPinned ? "取消置顶" : "置顶"}
                  >
                    <Button
                      type="text"
                      icon={
                        testPoint.isPinned ? (
                          <StarFilled style={{ color: "#faad14" }} />
                        ) : (
                          // <StarOutlined />
                          <VerticalAlignTopOutlined />
                        )
                      }
                      onClick={(e) => {
                        e.stopPropagation();
                        // 检查ID是否存在
                        if (testPoint.testPointId) {
                          console.log("点击置顶按钮:", testPoint);
                          handlePinToggle(
                            testPoint.testPointId,
                            !!testPoint.isPinned
                          );
                        } else {
                          console.error("测试要点ID缺失:", testPoint);
                          message.error("操作失败：测试要点数据不完整");
                        }
                      }}
                    />
                  </Tooltip>,
                ]}
                onClick={() =>
                  editingId !== testPoint.testPointId &&
                  onTestPointClick(testPoint.testPointId)
                }
                onMouseEnter={() => setHoveredId(testPoint.testPointId)}
                onMouseLeave={() => setHoveredId(null)}
              >
                <div className={styles.testPointContent}>
                  {testPoint.isPinned && (
                    <Tag color="gold" className={styles.pinnedTag}>
                      置顶
                    </Tag>
                  )}

                  {editingId === testPoint.testPointId ? (
                    <div
                      className={styles.editingContainer}
                      onClick={(e) => e.stopPropagation()}
                      ref={editingRef}
                    >
                      <Input
                        value={editingName}
                        onChange={(e) => setEditingName(e.target.value)}
                        onPressEnter={saveEditing}
                        autoFocus
                        style={{ flex: 1 }}
                        className={styles.editInput}
                      />
                      <Button
                        type="text"
                        icon={<CheckOutlined style={{ color: "#52c41a" }} />}
                        onClick={saveEditing}
                        style={{ marginLeft: 4 }}
                      />
                      <Button
                        type="text"
                        icon={<CloseOutlined style={{ color: "#ff4d4f" }} />}
                        onClick={cancelEditing}
                      />
                    </div>
                  ) : (
                    <>
                      <div className={styles.nameWithEditButton}>
                        <Text
                          ellipsis={{ tooltip: testPoint.name }}
                          strong={selectedTestPoint === testPoint.testPointId}
                          className={styles.testPointName}
                        >
                          {testPoint.name}
                        </Text>

                        {hoveredId === testPoint.testPointId && !editingId && (
                          <Button
                            type="text"
                            icon={<EditOutlined />}
                            size="small"
                            className={styles.editButton}
                            onClick={(e) => {
                              e.stopPropagation();
                              startEditing(
                                testPoint.testPointId,
                                testPoint.name
                              );
                            }}
                          />
                        )}
                      </div>
                    </>
                  )}
                </div>
              </List.Item>
            )}
          />
        </div>
      )}
    </Card>
  );
};

export default TestPoints;
