import React from "react";
import { <PERSON>, <PERSON>u, Typo<PERSON>, Button, Tooltip } from "antd";
import { PlusOutlined, InfoCircleOutlined } from "@ant-design/icons";
import { FormattedMessage } from "umi";
import styles from "../style.less";

const { Title } = Typography;

export interface ModuleItemProps {
  moduleId: string;
  name: string;
  icon?: string;
  description?: string;
}

interface MainModulesProps {
  modules: ModuleItemProps[];
  selectedModule: string | null;
  onModuleClick: (moduleId: string) => void;
  onAddModule: () => void;
}

const MainModules: React.FC<MainModulesProps> = ({
  modules,
  selectedModule,
  onModuleClick,
  onAddModule,
}) => {
  return (
    <Card
      className={styles.card}
      title={
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <Title level={4} style={{ margin: 0 }}>
            <FormattedMessage
              id="pages.blog.testModules"
              defaultMessage="测试模块"
            />
          </Title>
          <Tooltip
            title={
              <FormattedMessage
                id="pages.blog.addModule"
                defaultMessage="添加模块"
              />
            }
          >
            <Button
              type="primary"
              shape="circle"
              icon={<PlusOutlined />}
              size="small"
              onClick={onAddModule}
            />
          </Tooltip>
        </div>
      }
    >
      <div
        className={styles.modulesContainer}
        style={{
          height: "calc(100vh - 250px)",
          overflow: "auto",
          overflowX: "hidden",
        }}
      >
        <Menu
          mode="inline"
          selectedKeys={selectedModule ? [selectedModule] : []}
          style={{ border: "none", height: "100%", paddingRight: "6px" }}
        >
          {modules.map((module) => (
            <Tooltip
              key={module.moduleId}
              title={module.description ? module.description : null}
              placement="right"
              mouseEnterDelay={0.5}
            >
              <Menu.Item
                key={module.moduleId}
                onClick={() => onModuleClick(module.moduleId)}
                className={`${styles.moduleItem} ${selectedModule === module.moduleId ? styles.selectedModule : ""}`}
                style={{
                  borderRadius: "8px",
                  margin: "8px 0",
                  padding: "12px 16px",
                  overflow: "hidden",
                  position: "relative",
                }}
              >
                {selectedModule === module.moduleId && (
                  <div
                    className={styles.highlightBar}
                    style={{
                      position: "absolute",
                      top: 0,
                      left: 0,
                      bottom: 0,
                      width: "4px",
                      backgroundColor: "var(--ant-primary-color)",
                      borderRadius: "4px",
                    }}
                  />
                )}
                <div
                  style={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "space-between",
                  }}
                >
                  <span>{module.name}</span>
                  {module.description && (
                    <InfoCircleOutlined
                      style={{
                        fontSize: "12px",
                        color: "rgba(0, 0, 0, 0.45)",
                        marginLeft: "4px",
                        visibility:
                          selectedModule === module.moduleId
                            ? "visible"
                            : "hidden",
                      }}
                    />
                  )}
                </div>
              </Menu.Item>
            </Tooltip>
          ))}
        </Menu>
      </div>
    </Card>
  );
};

export default MainModules;
