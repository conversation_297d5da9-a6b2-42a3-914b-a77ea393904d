import React, { useState, useRef, useEffect } from "react";
import {
  Card,
  Input,
  Button,
  Typography,
  Avatar,
  Empty,
  List,
  Divider,
  Comment,
  Tooltip,
  message,
  Badge,
} from "antd";
import {
  SendOutlined,
  UserOutlined,
  CommentOutlined,
  LikeOutlined,
  LikeFilled,
  CloseCircleOutlined,
} from "@ant-design/icons";
import { FormattedMessage } from "umi";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import "dayjs/locale/zh-cn";
import { addComment, likePost } from "../service";
import styles from "../style.less";

dayjs.extend(relativeTime);
dayjs.locale("zh-cn");

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;

export interface ForumPostProps {
  id: string;
  content: string;
  author: string;
  createTime: string;
  testPointId: string;
  likes?: number;
  isLiked?: boolean;
  comments?: CommentProps[];
}

export interface CommentProps {
  id: string;
  content: string;
  author: string;
  createTime: string;
  postId: string;
  parentId?: string;
}

interface ForumContentProps {
  posts: ForumPostProps[];
  loading: boolean;
  testPointName: string;
  onPostSubmit: (content: string) => void;
}

const ForumContent: React.FC<ForumContentProps> = ({
  posts,
  loading,
  testPointName,
  onPostSubmit,
}) => {
  const [postContent, setPostContent] = useState<string>("");
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [commentContent, setCommentContent] = useState<string>("");
  const [replyTo, setReplyTo] = useState<{
    postId: string;
    commentId?: string;
    author: string;
  } | null>(null);
  const [expandedComments, setExpandedComments] = useState<Set<string>>(
    new Set()
  );
  const [localPosts, setLocalPosts] = useState<ForumPostProps[]>(posts);

  // 引用评论输入框和卡片容器
  const commentInputRef = useRef<HTMLDivElement>(null);
  const cardRef = useRef<HTMLDivElement>(null);

  // 当posts属性变化时，更新本地状态
  React.useEffect(() => {
    setLocalPosts(posts);
  }, [posts]);

  // 添加点击事件监听，实现点击其他区域取消评论编辑状态
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        replyTo &&
        commentInputRef.current &&
        !commentInputRef.current.contains(event.target as Node) &&
        event.target instanceof Node &&
        cardRef.current?.contains(event.target)
      ) {
        // 如果有评论内容，则提交评论
        if (commentContent.trim()) {
          handleSubmitComment();
        } else {
          // 否则取消回复状态
          handleCancelReply();
        }
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [replyTo, commentContent]);

  // 提交新帖子
  const handleSubmitPost = async () => {
    if (!postContent.trim()) return;

    setSubmitting(true);
    try {
      await onPostSubmit(postContent);
      setPostContent("");
    } finally {
      setSubmitting(false);
    }
  };

  // 点赞/取消点赞帖子
  const handleLikePost = async (postId: string, isLiked: boolean) => {
    try {
      const response = await likePost(postId, !isLiked);
      if (response.code === 200) {
        // 更新本地帖子状态
        setLocalPosts((prevPosts) =>
          prevPosts.map((post) =>
            post.id === postId
              ? {
                  ...post,
                  likes: response.data.likes,
                  isLiked: response.data.isLiked,
                }
              : post
          )
        );
      }
    } catch (error) {
      message.error("操作失败，请重试");
    }
  };

  // 提交评论
  const handleSubmitComment = async () => {
    if (!replyTo || !commentContent.trim()) return;

    try {
      setSubmitting(true);
      const response = await addComment({
        content: commentContent,
        postId: replyTo.postId,
        parentId: replyTo.commentId,
      });

      if (response.code === 200) {
        // 更新本地帖子状态
        setLocalPosts((prevPosts) =>
          prevPosts.map((post) => {
            if (post.id === replyTo.postId) {
              // 添加新评论到评论列表
              const updatedComments = [...(post.comments || []), response.data];
              return {
                ...post,
                comments: updatedComments,
              };
            }
            return post;
          })
        );
        message.success("评论成功");
        // 重置评论表单
        setCommentContent("");
        setReplyTo(null);
      }
    } catch (error) {
      message.error("评论失败，请重试");
    } finally {
      setSubmitting(false);
    }
  };

  // 展开/折叠评论区
  const toggleComments = (postId: string) => {
    setExpandedComments((prevSet) => {
      const newSet = new Set(prevSet);
      if (newSet.has(postId)) {
        newSet.delete(postId);
      } else {
        newSet.add(postId);
      }
      return newSet;
    });
  };

  // 回复评论
  const handleReply = (postId: string, commentId: string, author: string) => {
    setReplyTo({ postId, commentId, author });
    setCommentContent(`@${author} `);
    // 确保评论区展开
    setExpandedComments((prevSet) => {
      const newSet = new Set(prevSet);
      newSet.add(postId);
      return newSet;
    });
  };

  // 回复帖子
  const handleReplyToPost = (postId: string, author: string) => {
    setReplyTo({ postId, author });
    setCommentContent("");
    // 确保评论区展开
    setExpandedComments((prevSet) => {
      const newSet = new Set(prevSet);
      newSet.add(postId);
      return newSet;
    });
  };

  // 取消回复
  const handleCancelReply = () => {
    setReplyTo(null);
    setCommentContent("");
  };

  // 获取父级评论的作者
  const getParentCommentAuthor = (post: ForumPostProps, parentId?: string) => {
    if (!parentId || !post.comments) return null;
    return (
      post.comments.find((comment) => comment.id === parentId)?.author || null
    );
  };

  // 渲染帖子评论
  const renderComments = (post: ForumPostProps) => {
    if (!post.comments || post.comments.length === 0) {
      return (
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description="暂无评论，快来添加第一条评论吧"
        />
      );
    }

    return (
      <div className={styles.commentsList}>
        {post.comments.map((comment) => {
          const isReply = !!comment.parentId;
          const parentAuthor = getParentCommentAuthor(post, comment.parentId);

          return (
            <div
              key={comment.id}
              className={`${styles.commentItem} ${isReply ? styles.replyComment : ""}`}
            >
              <div>
                <span className={styles.commentAuthor}>{comment.author}</span>
                <Tooltip
                  title={dayjs(comment.createTime).format(
                    "YYYY-MM-DD HH:mm:ss"
                  )}
                >
                  <span className={styles.commentTime}>
                    {dayjs(comment.createTime).fromNow()}
                  </span>
                </Tooltip>
              </div>

              {parentAuthor && (
                <div className={styles.replyTarget}>回复 @{parentAuthor}:</div>
              )}

              <div className={styles.commentContent}>{comment.content}</div>

              <div className={styles.commentActions}>
                <span
                  onClick={() =>
                    handleReply(post.id, comment.id, comment.author)
                  }
                >
                  回复
                </span>
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  return (
    <Card
      className={styles.card}
      title={
        <div style={{ display: "flex", alignItems: "center" }}>
          <Title level={4} style={{ margin: 0 }}>
            <FormattedMessage
              id="pages.blog.forumContent"
              defaultMessage="测试论坛"
            />
            {testPointName && ` - ${testPointName}`}
          </Title>
        </div>
      }
      ref={cardRef}
    >
      {/* 发布表单 */}
      <div className={styles.postForm}>
        <TextArea
          value={postContent}
          onChange={(e) => setPostContent(e.target.value)}
          placeholder="分享你的测试经验和心得..."
          autoSize={{ minRows: 3, maxRows: 6 }}
          style={{ marginBottom: 16, borderRadius: 8 }}
        />
        <div style={{ textAlign: "right" }}>
          <Button
            type="primary"
            icon={<SendOutlined />}
            loading={submitting}
            onClick={handleSubmitPost}
            disabled={!postContent.trim()}
            className={styles.roundButton}
          >
            <FormattedMessage
              id="pages.blog.submitPost"
              defaultMessage="发布"
            />
          </Button>
        </div>
      </div>

      <Divider orientation="left">
        <FormattedMessage id="pages.blog.allPosts" defaultMessage="详细要点如下：" />
      </Divider>

      {/* 帖子列表 */}
      {localPosts.length === 0 ? (
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description={
            <span>
              <FormattedMessage
                id="pages.blog.noPosts"
                defaultMessage="暂无帖子，快来分享你的经验吧！"
              />
            </span>
          }
        />
      ) : (
        <div className={styles.postsContainer}>
          <List
            itemLayout="vertical"
            dataSource={localPosts}
            renderItem={(post) => (
              <div key={post.id} className={styles.postItemWrapper}>
                <Comment
                  className={styles.postItem}
                  author={<Text strong>{post.author}</Text>}
                  avatar={<Avatar icon={<UserOutlined />} />}
                  content={
                    <Paragraph
                      ellipsis={{ rows: 3, expandable: true, symbol: "展开" }}
                      className={styles.postContent}
                    >
                      {post.content}
                    </Paragraph>
                  }
                  datetime={
                    <Tooltip
                      title={dayjs(post.createTime).format(
                        "YYYY-MM-DD HH:mm:ss"
                      )}
                    >
                      <span>{dayjs(post.createTime).fromNow()}</span>
                    </Tooltip>
                  }
                  actions={[
                    <div key="actions" className={styles.postActions}>
                      <span
                        onClick={() =>
                          handleLikePost(post.id, post.isLiked || false)
                        }
                        className={`${styles.likeButton} ${post.isLiked ? styles.liked : ""}`}
                      >
                        {post.isLiked ? <LikeFilled /> : <LikeOutlined />}
                        <span className={styles.likeCount}>
                          {post.likes || 0}
                        </span>
                      </span>

                      <Badge
                        count={post.comments?.length || 0}
                        size="small"
                        className={styles.commentBadge}
                        offset={[2, 0]}
                      >
                        <span onClick={() => toggleComments(post.id)}>
                          <CommentOutlined />
                          <span>评论</span>
                        </span>
                      </Badge>
                    </div>,
                  ]}
                >
                  {/* 评论区 */}
                  {expandedComments.has(post.id) && (
                    <div className={styles.commentsContainer}>
                      {renderComments(post)}

                      {/* 评论表单 */}
                      <div className={styles.replyForm}>
                        {replyTo && replyTo.postId === post.id ? (
                          <div
                            style={{ position: "relative", width: "100%" }}
                            ref={commentInputRef}
                          >
                            {replyTo.commentId && (
                              <div className={styles.replyTarget}>
                                回复 @{replyTo.author}:
                                <CloseCircleOutlined
                                  onClick={handleCancelReply}
                                  style={{ marginLeft: 8, cursor: "pointer" }}
                                />
                              </div>
                            )}
                            <div className={styles.commentEditorContainer}>
                              <TextArea
                                value={commentContent}
                                onChange={(e) =>
                                  setCommentContent(e.target.value)
                                }
                                placeholder="写下你的评论..."
                                onPressEnter={handleSubmitComment}
                                className={styles.commentTextArea}
                                autoSize={{ minRows: 2, maxRows: 4 }}
                              />
                              <div className={styles.sendButtonContainer}>
                                <Button
                                  type="primary"
                                  icon={<SendOutlined />}
                                  loading={submitting}
                                  onClick={handleSubmitComment}
                                  disabled={!commentContent.trim()}
                                  className={styles.roundButton}
                                >
                                  发送
                                </Button>
                              </div>
                            </div>
                          </div>
                        ) : (
                          <Input
                            placeholder="写下你的评论..."
                            onClick={() =>
                              handleReplyToPost(post.id, post.author)
                            }
                            className={styles.roundOuterInput}
                          />
                        )}
                      </div>
                    </div>
                  )}
                </Comment>
              </div>
            )}
          />
        </div>
      )}
    </Card>
  );
};

export default ForumContent;
