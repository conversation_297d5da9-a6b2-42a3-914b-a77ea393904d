import { request } from 'umi';

// API 接口响应类型
interface ResponseType<T = any> {
  code: number;
  msg: string;
  data: T;
}

// API 命名空间
const API_NAMESPACE = '/api/blog';

// 测试模块类型定义
export interface ModuleItem {
  moduleId: string;
  name: string;
  description?: string;
  icon?: string;
  orderNum?: number;
  status?: string;
  delFlag?: string;
  createBy?: string;
  createTime?: string;
  updateBy?: string;
  updateTime?: string;
  remark?: string;
}

// 测试要点类型定义
export interface TestPointItem {
  testPointId: string;  // 修改为testPointId与后端一致
  name: string;
  moduleId: string;
  isPinned?: boolean | number; // 支持后端返回的数字类型（1表示置顶，0表示不置顶）
  description?: string;
  status?: string;
  delFlag?: string;
  orderNum?: number;
  createBy?: string;
  createTime?: string;
  updateBy?: string;
  updateTime?: string | null;
  remark?: string;
  params?: Record<string, any>;
  searchValue?: string | null;
}

// 论坛帖子类型定义
export interface ForumPost {
  id: string;
  content: string;
  author: string;
  createTime: string;
  testPointId: string;
  likes?: number;
  isLiked?: boolean;
  comments?: Comment[];
}

// 评论类型定义
export interface Comment {
  id: string;
  content: string;
  author: string;
  createTime: string;
  postId: string;
  parentId?: string;
}

// 获取测试模块列表
export async function getModules(): Promise<ResponseType<ModuleItem[]>> {
  // 实际项目中应替换为真实API请求
  return request(`${API_NAMESPACE}/modules`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });

  // 模拟响应数据
  // const mockData = [
  //   { moduleId: 'module-1', name: '账务类测试' },
  //   { moduleId: 'module-2', name: '事务类测试' },
  //   { moduleId: 'module-3', name: '冲正类测试' },
  //   { moduleId: 'module-4', name: '查询类测试' },
  //   { moduleId: 'module-5', name: '补偿类测试' },
  //   { moduleId: 'module-6', name: '批转联类测试' },
  //   { moduleId: 'module-7', name: '日终类测试' },
  //   { moduleId: 'module-8', name: '数据下档/数据同步测试' },
  //   { moduleId: 'module-9', name: '其他测试' },
  // ];

  // return new Promise((resolve) => {
  //   setTimeout(() => {
  //     resolve({
  //       code: 200,
  //       msg: 'success',
  //       data: mockData,
  //     });
  //   }, 300);
  // });
}

// 获取测试要点列表
export async function getTestPoints(moduleId: string): Promise<ResponseType<TestPointItem[]>> {
  // 实际项目中应替换为真实API请求
  return request(`${API_NAMESPACE}/testPoints`, {
    method: 'GET',
    params: { moduleId },
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });

  // 模拟响应数据
//   const mockDataMap: Record<string, TestPointItem[]> = {
//     '1': [
//       { id: 'tp-1-1', name: '账务测试要点：余额查询', moduleId: '1' },
//       { id: 'tp-1-2', name: '账务测试要点：交易明细查询', moduleId: '1' },
//       { id: 'tp-1-3', name: '账务测试要点：余额不足处理', moduleId: '1' },
//       { id: 'tp-1-4', name: '账务测试要点：跨账户交易', moduleId: '1' },
//       { id: 'tp-1-5', name: '账务测试要点：对账流程', moduleId: '1' },
//       { id: 'tp-1-6', name: '账务测试要点：账户冻结解冻', moduleId: '1' },
//       { id: 'tp-1-7', name: '账务测试要点：利息计算', moduleId: '1' },
//       { id: 'tp-1-8', name: '账务测试要点：大额交易限制', moduleId: '1' },
//       { id: 'tp-1-9', name: '账务测试要点：账户权限管理', moduleId: '1' },
//       { id: 'tp-1-10', name: '账务测试要点：异常账户监控', moduleId: '1' },
//       { id: 'tp-1-11', name: '账务测试要点：跨行交易手续费', moduleId: '1' },
//       { id: 'tp-1-12', name: '账务测试要点：支付限额设置', moduleId: '1' },
//       { id: 'tp-1-13', name: '账务测试要点：定期结算处理', moduleId: '1' },
//       { id: 'tp-1-14', name: '账务测试要点：余额变动通知', moduleId: '1' },
//       { id: 'tp-1-15', name: '账务测试要点：透支额度管理', moduleId: '1' },
//       { id: 'tp-1-16', name: '账务测试要点：账户状态转换', moduleId: '1' },
//       { id: 'tp-1-17', name: '账务测试要点：批量账务处理', moduleId: '1' },
//       { id: 'tp-1-18', name: '账务测试要点：账户注销流程', moduleId: '1' },
//       { id: 'tp-1-19', name: '账务测试要点：资金归集处理', moduleId: '1' },
//       { id: 'tp-1-20', name: '账务测试要点：账务预警机制', moduleId: '1' },
//       { id: 'tp-1-21', name: '账务测试要点：账务报表生成', moduleId: '1' },
//       { id: 'tp-1-22', name: '账务测试要点：账务调节处理', moduleId: '1' },
//       { id: 'tp-1-23', name: '账务测试要点：费用自动扣收', moduleId: '1' },
//       { id: 'tp-1-24', name: '账务测试要点：多币种账户管理', moduleId: '1' },
//       { id: 'tp-1-25', name: '账务测试要点：贷款利息计算', moduleId: '1' },
//       { id: 'tp-1-26', name: '账务测试要点：账户分级管理', moduleId: '1' },
//     ],
//     'module-2': [
//       { id: 'tp-2-1', name: '事务测试要点：事务一致性', moduleId: 'module-2' },
//       { id: 'tp-2-2', name: '事务测试要点：事务超时处理', moduleId: 'module-2' },
//       { id: 'tp-2-3', name: '事务测试要点：事务并发控制', moduleId: 'module-2' },
//       { id: 'tp-2-4', name: '事务测试要点：数据隔离级别', moduleId: 'module-2' },
//       { id: 'tp-2-5', name: '事务测试要点：事务回滚机制', moduleId: 'module-2' },
//       { id: 'tp-2-6', name: '事务测试要点：分布式事务', moduleId: 'module-2' },
//       { id: 'tp-2-7', name: '事务测试要点：死锁检测与处理', moduleId: 'module-2' },
//       { id: 'tp-2-8', name: '事务测试要点：长事务性能影响', moduleId: 'module-2' },
//       { id: 'tp-2-9', name: '事务测试要点：事务日志记录', moduleId: 'module-2' },
//       { id: 'tp-2-10', name: '事务测试要点：二阶段提交', moduleId: 'module-2' },
//       { id: 'tp-2-11', name: '事务测试要点：XA事务管理', moduleId: 'module-2' },
//       { id: 'tp-2-12', name: '事务测试要点：嵌套事务处理', moduleId: 'module-2' },
//       { id: 'tp-2-13', name: '事务测试要点：事务补偿机制', moduleId: 'module-2' },
//       { id: 'tp-2-14', name: '事务测试要点：跨系统事务', moduleId: 'module-2' },
//       { id: 'tp-2-15', name: '事务测试要点：TCC事务模型', moduleId: 'module-2' },
//       { id: 'tp-2-16', name: '事务测试要点：SAGA事务模式', moduleId: 'module-2' },
//       { id: 'tp-2-17', name: '事务测试要点：最终一致性验证', moduleId: 'module-2' },
//       { id: 'tp-2-18', name: '事务测试要点：事务监控告警', moduleId: 'module-2' },
//       { id: 'tp-2-19', name: '事务测试要点：本地事务优化', moduleId: 'module-2' },
//       { id: 'tp-2-20', name: '事务测试要点：事务边界划分', moduleId: 'module-2' },
//     ],
//     'module-3': [
//       { id: 'tp-3-1', name: '冲正测试要点：单笔交易冲正', moduleId: 'module-3' },
//       { id: 'tp-3-2', name: '冲正测试要点：批量交易冲正', moduleId: 'module-3' },
//       { id: 'tp-3-3', name: '冲正测试要点：跨渠道交易冲正', moduleId: 'module-3' },
//       { id: 'tp-3-4', name: '冲正测试要点：冲正权限管理', moduleId: 'module-3' },
//       { id: 'tp-3-5', name: '冲正测试要点：冲正审批流程', moduleId: 'module-3' },
//       { id: 'tp-3-6', name: '冲正测试要点：冲正时间限制', moduleId: 'module-3' },
//       { id: 'tp-3-7', name: '冲正测试要点：冲正影响分析', moduleId: 'module-3' },
//       { id: 'tp-3-8', name: '冲正测试要点：冲正通知机制', moduleId: 'module-3' },
//       { id: 'tp-3-9', name: '冲正测试要点：异常冲正处理', moduleId: 'module-3' },
//       { id: 'tp-3-10', name: '冲正测试要点：冲正操作日志', moduleId: 'module-3' },
//       { id: 'tp-3-11', name: '冲正测试要点：冲正数据校验', moduleId: 'module-3' },
//       { id: 'tp-3-12', name: '冲正测试要点：冲正失败重试', moduleId: 'module-3' },
//       { id: 'tp-3-13', name: '冲正测试要点：自动冲正规则', moduleId: 'module-3' },
//       { id: 'tp-3-14', name: '冲正测试要点：冲正交易记录', moduleId: 'module-3' },
//       { id: 'tp-3-15', name: '冲正测试要点：冲正业务限制', moduleId: 'module-3' },
//       { id: 'tp-3-16', name: '冲正测试要点：冲正状态跟踪', moduleId: 'module-3' },
//       { id: 'tp-3-17', name: '冲正测试要点：部分冲正处理', moduleId: 'module-3' },
//       { id: 'tp-3-18', name: '冲正测试要点：跨日冲正机制', moduleId: 'module-3' },
//     ],
//     'module-4': [
//       { id: 'tp-4-1', name: '查询测试要点：单表查询性能', moduleId: 'module-4' },
//       { id: 'tp-4-2', name: '查询测试要点：多表关联查询', moduleId: 'module-4' },
//       { id: 'tp-4-3', name: '查询测试要点：分页查询机制', moduleId: 'module-4' },
//       { id: 'tp-4-4', name: '查询测试要点：高级检索条件', moduleId: 'module-4' },
//       { id: 'tp-4-5', name: '查询测试要点：历史数据查询', moduleId: 'module-4' },
//       { id: 'tp-4-6', name: '查询测试要点：导出查询结果', moduleId: 'module-4' },
//       { id: 'tp-4-7', name: '查询测试要点：查询结果排序', moduleId: 'module-4' },
//       { id: 'tp-4-8', name: '查询测试要点：特殊字符处理', moduleId: 'module-4' },
//       { id: 'tp-4-9', name: '查询测试要点：模糊查询性能', moduleId: 'module-4' },
//       { id: 'tp-4-10', name: '查询测试要点：返回字段过滤', moduleId: 'module-4' },
//       { id: 'tp-4-11', name: '查询测试要点：大数据量查询', moduleId: 'module-4' },
//       { id: 'tp-4-12', name: '查询测试要点：实时查询与缓存查询', moduleId: 'module-4' },
//       { id: 'tp-4-13', name: '查询测试要点：统计聚合查询', moduleId: 'module-4' },
//       { id: 'tp-4-14', name: '查询测试要点：条件组合查询', moduleId: 'module-4' },
//       { id: 'tp-4-15', name: '查询测试要点：查询结果格式化', moduleId: 'module-4' },
//       { id: 'tp-4-16', name: '查询测试要点：复杂条件解析', moduleId: 'module-4' },
//       { id: 'tp-4-17', name: '查询测试要点：范围查询优化', moduleId: 'module-4' },
//       { id: 'tp-4-18', name: '查询测试要点：多字段组合索引', moduleId: 'module-4' },
//       { id: 'tp-4-19', name: '查询测试要点：分布式查询', moduleId: 'module-4' },
//       { id: 'tp-4-20', name: '查询测试要点：查询权限控制', moduleId: 'module-4' },
//       { id: 'tp-4-21', name: '查询测试要点：全文索引查询', moduleId: 'module-4' },
//       { id: 'tp-4-22', name: '查询测试要点：时间序列查询', moduleId: 'module-4' },
//     ],
//     'module-5': [
//       { id: 'tp-5-1', name: '补偿测试要点：失败交易补偿', moduleId: 'module-5' },
//       { id: 'tp-5-2', name: '补偿测试要点：定时补偿任务', moduleId: 'module-5' },
//       { id: 'tp-5-3', name: '补偿测试要点：手动补偿流程', moduleId: 'module-5' },
//       { id: 'tp-5-4', name: '补偿测试要点：补偿事务一致性', moduleId: 'module-5' },
//       { id: 'tp-5-5', name: '补偿测试要点：补偿通知机制', moduleId: 'module-5' },
//       { id: 'tp-5-6', name: '补偿测试要点：补偿结果验证', moduleId: 'module-5' },
//       { id: 'tp-5-7', name: '补偿测试要点：补偿重试机制', moduleId: 'module-5' },
//       { id: 'tp-5-8', name: '补偿测试要点：多渠道补偿', moduleId: 'module-5' },
//       { id: 'tp-5-9', name: '补偿测试要点：补偿优先级管理', moduleId: 'module-5' },
//       { id: 'tp-5-10', name: '补偿测试要点：补偿触发条件', moduleId: 'module-5' },
//       { id: 'tp-5-11', name: '补偿测试要点：补偿流程可视化', moduleId: 'module-5' },
//       { id: 'tp-5-12', name: '补偿测试要点：补偿操作审计', moduleId: 'module-5' },
//       { id: 'tp-5-13', name: '补偿测试要点：补偿状态查询', moduleId: 'module-5' },
//       { id: 'tp-5-14', name: '补偿测试要点：自动化补偿策略', moduleId: 'module-5' },
//       { id: 'tp-5-15', name: '补偿测试要点：补偿任务监控', moduleId: 'module-5' },
//       { id: 'tp-5-16', name: '补偿测试要点：补偿异常处理', moduleId: 'module-5' },
//       { id: 'tp-5-17', name: '补偿测试要点：补偿任务调度', moduleId: 'module-5' },
//       { id: 'tp-5-18', name: '补偿测试要点：补偿数据留存', moduleId: 'module-5' },
//     ],
//     'module-6': [
//       { id: 'tp-6-1', name: '批转联测试要点：批量转账性能', moduleId: 'module-6' },
//       { id: 'tp-6-2', name: '批转联测试要点：批量处理策略', moduleId: 'module-6' },
//       { id: 'tp-6-3', name: '批转联测试要点：联机转账安全', moduleId: 'module-6' },
//       { id: 'tp-6-4', name: '批转联测试要点：并发处理能力', moduleId: 'module-6' },
//       { id: 'tp-6-5', name: '批转联测试要点：批量失败回滚', moduleId: 'module-6' },
//       { id: 'tp-6-6', name: '批转联测试要点：批量任务监控', moduleId: 'module-6' },
//       { id: 'tp-6-7', name: '批转联测试要点：联机交易超时', moduleId: 'module-6' },
//       { id: 'tp-6-8', name: '批转联测试要点：批量数据验证', moduleId: 'module-6' },
//       { id: 'tp-6-9', name: '批转联测试要点：大规模批处理', moduleId: 'module-6' },
//       { id: 'tp-6-10', name: '批转联测试要点：批量文件导入', moduleId: 'module-6' },
//       { id: 'tp-6-11', name: '批转联测试要点：批量处理日志', moduleId: 'module-6' },
//       { id: 'tp-6-12', name: '批转联测试要点：批量任务优先级', moduleId: 'module-6' },
//       { id: 'tp-6-13', name: '批转联测试要点：批量处理资源控制', moduleId: 'module-6' },
//       { id: 'tp-6-14', name: '批转联测试要点：批处理节点间同步', moduleId: 'module-6' },
//       { id: 'tp-6-15', name: '批转联测试要点：批处理容错机制', moduleId: 'module-6' },
//       { id: 'tp-6-16', name: '批转联测试要点：批量处理审计', moduleId: 'module-6' },
//       { id: 'tp-6-17', name: '批转联测试要点：批处理事务管理', moduleId: 'module-6' },
//       { id: 'tp-6-18', name: '批转联测试要点：批处理切片机制', moduleId: 'module-6' },
//       { id: 'tp-6-19', name: '批转联测试要点：联机与批处理数据一致性', moduleId: 'module-6' },
//     ],
//     'module-7': [
//       { id: 'tp-7-1', name: '日终测试要点：日终清算流程', moduleId: 'module-7' },
//       { id: 'tp-7-2', name: '日终测试要点：账务核对', moduleId: 'module-7' },
//       { id: 'tp-7-3', name: '日终测试要点：日终报表生成', moduleId: 'module-7' },
//       { id: 'tp-7-4', name: '日终测试要点：业务暂停机制', moduleId: 'module-7' },
//       { id: 'tp-7-5', name: '日终测试要点：日终处理性能', moduleId: 'module-7' },
//       { id: 'tp-7-6', name: '日终测试要点：异常处理流程', moduleId: 'module-7' },
//       { id: 'tp-7-7', name: '日终测试要点：日终任务依赖', moduleId: 'module-7' },
//       { id: 'tp-7-8', name: '日终测试要点：手工平账处理', moduleId: 'module-7' },
//       { id: 'tp-7-9', name: '日终测试要点：日终数据备份', moduleId: 'module-7' },
//       { id: 'tp-7-10', name: '日终测试要点：跨天业务处理', moduleId: 'module-7' },
//       { id: 'tp-7-11', name: '日终测试要点：日终通知机制', moduleId: 'module-7' },
//       { id: 'tp-7-12', name: '日终测试要点：日终任务重试', moduleId: 'module-7' },
//       { id: 'tp-7-13', name: '日终测试要点：日终预警策略', moduleId: 'module-7' },
//       { id: 'tp-7-14', name: '日终测试要点：日终切换点控制', moduleId: 'module-7' },
//       { id: 'tp-7-15', name: '日终测试要点：日终任务调度', moduleId: 'module-7' },
//       { id: 'tp-7-16', name: '日终测试要点：日终操作审计', moduleId: 'module-7' },
//       { id: 'tp-7-17', name: '日终测试要点：日终状态监控', moduleId: 'module-7' },
//       { id: 'tp-7-18', name: '日终测试要点：日终失败恢复', moduleId: 'module-7' },
//       { id: 'tp-7-19', name: '日终测试要点：日终任务分组', moduleId: 'module-7' },
//       { id: 'tp-7-20', name: '日终测试要点：日终进度跟踪', moduleId: 'module-7' },
//     ],
//     'module-8': [
//       { id: 'tp-8-1', name: '数据同步测试要点：增量同步机制', moduleId: 'module-8' },
//       { id: 'tp-8-2', name: '数据同步测试要点：全量同步效率', moduleId: 'module-8' },
//       { id: 'tp-8-3', name: '数据同步测试要点：同步异常处理', moduleId: 'module-8' },
//       { id: 'tp-8-4', name: '数据同步测试要点：同步冲突解决', moduleId: 'module-8' },
//       { id: 'tp-8-5', name: '数据同步测试要点：下档归档策略', moduleId: 'module-8' },
//       { id: 'tp-8-6', name: '数据同步测试要点：归档数据恢复', moduleId: 'module-8' },
//       { id: 'tp-8-7', name: '数据同步测试要点：数据一致性校验', moduleId: 'module-8' },
//       { id: 'tp-8-8', name: '数据同步测试要点：同步性能监控', moduleId: 'module-8' },
//       { id: 'tp-8-9', name: '数据同步测试要点：跨系统同步', moduleId: 'module-8' },
//       { id: 'tp-8-10', name: '数据同步测试要点：同步过程审计', moduleId: 'module-8' },
//       { id: 'tp-8-11', name: '数据同步测试要点：同步调度机制', moduleId: 'module-8' },
//       { id: 'tp-8-12', name: '数据同步测试要点：同步优先级管理', moduleId: 'module-8' },
//       { id: 'tp-8-13', name: '数据同步测试要点：同步数据转换', moduleId: 'module-8' },
//       { id: 'tp-8-14', name: '数据同步测试要点：数据同步安全', moduleId: 'module-8' },
//       { id: 'tp-8-15', name: '数据同步测试要点：同步节点管理', moduleId: 'module-8' },
//       { id: 'tp-8-16', name: '数据同步测试要点：数据变更捕获', moduleId: 'module-8' },
//       { id: 'tp-8-17', name: '数据同步测试要点：大量数据同步性能', moduleId: 'module-8' },
//       { id: 'tp-8-18', name: '数据同步测试要点：实时同步机制', moduleId: 'module-8' },
//       { id: 'tp-8-19', name: '数据同步测试要点：批量同步执行', moduleId: 'module-8' },
//       { id: 'tp-8-20', name: '数据同步测试要点：同步配置管理', moduleId: 'module-8' },
//       { id: 'tp-8-21', name: '数据同步测试要点：同步失败重试', moduleId: 'module-8' },
//     ],
//     'module-9': [
//       { id: 'tp-9-1', name: '其他测试要点：系统可用性', moduleId: 'module-9' },
//       { id: 'tp-9-2', name: '其他测试要点：安全合规检查', moduleId: 'module-9' },
//       { id: 'tp-9-3', name: '其他测试要点：接口兼容性', moduleId: 'module-9' },
//       { id: 'tp-9-4', name: '其他测试要点：灾备切换测试', moduleId: 'module-9' },
//       { id: 'tp-9-5', name: '其他测试要点：系统并发能力', moduleId: 'module-9' },
//       { id: 'tp-9-6', name: '其他测试要点：用户体验评估', moduleId: 'module-9' },
//       { id: 'tp-9-7', name: '其他测试要点：监控告警机制', moduleId: 'module-9' },
//       { id: 'tp-9-8', name: '其他测试要点：第三方系统集成', moduleId: 'module-9' },
//       { id: 'tp-9-9', name: '其他测试要点：系统负载测试', moduleId: 'module-9' },
//       { id: 'tp-9-10', name: '其他测试要点：网络连接稳定性', moduleId: 'module-9' },
//       { id: 'tp-9-11', name: '其他测试要点：系统响应时间', moduleId: 'module-9' },
//       { id: 'tp-9-12', name: '其他测试要点：数据库性能优化', moduleId: 'module-9' },
//       { id: 'tp-9-13', name: '其他测试要点：缓存使用效率', moduleId: 'module-9' },
//       { id: 'tp-9-14', name: '其他测试要点：多环境部署测试', moduleId: 'module-9' },
//       { id: 'tp-9-15', name: '其他测试要点：内存泄漏检测', moduleId: 'module-9' },
//       { id: 'tp-9-16', name: '其他测试要点：API接口稳定性', moduleId: 'module-9' },
//       { id: 'tp-9-17', name: '其他测试要点：日志记录完整性', moduleId: 'module-9' },
//       { id: 'tp-9-18', name: '其他测试要点：资源使用监控', moduleId: 'module-9' },
//       { id: 'tp-9-19', name: '其他测试要点：突发流量应对', moduleId: 'module-9' },
//       { id: 'tp-9-20', name: '其他测试要点：系统容灾验证', moduleId: 'module-9' },
//     ],
//   };

//   return new Promise((resolve) => {
//     setTimeout(() => {
//       resolve({
//         code: 200,
//         msg: 'success',
//         data: mockDataMap[moduleId] || [],
//       });
//     }, 300);
//   });
}

// 获取论坛帖子列表
export async function getForumPosts(testPointId: string): Promise<ResponseType<ForumPost[]>> {
  // 实际项目中应替换为真实API请求
  // return request(`${API_NAMESPACE}/forum-posts`, {
  //   method: 'GET',
  //   params: { testPointId },
  // });

  // 生成随机帖子
  const generateRandomPosts = (testPointId: string, count: number): ForumPost[] => {
    const posts: ForumPost[] = [];
    const contents = [
      '这个测试点需要注意系统响应时间，我发现在高并发情况下会有延迟。',
      '我在测试过程中发现一个潜在的问题，当数据量超过1000条时，查询性能会明显下降。',
      '这个功能我已经做了全面的测试，目前没有发现异常情况，运行稳定。',
      '根据我的测试经验，这个模块在处理大量并发请求时需要优化缓存策略。',
      '昨天遇到一个奇怪的bug，但今天重现不了，有遇到类似情况的同事吗？',
      '我建议在这个测试点增加边界值测试和异常处理测试，可以提高代码健壮性。',
      '我对这个功能做了性能测试，TPS可以达到200，符合需求规格。',
      '这个模块的测试要充分考虑数据一致性问题，特别是在分布式环境下。',
      '在测试过程中发现系统对错误输入的处理不够友好，建议优化用户提示。',
      '已经完成了这部分的自动化测试脚本编写，下周可以并入持续集成流程。',
    ];

    const authors = ['张三', '李四', '王五', '赵六', '测试工程师'];
    const commentContents = [
      '完全同意你的观点，这确实是个需要注意的问题。',
      '我也遇到过类似情况，建议检查一下网络连接配置。',
      '这个问题已经在新版本修复了，可以升级试试。',
      '感谢分享，对我们的测试很有帮助！',
      '这种情况可能与服务器负载有关，我们需要进一步分析。',
      '你提到的这个点非常关键，需要列入重点测试项。',
      '我认为还需要考虑极端情况下的处理逻辑。',
      '你的测试方法很有参考价值，学习了。',
      '建议增加这种情况的自动化测试用例。',
      '我们团队也遇到过，最终通过调整缓存策略解决了。'
    ];

    // 当前时间
    const now = new Date();

    for (let i = 0; i < count; i++) {
      const randomContentIndex = Math.floor(Math.random() * contents.length);
      const randomAuthorIndex = Math.floor(Math.random() * authors.length);

      // 随机时间：最近30天内
      const randomDays = Math.floor(Math.random() * 30);
      const randomHours = Math.floor(Math.random() * 24);
      const randomMinutes = Math.floor(Math.random() * 60);

      const createTime = new Date(now);
      createTime.setDate(now.getDate() - randomDays);
      createTime.setHours(now.getHours() - randomHours);
      createTime.setMinutes(now.getMinutes() - randomMinutes);

      // 生成随机评论
      const commentCount = Math.floor(Math.random() * 5); // 0-4条评论
      const comments: Comment[] = [];

      for (let j = 0; j < commentCount; j++) {
        const randomCommentContent = commentContents[Math.floor(Math.random() * commentContents.length)];
        const randomCommentAuthor = authors[Math.floor(Math.random() * authors.length)];

        // 评论时间应该晚于帖子创建时间
        const commentTime = new Date(createTime);
        commentTime.setMinutes(commentTime.getMinutes() + Math.floor(Math.random() * 60 * 24)); // 随机增加最多24小时

        const comment: Comment = {
          id: `comment-${testPointId}-${i}-${j}`,
          content: randomCommentContent,
          author: randomCommentAuthor,
          createTime: commentTime.toISOString(),
          postId: `post-${testPointId}-${i}`,
          parentId: j > 0 && Math.random() > 0.7 ? `comment-${testPointId}-${i}-${Math.floor(Math.random() * j)}` : undefined // 30%概率是回复某条评论
        };

        comments.push(comment);
      }

      posts.push({
        id: `post-${testPointId}-${i}`,
        content: contents[randomContentIndex],
        author: authors[randomAuthorIndex],
        createTime: createTime.toISOString(),
        testPointId,
        likes: Math.floor(Math.random() * 15), // 0-14个点赞
        isLiked: Math.random() > 0.7, // 30%概率当前用户已点赞
        comments
      });
    }

    // 按时间排序，从新到旧
    return posts.sort((a, b) => new Date(b.createTime).getTime() - new Date(a.createTime).getTime());
  };

  // 为每个测试点生成随机数量的帖子
  const randomCount = Math.floor(Math.random() * 8) + 3; // 3-10条帖子
  const mockPosts = generateRandomPosts(testPointId, randomCount);

  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        code: 200,
        msg: 'success',
        data: mockPosts,
      });
    }, 300);
  });
}

// 发布新帖子
export async function createForumPost(params: {
  content: string;
  testPointId: string;
}): Promise<ResponseType<ForumPost>> {
  // 实际项目中应替换为真实API请求
  // return request(`${API_NAMESPACE}/forum-posts`, {
  //   method: 'POST',
  //   data: params,
  // });

  // 模拟响应数据
  const newPost: ForumPost = {
    id: `post-${Date.now()}`,
    content: params.content,
    author: '当前用户',
    createTime: new Date().toISOString(),
    testPointId: params.testPointId,
    likes: 0,
    isLiked: false,
    comments: []
  };

  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        code: 200,
        msg: 'success',
        data: newPost,
      });
    }, 300);
  });
}

// 点赞/取消点赞帖子
export async function likePost(postId: string, isLike: boolean): Promise<ResponseType<{ postId: string; likes: number; isLiked: boolean }>> {
  // 实际项目中应替换为真实API请求
  // return request(`${API_NAMESPACE}/forum-posts/${postId}/like`, {
  //   method: 'POST',
  //   data: { isLike },
  // });

  // 模拟响应数据
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        code: 200,
        msg: 'success',
        data: {
          postId,
          likes: Math.floor(Math.random() * 15) + (isLike ? 1 : 0), // 随机返回点赞数
          isLiked: isLike
        },
      });
    }, 300);
  });
}

// 添加评论
export async function addComment(params: {
  content: string;
  postId: string;
  parentId?: string;
}): Promise<ResponseType<Comment>> {
  // 实际项目中应替换为真实API请求
  // return request(`${API_NAMESPACE}/comments`, {
  //   method: 'POST',
  //   data: params,
  // });

  // 模拟响应数据
  const newComment: Comment = {
    id: `comment-${Date.now()}`,
    content: params.content,
    author: '当前用户',
    createTime: new Date().toISOString(),
    postId: params.postId,
    parentId: params.parentId
  };

  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        code: 200,
        msg: 'success',
        data: newComment,
      });
    }, 300);
  });
}

// 创建测试模块
export async function createModule(params: {
  name: string;
  description?: string;
  icon?: string;
}): Promise<ResponseType<ModuleItem>> {
  // 实际项目中应替换为真实API请求
  // return request(`${API_NAMESPACE}/modules`, {
  //   method: 'POST',
  //   data: params,
  // });

  // 模拟响应数据
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        code: 200,
        msg: '创建成功',
        data: {
          moduleId: `module-${Date.now()}`,
          name: params.name,
          icon: params.icon || 'AppstoreOutlined',
        },
      });
    }, 500);
  });
}

// 创建测试要点
export async function createTestPoint(params: {
  name: string;
  description?: string;
  moduleId: string;
}): Promise<ResponseType<TestPointItem>> {
  // 实际项目中应替换为真实API请求
  // return request(`${API_NAMESPACE}/test-points`, {
  //   method: 'POST',
  //   data: params,
  // });

  // 模拟响应数据
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        code: 200,
        msg: '创建成功',
        data: {
          testPointId: `tp-${Date.now()}`,
          name: params.name,
          moduleId: params.moduleId,
        },
      });
    }, 500);
  });
}

// 更新测试要点（例如置顶状态）
export async function updateTestPoint(params: {
  testPointId: string;
  isPinned: boolean;
  moduleId: string;
}): Promise<ResponseType<TestPointItem>> {
  console.log('更新测试要点:', params); // 调试输出参数

  // 由于后端API未完成或有问题，使用模拟数据
  // return new Promise((resolve) => {
  //   setTimeout(() => {
  //     resolve({
  //       code: 200,
  //       msg: '更新成功',
  //       data: {
  //         id: params.id,
  //         name: `测试要点 ${params.id}`,
  //         moduleId: params.moduleId,
  //         isPinned: params.isPinned
  //       },
  //     });
  //   }, 300);
  // });

  // 当后端API完成后，可使用以下代码
  return request(`${API_NAMESPACE}/testPoints/${params.testPointId}`, {
    method: 'PUT',
    data: params,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}