declare namespace API {
  // 测试模块类型
  interface ModuleItem {
    moduleId: string;
    name: string;
    description?: string;
    orderNum?: number;
    status?: string;
    delFlag?: string;
    createBy?: string;
    createTime?: string;
    updateBy?: string;
    updateTime?: string;
    remark?: string;
  }

  // 测试要点类型
  interface TestPointItem {
    testPointId: string; // 修改为testPointId与后端一致
    name: string;
    moduleId: string;
    isPinned?: boolean;
    description?: string;
  }

  // 评论类型
  interface Comment {
    id: string;
    content: string;
    author: string;
    createTime: string;
    postId: string;
    parentId?: string; // 回复某条评论时使用
  }

  // 论坛帖子类型
  interface ForumPost {
    id: string;
    content: string;
    author: string;
    createTime: string;
    testPointId: string;
    likes?: number; // 点赞数
    isLiked?: boolean; // 当前用户是否点赞
    comments?: Comment[]; // 评论列表
  }

  // API 响应类型
  interface ResponseType<T = any> {
    code: number;
    msg: string;
    data: T;
  }

  // 分页查询参数
  interface PageParams {
    current?: number;
    pageSize?: number;
  }

  // 分页结果
  interface PageResult<T = any> {
    list: T[];
    total: number;
    current: number;
    pageSize: number;
  }
}