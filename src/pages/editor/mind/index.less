@import '~antd/es/style/themes/default.less';

.editor {
  display: flex;
  flex: 1;
  flex-direction: column;
  width: 100%;
  height: calc(100vh - 250px);
  background: @descriptions-bg;
}

.editorHd {
  padding: 8px;
  background: @descriptions-bg;
  border: 1px solid @item-active-bg;
}

.editorBd {
  flex: 1;
}

.editorSidebar,
.editorContent {
  display: flex;
  flex-direction: column;
}

.editorContent {
  :global {
    .graph-container canvas {
      vertical-align: middle;
    }
  }
}

.editorSidebar {
  background: @descriptions-bg;
  :global {
    .g6-editor-minimap-container {
      background: none !important;
    }
  }
  &:first-child {
    border-right: 1px solid @item-active-bg;
  }

  &:last-child {
    border-left: 1px solid @item-active-bg;
  }
}

.flow,
.mind,
.koni {
  flex: 1;
}
