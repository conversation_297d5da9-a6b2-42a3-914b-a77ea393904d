@import '~antd/es/style/themes/default.less';

.contextMenu {
  display: none;
  overflow: hidden;
  background: @component-background;
  border-radius: 4px;
  box-shadow: @box-shadow-base;
  .item {
    display: flex;
    align-items: center;
    padding: 5px 12px;
    cursor: pointer;
    transition: all 0.3s;
    user-select: none;

    &:hover {
      background: @select-item-selected-bg;
    }

    .anticon {
      margin-right: 8px;
    }
  }

  :global {
    .disable {
      :local {
        .item {
          color: @disabled-color;
          cursor: auto;

          &:hover {
            background: @item-hover-bg;
          }
        }
      }
    }
  }
}
