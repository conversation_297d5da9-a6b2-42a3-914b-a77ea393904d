import { Card, Form, Input, Space } from 'antd'; // 添加新的组件引入
import { PlusOutlined } from '@ant-design/icons';
import WrapContent from '../../../components/WrapContent';
import type { FormInstance } from 'antd';
import { Button, message, Row, Col, Tabs } from 'antd';
import { useIntl, FormattedMessage, useAccess } from 'umi';
import React, { useState, useRef } from 'react';
import type { ProColumns, ActionType } from '@ant-design/pro-table';
import ProTable from '@ant-design/pro-table';
import type { AccQueryData, AccListParams } from './data.d';
import { accQueryList, exportAccQuery } from './service';
import { getDict } from '../../system/dict/service';
import { useMount } from 'ahooks';

import styles from './style.less';

interface FormValueType {
  mediumTpCd?: string;
  prodNo?: string;
  cardKindCd?: string;
  dcardCategNo?: string;
}

// tabPanel 组件
interface TabPanelProps {
  tableData: AccQueryData[];
  handleExport: (selectedRowsState: AccQueryData[]) => Promise<boolean>;
  tabKey: string;
}
const TabPanel: React.FC<TabPanelProps> = ({ tableData, handleExport, tabKey }) => {
  const actionRef = useRef<ActionType>();
  const [selectedRowsState, setSelectedRows] = useState<AccQueryData[]>([]);

  const columns: ProColumns<AccQueryData>[] = [
    {
      title: <FormattedMessage id="autoTest.accquery.mediumTpCd" defaultMessage="介质类型" />,
      dataIndex: 'mediumTpCd',
    },
    {
      title: <FormattedMessage id="autoTest.accquery.prodNo" defaultMessage="产品编码" />,
      dataIndex: 'prodNo',
    },
    {
      title: <FormattedMessage id="autoTest.accquery.cardKindCd" defaultMessage="卡品种" />,
      dataIndex: 'cardKindCd',
    },
    {
      title: <FormattedMessage id="autoTest.accquery.dcardCategNo" defaultMessage="借记卡类别" />,
      dataIndex: 'dcardCategNo',
    },
    {
      title: <FormattedMessage id="autoTest.accquery.operation" defaultMessage="操作" />,
      dataIndex: 'option',
      width: '220px',
      valueType: 'option',
      render: (_, record) => [
        <Button type="link" size="small" key="resetpwd">
          <FormattedMessage id="autoTest.accquery.operation" defaultMessage="操作" />
        </Button>,
        <Button
          type="link"
          size="small"
          key="edit"
          onClick={() => {
            // 编辑操作
          }}
        >
          <FormattedMessage id="pages.searchTable.edit" defaultMessage="编辑" />
        </Button>,
        <Button type="link" size="small" danger key="batchRemove" hidden={true}>
          <FormattedMessage id="pages.searchTable.delete" defaultMessage="删除" />
        </Button>,
      ],
    },
  ];
  return (
    <ProTable<AccQueryData>
      headerTitle={<FormattedMessage id="pages.searchTable.title" defaultMessage="信息" />}
      actionRef={actionRef}
      rowKey={(record) =>
        `${record.mediumTpCd}_${record.prodNo}_${record.cardKindCd}_${record.dcardCategNo}_${tabKey}`
      }
      key={tabKey}
      search={false}
      toolBarRender={() => [
        <Button type="primary" key="export" onClick={() => handleExport(selectedRowsState)}>
          <PlusOutlined />
          <FormattedMessage id="pages.searchTable.export" defaultMessage="导出" />
        </Button>,
      ]}
      dataSource={tableData}
      columns={columns}
      rowSelection={{
        onChange: (_, selectedRows) => {
          setSelectedRows(selectedRows);
        },
      }}
    />
  );
};

const mediumQueryList: React.FC = () => {
  const [activeTab, setActiveTab] = useState<string>('tab1');
  const [formValues, setFormValues] = useState<FormValueType>({});
  const [allTableData, setAllTableData] = useState<AccQueryData[]>([]);

  const [cardKindCdOptions, setCardKindCdOptions] = useState<Record<string, string>>({});
  const [mediumTpCdOptions, setMediumTpCdOptions] = useState<Record<string, string>>({});

  const access = useAccess();
  const intl = useIntl();
  const formTableRef = useRef<FormInstance>();
  const tabs = [
    { label: 'Table 1', key: 'tab1' },
    { label: 'Table 2', key: 'tab2' },
    { label: 'Table 3', key: 'tab3' },
  ];
  const [form] = Form.useForm(); // 使用Form.useForm替代ref

  useMount(() => {
    // 获取介质类型字典
    getDict('medium_tp_cd').then((res) => {
      if (res.code === 200) {
        const opts: Record<string, string> = {};
        res.data.forEach((item: any) => {
          opts[item.dictValue] = `${item.dictValue}-${item.dictLabel}`;
        });
        setMediumTpCdOptions(opts);
      }
    });
    // 获取卡品种字典
    getDict('card_kind_cd').then((res) => {
      if (res.code === 200) {
        const opts: Record<string, string> = {};
        res.data.forEach((item: any) => {
          opts[item.dictValue] = `${item.dictValue}-${item.dictLabel}`;
        });
        setCardKindCdOptions(opts);
      }
    });
  });

  // const handleReset = (form: FormInstance | undefined) => {
  //   form?.resetFields();
  //   setFormValues({});
  //   setAllTableData([]);
  // };

  const handleReset = () => {
    form.resetFields();
    setFormValues({});
    setAllTableData([]);
  };

  const handleSearch = (values: FormValueType) => {
    const requestParams = {
      ...values,
      mediumTpCd: values.mediumTpCd?.split('-')[0],
      cardKindCd: values.cardKindCd?.split('-')[0],
    };
    setFormValues(values);
    accQueryList(requestParams as AccListParams).then((res) => {
      const parsedData = JSON.parse(res.data);
      setAllTableData(parsedData);
    });
  };

  const handleExport = async (selectedRowsState: AccQueryData[]) => {
    if (selectedRowsState.length === 0) {
      message.warning('请选择数据进行导出');
      return false;
    }
    const hide = message.loading('正在导出');
    try {
      await exportAccQuery({
        list: selectedRowsState,
      });
      hide();
      message.success('导出成功');
      return true;
    } catch (error) {
      hide();
      message.error('导出失败，请重试');
      return false;
    }
  };

  return (
    <WrapContent>
      <div style={{ padding: '24px' }}>
        <Card bordered={false} style={{ marginBottom: '24px' }} className="search-card">
          <Form
            form={form}
            layout="vertical"
            onFinish={(values) => handleSearch(values as FormValueType)}
            initialValues={{}}>
            <Row gutter={[24, 0]}>
              <Col xs={24} sm={12} md={8} lg={6}>
                <Form.Item label="介质号" name="prodNo" rules={[{ required: false }]}>
                  <Input placeholder="请输入介质号" allowClear />
                </Form.Item>
              </Col>
            </Row>
            <Row>
              <Col span={24} style={{ textAlign: 'right' }}>
                <Space>
                  <Button type="primary" htmlType="submit">
                    查询
                  </Button>
                  <Button onClick={handleReset}>
                    重置
                  </Button>
                </Space>
              </Col>
            </Row>
          </Form>
        </Card>

        <Card
          bordered={false}
          style={{
            marginTop: 24,
          }}
        >
          <Tabs
            activeKey={activeTab}
            onChange={(key: string) => setActiveTab(key)}
            className={styles.tabsWrapper}
          >
            {tabs.map((tab) => (
              <Tabs.TabPane tab={tab.label} key={tab.key}>
                <TabPanel
                  tableData={allTableData.filter((item, index) => {
                    if (tab.key === 'tab1') return index % 3 === 0;
                    if (tab.key === 'tab2') return index % 3 === 1;
                    if (tab.key === 'tab3') return index % 3 === 2;
                    return false;
                  })}
                  handleExport={handleExport}
                  tabKey={tab.key}
                />
              </Tabs.TabPane>
            ))}
          </Tabs>
        </Card>
      </div>
    </WrapContent>
  );
};

export default mediumQueryList;
