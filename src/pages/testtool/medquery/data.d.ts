export type AccQueryData = {
  mediumTpCd: string;
  prodNo: string;
  cardKindCd: string;
  dcardCategNo: string;
};

// AccListPagination 定义了账户列表的分页信息
export type AccListPagination = {
  total: number; // 总数
  pageSize: number; // 每页大小
  current: number; // 当前页码
};

// AccListData 定义了账户列表的数据结构
export type AccListData = {
  list: AccQueryData[]; // 用户列表
  pagination: Partial<AccListPagination>; // 分页信息
};

// AccListParams 定义了账户列表的请求参数
export type AccListParams = {
  mediumTpCd?: string;
  prodNo?: string;
  cardKindCd?: string;
  dcardCategNo?: string;
  pageSize?: string;
  currentPage?: string;
  filter?: string;
  sorter?: string;
};

// AccExportParams 定义了导出功能的请求参数
export type AccExportParams = {
  list: AccQueryData[]; // 直接使用选中的数据对象数组
};
