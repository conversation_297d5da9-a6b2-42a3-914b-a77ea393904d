import request from '@/utils/request';
import { downLoadXlsx } from '@/utils/downloadfile';
import type { AccListParams, AccExportParams } from './data.d';

// 查询账户列表
export async function accQueryList(params?: AccListParams) {
  const queryString = new URLSearchParams(params).toString();
  return request(`/testtool/accQuery/list?${queryString}`, {
    data: params,
    method: 'GET',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

// 导出账户数据
export function exportAccQuery(params: AccExportParams) {
  return downLoadXlsx(
    `/testtool/accQuery/export`,
    {
      method: 'POST',
      data: params.list, // 直接传递数据数组
    },
    `account_${new Date().getTime()}.xlsx`,
  );
}
