import React, { useState, useEffect, useRef } from 'react';
import {
  Card,
  Form,
  Select,
  Button,
  Spin,
  Empty,
  Tabs,
  Typography,
  Space,
  Tooltip,
  Input,
  Row,
  Col,
  Divider,
  Tag
} from 'antd';
import { KeepAlive, useActivate, useUnactivate } from "react-activation";
import { SearchOutlined, InfoCircleOutlined, ExclamationCircleOutlined, FilterOutlined, ReloadOutlined, SyncOutlined } from '@ant-design/icons';
import { getPcsMessage } from '../../message/send/service';
import { storage } from '@/utils/storageUtils';
import {
  queryRules,
  queryComplexRuleScene,
} from './service';
import type {
  TransactionType,
  RuleQueryResult,
  RuleLevel,
  RuleStatus,
  ComplexSceneDto
} from './data.d';
import './style.less';

const { Title, Text } = Typography;
const { Option } = Select;
const { TabPane } = Tabs;
const { Search } = Input;

// 缓存key常量
const CACHE_KEYS = {
  TRANSACTION_TYPES: 'complex_rules_transaction_types',
  SCENE_OPTIONS: 'complex_rules_scene_options',
  SEARCH_PARAMS: 'complex_rules_search',
  RESULT: 'complex_rules_result',
  FILTERS: 'complex_rules_filters'
};

// 缓存过期时间（30分钟）
const CACHE_EXPIRE_TIME = 30 * 60;

const ComplexRulesQuery: React.FC = () => {
  const [form] = Form.useForm();
  // 使用 useRef 跟踪动画状态，避免重复触发
  const animationTriggered = useRef(false);
  // 使用 useRef 跟踪是否正在查询，避免重复查询
  const isQuerying = useRef(false);

  const [transactionTypes, setTransactionTypes] = useState<TransactionType[]>(() => {
    const cachedTypes = storage.get(CACHE_KEYS.TRANSACTION_TYPES);
    return cachedTypes || [];
  });
  
  const [sceneOptions, setSceneOptions] = useState<ComplexSceneDto[]>(() => {
    const savedSceneOptions = storage.get(CACHE_KEYS.SCENE_OPTIONS);
    return savedSceneOptions || [];
  });
  
  const [loading, setLoading] = useState<boolean>(false);
  const [sceneLoading, setSceneLoading] = useState<boolean>(false);
  const [ruleResult, setRuleResult] = useState<RuleQueryResult | null>(() => {
    const savedResult = storage.get(CACHE_KEYS.RESULT);
    return savedResult || null;
  });
  
  const [searchValue, setSearchValue] = useState<string>(() => {
    const savedSearch = storage.get(CACHE_KEYS.SEARCH_PARAMS);
    return savedSearch?.transactionCode || '';
  });
  
  const [sceneCode, setSceneCode] = useState<string>(() => {
    const savedSearch = storage.get(CACHE_KEYS.SEARCH_PARAMS);
    return savedSearch?.sceneCode || '';
  });
  
  const [fadeIn, setFadeIn] = useState<boolean>(false);

  // 搜索状态相关
  const [mediumSearchText, setMediumSearchText] = useState<string>(() => {
    const savedFilters = storage.get(CACHE_KEYS.FILTERS);
    return savedFilters?.mediumSearchText || '';
  });
  
  const [contractSearchText, setContractSearchText] = useState<string>(() => {
    const savedFilters = storage.get(CACHE_KEYS.FILTERS);
    return savedFilters?.contractSearchText || '';
  });
  
  const [accountSearchText, setAccountSearchText] = useState<string>(() => {
    const savedFilters = storage.get(CACHE_KEYS.FILTERS);
    return savedFilters?.accountSearchText || '';
  });

  // 重置动画状态的函数
  const resetAnimation = () => {
    setFadeIn(false);
    animationTriggered.current = false;
  };

  // 触发动画的安全函数，确保只触发一次
  const triggerAnimation = () => {
    if (!animationTriggered.current) {
      animationTriggered.current = true;
      setTimeout(() => {
        setFadeIn(true);
      }, 300);
    }
  };

  // 缓存交易类型列表
  useEffect(() => {
    if (transactionTypes && transactionTypes.length > 0) {
      storage.set(CACHE_KEYS.TRANSACTION_TYPES, transactionTypes, CACHE_EXPIRE_TIME);
    }
  }, [transactionTypes]);

  // 缓存场景选项
  useEffect(() => {
    if (sceneOptions && sceneOptions.length > 0) {
      storage.set(CACHE_KEYS.SCENE_OPTIONS, sceneOptions, CACHE_EXPIRE_TIME);
    }
  }, [sceneOptions]);

  // 缓存过滤器
  useEffect(() => {
    if (mediumSearchText || contractSearchText || accountSearchText) {
      storage.set(CACHE_KEYS.FILTERS, {
        mediumSearchText,
        contractSearchText,
        accountSearchText
      }, CACHE_EXPIRE_TIME);
    }
  }, [mediumSearchText, contractSearchText, accountSearchText]);

  // 缓存查询结果
  useEffect(() => {
    if (ruleResult) {
      storage.set(CACHE_KEYS.RESULT, ruleResult, CACHE_EXPIRE_TIME);
    }
  }, [ruleResult]);

  // 初始化加载保存的搜索条件
  useEffect(() => {
    const savedSearch = storage.get(CACHE_KEYS.SEARCH_PARAMS);
    if (savedSearch) {
      form.setFieldsValue(savedSearch);
      if (savedSearch.transactionCode) {
        handleTransactionCodeChange(savedSearch.transactionCode);
      }
    }
    
    // 如果已经有查询结果，触发动画显示
    if (ruleResult && !fadeIn && !animationTriggered.current) {
      triggerAnimation();
    }
  }, [form]);

  // 获取交易类型列表（仅在没有缓存或缓存为空时请求）
  useEffect(() => {
    const fetchTransactionTypes = async () => {
      // 检查是否已有缓存数据
      const cachedTypes = storage.get(CACHE_KEYS.TRANSACTION_TYPES);
      
      // 如果有缓存且不为空，直接使用缓存数据
      if (cachedTypes && cachedTypes.length > 0) {
        console.log('使用缓存的交易类型列表数据');
        setTransactionTypes(cachedTypes);
        return;
      }
      
      // 否则请求后端数据
      try {
        console.log('请求后端获取交易类型列表');
        const response = await getPcsMessage();
        setTransactionTypes(response);
        // 存储到缓存
        storage.set(CACHE_KEYS.TRANSACTION_TYPES, response, CACHE_EXPIRE_TIME);
      } catch (error) {
        console.error('获取交易类型失败:', error);
      }
    };

    fetchTransactionTypes();
  }, []);

  // 组件激活时重新加载数据
  useActivate(() => {
    const savedSearch = storage.get(CACHE_KEYS.SEARCH_PARAMS);
    if (savedSearch) {
      form.setFieldsValue(savedSearch);
      if (savedSearch.transactionCode && (!searchValue || searchValue !== savedSearch.transactionCode)) {
        handleTransactionCodeChange(savedSearch.transactionCode);
      }
    }
    
    // 加载保存的交易类型列表
    const cachedTypes = storage.get(CACHE_KEYS.TRANSACTION_TYPES);
    if (cachedTypes && cachedTypes.length > 0 && (!transactionTypes || transactionTypes.length === 0)) {
      setTransactionTypes(cachedTypes);
    }
    
    // 加载保存的查询结果
    const savedResult = storage.get(CACHE_KEYS.RESULT);
    if (savedResult && (!ruleResult || JSON.stringify(ruleResult) !== JSON.stringify(savedResult))) {
      setRuleResult(savedResult);
    }
    
    // 加载保存的过滤条件
    const savedFilters = storage.get(CACHE_KEYS.FILTERS);
    if (savedFilters) {
      setMediumSearchText(savedFilters.mediumSearchText || '');
      setContractSearchText(savedFilters.contractSearchText || '');
      setAccountSearchText(savedFilters.accountSearchText || '');
    }
    
    // 只有当有结果数据并且fadeIn为false时才触发动画
    if (ruleResult && !fadeIn) {
      triggerAnimation();
    }
  });

  // 组件失活时清理状态
  useUnactivate(() => {
    setLoading(false);
    isQuerying.current = false;
    // 不重置fadeIn状态，以便下次激活时保持原状态
  });

  // 处理交易类型选择变更
  const handleTransactionCodeChange = async (value: string) => {
    if (!value) {
      setSceneOptions([]);
      form.setFieldsValue({ sceneCode: undefined });
      storage.remove(CACHE_KEYS.SCENE_OPTIONS);
      return;
    }

    // 无论如何，先清除当前场景编码选择
    form.setFieldsValue({ sceneCode: undefined });
    
    // 先检查缓存中是否有对应的场景选项
    const cachedSceneOptions = storage.get(CACHE_KEYS.SCENE_OPTIONS);
    const cachedTransactionCode = storage.get(CACHE_KEYS.SEARCH_PARAMS)?.transactionCode;
    
    // 只有当没有缓存或缓存的交易类型与当前不同时，才请求场景数据
    if (!cachedSceneOptions || cachedSceneOptions.length === 0 || cachedTransactionCode !== value) {
      setSceneLoading(true);
      try {
        const response = await queryComplexRuleScene(value);
        console.log('场景编码接口返回数据:', response);
        const sceneData = Array.isArray(response) ? response : [];
        setSceneOptions(sceneData);
        
        // 更新缓存
        storage.set(CACHE_KEYS.SCENE_OPTIONS, sceneData, CACHE_EXPIRE_TIME);
        
        // 如果场景数据为空，确保清除场景编码选择
        if (sceneData.length === 0) {
          form.setFieldsValue({ sceneCode: undefined });
          // 清除与场景相关的缓存
          const searchParams = storage.get(CACHE_KEYS.SEARCH_PARAMS);
          if (searchParams) {
            delete searchParams.sceneCode;
            storage.set(CACHE_KEYS.SEARCH_PARAMS, searchParams, CACHE_EXPIRE_TIME);
          }
        }
      } catch (error) {
        console.error('获取场景编码失败:', error);
        setSceneOptions([]);
        form.setFieldsValue({ sceneCode: undefined });
        storage.remove(CACHE_KEYS.SCENE_OPTIONS);
      } finally {
        setSceneLoading(false);
      }
    } else {
      // 使用缓存数据
      console.log('使用缓存的场景编码数据');
      setSceneOptions(cachedSceneOptions);
      
      // 如果场景选项为空，确保清除场景编码选择
      if (cachedSceneOptions.length === 0) {
        form.setFieldsValue({ sceneCode: undefined });
        // 清除与场景相关的缓存
        const searchParams = storage.get(CACHE_KEYS.SEARCH_PARAMS);
        if (searchParams) {
          delete searchParams.sceneCode;
          storage.set(CACHE_KEYS.SEARCH_PARAMS, searchParams, CACHE_EXPIRE_TIME);
        }
      }
    }
  };

  // 处理查询
  const handleSearch = async (values: { transactionCode: string; sceneCode?: string }) => {
    const { transactionCode, sceneCode } = values;
    if (!transactionCode) return;
    
    // 防止重复查询
    if (isQuerying.current) return;
    isQuerying.current = true;

    // 保存搜索条件到storage
    storage.set(CACHE_KEYS.SEARCH_PARAMS, values, CACHE_EXPIRE_TIME);

    // 完全重置动画状态
    resetAnimation();
    setLoading(true);
    setSearchValue(transactionCode);
    setSceneCode(sceneCode || '');

    // 重置搜索框
    setMediumSearchText('');
    setContractSearchText('');
    setAccountSearchText('');

    try {
      const response = await queryRules({
        pcsNo: transactionCode,
        sceneNo: sceneCode
      });

      console.log('API返回数据:', response);
      
      // 先保存结果数据
      setRuleResult(response);
      storage.set(CACHE_KEYS.RESULT, response, CACHE_EXPIRE_TIME);
      
      // 等待数据设置完成后触发动画
      triggerAnimation();
    } catch (error) {
      console.error('查询规则失败:', error);
      setRuleResult(null);
      storage.remove(CACHE_KEYS.RESULT);
      resetAnimation();
    } finally {
      setLoading(false);
      isQuerying.current = false;
    }
  };

  // 获取所有规则状态项
  const getAllStatusItems = (result: RuleQueryResult | null) => {
    if (!result) return [];

    const items: { value: string; label: string; level: string; position?: number; index?: number }[] = [];

    // 检查每个层级并安全地添加状态项
    if (result.mediumLevel && result.mediumLevel.name && result.mediumLevel.statuses) {
      result.mediumLevel.statuses.forEach((status, index) => {
        if (status && status.name !== undefined) {
          items.push({
            // 将索引作为value的一部分
            value: `${result.mediumLevel.name}_${status.name}_${index}`,
            label: status.name,
            level: result.mediumLevel.name,
            position: index + 1, // 显示用位置信息 (从1开始)
            index: index // 保存原始索引 (从0开始)
          });
        }
      });
    }

    if (result.contractLevel && result.contractLevel.name && result.contractLevel.statuses) {
      result.contractLevel.statuses.forEach((status, index) => {
        if (status && status.name !== undefined) {
          items.push({
            // 将索引作为value的一部分
            value: `${result.contractLevel.name}_${status.name}_${index}`,
            label: status.name,
            level: result.contractLevel.name,
            position: index + 1, // 显示用位置信息 (从1开始)
            index: index // 保存原始索引 (从0开始)
          });
        }
      });
    }

    if (result.accountLevel && result.accountLevel.name && result.accountLevel.statuses) {
      result.accountLevel.statuses.forEach((status, index) => {
        if (status && status.name !== undefined) {
          items.push({
            // 将索引作为value的一部分
            value: `${result.accountLevel.name}_${status.name}_${index}`,
            label: status.name,
            level: result.accountLevel.name,
            position: index + 1, // 显示用位置信息 (从1开始)
            index: index // 保存原始索引 (从0开始)
          });
        }
      });
    }

    console.log('生成的所有状态项:', items);
    return items;
  };

  // 过滤规则状态列表
  const filterStatusList = (statuses: RuleStatus[], searchText: string): RuleStatus[] => {
    if (!searchText) return statuses;
    
    // 解析搜索文本，现在包含了位置信息和索引
    const [searchLevel, searchStatus, searchPosition] = searchText.split('_');
    
    if (!searchLevel || !searchStatus) return statuses;

    // 如果有位置信息，直接按位置/索引筛选
    if (searchPosition && !isNaN(parseInt(searchPosition, 10))) {
      const position = parseInt(searchPosition, 10);
      return statuses.filter((_, index) => index === position);
    }
    
    // 否则按状态名称筛选
    return statuses.filter(status => status.name === searchStatus);
  };

  // 获取状态显示文本和样式
  const getStatusDisplay = (status: RuleStatus, level: string, index: number) => {
    // 合约层的"证件过期"特殊处理
    if (level === '合约层' && index === 10 && status.name === '证件过期') {
      if (status.controlled === 0) {
        return {
          text: '不管控',
          className: 'uncontrolled'
        };
      } else if (status.controlled === 1) {
        return {
          text: '3个月',
          className: 'controlled'
        };
      } else if (status.controlled === 2) {
        return {
          text: '过期',
          className: 'controlled'
        };
      }
    }
    
    // 介质层的"待更换"特殊处理
    if (level === '介质层' && index === 11 && status.name === '待更换') {
      if (status.controlled === 0) {
        return {
          text: '不管控',
          className: 'uncontrolled'
        };
      } else if (status.controlled === 1) {
        return {
          text: '提示',
          className: 'controlled'
        };
      } else if (status.controlled === 2) {
        return {
          text: '报错',
          className: 'controlled'
        };
      }
    }
    
    // 默认处理
    return {
      text: status.controlled === 1 ? '管控' : '不管控',
      className: status.controlled === 1 ? 'controlled' : 'uncontrolled'
    };
  };

  // 渲染规则状态列表
  const renderStatusList = (level: RuleLevel, searchText: string = '') => {
    // 添加安全检查
    if (!level || !level.statuses) {
      console.error('渲染状态列表时出错：level对象无效或缺少statuses', level);
      return (
        <div className="empty-search-result">
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description="无法加载状态数据"
          />
        </div>
      );
    }

    let filteredStatuses = level.statuses;

    if (searchText) {
      const [searchLevel, searchStatus, searchPosition] = searchText.split('_');
      if (searchLevel === level.name || searchLevel === '所有层级') {
        if (searchPosition && !isNaN(parseInt(searchPosition, 10))) {
          // 在这里，searchPosition是原始索引值
          console.log(`筛选位置: ${searchPosition}, 层级: ${level.name}`);
          filteredStatuses = [level.statuses[parseInt(searchPosition, 10)]];
        } else if (searchStatus) {
          // 否则按名称筛选，确保完全匹配
          filteredStatuses = level.statuses.filter(status => status.name === searchStatus);
        }
      } else {
        filteredStatuses = [];
      }
    }

    if (filteredStatuses.length === 0) {
      return (
        <div className="empty-search-result">
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description={searchText ? `未找到匹配的${level.name}状态` : `暂无${level.name}状态`}
          />
        </div>
      );
    }

    return (
      <div className="statusList">
        {filteredStatuses.map((status, index) => {
          // 对于筛选后的单个元素，使用原始位置
          const displayIndex = searchText && filteredStatuses.length === 1 && searchText.split('_')[2] 
            ? parseInt(searchText.split('_')[2], 10) 
            : index;
          
          // 获取正确的位置信息用于显示
          const positionDisplay = displayIndex + 1;
          
          // 使用正确的索引获取状态显示
          const statusDisplay = getStatusDisplay(status, level.name, displayIndex);
          
          console.log(`渲染位置: ${positionDisplay}, 状态: ${status.name}, 层级: ${level.name}`);
          
          return (
            <div
              key={index}
              className={`statusItem ${fadeIn ? 'fade-in' : ''}`}
              style={{ transitionDelay: `${index * 0.05}s` }}
            >
              <span className="statusName">
                <span className="bit-position">{`位${positionDisplay}`}</span>
                <span className="bit-meaning">{status.name}</span>
              </span>
              <span
                className={`statusValue ${statusDisplay.className}`}
              >
                {statusDisplay.text}
              </span>
            </div>
          );
        })}
      </div>
    );
  };

  // 渲染搜索框 - 改为下拉菜单
  const renderSearchBox = (level: string, value: string, onChange: (value: string) => void) => {
    const statusItems = getAllStatusItems(ruleResult);
    let filteredItems = statusItems;

    // 如果不是"所有层级"，则只显示对应层级的状态
    if (level !== '所有层级') {
      filteredItems = statusItems.filter(item => item.level === level);
    }

    // 调试输出
    console.log('当前选中value:', value);
    console.log('可选状态项:', filteredItems);

    return (
      <div className="search-box">
        <Select
          placeholder={`选择${level}状态...`}
          allowClear
          showSearch
          style={{ width: '350px' }}
          value={value || undefined}
          onChange={onChange}
          optionFilterProp="label"
          options={filteredItems.map(item => ({
            // 确保value中包含原始索引信息
            value: item.value,
            label: (
              <span>
                {item.label}
                {item.label === '无效' && <span style={{ color: '#999', marginLeft: 4 }}>位{item.position}</span>}
                {level === '所有层级' && <Tag color="blue" style={{ marginLeft: 8 }}>{item.level}</Tag>}
                <span style={{ fontSize: '12px', color: '#999', marginLeft: 4 }}>(位{item.position})</span>
              </span>
            ),
          }))}
          filterOption={(input, option) => {
            if (!input || !option) return true;
            
            // 获取选项的标签内容（字符串形式）
            let labelText = '';
            if (option.label) {
              if (typeof option.label === 'string') {
                labelText = option.label;
              } else if (option.label.props && option.label.props.children) {
                // 假设children是数组，提取出第一个文本节点（状态名称）
                const children = option.label.props.children;
                if (Array.isArray(children) && children.length > 0) {
                  labelText = typeof children[0] === 'string' ? children[0] : '';
                }
              }
            }
            
            return labelText.toLowerCase().includes(input.toLowerCase());
          }}
          className="status-select"
        />
      </div>
    );
  };

  // 处理状态筛选变更
  const handleStatusChange = (level: string, setValue: (value: string) => void) => (value: string) => {
    console.log(`${level}状态选择变更:`, value);
    
    if (value) {
      // 解析选择的值以获取位置信息
      const [valueLevel, valueStatus, positionIndex] = value.split('_');
      console.log(`选择的层级: ${valueLevel}, 状态: ${valueStatus}, 原始索引: ${positionIndex}, 显示位置: ${parseInt(positionIndex, 10) + 1}`);
    }
    
    setValue(value);
  };

  // 渲染规则层级
  const renderRuleLevel = (level: RuleLevel, searchText: string = '') => {
    // 添加安全检查，确保level存在且有name属性
    if (!level || !level.name) {
      console.error('渲染规则层级时出错：level对象无效', level);
      return null;
    }

    const isSelected = searchText && searchText.split('_')[0] === level.name;
    const isHighlighted = searchText && (searchText.split('_')[0] === level.name || searchText.split('_')[0] === '所有层级');

    return (
      <div className={`levelContainer ${fadeIn ? 'fade-in' : ''} ${isSelected ? 'selected-level' : ''} ${isHighlighted ? 'highlighted-level' : ''}`}>
        <div className={`levelTitle ${isSelected ? 'selected-title' : ''}`}>
          {level.name}
          {isSelected && <Tag color="green" style={{ marginLeft: 8 }}>已筛选</Tag>}
          <Tooltip title={`显示${level.name}的各种状态及其管控情况`}>
            <InfoCircleOutlined />
          </Tooltip>
        </div>
        {renderStatusList(level, searchText)}
      </div>
    );
  };

  // 获取当前选中的交易类型名称
  const getSelectedTransactionName = () => {
    if (!searchValue) return '';
    const selected = transactionTypes.find(type => type.code === searchValue);
    return selected ? selected.name : '';
  };

  // 获取当前选中的场景编码名称
  const getSelectedSceneName = () => {
    if (!sceneCode || !Array.isArray(sceneOptions)) return '';
    const selected = sceneOptions.find(scene => scene.complexRuleSceneNo === sceneCode);
    return selected ? selected.complexRuleSceneName : '';
  };

  // 自定义Select组件样式
  const selectStyle = {
    width: 300,
    borderRadius: '8px',
  };

  // 自定义Input组件样式
  const inputStyle = {
    width: 200,
    borderRadius: '8px',
  };

  // 自定义Button组件样式
  const buttonStyle = {
    backgroundColor: '#13C2C2',
    borderColor: '#13C2C2',
    borderRadius: '8px',
  };

  // 重置筛选器
  const resetFilters = () => {
    setMediumSearchText('');
    setContractSearchText('');
    setAccountSearchText('');
  };
  
  // 完全重置页面
  const resetAll = () => {
    // 重置表单
    form.resetFields();
    // 重置状态
    setSearchValue('');
    setSceneCode('');
    setRuleResult(null);
    resetFilters();
    // 重置动画状态
    resetAnimation();
    // 清除缓存
    storage.remove(CACHE_KEYS.SEARCH_PARAMS);
    storage.remove(CACHE_KEYS.RESULT);
    storage.remove(CACHE_KEYS.FILTERS);
  };

  // 单独提取重置筛选按钮组件，供多个地方复用
  const ResetFilterButton = () => (
    <div className="resetFilterButton">
      <Button
        type="primary"
        size="middle"
        icon={<FilterOutlined />}
        onClick={resetFilters}
      >
        重置筛选
      </Button>
    </div>
  );

  return (
    <KeepAlive>
      <div className="complexRulesQuery">
        <div className="header">
          <Title level={4}>复杂规则查询</Title>
        </div>

        <div className="searchSection">
          <Form
            form={form}
            layout="vertical"
            onFinish={handleSearch}
          >
            <Row gutter={[16, 16]} align="bottom" style={{ width: '100%' }}>
              <Col xs={24} md={12} lg={10} xl={8}>
                <Form.Item
                  name="transactionCode"
                  label="交易类型："
                  rules={[{ required: true, message: '请选择交易类型' }]}
                  style={{ width: '100%' }}
                >
                  <Select
                    placeholder="请选择交易类型"
                    style={{ width: '100%' }}
                    className="rounded-select"
                    showSearch
                    optionFilterProp="children"
                    onChange={handleTransactionCodeChange}
                    filterOption={(input, option) => {
                      if (!option || !input) return false;
                      
                      // 获取选项的值
                      const optionValue = option.value?.toString() || '';
                      // 安全获取children内容
                      const optionLabel = option.children ? 
                        (typeof option.children === 'string' ? 
                          option.children : 
                          // 如果children是React元素，尝试转换为字符串
                          option.children.toString()) : 
                        '';
                      
                      // 转为小写进行比较
                      return (
                        optionValue.toLowerCase().includes(input.toLowerCase()) ||
                        optionLabel.toLowerCase().includes(input.toLowerCase())
                      );
                    }}
                  >
                    {transactionTypes.map(type => (
                      <Option key={type.code} value={type.code}>
                        {type.code} - {type.name}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col xs={24} md={8} lg={8} xl={7}>
                <Form.Item
                  name="sceneCode"
                  label="场景编码："
                  rules={[{ required: true, message: '请选择场景编码' }]}
                  style={{ width: '100%' }}
                >
                  <Select
                    placeholder="请选择场景编码"
                    style={{ width: '100%' }}
                    className="rounded-select"
                    showSearch
                    allowClear
                    loading={sceneLoading}
                    optionFilterProp="children"
                    filterOption={(input, option) => {
                      if (!option || !input) return false;
                      
                      // 安全获取children内容
                      const optionLabel = option.children ? 
                        (typeof option.children === 'string' ? 
                          option.children : 
                          // 如果children是React元素，尝试转换为字符串
                          option.children.toString()) : 
                        '';
                      
                      // 转为小写进行比较
                      return optionLabel.toLowerCase().includes(input.toLowerCase());
                    }}
                    notFoundContent={
                      sceneLoading ? <Spin size="small" /> : (
                        sceneOptions.length === 0 && form.getFieldValue('transactionCode') ?
                          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description="无可用场景编码" /> :
                          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description="请先选择交易类型" />
                      )
                    }
                    dropdownMatchSelectWidth={false}
                    listHeight={250}
                  >
                    {Array.isArray(sceneOptions) && sceneOptions.map(scene => (
                      <Option key={scene.complexRuleSceneNo} value={scene.complexRuleSceneNo}>
                        {scene.complexRuleSceneNo} - {scene.complexRuleSceneName}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col xs={24} md={4} lg={3} xl={2}>
                <Form.Item
                  label=" "
                  style={{ width: '100%' }}
                >
                  <Button
                    type="primary"
                    htmlType="submit"
                    icon={<SearchOutlined />}
                    style={buttonStyle}
                    className="rounded-button"
                    block
                  >
                    查询
                  </Button>
                </Form.Item>
              </Col>
              <Col xs={24} md={4} lg={3} xl={2}>
                <Form.Item
                  label=" "
                  style={{ width: '100%' }}
                >
                  <Button
                    type="default"
                    icon={<ReloadOutlined />}
                    onClick={resetAll}
                    style={{...buttonStyle, backgroundColor: '#f5f5f5', borderColor: '#d9d9d9', color: 'rgba(0, 0, 0, 0.65)'}}
                    className="rounded-button"
                    block
                  >
                    重置
                  </Button>
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </div>

        {loading ? (
          <div className={`loadingContainer ${fadeIn ? 'fade-in' : ''}`}>
            <Spin size="large" tip="正在加载规则数据..." />
            <Text type="secondary" style={{ marginTop: 8 }}>请稍候，正在查询规则配置...</Text>
          </div>
        ) : ruleResult ? (
          <div className={`resultSection ${fadeIn ? 'fade-in' : ''}`}>
            <Title level={5} style={{ marginBottom: 24 }}>
              <Row align="middle">
                <Col flex="auto">
                  交易类型: {searchValue} - {getSelectedTransactionName()}
                  {sceneCode && <span style={{ marginLeft: 16 }}>场景编码: {sceneCode} - {getSelectedSceneName()}</span>}
                </Col>
              </Row>
            </Title>

            <Tabs defaultActiveKey="1" animated={{ inkBar: true, tabPane: true }}>
              <TabPane tab="全部规则" key="1">
                <Space direction="vertical" size="large" style={{ width: '100%' }}>
                  {renderSearchBox('所有层级', mediumSearchText, handleStatusChange('所有层级', setMediumSearchText))}
                  {!mediumSearchText || (mediumSearchText.split('_')[0] === '介质层' || mediumSearchText.split('_')[0] === '所有层级') ? (
                    <>
                      {renderRuleLevel(ruleResult.mediumLevel, mediumSearchText)}
                      {!mediumSearchText && <Divider style={{ margin: '16px 0' }} />}
                    </>
                  ) : null}

                  {!mediumSearchText || (mediumSearchText.split('_')[0] === '合约层' || mediumSearchText.split('_')[0] === '所有层级') ? (
                    <>
                      {renderRuleLevel(ruleResult.contractLevel, mediumSearchText)}
                      {!mediumSearchText && <Divider style={{ margin: '16px 0' }} />}
                    </>
                  ) : null}

                  {!mediumSearchText || (mediumSearchText.split('_')[0] === '账户层' || mediumSearchText.split('_')[0] === '所有层级') ? (
                    renderRuleLevel(ruleResult.accountLevel, mediumSearchText)
                  ) : null}

                  {mediumSearchText && <ResetFilterButton />}
                </Space>
              </TabPane>
              <TabPane tab="介质层规则" key="2">
                {renderSearchBox('介质层', mediumSearchText, handleStatusChange('介质层', setMediumSearchText))}
                {renderRuleLevel(ruleResult.mediumLevel, mediumSearchText)}
                {mediumSearchText && <ResetFilterButton />}
              </TabPane>
              <TabPane tab="合约层规则" key="3">
                {renderSearchBox('合约层', contractSearchText, handleStatusChange('合约层', setContractSearchText))}
                {renderRuleLevel(ruleResult.contractLevel, contractSearchText)}
                {contractSearchText && <ResetFilterButton />}
              </TabPane>
              <TabPane tab="账户层规则" key="4">
                {renderSearchBox('账户层', accountSearchText, handleStatusChange('账户层', setAccountSearchText))}
                {renderRuleLevel(ruleResult.accountLevel, accountSearchText)}
                {accountSearchText && <ResetFilterButton />}
              </TabPane>
            </Tabs>
          </div>
        ) : (
          <div className={`emptyResult ${fadeIn ? 'fade-in' : ''}`}>
            <Empty
              description={
                <span>
                  请选择交易类型并点击查询按钮
                  <div style={{ marginTop: 8, fontSize: 13, color: 'rgba(0,0,0,0.45)' }}>
                    查询结果将显示对应交易类型的规则配置
                  </div>
                </span>
              }
            />
          </div>
        )}
      </div>
    </KeepAlive>
  );
};

export default ComplexRulesQuery;
