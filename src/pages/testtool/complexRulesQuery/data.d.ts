// 交易类型接口
export interface TransactionType {
  code: string;
  name: string;
}

// 场景编码接口
export interface ComplexSceneDto {
  complexRuleSceneNo: string;
  complexRuleSceneName: string;
}

// 规则状态接口
export interface RuleStatus {
  name: string;
  controlled: number; // 0表示不管控，1表示管控
}

// 规则层级接口
export interface RuleLevel {
  name: string;
  statuses: RuleStatus[];
}

// 规则查询结果接口
export interface RuleQueryResult {
  mediumLevel: RuleLevel;
  contractLevel: RuleLevel;
  accountLevel: RuleLevel;
}

// 查询参数接口
export interface QueryParams {
  pcsNo: string;
  sceneNo?: string; // 场景编码参数，可选
}

// 分页接口
export interface Pagination {
  total: number;
  pageSize: number;
  current: number;
  onChange?: (page: number, pageSize: number) => void;
}

// 页面响应接口
export interface PageResponse<T> {
  records: T[];
  total: number;
  size: number;
  current: number;
}

// 控制位含义接口
export interface ControlBitMeaning {
  position: number;
  meaning: string;
}

// 介质层控制位含义
export const MEDIUM_CONTROL_MEANINGS: ControlBitMeaning[] = [
  { position: 1, meaning: '注销' },
  { position: 2, meaning: '未激活' },
  { position: 3, meaning: '密码锁定' },
  { position: 4, meaning: '可疑锁定' },
  { position: 5, meaning: '凭证临时挂失' },
  { position: 6, meaning: '正式挂失' },
  { position: 7, meaning: '密码挂失' },
  { position: 8, meaning: '双挂' },
  { position: 9, meaning: '吞没' },
  { position: 10, meaning: '作废' },
  { position: 11, meaning: '上缴' },
  { position: 12, meaning: '待更换' },
  { position: 13, meaning: '联名账户' },
  { position: 14, meaning: '待核实多账户' },
  { position: 15, meaning: '特殊客户号校验' },
  { position: 16, meaning: '副卡办理' },
  { position: 17, meaning: '客户九项信息' },
  { position: 18, meaning: '客户信息待核实' },
  { position: 19, meaning: '芯片过期' },
  { position: 20, meaning: '客户非柜面限制' },
  { position: 21, meaning: '老存折检查' },
  { position: 22, meaning: '惩戒客户' }
];

// 合约层控制位含义
export const CONTRACT_CONTROL_MEANINGS: ControlBitMeaning[] = [
  { position: 1, meaning: '停用' },
  { position: 2, meaning: '合约冻结' },
  { position: 3, meaning: '合约止付' },
  { position: 4, meaning: '中止' },
  { position: 5, meaning: '限制非柜面' },
  { position: 6, meaning: '解约' },
  { position: 7, meaning: '副卡停用' },
  { position: 8, meaning: '长期不动户' },
  { position: 9, meaning: '外币结算主合约' },
  { position: 10, meaning: '是否实名' },
  { position: 11, meaning: '证件过期' },
  { position: 12, meaning: '特殊客户号校验' },
  { position: 13, meaning: '客户九项信息' },
  { position: 14, meaning: '养老金转移' },
  { position: 15, meaning: '客户信息不符' },
  { position: 16, meaning: '证件为户口簿且满十六周岁' },
  { position: 17, meaning: '养老金专户资金归集状态' },
  { position: 18, meaning: '支持账户类型' },
  { position: 19, meaning: '介质未领用' }
];

// 账户层控制位含义
export const ACCOUNT_CONTROL_MEANINGS: ControlBitMeaning[] = [
  { position: 1, meaning: '未启用' },
  { position: 2, meaning: '销户' },
  { position: 3, meaning: '账户冻结' },
  { position: 4, meaning: '金额冻结' },
  { position: 5, meaning: '账户止付' },
  { position: 6, meaning: '金额止付' },
  { position: 7, meaning: '长期不动户' },
  { position: 8, meaning: '账户待结清' }
];
