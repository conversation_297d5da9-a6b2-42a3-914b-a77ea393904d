.complexRulesQuery {
  padding: 24px;
  background-color: #f0f2f5;
  min-height: 100vh;
  padding-top: 64px;

  .header {
    margin-bottom: 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .title {
      font-size: 20px;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.85);
    }
  }

  .searchSection {
    background-color: #fff;
    padding: 24px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    margin-bottom: 24px;
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
    }
    
    .ant-form-item-label > label {
      color: rgba(0, 0, 0, 0.8);
      font-weight: 500;
      font-size: 14px;
    }

    .rounded-select {
      .ant-select-selector {
        border-radius: 8px !important;
        transition: all 0.3s ease;
      }

      &:hover .ant-select-selector {
        border-color: #13C2C2;
      }
      
      .ant-select-selection-item {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    .rounded-input {
      border-radius: 8px !important;
      transition: all 0.3s ease;

      &:hover, &:focus {
        border-color: #13C2C2;
      }
    }

    .rounded-button {
      border-radius: 8px !important;
      transition: all 0.3s ease-in-out;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(19, 194, 194, 0.2);
      }
    }

    :global {
      .ant-select {
        .ant-select-selector {
          border-radius: 8px !important;
        }
        
        &.ant-select-focused .ant-select-selector {
          border-color: #13C2C2 !important;
          box-shadow: 0 0 0 2px rgba(19, 194, 194, 0.2) !important;
        }
        
        .ant-select-selection-placeholder {
          color: rgba(0, 0, 0, 0.25);
        }
        
        .ant-select-arrow {
          color: rgba(0, 0, 0, 0.25);
          transition: all 0.3s;
        }
        
        &:hover .ant-select-arrow {
          color: #13C2C2;
        }
      }
      
      .ant-input {
        border-radius: 8px !important;
      }
      
      .ant-btn {
        border-radius: 8px !important;
      }
      
      .ant-spin-nested-loading .ant-spin-container {
        min-height: 32px;
      }
      
      .ant-select-dropdown {
        border-radius: 8px;
        overflow: hidden;
        min-width: 180px !important;
        max-width: 320px !important;
        
        .ant-select-item-option {
          transition: all 0.2s;
          
          &-content {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          
          &-active:not(.ant-select-item-option-disabled) {
            background-color: rgba(19, 194, 194, 0.1);
          }
          
          &-selected {
            background-color: rgba(19, 194, 194, 0.2);
            font-weight: 500;
            
            &:hover {
              background-color: rgba(19, 194, 194, 0.3);
            }
          }
        }
      }
    }
  }

  .resultSection {
    background-color: #fff;
    padding: 24px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.5s ease-out, transform 0.5s ease-out, box-shadow 0.3s ease;
    will-change: opacity, transform;

    &.fade-in {
      opacity: 1;
      transform: translateY(0);
    }

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
    }

    .search-box {
      position: relative;
      margin-bottom: 20px;
      transition: all 0.3s ease;
      
      .ant-input-search {
        .ant-input {
          border-radius: 8px 0 0 8px !important;
          transition: all 0.3s ease;
          
          &:focus, &:hover {
            border-color: #13C2C2;
          }
        }
        
        .ant-input-group-addon {
          .ant-btn {
            border-radius: 0 8px 8px 0 !important;
            background-color: #13C2C2;
            border-color: #13C2C2;
            
            &:hover {
              background-color: #08979c;
              border-color: #08979c;
            }
          }
        }
      }
      
      .status-select {
        .ant-select-selector {
          border-radius: 8px !important;
          transition: all 0.3s ease;
          
          &:focus, &:hover {
            border-color: #13C2C2;
          }
        }
        
        &.ant-select-focused .ant-select-selector {
          border-color: #13C2C2;
          box-shadow: 0 0 0 2px rgba(19, 194, 194, 0.2);
        }
        
        .ant-select-arrow {
          color: #13C2C2;
        }
      }
      
      .search-result-info {
        margin-top: 8px;
        padding: 6px 10px;
        background-color: #f6ffed;
        border: 1px solid #b7eb8f;
        border-radius: 4px;
        color: rgba(0, 0, 0, 0.65);
        font-size: 13px;
        display: flex;
        align-items: center;
        animation: fadeIn 0.3s;
        
        .anticon {
          color: #52c41a;
          margin-right: 6px;
        }
      }
    }

    .filter-info {
      display: flex;
      justify-content: center;
      margin-top: 16px;
      padding: 12px 16px;
      background-color: #f6ffed;
      border-radius: 8px;
      border: 1px solid #b7eb8f;
      animation: fadeIn 0.3s;
      
      .filter-text {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.65);
        display: flex;
        align-items: center;
      }
      
      .ant-tag {
        margin-right: 0;
        margin-left: 8px;
      }
      
      .ant-btn {
        transition: all 0.3s ease;
        margin-left: 16px;
        
        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 8px rgba(82, 196, 26, 0.2);
        }
      }
    }

    .empty-search-result {
      animation: fadeIn 0.3s;
      padding: 24px 0;
      background-color: #fafafa;
      border-radius: 8px;
      margin-bottom: 20px;
    }

    .levelContainer {
      opacity: 0;
      transform: translateY(15px);
      transition: opacity 0.4s ease, transform 0.4s ease, background-color 0.4s ease, padding 0.4s ease, border 0.4s ease;
      will-change: opacity, transform;
      
      &.fade-in {
        opacity: 1;
        transform: translateY(0);
      }
      
      &.selected-level {
        background-color: #f6ffed;
        padding: 16px;
        border-radius: 8px;
        border: 1px solid #b7eb8f;
      }
      
      &.highlighted-level {
        padding: 16px;
        border-radius: 8px;
        border: 1px solid #f0f0f0;
      }
    }

    .levelTitle {
      font-size: 18px;
      font-weight: 500;
      margin-bottom: 20px;
      color: rgba(0, 0, 0, 0.85);
      border-bottom: 1px solid #f0f0f0;
      padding-bottom: 12px;
      display: flex;
      align-items: center;

      &.selected-title {
        color: #52c41a;
        border-bottom-color: #b7eb8f;
      }

      .anticon {
        margin-left: 8px;
        color: #1890ff;
        transition: all 0.3s;

        &:hover {
          transform: scale(1.2);
        }
      }
    }

    .statusList {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
      gap: 16px;
      margin-bottom: 30px;

      .statusItem {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 16px;
        background-color: #fafafa;
        border-radius: 8px;
        transition: all 0.3s ease;
        border: 1px solid #f0f0f0;
        opacity: 0;
        transform: translateY(10px);
        
        &.fade-in {
          opacity: 1;
          transform: translateY(0);
          transition: opacity 0.5s ease, transform 0.5s ease;
        }
        
        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          background-color: #f5f5f5;
        }

        .statusName {
          font-size: 14px;
          color: rgba(0, 0, 0, 0.75);
          flex: 1;
          margin-right: 12px;
          display: flex;
          align-items: center;

          .bit-position {
            color: #8c8c8c;
            font-size: 12px;
            margin-right: 8px;
            background-color: #f0f0f0;
            padding: 2px 6px;
            border-radius: 4px;
          }

          .bit-meaning {
            flex: 1;
          }
        }

        .statusValue {
          display: inline-flex;
          align-items: center;
          justify-content: center;
          min-width: 60px;
          height: 28px;
          border-radius: 14px;
          font-weight: 500;
          padding: 0 12px;
          font-size: 13px;
          transition: all 0.3s ease;
          
          &.controlled {
            background-color: #ff4d4f;
            color: #fff;
            
            &:hover {
              background-color: #ff7875;
              box-shadow: 0 2px 8px rgba(255, 77, 79, 0.3);
            }
          }
          
          &.uncontrolled {
            background-color: #52c41a;
            color: #fff;
            
            &:hover {
              background-color: #73d13d;
              box-shadow: 0 2px 8px rgba(82, 196, 26, 0.3);
            }
          }
        }
      }
    }
  }

  .emptyResult {
    text-align: center;
    padding: 64px 0;
    color: rgba(0, 0, 0, 0.45);
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    opacity: 0;
    transform: translateY(20px);
    
    &.fade-in {
      opacity: 1;
      transform: translateY(0);
      transition: opacity 0.5s ease-out, transform 0.5s ease-out;
    }

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
    }
  }

  .loadingContainer {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 64px 0;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    opacity: 0;
    transform: translateY(20px);
    
    &.fade-in {
      opacity: 1;
      transform: translateY(0);
      transition: opacity 0.3s ease-out, transform 0.3s ease-out;
    }
    
    .ant-spin {
      margin-bottom: 16px;
    }
  }

  .resetFilterButton {
    display: flex;
    justify-content: center;
    margin-top: 16px;
    margin-bottom: 16px;
    
    .ant-btn {
      border-radius: 8px;
      background-color: #52c41a;
      border-color: #52c41a;
      color: #fff;
      font-weight: 500;
      height: 36px;
      padding: 0 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s;
      
      &:hover {
        background-color: #73d13d;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(82, 196, 26, 0.2);
      }
      
      .anticon {
        margin-right: 8px;
      }
    }
  }
  
  .resetButton {
    position: sticky;
    bottom: 20px;
    left: 0;
    right: 0;
    margin: 0 auto;
    width: fit-content;
    z-index: 100;
    
    .ant-btn {
      background-color: #52c41a;
      border-color: #52c41a;
      color: #fff;
      height: 40px;
      border-radius: 20px;
      padding: 0 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      font-weight: 500;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      transition: all 0.3s;
      
      &:hover {
        background-color: #73d13d;
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
      }
      
      .anticon {
        margin-right: 8px;
        font-size: 16px;
      }
    }
  }
}

:global {
  .ant-select .ant-select-selector {
    border-radius: 8px !important;
  }

  .ant-input {
    border-radius: 8px !important;
  }

  .ant-btn {
    border-radius: 8px !important;
  }

  .ant-tabs-tab {
    transition: all 0.3s ease;
    
    &:hover {
      color: #13C2C2;
    }
  }
  
  .ant-tabs-ink-bar {
    background-color: #13C2C2;
  }
  
  .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
    color: #13C2C2;
  }

  .ant-divider {
    margin: 24px 0;
    border-top: 1px dashed #f0f0f0;
  }

  .highlight-text {
    background-color: #ffe58f;
    padding: 0 2px;
    border-radius: 2px;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}
