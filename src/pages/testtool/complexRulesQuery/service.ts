import request from '@/utils/request';
import type { 
  TransactionType,
  RuleQueryResult,
  QueryParams,
  PageResponse,
  ComplexSceneDto
} from './data.d';
import {
  MEDIUM_CONTROL_MEANINGS,
  CONTRACT_CONTROL_MEANINGS,
  ACCOUNT_CONTROL_MEANINGS
} from './data.d';

/**
 * 获取所有交易类型
 * @returns 返回所有交易类型列表
 */
export async function getTransactionTypes() {
  return request<TransactionType[]>('/testtool/complexRulesQuery/transactionTypes', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

interface ComplexRuleItem {
  accCtrlTypeNo: string;
  contCtrlTypeNo: string;
  mediumCtrlTypeNo: string;
  isExempt: string;
}

/**
 * 根据交易代码查询规则
 * @param params 查询参数
 * @returns 返回规则查询结果
 */
export async function queryRules(params: { pcsNo: string; sceneNo?: string }) {
  const response = await request<{code: number; msg: string; data: string}>('/commonTool/queryComplexRule', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
    data: params,
  });

  console.log('原始响应数据:', response);

  // 处理返回的数据
  if (response && response.code === 200 && response.data) {
    try {
      // data是JSON字符串，需要解析
      const parsedData = JSON.parse(response.data) as ComplexRuleItem[];
      console.log('解析后的规则数据:', parsedData);

      // 转换为RuleQueryResult格式
      const result: RuleQueryResult = {
        mediumLevel: {
          name: '介质层',
          statuses: parsedData[0].mediumCtrlTypeNo.split('').map((bit, index) => ({
            name: `${MEDIUM_CONTROL_MEANINGS[index].meaning}`,
            controlled: bit === '1' ? 1 : 0
          }))
        },
        contractLevel: {
          name: '合约层',
          statuses: parsedData[0].contCtrlTypeNo.split('').map((bit, index) => ({
            name: `${CONTRACT_CONTROL_MEANINGS[index].meaning}`,
            controlled: bit === '1' ? 1 : 0
          }))
        },
        accountLevel: {
          name: '账户层',
          statuses: parsedData[0].accCtrlTypeNo.split('').map((bit, index) => ({
            name: `${ACCOUNT_CONTROL_MEANINGS[index].meaning}`,
            controlled: bit === '1' ? 1 : 0
          }))
        }
      };

      return result;
    } catch (error) {
      console.error('解析规则数据失败:', error);
      throw error;
    }
  }

  throw new Error('获取规则数据失败');
}

/**
 * 根据交易代码查询场景编码列表
 * @param pcsNo 交易代码
 * @returns 返回场景编码列表
 */
export async function queryComplexRuleScene(pcsNo: string) {
  const response = await request<{code: number; msg: string; data: string}>('/commonTool/queryComplexRuleScene', {
    method: 'GET',
    params: { pcsNo: pcsNo },
  });
  
  console.log('原始响应数据:', response);
  
  // 处理返回的数据
  if (response && response.code === 200 && response.data) {
    try {
      // data是JSON字符串，需要解析
      const parsedData = JSON.parse(response.data);
      console.log('解析后的场景编码数据:', parsedData);
      return parsedData;
    } catch (error) {
      console.error('解析场景编码数据失败:', error);
      return [];
    }
  }
  
  return [];
}
