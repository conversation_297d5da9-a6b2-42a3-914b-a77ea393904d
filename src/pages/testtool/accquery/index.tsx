import { PlusOutlined } from "@ant-design/icons";
import WrapContent from "@/components/WrapContent";
import { KeepAlive } from "react-activation";
import type { FormInstance } from "antd";
import { Button, message, Row, Col } from "antd";
import { useIntl, FormattedMessage, useAccess } from "umi";
import React, { useState, useRef, useEffect } from "react";
import type { ProColumns, ActionType } from "@ant-design/pro-table";
import ProTable from "@ant-design/pro-table";
import type { AccQueryData } from "./data.d";
import { accQueryList, exportAccQuery, getDict } from "./service";
import { storage } from "@/utils/storageUtils";

interface FormValueType {
  mediumTpCd?: string;
  vendibiProdtNo?: string;
  cardKindCd?: string;
  dcardCategNo?: string;
  persDepAccKindCd?: string;
  perCertTpCd?: string;
  openAccInstNo?: string;
  shardingId?: string;
}

const AccountQueryList: React.FC = () => {
  const formTableRef = useRef<FormInstance>();
  const actionRef = useRef<ActionType>();
  const [selectedRowsState, setSelectedRows] = useState<AccQueryData[]>([]);
  const [formValues, setFormValues] = useState<FormValueType>({});
  const [tableData, setTableData] = useState<AccQueryData[]>([]);
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const [isFirstRequest, setIsFirstRequest] = useState(true);
  // 添加防抖标志
  const [isRequesting, setIsRequesting] = useState(false);
  const access = useAccess();
  const intl = useIntl();

  // 添加字典数据状态
  const [vendibiProdtOptions, setVendibiProdtOptions] = useState<{ value: string; label: string; }[]>([]);
  // 获取字典数据
  const loadDictData = async () => {
    try {
      console.log('获取字典数据');
      const res = await getDict();
      if (res.code === 200) {
        // 解析 JSON 字符串
        const dictData = JSON.parse(res.data);
        const options = dictData.map((item: any) => ({
          value: item.vendibiProdtNo,
          label: `${item.vendibiProdtNo}-${item.vendibiProdtName}`
        }));
        setVendibiProdtOptions(options);
      }
    } catch (error) {
      console.error('获取字典数据失败:', error);
    }
  };
  // 在组件加载时获取字典数据
  useEffect(() => {
    loadDictData();
  }, []);

  // 搜索表单状态初始化
  const [searchForm, setSearchForm] = useState(() => {
    const savedSearch = storage.get("accquery_search");
    return savedSearch || {};
  });

  // 加载缓存数据的函数
  const loadCacheData = () => {
    try {
      const savedData = storage.get("accquery_data");
      const savedSearch = storage.get("accquery_search");
      console.log('初始化数据:', savedData, savedSearch);

      if (savedData?.records && savedSearch) {
        const records = Array.isArray(savedData.records) ? savedData.records : [];
        // 先设置表单值
        setFormValues(savedSearch);
        // 延迟设置表格数据
        setTimeout(() => {
          if (formTableRef.current) {
            formTableRef.current.setFieldsValue(savedSearch);
          }
          // 再设置表格数据
          setTableData(records);
        }, 100);
      }
    } catch (error) {
      console.error('加载缓存数据失败:', error);
    }
    setIsInitialLoad(false);
  };

  // 初始化数据
  useEffect(() => {
    loadCacheData();
    return () => {
      setIsFirstRequest(true);
      setIsInitialLoad(true);
    };
  }, []);

  // 检查字段使用状态
  const isGroup1Used = !!(formValues.mediumTpCd || formValues.vendibiProdtNo);
  const isGroup2Used = !!(formValues.cardKindCd || formValues.dcardCategNo);

  // 处理重置
  const handleReset = (form: FormInstance | undefined) => {
    // 重置所有状态
    setFormValues({});
    setSelectedRows([]);
    setTableData([]);
    setSearchForm({});

    // 重置表单 - 使用 setTimeout 确保状态更新后再重置表单
    setTimeout(() => {
      form?.resetFields();
      formTableRef.current?.resetFields();

      // 清除表格选中状态
      if (actionRef.current?.clearSelected) {
        actionRef.current.clearSelected();
      }

      // 清除本地存储
      storage.set("accquery_data", null, 0);
      storage.set("accquery_search", null, 0);
    }, 0);
  };

  // 处理导出
  const handleExport = async () => {
    if (selectedRowsState.length === 0) {
      message.warning("请选择数据进行导出");
      return;
    }
    const hide = message.loading("正在导出");
    try {
      await exportAccQuery({
        list: selectedRowsState,
      });
      hide();
      message.success("导出成功");
      return true;
    } catch (error) {
      hide();
      message.error("导出失败，请重试");
      return false;
    }
  };

  // 处理请求
  const handleRequest = async (params: any) => {
    // 如果正在请求中，直接返回空数据
    if (isRequesting) {
      return {
        data: [],
        total: 0,
        success: false
      };
    }

    const { current = 1, pageSize = 10, ...searchParams } = params;
    const requestParams = {
      current: parseInt(current),  // 确保 current 是数字类型
      pageSize: parseInt(pageSize),
      mediumTpCd: searchParams.mediumTpCd
        ? searchParams.mediumTpCd.split("-")[0]
        : '',
      cardKindCd: searchParams.cardKindCd
        ? searchParams.cardKindCd.split("-")[0]
        : '',
      persDepAccKindCd: searchParams.persDepAccKindCd
        ? searchParams.persDepAccKindCd.charAt(0)
        : '',
      perCertTpCd: searchParams.perCertTpCd
        ? searchParams.perCertTpCd.split("-")[0]
        : '',
      // vendibiProdtNo: searchParams.vendibiProdtNo || '',
      vendibiProdtNo: searchParams.vendibiProdtNo
        ? searchParams.vendibiProdtNo.split('-')[0]
        : '',
      dcardCategNo: searchParams.dcardCategNo || '',
      shardingId: searchParams.shardingId || '',
      openAccInstNo: searchParams.openAccInstNo || '',
    };

    try {
      // 只有在非首次请求时才更新缓存
      if (!isFirstRequest) {
        setSearchForm({
          ...requestParams,
          current: parseInt(current),  // 确保缓存中的 current 也是正确的
          pageSize: parseInt(pageSize)
        });
        storage.set("accquery_search", requestParams, 30 * 60);
      }
      setIsFirstRequest(false);

      setIsRequesting(true);  // 设置请求标志
      const res = await accQueryList(requestParams);
      if (res.code === 200) {
        const parsedData = typeof res.data === 'string' ? JSON.parse(res.data) : res.data;
        const records = Array.isArray(parsedData.records) ? parsedData.records : [];
        const total = parsedData.total || 0;

        // 只有在非首次请求时才更新缓存
        if (!isFirstRequest) {
          storage.set("accquery_data", parsedData, 30 * 60);
        }
        setTableData(records);
        return {
          data: records,
          total: total,
          success: true
        };
      }
      return {
        data: [],
        total: 0,
        success: false
      };
    } catch (error) {
      console.error('请求失败:', error);
      return {
        data: [],
        total: 0,
        success: false
      };
    } finally {
      setIsRequesting(false);  // 重置请求标志
      setIsFirstRequest(false);  // 更新首次请求标志
    }
  };

  const columns: ProColumns<AccQueryData>[] = [
    {
      title: (
        <FormattedMessage
          id="autoTest.accquery.mediumNo"
          defaultMessage="介质编号"
        />
      ),
      dataIndex: "mediumNo",
      valueType: "textarea",
      hideInSearch: true,
      ellipsis: true,  // 添加这一行，使用省略号
      width: 200,      // 添加这一行，限制列宽
      fixed: 'left',  // 添加这一行，固定在右侧
      align: 'center',  // 添加这一行，实现居中对齐
    },
    {
      title: (
        <FormattedMessage
          id="autoTest.accquery.mediumTpCd"
          defaultMessage="介质类型"
        />
      ),
      dataIndex: "mediumTpCd",
      valueType: "select",
      valueEnum: {
        "0201": "0201-绿卡",
        "0202": "0202-绿卡通",
        "0203": "0203-绿卡通副卡",
        "0204": "0204-小额支付卡",
        "0301": "0301-活期存折",
        "0302": "0302-定期零整存折",
        "0303": "0303-定期整零存折",
        "0304": "0304-存本取息存折",
        "0305": "0305-本外币活期一本通",
        "0306": "0306-本外币定期一本通",
        "0309": "0309-本币定期一本通",
        "0401": "0401-整存整取存单",
        "0402": "0402-定活两便存单",
        "0403": "0403-通知存款存单",
        "0404": "0404-整存整取特种存单",
      },
      formItemProps: {
        rules: [
          {
            required: isGroup1Used || (!isGroup1Used && !isGroup2Used),
            message: "请输入介质类型",
          },
        ],
      },
      fieldProps: {
        disabled: isGroup2Used,
      },
      ellipsis: true,  // 添加这一行，使用省略号
      width: 180,      // 添加这一行，限制列宽
      align: 'center',  // 添加这一行，实现居中对齐
    },
    {
      title: (
        <FormattedMessage
          id="autoTest.accquery.vendibiProdtNo"
          defaultMessage="可售产品编码"
        />
      ),
      dataIndex: "vendibiProdtNo",
      valueType: "select",  // 改为 select 类型
      valueEnum: Object.fromEntries(
        vendibiProdtOptions.map(option => [
          option.value,
          option.label
        ])
      ),
      formItemProps: {
        rules: [
          {
            required: isGroup1Used || (!isGroup1Used && !isGroup2Used),
            message: "请输入可售产品编码",
          },
        ],
      },
      fieldProps: {
        disabled: isGroup2Used,
        options: vendibiProdtOptions,  // 使用options来提供选项
        // 添加以下配置来调整下拉菜单的样式
        listHeight: 256,          // 下拉列表的高度
        popupMatchSelectWidth: 400,  // 下拉菜单的宽度（像素）
        showSearch: true,  // 启用搜索功能
        filterOption: (input: string, option?: { label: string; value: string }) => {
          if (!option) return false;
          // 将输入和选项都转换为小写进行比较
          const inputLower = input.toLowerCase();
          const labelLower = option.label.toLowerCase();
          // 匹配编码或名称中的任何部分
          return labelLower.includes(inputLower);
        },
        style: {
          width: '100%',
          minWidth: '250px',  // 确保输入框最小宽度
        },
        dropdownStyle: {
          maxWidth: '500px',  // 下拉框最大宽度
          whiteSpace: 'normal',  // 允许文本换行
          wordBreak: 'break-all'  // 在任意字符间断行
        }
      },
      width: 250,      // 添加这一行，限制列宽
      align: 'center',  // 添加这一行，实现居中对齐
      ellipsis: true,  // 添加这一行，使用省略号
    },
    {
      title: (
        <FormattedMessage
          id="autoTest.accquery.custNo"
          defaultMessage="客户编号"
        />
      ),
      dataIndex: "custNo",
      valueType: "textarea",
      hideInSearch: true,
      width: 160,      // 添加这一行，限制列宽
      align: 'center',  // 添加这一行，实现居中对齐
    },
    {
      title: (
        <FormattedMessage
          id="autoTest.accquery.custNm"
          defaultMessage="客户名称"
        />
      ),
      dataIndex: "custNm",
      valueType: "textarea",
      hideInSearch: true,
      ellipsis: true,  // 添加这一行，使用省略号
      width: 120,      // 添加这一行，限制列宽
      align: 'center',  // 添加这一行，实现居中对齐
    },
    {
      title: (
        <FormattedMessage
          id="autoTest.accquery.cardKindCd"
          defaultMessage="卡品种"
        />
      ),
      dataIndex: "cardKindCd",
      valueType: "select",
      valueEnum: {
        "01": "01-储蓄卡",
        "02": "02-联名卡(储蓄卡)",
        "03": "03-联名卡(绿卡通)",
        "04": "04-认同卡(绿卡通)",
        "05": "05-认同卡(储蓄卡)",
        "06": "06-绿卡通卡",
        "07": "07-绿卡通副卡",
        "08": "08-小额支付卡",
        "09": "09-万事达卡",
        "13": "13-绿卡通(万事网联)",
      },
      formItemProps: {
        rules: [
          {
            required: isGroup2Used || (!isGroup1Used && !isGroup2Used),
            message: "请输入卡品种",
          },
        ],
      },
      fieldProps: {
        disabled: isGroup1Used,
      },
      width: 180,      // 添加这一行，限制列宽
      align: 'center',  // 添加这一行，实现居中对齐
    },
    {
      title: (
        <FormattedMessage
          id="autoTest.accquery.dcardCategNo"
          defaultMessage="借记卡类别"
        />
      ),
      dataIndex: "dcardCategNo",
      valueType: "textarea",
      formItemProps: {
        rules: [
          {
            required: isGroup2Used || (!isGroup1Used && !isGroup2Used),
            message: "请输入借记卡类别",
          },
        ],
      },
      fieldProps: {
        disabled: isGroup1Used,
      },
      width: 120,      // 添加这一行，限制列宽
      align: 'center',  // 添加这一行，实现居中对齐
    },
    {
      title: (
        <FormattedMessage
          id="autoTest.accquery.persDepAccKindCd"
          defaultMessage="个人存款账户种类代码"
        />
      ),
      dataIndex: "persDepAccKindCd",
      valueType: "select",
      valueEnum: {
        "0": "0-无关",
        "1": "1-Ⅰ类户",
        "2": "2-Ⅱ类户",
        "3": "3-Ⅲ类户",
      },
      width: 160,      // 添加这一行，限制列宽
      align: 'center',  // 添加这一行，实现居中对齐
    },
    {
      title: (
        <FormattedMessage
          id="autoTest.accquery.perCertTpCd"
          defaultMessage="个人证件类型代码"
        />
      ),
      dataIndex: "perCertTpCd",
      valueType: "select",
      valueEnum: {
        "1010": "1010-居民身份证",
        "1011": "1011-临时居民身份证",
        "1020": "1020-军人身份证件",
        "1021": "1021-士兵证",
        "1022": "1022-军官证",
        "1023": "1023-文职干部证",
        "1024": "1024-军官退休证",
        "1025": "1025-文职干部退休证",
        "1030": "1030-武警身份证件",
        "1031": "1031-武警士兵证",
        "1032": "1032-警官证",
        "1033": "1033-武警文职干部证",
        "1034": "1034-武警军官退休证",
        "1035": "1035-武警文职干部退休证",
        "1040": "1040-户口簿",
        "1050": "1050-中国护照",
        "1051": "1051-外国护照",
        "1060": "1060-学生证",
        "1070": "1070-港澳居民来往内地通行证",
        "1071": "1071-往来港澳通行证",
        "1080": "1080-台湾居民来往大陆通行证",
        "1090": "1090-执行公务证",
        "1100": "1100-机动车驾驶证",
        "1110": "1110-社会保障卡",
        "1120": "1120-外国人居留证",
        "1121": "1121-外国人永久居留证",
        "1130": "1130-旅行证件",
        "1140": "1140-香港居民身份证",
        "1150": "1150-澳门居民身份证",
        "1160": "1160-台湾居民身份证",
        "1170": "1170-边民证",
        "1180": "1180-港澳台居民居住证",
        "1181": "1181-港澳居民居住证",
        "1182": "1182-台湾居民居住证",
        "1190": "1190-外国身份证",
        "1998": "1998-其他（原98类）",
        "1999": "1999-其他证件（个人）",
      },
      fieldProps: {
        // 添加以下配置来调整下拉菜单的样式
        listHeight: 256,          // 下拉列表的高度
        popupMatchSelectWidth: 400,  // 下拉菜单的宽度（像素）
        style: {
          width: '100%',
          minWidth: '250px'  // 确保输入框最小宽度
        },
        dropdownStyle: {
          maxWidth: '500px',  // 下拉框最大宽度
          whiteSpace: 'normal',  // 允许文本换行
          wordBreak: 'break-all'  // 在任意字符间断行
        }
      },
      ellipsis: true,  // 添加这一行，使用省略号
      width: 300,      // 添加这一行，限制列宽
      align: 'center',  // 添加这一行，实现居中对齐
    },
    {
      title: (
        <FormattedMessage
          id="autoTest.accquery.personalCertNo"
          defaultMessage="证件号码"
        />
      ),
      dataIndex: "personalCertNo",
      valueType: "textarea",
      hideInSearch: true,
      width: 180,      // 添加这一行，限制列宽
      align: 'center',  // 添加这一行，实现居中对齐
    },
    {
      title: (
        <FormattedMessage
          id="autoTest.accquery.mobileNo"
          defaultMessage="手机号码"
        />
      ),
      dataIndex: "mobileNo",
      valueType: "textarea",
      hideInSearch: true, // 在搜索表单中隐藏该列
      width: 140,      // 添加这一行，限制列宽
      align: 'center',  // 添加这一行，实现居中对齐
    },
    {
      title: (
        <FormattedMessage
          id="autoTest.accquery.openAccInstNo"
          defaultMessage="开户机构号"
        />
      ),
      dataIndex: "openAccInstNo",
      valueType: "textarea",
      hideInSearch: false, // 在搜索表单中隐藏该列
      width: 120,      // 添加这一行，限制列宽
      align: 'center',  // 添加这一行，实现居中对齐
    },
    {
      title: (
        <FormattedMessage
          id="autoTest.accquery.currCode"
          defaultMessage="币种代码"
        />
      ),
      dataIndex: "currCode",
      valueType: "select",
      hideInSearch: true, // 在搜索表单中隐藏该列
      valueEnum: {
        "036": "036-澳大利亚元",
        "124": "124-加元",
        "344": "344-香港元",
        "392": "392-日元",
        "826": "826-英镑",
        "840": "840-美元",
        "978": "978-欧元（EUR）",
        "156": "156-人民币元",
      },
      width: 120,      // 添加这一行，限制列宽
      align: 'center',  // 添加这一行，实现居中对齐
    },
    {
      title: (
        <FormattedMessage
          id="autoTest.accquery.prodtContractName"
          defaultMessage="产品合约名称"
        />
      ),
      dataIndex: "prodtContractName",
      valueType: "textarea",
      hideInSearch: true, // 在搜索表单中隐藏该列
      ellipsis: true,  // 添加这一行，使用省略号
      width: 200,      // 添加这一行，限制列宽
      align: 'center',  // 添加这一行，实现居中对齐
    },
    {
      title: (
        <FormattedMessage
          id="autoTest.accquery.persDepAccTpCd"
          defaultMessage="个人存款账户类型代码"
        />
      ),
      dataIndex: "persDepAccTpCd",
      valueType: "select",
      hideInSearch: true, // 在搜索表单中隐藏该列
      valueEnum: {
        "1001": "1001-人民币活期储蓄合约",
        "1002": "1002-人民币活期结算合约",
        "1003": "1003-外币活期储蓄合约",
        "1004": "1004-外币活期结算合约",
        "1005": "1005-本外币合一结算合约",
        "2001": "2001-整存整取储蓄存款合约",
        "2002": "2002-整存整取协议存款合约",
        "2003": "2003-提前付息定期存款合约",
        "2004": "2004-定活两便储蓄存款合约",
        "2005": "2005-整存零取储蓄存款合约",
        "2006": "2006-存本取息储蓄存款合约",
        "2007": "2007-零存整取储蓄存款合约",
        "2008": "2008-通知存款合约",
        "2009": "2009-结构性存款合约",
        "2010": "2010-递增计息合约",
        "2011": "2011-梦想加邮站合约",
        "2012": "2012-大额存单合约",
        "2013": "2013-礼仪存单合约",
        "2014": "2014-邮智存合约",
        "3001": "3001-行业应用子账户合约",
        "3002": "3002-电子现金账户合约",
        "4001": "4001-副卡合约",
        "4002": "4002-映射卡合约",
        "4003": "4003-本外币定期一本通合约",
      },
      ellipsis: true,  // 添加这一行，使用省略号
      width: 200,      // 添加这一行，限制列宽
      align: 'center',  // 添加这一行，实现居中对齐
    },
    {
      title: (
        <FormattedMessage
          id="autoTest.accquery.accBal"
          defaultMessage="账户余额"
        />
      ),
      dataIndex: "accBal",
      valueType: "textarea",
      hideInSearch: true, // 在搜索表单中隐藏该列
      width: 120,      // 添加这一行，限制列宽
      align: 'center',  // 添加这一行，实现居中对齐
    },
    {
      title: (
        <FormattedMessage
          id="autoTest.accquery.accAvalBal"
          defaultMessage="账户可用余额"
        />
      ),
      dataIndex: "accAvalBal",
      valueType: "textarea",
      hideInSearch: true, // 在搜索表单中隐藏该列
      width: 120,      // 添加这一行，限制列宽
      align: 'center',  // 添加这一行，实现居中对齐
    },
    {
      title: (
        <FormattedMessage
          id="autoTest.accquery.relMediumNo"
          defaultMessage="关联介质编号"
        />
      ),
      dataIndex: "relMediumNo",
      valueType: "textarea",
      hideInSearch: true, // 在搜索表单中隐藏该列
      width: 200,      // 添加这一行，限制列宽
      align: 'center',  // 添加这一行，实现居中对齐
    },
    {
      title: (
        <FormattedMessage
          id="autoTest.accquery.shardingId"
          defaultMessage="分片值"
        />
      ),
      dataIndex: "shardingId",
      valueType: "select",
      valueEnum: {
        "0001": "0001",
        "0002": "0002",
        "0003": "0003",
        "0004": "0004",
        "0005": "0005",
        "0006": "0006",
        "0007": "0007",
        "0008": "0008",
        "0009": "0009",
        "0010": "0010",
        "0011": "0011",
        "0012": "0012",
        "0013": "0013",
        "0014": "0014",
        "0015": "0015",
        "0016": "0016",
      },
      width: 100,      // 添加这一行，限制列宽
      align: 'center',  // 添加这一行，实现居中对齐
    },
    {
      title: (
        <FormattedMessage
          id="autoTest.accquery.operation"
          defaultMessage="操作"
        />
      ),
      dataIndex: "option",
      width: "120px",
      valueType: "option",
      fixed: 'right',  // 添加这一行，固定在右侧
      align: 'center',  // 添加这一行，实现居中对齐
      render: (_, record) => [
        <Button type="link" size="small" key="resetpwd" hidden={true}>
          <FormattedMessage
            id="autoTest.accquery.operation"
            defaultMessage="操作"
          />
        </Button>,
        <Button
          type="link"
          size="small"
          key="edit"
          // hidden={!access.hasPerms('system:user:edit')} // 根据权限控制按钮是否显示
          disabled={true}
          onClick={() => {
            // 编辑操作
          }}
        >
          <FormattedMessage id="pages.searchTable.edit" defaultMessage="编辑" />
        </Button>,
        <Button
          type="link"
          size="small"
          danger
          key="batchRemove"
          disabled={true}
        >
          <FormattedMessage
            id="pages.searchTable.delete"
            defaultMessage="删除"
          />
        </Button>,
      ],
    },
  ];

  return (
    <KeepAlive
      saveScrollPosition={false}
      when={() => true}
      cacheKey="accquery"
      onActivate={() => {
        setIsFirstRequest(true);
        // 使用 requestAnimationFrame 确保在下一帧渲染
        requestAnimationFrame(() => {
          loadCacheData();
        });
      }}
    >
      <WrapContent>
        <Row gutter={[16, 24]}>
          <Col lg={24} md={24}>
            <ProTable<AccQueryData>
              headerTitle={intl.formatMessage({
                id: "pages.searchTable.title",
                defaultMessage: "信息",
              })}
              actionRef={actionRef}
              formRef={formTableRef}
              form={{
                ignoreRules: false,
                onReset: () => {
                  handleReset(formTableRef.current);
                },
                onValuesChange: (_, values: FormValueType) => {
                  setFormValues(values);
                },
                // 添加初始值
                initialValues: searchForm,
              }}
              rowKey={(record) => record.mediumNo}  // 修改为使用 mediumNo 作为唯一标识
              key="accList"
              search={{
                labelWidth: 150,
                span: {
                  xs: 24,
                  sm: 24,
                  md: 12,
                  lg: 12,
                  xl: 8,
                  xxl: 6,
                },
                layout: "vertical",
                defaultCollapsed: false,
                optionRender: ({ searchText, form }) => [
                  <Button
                    type="primary"
                    key="search"
                    style={{ borderRadius: '8px' }}
                    onClick={() => {
                      setIsFirstRequest(false);
                      form?.submit();
                    }}
                  >
                    {searchText}
                  </Button>,
                  <Button
                    key="reset"
                    style={{ marginLeft: 8, borderRadius: '8px' }}
                    onClick={() => {
                      handleReset(form);
                      // 强制更新表单状态
                      form?.setFieldsValue({});
                    }}
                  >
                    重置
                  </Button>,
                ],
              }}
              toolBarRender={() => [
                <Button
                  type="primary"
                  key="export"
                  style={{ borderRadius: '8px' }}
                  onClick={handleExport}
                  disabled={!access.hasEnvPerms(
                    localStorage.getItem("currentEnv") as string
                  )}
                >
                  <PlusOutlined />
                  <FormattedMessage
                    id="pages.searchTable.export"
                    defaultMessage="导出"
                  />
                </Button>,
              ]}
              pagination={{
                defaultPageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                current: parseInt(storage.get("accquery_search")?.current) || 1,
                pageSize: parseInt(storage.get("accquery_search")?.pageSize) || 10,
                onChange: (page, pageSize) => {
                  const currentSearchParams = formTableRef.current?.getFieldsValue();
                  const params = {
                    ...currentSearchParams,
                    current: page,
                    pageSize: pageSize
                  };
                  // 直接调用 handleRequest 而不是使用 reload
                  handleRequest(params).then((result) => {
                    if (result.success) {
                      // 更新分页信息到缓存
                      storage.set("accquery_search", {
                        ...storage.get("accquery_search"),
                        current: page,
                        pageSize: pageSize
                      }, 30 * 60);
                    }
                  });
                }
              }}
              request={async (params) => {
                // 如果有缓存数据且是首次加载，直接返回缓存数据
                if (isFirstRequest && tableData.length > 0) {
                  const savedData = storage.get("accquery_data");
                  setIsFirstRequest(false);
                  return {
                    data: tableData,
                    success: true,
                    total: savedData?.total || tableData.length,
                    current: parseInt(storage.get("accquery_search")?.current) || 1
                  };
                }
                // 否则执行正常的请求逻辑
                return handleRequest(params);
              }}
              dataSource={tableData}
              rowSelection={{
                type: 'checkbox',
                preserveSelectedRowKeys: true,
                getCheckboxProps: (record) => ({
                  name: record.mediumNo,  // 使用 mediumNo 作为 checkbox 的 name
                }),
                selectedRowKeys: selectedRowsState.map(row => row.mediumNo),  // 使用 mediumNo 作为选中的 key
                onChange: (selectedRowKeys, selectedRows) => {
                  setSelectedRows(selectedRows);
                },
                selections: [
                  {
                    key: 'clear',
                    text: '清空选择',
                    onSelect: () => {
                      setSelectedRows([]);
                    },
                  },
                ],
              }}
              scroll={{ x: 3500 }}
              columnEmptyText="-"
              columns={columns}
            />
          </Col>
        </Row>
      </WrapContent>
    </KeepAlive>
  );
};

export default AccountQueryList;