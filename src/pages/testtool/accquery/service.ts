import request from '@/utils/request';
import { downLoadXlsx } from '@/utils/downloadfile';
import type { AccListParams, AccExportParams } from './data.d';

// 查询账户列表
export async function accQueryList(params: Partial<AccListParams>) {
  // 设置默认值
  const defaultParams: AccListParams = {
    mediumTpCd: '',
    vendibiProdtNo: '',
    cardKindCd: '',
    dcardCategNo: '',
    persDepAccKindCd: '',
    perCertTpCd: '',
    shardingId: '',
    current: 1,
    pageSize: 10,
    ...params  // 使用传入的参数覆盖默认值
  };

  // 将所有参数转换为字符串类型
  const convertedParams: Record<string, string> = {};
  Object.keys(defaultParams).forEach(key => {
    const value = defaultParams[key as keyof AccListParams];
    convertedParams[key] = value?.toString() || '';
  });
  
  const queryString = new URLSearchParams(convertedParams).toString();
  return request(`/testtool/accQuery/list?${queryString}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

// 导出账户数据
// export function exportAccQuery(params: AccExportParams) {
//   return downLoadXlsx(
//     `/testtool/accQuery/export`,
//     {
//       method: 'POST',
//       data: params.list, // 直接传递数据数组
//     },
//     `account_${new Date().getTime()}.xlsx`,
//   );
// }

export function exportAccQuery (params: AccExportParams) {  
  // return downLoadXlsx(`/testtool/accQuery/export`, { params }, `account_${new Date().getTime()}.xlsx`);
  return downLoadXlsx(
    `/testtool/accQuery/export`,
    {
      method: 'POST',
      data: params.list, // 直接传递数据数组
    },
    `account_${new Date().getTime()}.xlsx`,
  );
}

// 获取可售产品编码字典
export async function getDict() {
  return request(`/testtool/accQuery/dict/`, {
    method: 'GET',
  });
}
