export type AccQueryData = {
  mediumNo: string;  // 添加介质编号字段
  mediumTpCd: string;
  vendibiProdtNo: string;
  cardKindCd: string;
  dcardCategNo: string;
  persDepAccKindCd: string;
  perCertTpCd: string;
  openAccInstNo: string;
  shardingId: string;
};

// AccListPagination 定义了账户列表的分页信息
export type AccListPagination = {
  total: number; // 总数
  pageSize: number; // 每页大小
  current: number; // 当前页码
};

// AccListData 定义了账户列表的数据结构
export type AccListData = {
  list: AccQueryData[]; // 用户列表
  pagination: Partial<AccListPagination>; // 分页信息
};

// AccListParams 定义了账户列表的请求参数
export type AccListParams = {
  mediumTpCd: string;    // 改为必填
  vendibiProdtNo: string; // 改为必填
  cardKindCd: string;    // 改为必填
  dcardCategNo: string;  // 改为必填
  persDepAccKindCd: string; // 改为必填
  perCertTpCd: string;   // 改为必填
  shardingId: string;    // 改为必填
  current: number;       // 改为必填
  pageSize: number;      // 改为必填
};

// AccExportParams 定义了导出功能的请求参数
export type AccExportParams = {
  list: AccQueryData[]; // 直接使用选中的数据对象数组
};
