import request from '@/utils/request';
import type { CustQueryParams, CustQueryResponse } from './data.d';

// 查询客户信息
export async function queryCustInfo(params: CustQueryParams): Promise<CustQueryResponse> {
  console.log('service接收到的参数:', params);
  
  // 设置默认值
  const defaultParams: CustQueryParams = {
    custNo: '',
    pageSize: 10,
    current: 1,
  };
  
  // 合并参数，确保传入的参数优先级更高
  const mergedParams = {
    ...defaultParams,
    ...params
  };
  
  console.log('合并后的参数:', mergedParams);
  
  // 处理参数 - 只需要custNo参数
  const queryParams: Record<string, string> = {};
  if (mergedParams.custNo) {
    queryParams.custNo = mergedParams.custNo.toString();
  }
  
  console.log('最终请求参数:', queryParams);
  
  try {
    // 使用GET方法请求后台接口
    const response = await request('/testtool/custQuery', {
      method: 'GET',
      params: queryParams,
    });
    
    return response;
  } catch (error) {
    console.error('请求错误:', error);
    throw error;
  }
}

// 导出客户信息
export async function exportCustInfo(params: CustQueryParams): Promise<Blob> {
  console.log('导出参数:', params);
  
  try {
    const response = await request('/testtool/custQuery/export', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json;charset=UTF-8',
      },
      data: params,
      responseType: 'blob',
    });
    
    return response;
  } catch (error) {
    console.error('导出错误:', error);
    throw error;
  }
}
