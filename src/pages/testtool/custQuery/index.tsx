import React, { useState, useEffect, useRef } from "react";
import {
  Card,
  Typography,
  message,
  Spin,
  Row,
  Col,
  Button,
  Descriptions,
  Tooltip,
} from "antd";
import {
  SearchOutlined,
  ReloadOutlined,
  DownloadOutlined,
  InfoCircleOutlined,
} from "@ant-design/icons";
import { KeepAlive, useActivate, useUnactivate } from "react-activation";
import { storage } from "@/utils/storageUtils";
import WrapContent from "@/components/WrapContent";
import { useAccess } from "umi";
import ProForm, { ProFormText } from "@ant-design/pro-form";
import ProTable from "@ant-design/pro-table";
import type { ProColumns, ActionType } from "@ant-design/pro-table";
import type { FormInstance } from "antd";
import moment from "moment";
import type {
  CustomerInfo,
  MediumInfo,
  CustQueryParams,
  CustQueryResult,
} from "./data.d";
import { queryCustInfo, exportCustInfo } from "./service";
import styles from "./style.less";

const { Title, Paragraph } = Typography;

// 证件类型枚举
const CERT_TYPE_ENUM: Record<string, string> = {
  "1010": "1010-居民身份证",
  "1011": "1011-临时居民身份证",
  "1020": "1020-军人身份证件",
  "1021": "1021-士兵证",
  "1022": "1022-军官证",
  "1023": "1023-文职干部证",
  "1024": "1024-军官退休证",
  "1025": "1025-文职干部退休证",
  "1030": "1030-武警身份证件",
  "1031": "1031-武警士兵证",
  "1032": "1032-警官证",
  "1033": "1033-武警文职干部证",
  "1034": "1034-武警军官退休证",
  "1035": "1035-武警文职干部退休证",
  "1040": "1040-户口簿",
  "1050": "1050-中国护照",
  "1051": "1051-外国护照",
  "1060": "1060-学生证",
  "1070": "1070-港澳居民来往内地通行证",
  "1071": "1071-往来港澳通行证",
  "1080": "1080-台湾居民来往大陆通行证",
  "1090": "1090-执行公务证",
  "1100": "1100-机动车驾驶证",
  "1110": "1110-社会保障卡",
  "1120": "1120-外国人居留证",
  "1121": "1121-外国人永久居留证",
  "1130": "1130-旅行证件",
  "1140": "1140-香港居民身份证",
  "1150": "1150-澳门居民身份证",
  "1160": "1160-台湾居民身份证",
  "1170": "1170-边民证",
  "1180": "1180-港澳台居民居住证",
  "1181": "1181-港澳居民居住证",
  "1182": "1182-台湾居民居住证",
  "1190": "1190-外国身份证",
  "1998": "1998-其他（原98类）",
  "1999": "1999-其他证件（个人）",
};

// 介质类型枚举
const MEDIUM_TYPE_ENUM: Record<string, string> = {
  "0201": "0201-绿卡",
  "0202": "0202-绿卡通",
  "0203": "0203-绿卡通副卡",
  "0204": "0204-小额支付卡",
  "0301": "0301-活期存折",
  "0302": "0302-定期零整存折",
  "0303": "0303-定期整零存折",
  "0304": "0304-存本取息存折",
  "0305": "0305-本外币活期一本通",
  "0306": "0306-本外币定期一本通",
  "0309": "0309-本币定期一本通",
  "0401": "0401-整存整取存单",
  "0402": "0402-定活两便存单",
  "0403": "0403-通知存款存单",
  "0404": "0404-整存整取特种存单",
};

// 类别标识代码枚举
const CATEG_FLAG_ENUM: Record<string, string> = {
  "01": "01-活期",
  "02": "02-定期",
};

// 卡介质代码枚举
const CARD_MEDIUM_ENUM: Record<string, string> = {
  "1": "1-磁条卡",
  "2": "2-复合卡",
  "3": "3-单芯片卡",
  "4": "4-虚拟卡",
};

// 卡品种代码枚举
const CARD_KIND_ENUM: Record<string, string> = {
  "01": "01-储蓄卡",
  "02": "02-联名卡(储蓄卡)",
  "03": "03-联名卡(绿卡通)",
  "04": "04-认同卡(绿卡通)",
  "05": "05-认同卡(储蓄卡)",
  "06": "06-绿卡通卡",
  "07": "07-绿卡通副卡",
  "08": "08-小额支付卡",
  "09": "09-万事达卡",
  "13": "13-绿卡通(万事网联)",
};

const CustQuery: React.FC = () => {
  // 表单和表格引用
  const formRef = useRef<FormInstance>();
  const actionRef = useRef<ActionType>();
  const access = useAccess();

  // 状态管理
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [isVisible, setIsVisible] = useState<boolean>(false);
  const [formKey, setFormKey] = useState<number>(Date.now());

  // 数据状态
  const [customerInfo, setCustomerInfo] = useState<CustomerInfo | null>(null);
  const [mediumList, setMediumList] = useState<MediumInfo[]>([]);
  const [searchParams, setSearchParams] = useState<CustQueryParams>(() => {
    const savedSearch = storage.get("custQuery_search");
    return savedSearch || { custNo: "" };
  });

  // 初始化加载
  useEffect(() => {
    // 从缓存加载数据
    const savedResult = storage.get("custQuery_result");
    if (savedResult) {
      setCustomerInfo(savedResult.customerInfo);
      setMediumList(savedResult.mediumList || []);
    }

    // 从缓存加载搜索参数
    const savedSearch = storage.get("custQuery_search");
    if (savedSearch && formRef.current) {
      formRef.current.setFieldsValue(savedSearch);
      setSearchParams(savedSearch);
    }

    // 添加动画触发
    setTimeout(() => {
      setIsVisible(true);
    }, 100);
  }, []);

  // 当查询结果更新时保存到localStorage
  useEffect(() => {
    if (customerInfo) {
      const result: CustQueryResult = {
        customerInfo,
        mediumList,
      };
      storage.set("custQuery_result", result, 30 * 60); // 保存30分钟
    }
  }, [customerInfo, mediumList]);

  // 当搜索参数更新时保存到localStorage
  useEffect(() => {
    if (searchParams.custNo) {
      storage.set("custQuery_search", searchParams, 30 * 60); // 保存30分钟
    }
  }, [searchParams]);

  // 组件激活时处理
  useActivate(() => {
    setTimeout(() => {
      const savedResult = storage.get("custQuery_result");
      if (savedResult) {
        setCustomerInfo(savedResult.customerInfo);
        setMediumList(savedResult.mediumList || []);
      }

      const savedSearch = storage.get("custQuery_search");
      if (savedSearch && formRef.current) {
        formRef.current.setFieldsValue(savedSearch);
        setSearchParams(savedSearch);
      }

      setIsVisible(true);
      setFormKey(Date.now());
    }, 0);
  });

  // 组件失活时处理
  useUnactivate(() => {
    setLoading(false);
    setError(null);
    setIsVisible(false);
  });

  // 查询客户信息
  const handleSearch = async (values: CustQueryParams) => {
    if (!values.custNo) {
      message.warning("请输入客户编号");
      return;
    }

    // 验证客户编号是否只包含数字
    if (!/^\d+$/.test(values.custNo)) {
      message.error("客户编号只能包含数字");
      return;
    }

    setLoading(true);
    setError(null);
    setSearchParams(values);

    try {
      const response = await queryCustInfo(values);

      if (response.code === 200 && response.data) {
        // 解析后端返回的JSON字符串
        const rawData = JSON.parse(response.data);

        // 处理后端返回的数据结构，映射字段名
        const customerInfo: CustomerInfo = rawData.custInfoVo || {};

        // 处理介质列表数据，并进行字段映射
        const mediumList: MediumInfo[] = (rawData.mediumInfoVoList || []).map(
          (item: any) => ({
            ...item,
            // 映射字段名不一致的部分
            cardKindCd: item.cardKindCd, // 将cardKindCd映射为cardKindCd
            categFlagCd: item.categFlagCd, // 将cateFlagCd映射为categFlagCd
            dcardCategNo: item.dcardCategNo, // 将dcardCateNo映射为dcardCategNo
          })
        );

        setCustomerInfo(customerInfo);
        setMediumList(mediumList);
        message.success("查询成功");

        // 刷新表格
        if (actionRef.current) {
          actionRef.current.reload();
        }
      } else {
        setError(response.msg || "查询失败");
        message.error(response.msg || "查询失败");
      }
    } catch (error) {
      console.error("查询出错:", error);
      setError("查询出错，请稍后重试");
      message.error("查询出错，请稍后重试");
    } finally {
      setLoading(false);
    }
  };

  // 重置表单
  const handleReset = () => {
    if (formRef.current) {
      formRef.current.resetFields();
    }
    setCustomerInfo(null);
    setMediumList([]);
    setError(null);
    setSearchParams({ custNo: "" });
    storage.remove("custQuery_search");
    storage.remove("custQuery_result");
  };

  // 导出数据
  const handleExport = async () => {
    if (!searchParams.custNo) {
      message.warning("请先查询客户信息");
      return;
    }

    try {
      message.loading("正在导出数据...");
      const blob = await exportCustInfo(searchParams);

      // 创建下载链接
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `客户信息_${searchParams.custNo}_${moment().format("YYYYMMDDHHmmss")}.xlsx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      message.success("导出成功");
    } catch (error) {
      console.error("导出失败:", error);
      message.error("导出失败");
    }
  };

  // 格式化日期
  const formatDate = (dateStr?: string) => {
    if (!dateStr) return "-";
    return moment(dateStr, "YYYYMMDD").format("YYYY-MM-DD");
  };

  // 获取证件类型名称
  const getCertTypeName = (code?: string) => {
    if (!code) return "-";
    return CERT_TYPE_ENUM[code] || code;
  };

  // 获取介质类型名称
  const getMediumTypeName = (code?: string) => {
    if (!code) return "-";
    return MEDIUM_TYPE_ENUM[code] || code;
  };

  // 获取类别标识代码名称
  const getCategFlagName = (code?: string) => {
    if (!code) return "-";
    return CATEG_FLAG_ENUM[code] || code;
  };

  // 获取卡介质代码名称
  const getCardMediumName = (code?: string) => {
    if (!code) return "-";
    return CARD_MEDIUM_ENUM[code] || code;
  };

  // 获取卡品种代码名称
  const getCardKindName = (code?: string) => {
    if (!code) return "-";
    return CARD_KIND_ENUM[code] || code;
  };

  // 介质表格列定义
  const columns: ProColumns<MediumInfo>[] = [
    {
      title: "介质号",
      dataIndex: "mediumNo",
      ellipsis: true,
      width: 180,
    },
    {
      title: "介质类型",
      dataIndex: "mediumTpCd",
      ellipsis: true,
      width: 120,
      render: (_, record) => getMediumTypeName(record.mediumTpCd),
    },
    {
      title: "主合约编号",
      dataIndex: "mainContrNo",
      ellipsis: true,
      width: 220,
    },
    {
      title: "类别标识",
      dataIndex: "categFlagCd",
      ellipsis: true,
      width: 100,
      render: (_, record) => getCategFlagName(record.categFlagCd),
    },
    {
      title: "介质印刷号",
      dataIndex: "mediumPrintNo",
      ellipsis: true,
      width: 150,
    },
    {
      title: "卡序列号",
      dataIndex: "cardSeqNo",
      ellipsis: true,
      width: 120,
    },
    {
      title: "卡介质代码",
      dataIndex: "cardMediumCode",
      ellipsis: true,
      width: 120,
      render: (_, record) => getCardMediumName(record.cardMediumCode),
    },
    {
      title: "卡品种代码",
      dataIndex: "cardKindCd",
      ellipsis: true,
      width: 120,
      render: (_, record) => getCardKindName(record.cardKindCd),
    },
    {
      title: "借记卡类别",
      dataIndex: "dcardCategNo",
      ellipsis: true,
      width: 120,
    },
    {
      title: () => (
        <Tooltip
          title={
            <div style={{ width: "100%", color: "rgba(0, 0, 0, 0.85)" }}>
              <p>介质状态标志码说明</p>
              <ul>
                <li>
                  <strong>第1位 介质状态代码</strong>
                </li>
                <li>0-正常；1-注销（销户或换卡交易修改）；</li>
                <li>2-已移出。</li>
                <li>
                  <strong>第2位 挂失状态代码</strong>
                </li>
                <li>0-正常；1-凭证临时挂失；2-凭证正式挂失；</li>
                <li>3-密码挂失；4-双挂失(挂失交易修改)。</li>
                <li>
                  <strong>第3位 激活状态代码</strong>
                </li>
                <li>0-正常；1-未激活（激活交易修改）。</li>
                <li>
                  <strong>第4位 吞没状态代码</strong>
                </li>
                <li>0-正常；1-吞没（吞没或领用时修改）。</li>
                <li>
                  <strong>第5位 可疑状态代码</strong> 0-正常；1-可疑
                </li>
                <li>
                  <strong>第6位 密码状态代码</strong> 0-正常；1-密码锁定
                </li>
                <li>
                  <strong>第7位 需更换折/单标志</strong> 0-否，1-是
                </li>
                <li>
                  <strong>第8位 已换卡标志</strong> 0-否，1-是
                </li>
              </ul>
            </div>
          }
          placement="top"
          overlayClassName={styles.customTooltip}
          color="#fff"
          overlayStyle={{
            minWidth: "350px",
            maxWidth: "500px",
          }}
        >
          <span>
            介质状态标志码{" "}
            <InfoCircleOutlined style={{ marginLeft: 4, color: "#1890ff" }} />
          </span>
        </Tooltip>
      ),
      dataIndex: "statusFlagCd",
      ellipsis: true,
      width: 220,
    },
    {
      title: () => (
        <Tooltip
          title={
            <div style={{ width: "100%", color: "rgba(0, 0, 0, 0.85)" }}>
              <p>介质属性标志码说明</p>
              <ul>
                <li>
                  <strong>第1位 个人联名账户标志</strong>
                </li>
                <li>0-否；1-是</li>
                <li>
                  <strong>第2位 新旧介质标志代码</strong>
                </li>
                <li>0-无关；1-新；2-旧</li>
                <li>
                  <strong>第3位 虚拟介质标志</strong>
                </li>
                <li>0-否；1-是</li>
                <li>
                  <strong>第4位 特定范围挂失标志</strong>
                </li>
                <li>0-否；1-是</li>
                <li>
                  <strong>第5位：是否不校验迁移介质有效性</strong>
                </li>
                <li>0-否，1-是</li>
              </ul>
            </div>
          }
          placement="top"
          overlayClassName={styles.customTooltip}
          color="#fff"
          overlayStyle={{
            minWidth: "300px",
            maxWidth: "500px",
          }}
        >
          <span>
            介质属性标志码{" "}
            <InfoCircleOutlined style={{ marginLeft: 4, color: "#1890ff" }} />
          </span>
        </Tooltip>
      ),
      dataIndex: "attrbuteFlagCd",
      ellipsis: true,
      width: 220,
    },
    {
      title: () => (
        <Tooltip
          title={
            <div style={{ width: "100%", color: "rgba(0, 0, 0, 0.85)" }}>
              <p>介质认证方式标志码说明</p>
              <ul>
                <li>
                  <strong>第1位</strong> 密码 0-无；1-密码。
                </li>
                <li>
                  <strong>第2位</strong> 印鉴 0-无；1-印鉴。
                </li>
                <li>
                  <strong>第3位</strong> 指纹 0-无；1-指纹。
                </li>
                <li>
                  <strong>第4位</strong> 人脸 0-无；1-人脸。
                </li>
                <li>
                  <strong>第5位</strong> 声纹 0-无；1-声纹。
                </li>
                <li>
                  <strong>第6位</strong> 指静脉 0-无；1-指静脉。
                </li>
              </ul>
            </div>
          }
          placement="top"
          overlayClassName={styles.customTooltip}
          color="#fff"
          overlayStyle={{
            minWidth: "260",
            maxWidth: "500px",
          }}
        >
          <span>
            介质认证方式标志码{" "}
            <InfoCircleOutlined style={{ marginLeft: 4, color: "#1890ff" }} />
          </span>
        </Tooltip>
      ),
      dataIndex: "crtModeFlagCd",
      ellipsis: true,
      width: 220,
    },
    {
      title: "开立日期",
      dataIndex: "openDt",
      ellipsis: true,
      width: 120,
      render: (_, record) => formatDate(record.openDt),
    },
    {
      title: "开立机构号",
      dataIndex: "drawInstNo",
      ellipsis: true,
      width: 120,
    },
    {
      title: "申请制卡日期",
      dataIndex: "applyMakecardDt",
      ellipsis: true,
      width: 120,
      render: (_, record) => formatDate(record.applyMakecardDt),
    },
    {
      title: "介质有效日期",
      dataIndex: "mediumValidDt",
      ellipsis: true,
      width: 120,
      render: (_, record) => formatDate(record.mediumValidDt),
    },
  ];

  return (
    <WrapContent>
      <div className={styles.container} style={{ opacity: isVisible ? 1 : 0 }}>
        {/* 上部分：查询表单 */}
        <Card className={styles.card}>
          <div className={styles.titleContainer}>
            <Title level={4}>客户信息查询</Title>
          </div>

          <div className={styles.formContainer}>
            <ProForm
              formRef={formRef}
              key={formKey}
              layout="horizontal"
              onFinish={handleSearch}
              submitter={{
                render: (props) => {
                  return (
                    <Row justify="end" gutter={16}>
                      <Col>
                        <Button
                          type="primary"
                          icon={<SearchOutlined />}
                          onClick={() => props.form?.submit()}
                          loading={loading}
                          className={styles.roundButton}
                        >
                          查询
                        </Button>
                      </Col>
                      <Col>
                        <Button
                          icon={<ReloadOutlined />}
                          onClick={handleReset}
                          className={styles.roundButton}
                        >
                          重置
                        </Button>
                      </Col>
                      {customerInfo && (
                        <Col>
                          <Button
                            icon={<DownloadOutlined />}
                            onClick={handleExport}
                            className={styles.roundButton}
                          >
                            导出
                          </Button>
                        </Col>
                      )}
                    </Row>
                  );
                },
              }}
            >
              <Row gutter={16}>
                <Col xs={24} sm={24} md={12} lg={8} xl={6}>
                  <ProFormText
                    name="custNo"
                    label="客户编号"
                    placeholder="请输入客户编号"
                    rules={[
                      { required: true, message: "请输入客户编号" },
                      {
                        pattern: /^\d+$/,
                        message: "客户编号只能输入数字",
                      },
                    ]}
                    className={styles.formItem}
                    fieldProps={{
                      bordered: true,
                      style: {
                        borderRadius: "6px",
                        textAlign: "left",
                        paddingLeft: "12px",
                      },
                      allowClear: true,
                      onChange: (e) => {
                        // 移除非数字字符
                        const value = e.target.value;
                        if (value && !/^\d*$/.test(value)) {
                          e.target.value = value.replace(/\D/g, "");
                          formRef.current?.setFieldsValue({
                            custNo: e.target.value,
                          });
                        }
                      },
                    }}
                  />
                </Col>
              </Row>
            </ProForm>
          </div>
        </Card>

        {/* 中间部分：客户详细信息 */}
        {loading ? (
          <Card className={styles.card}>
            <div className={styles.loadingContainer}>
              <Spin tip="正在加载客户信息..." size="large" />
            </div>
          </Card>
        ) : error ? (
          <Card className={styles.card}>
            <div className={styles.emptyState}>
              <InfoCircleOutlined className={styles.emptyIcon} />
              <div className={styles.emptyText}>{error}</div>
            </div>
          </Card>
        ) : customerInfo ? (
          <Card
            className={styles.card}
            title={<Title level={4}>客户详细信息</Title>}
          >
            <div className={styles.descriptionsContainer}>
              <Descriptions
                bordered
                column={{ xxl: 4, xl: 3, lg: 3, md: 2, sm: 1, xs: 1 }}
              >
                <Descriptions.Item label="客户分片">
                  {customerInfo.shardingId || "-"}
                </Descriptions.Item>
                <Descriptions.Item label="客户编号">
                  {customerInfo.custNo || "-"}
                </Descriptions.Item>
                <Descriptions.Item label="客户名称">
                  {customerInfo.custNm || "-"}
                </Descriptions.Item>
                <Descriptions.Item label="证件类型">
                  {getCertTypeName(customerInfo.perCertTpCd)}
                </Descriptions.Item>
                <Descriptions.Item label="证件号码">
                  {customerInfo.personalCertNo || "-"}
                </Descriptions.Item>
                <Descriptions.Item label="证件有效期">
                  {formatDate(customerInfo.certValidDlineDate)}
                </Descriptions.Item>
                <Descriptions.Item label="普通卡张数">
                  {customerInfo.ordinaryCardNumShee || "0"}
                </Descriptions.Item>
                <Descriptions.Item label="特殊卡张数">
                  {customerInfo.specCardNumShee || "0"}
                </Descriptions.Item>
                <Descriptions.Item label="活期账户数">
                  {customerInfo.curtAccnum || "0"}
                </Descriptions.Item>
                <Descriptions.Item label="I类账户数">
                  {customerInfo.iLaccNum || "0"}
                </Descriptions.Item>
                <Descriptions.Item label="II类账户数">
                  {customerInfo.iiLaccNum || "0"}
                </Descriptions.Item>
                <Descriptions.Item label="III类账户数">
                  {customerInfo.iiiLaccNum || "0"}
                </Descriptions.Item>
              </Descriptions>
            </div>
          </Card>
        ) : null}

        {/* 下部分：介质信息表格 */}
        {customerInfo && (
          <Card
            className={styles.card}
            title={<Title level={4}>介质信息列表</Title>}
          >
            <div className={styles.table}>
              <ProTable<MediumInfo>
                headerTitle="介质信息"
                actionRef={actionRef}
                rowKey="mediumNo"
                search={false}
                options={{
                  density: true,
                  fullScreen: true,
                  setting: true,
                  reload: false,
                }}
                pagination={{
                  showSizeChanger: true,
                  showQuickJumper: true,
                  defaultPageSize: 10,
                }}
                dataSource={mediumList}
                columns={columns}
                scroll={{ x: "max-content" }}
                loading={loading}
              />
            </div>
          </Card>
        )}
      </div>
    </WrapContent>
  );
};

export default () => (
  <KeepAlive>
    <CustQuery />
  </KeepAlive>
);
