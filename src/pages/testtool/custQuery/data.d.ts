// 客户查询相关数据类型定义

// 客户信息类型
export interface CustomerInfo {
  custNo?: string;           // 客户编号
  custNm?: string;           // 客户名称
  perCertTpCd?: string;      // 证件类型
  personalCertNo?: string;   // 证件号码
  certValidDlineDate?: string; // 证件有效期
  ordinaryCardNumShee?: string; // 普通卡张数
  specCardNumShee?: string;  // 特殊卡张数
  curtAccnum?: string;       // 活期账户数
  iLaccNum?: string;         // I类账户数
  iiLaccNum?: string;        // II类账户数
  iiiLaccNum?: string;       // III类账户数
  mobileNo?: string;         // 手机号码
  shardingId?: string;       // 分片值
}

// 介质信息类型
export interface MediumInfo {
  mediumNo?: string;         // 介质号
  mediumTpCd?: string;       // 介质类型代码
  mainContrNo?: string;      // 主合约编号
  categFlagCd?: string;      // 类别标识代码
  mediumPrintNo?: string;    // 介质印刷号
  cardSeqNo?: string;        // 卡序列号
  cardMediumCode?: string;   // 卡介质代码
  cardKindCd?: string;       // 卡品种代码
  dcardCategNo?: string;     // 借记卡类别编码
  openDt?: string;           // 开立日期
  drawInstNo?: string;       // 开立机构号
  applyMakecardDt?: string;  // 申请制卡日期
  mediumValidDt?: string;    // 介质有效日期
  relMediumTypeFlagCd?: string; // 关联介质类型标志码
  statusFlagCd?: string;     // 介质状态标志码
  attrbuteFlagCd?: string;   // 介质属性标志码
  crtModeFlagCd?: string;    // 介质认证方式标志码
}

// 查询参数类型
export interface CustQueryParams {
  custNo: string;            // 客户编号
  pageSize?: number;         // 每页记录数
  current?: number;          // 当前页码
}

// 分页响应类型
export interface PageResponse<T> {
  records: T[];              // 记录列表
  total: number;             // 总记录数
  size: number;              // 每页记录数
  current: number;           // 当前页码
  pages: number;             // 总页数
}

// 查询响应类型
export interface CustQueryResponse {
  code: number;              // 响应代码
  msg: string;               // 响应消息
  data: string;              // 响应数据，JSON字符串
}

// 完整的查询结果类型
export interface CustQueryResult {
  customerInfo: CustomerInfo; // 客户信息
  mediumList: MediumInfo[];  // 介质列表
}