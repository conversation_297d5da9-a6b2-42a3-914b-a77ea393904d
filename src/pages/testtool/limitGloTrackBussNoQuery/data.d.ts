export interface LimitTxSumInfoOutputVo {
  lmtAmtCtrlItemNo: string;
  lmtAmtCtrlItemName: string;
  lmtAmtCtrlItemSumVal: string;
  lmtAmtCtrlItemVal: string;
}

export interface LimitTrackData {
  globalBusiTrackNo: string;
  zoneVal: string;
  lmtAmtCheckObjAttrbVal: string;
  currCode: string;
  txAmt: string;
  convtEquivaleRmb: string;
  sceneNo: string;
  sceneName: string;
  limitTxSumInfoOutputList: LimitTxSumInfoOutputVo[];
}

export interface LimitTrackParams {
  globalBusiTrackNo?: string;
  zoneVal?: string;
  [key: string]: any;
}