import request from '@/utils/request';
import type { LimitTrackParams } from './data.d';

// 查询限额流水号列表
export async function limitTrackList(params: Partial<LimitTrackParams>) {
  console.log('service接收到的参数:', params);
  
  // 设置默认值
  const defaultParams: LimitTrackParams = {
    globalBusiTrackNo: '',
    zoneVal: '',
  };
  
  // 合并参数，确保传入的参数优先级更高
  const mergedParams = {
    ...defaultParams,
    ...params
  };
  
  console.log('合并后的参数:', mergedParams);
  
  // 处理参数
  const requestBody: Record<string, any> = {};
  Object.keys(mergedParams).forEach(key => {
    const value = mergedParams[key as keyof LimitTrackParams];
    
    if (value !== undefined && value !== null) {
      requestBody[key] = value.toString();
    } else {
      requestBody[key] = '';
    }
  });
  
  console.log('最终请求体:', requestBody);
  
  try {
    const response = await request('/testtool/limitSumInfoQueryByGlobalBusiTrackNo/list', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json;charset=UTF-8',
      },
      data: requestBody,
    });
    
    // 处理响应数据，转换为新的数据结构
    if (response.code === 200 && response.data) {
      const parsedData = typeof response.data === 'string' ? JSON.parse(response.data) : response.data;
      
      // 转换数据结构
      if (Array.isArray(parsedData)) {
        response.data = transformResponseData(parsedData);
      } else {
        response.data = [transformResponseData([parsedData])][0];
      }
    }
    
    return response;
  } catch (error) {
    console.error('请求错误:', error);
    throw error;
  }
}

// 转换响应数据结构，将控制项相关字段整合到子列表中
function transformResponseData(data: any[]) {
  // 按流水号分组
  const groupedData = data.reduce((acc: Record<string, any[]>, item: any) => {
    // 使用更精确的组合键来区分不同记录
    // 这里我们使用流水号+场景编码作为唯一标识
    const key = `${item.globalBusiTrackNo}_${item.sceneNo}`;
    if (!acc[key]) {
      acc[key] = [];
    }
    acc[key].push(item);
    return acc;
  }, {});
  
  // 转换为新的数据结构
  return Object.keys(groupedData).map(key => {
    const items = groupedData[key];
    const firstItem = items[0];
    
    // 打印日志，检查原始数据中的 limitTxSumInfoOutputList
    console.log('原始数据中的控制项:', firstItem.globalBusiTrackNo, firstItem.limitTxSumInfoOutputList);
    
    // 提取主记录信息
    const result = {
      globalBusiTrackNo: firstItem.globalBusiTrackNo || '',
      zoneVal: firstItem.zoneVal || '',
      lmtAmtCheckObjAttrbVal: firstItem.lmtAmtCheckObjAttrbVal || '',
      currCode: firstItem.currCode || '',
      txAmt: firstItem.txAmt || '',
      convtEquivaleRmb: firstItem.convtEquivaleRmb || '',
      sceneNo: firstItem.sceneNo || '',
      sceneName: firstItem.sceneName || '',
      // 直接传递原始的 limitTxSumInfoOutputList 数组，不做任何处理
      limitTxSumInfoOutputList: firstItem.limitTxSumInfoOutputList || []
    };
    
    return result;
  });
}