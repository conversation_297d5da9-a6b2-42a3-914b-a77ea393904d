import React, { useState, useRef, useEffect } from "react";
import { Button, message, Card, Typography, Table } from "antd";
import type { ColumnType } from "antd/es/table";
import {
  SearchOutlined,
  ReloadOutlined,
} from "@ant-design/icons";
import type { ProColumns, ActionType } from "@ant-design/pro-table";
import ProTable from "@ant-design/pro-table";
import type { FormInstance } from "antd";
import WrapContent from "@/components/WrapContent";
import { KeepAlive } from "react-activation";
import { storage } from "@/utils/storageUtils";
import type { LimitTrackData, LimitTxSumInfoOutputVo } from "./data.d";
import { limitTrackList } from "./service";
import styles from "./style.less";

const { Title } = Typography;

// 添加币种映射的类型定义
interface CurrCodeItem {
  text: string;
}

interface CurrCodeMapType {
  [key: string]: CurrCodeItem;
}

interface FormValueType {
  globalBusiTrackNo?: string;
  zoneVal?: string;
}

// 搜索表单类型定义
interface SearchFormType {
  current?: number;
  pageSize?: number;
  globalBusiTrackNo?: string;
  zoneVal?: string;
}

const LimitGloTrackBussNoQuery: React.FC = () => {
  const formTableRef = useRef<FormInstance>();
  const actionRef = useRef<ActionType>();
  const [formValues, setFormValues] = useState<FormValueType>({});
  const [tableData, setTableData] = useState<LimitTrackData[]>([]);
  const [isFirstRequest, setIsFirstRequest] = useState(true);
  const [manualSearch, setManualSearch] = useState(false);
  
  // 搜索表单状态初始化
  const [searchForm, setSearchForm] = useState<SearchFormType>(() => {
    const savedSearch = storage.get("limitTrack_search");
    return savedSearch || {};
  });

  // 修改币种代码映射的声明
  const currCodeMap: CurrCodeMapType = {
    "036": { text: "036-澳大利亚元" },
    "124": { text: "124-加元" },
    "344": { text: "344-香港元" },
    "392": { text: "392-日元" },
    "826": { text: "826-英镑" },
    "840": { text: "840-美元" },
    "978": { text: "978-欧元（EUR）" },
    "156": { text: "156-人民币元" },
  } as const;

  // 加载缓存数据的函数
  const loadCacheData = () => {
    try {
      const savedData = storage.get("limitTrack_data");
      const savedSearch = storage.get("limitTrack_search");

      console.log("加载缓存数据:", savedData, savedSearch);

      if (savedData?.records && savedSearch) {
        // 设置表单值
        setFormValues(savedSearch);

        // 确保表单值被正确设置
        setTimeout(() => {
          if (formTableRef.current) {
            formTableRef.current.setFieldsValue(savedSearch);
          }
        }, 100);

        // 设置表格数据
        setTableData(savedData.records || []);
        setSearchForm(savedSearch);
        
        // 标记为非首次请求，允许显示数据
        setIsFirstRequest(false);
        setManualSearch(true);
      }
    } catch (error) {
      console.error("加载缓存数据失败:", error);
    }
  };

  // 初始化数据
  useEffect(() => {
    loadCacheData();

    // 添加一个延迟设置表单值的操作，确保表单已经渲染完成
    const timer = setTimeout(() => {
      const savedSearch = storage.get("limitTrack_search");
      if (savedSearch && formTableRef.current) {
        formTableRef.current.setFieldsValue(savedSearch);
      }
    }, 300);

    return () => {
      clearTimeout(timer);
      setIsFirstRequest(true);
    };
  }, []);

  // 处理重置
  const handleReset = (form: FormInstance | undefined) => {
    // 重置所有状态
    setFormValues({});
    setTableData([]);
    setIsFirstRequest(true); // 重置为首次请求状态
    setManualSearch(false); // 重置手动搜索标志
    
    // 完全清空搜索表单，包括分页信息
    setSearchForm({
      current: 1,
      pageSize: 10
    });

    // 重置表单
    setTimeout(() => {
      form?.resetFields();
      formTableRef.current?.resetFields();

      // 清除本地存储
      storage.set("limitTrack_data", null, 0);
      storage.set("limitTrack_search", null, 0);
      
      // 重置表格数据，但不触发新的请求
      if (actionRef.current) {
        actionRef.current.clearSelected?.();
      }
      
      // 手动更新表格数据为空
      setTableData([]);
    }, 0);
  };

  // 处理搜索按钮点击
  const handleSearch = () => {
    formTableRef.current?.validateFields().then(values => {
      // 记录表单值，确保它们被正确传递
      setFormValues(values);
      
      // 重要：重置分页参数到第一页
      const newSearchForm = {
        ...values,
        current: 1,  // 重置为第一页
        pageSize: searchForm.pageSize || 10
      };
      
      // 一次性更新所有状态，避免多次渲染
      setSearchForm(newSearchForm);
      setManualSearch(true);
      
      // 使用 reload 方法
      actionRef.current?.reload();
    }).catch(error => {
      console.error('表单验证失败:', error);
    });
  };

  // 处理请求
  const handleRequest = async (params: any) => {
    // 如果是首次加载且未手动搜索，但有缓存数据，则返回缓存数据
    if (isFirstRequest && !manualSearch) {
      const cachedData = storage.get("limitTrack_data");
      if (cachedData) {
        // 处理缓存数据，确保是数组格式
        const records = Array.isArray(cachedData) ? cachedData : 
                       (cachedData.records ? cachedData.records : [cachedData]);
        setTableData(records);
        return {
          data: records,
          total: records.length,
          success: true,
        };
      }
      return {
        data: [],
        total: 0,
        success: true,
      };
    }

    // 获取当前表单值，确保使用最新的表单数据
    const formData = formTableRef.current?.getFieldsValue() || {};

    const requestParams = {
      globalBusiTrackNo: formData.globalBusiTrackNo || "",
      zoneVal: formData.zoneVal || "",
    };

    try {
      // 更新缓存
      setSearchForm(formData);
      storage.set("limitTrack_search", formData, 30 * 60);
      
      setIsFirstRequest(false);

      const res = await limitTrackList(requestParams);

      if (res.code === 200) {
        // 处理新的响应格式
        let parsedData;
        if (typeof res.data === "string") {
          try {
            parsedData = JSON.parse(res.data);
          } catch (e) {
            console.error("解析响应数据失败:", e);
            parsedData = res.data;
          }
        } else {
          parsedData = res.data;
        }
        
        // 确保数据是数组格式
        const dataArray = Array.isArray(parsedData) ? parsedData : [parsedData];
        
        // 打印原始数据，检查 limitTxSumInfoOutputList 字段
        console.log("原始数据:", dataArray);
        
        // 转换为新的数据结构
        const records = dataArray.map(transformData);
        
        console.log("处理后的记录:", records);
        
        // 更新缓存
        storage.set("limitTrack_data", { records }, 30 * 60);
        setTableData(records);
        
        return {
          data: records,
          total: records.length,
          success: true,
        };
      } else {
        message.error(res.msg || "查询失败");
      }
      return {
        data: [],
        total: 0,
        success: false,
      };
    } catch (error) {
      console.error("查询失败:", error);
      return {
        data: [],
        total: 0,
        success: false,
      };
    }
  };

  // 转换数据结构的函数
  const transformData = (item: any): LimitTrackData => {
    console.log("转换单条数据:", item);
    
    // 提取主记录信息
    const mainData: LimitTrackData = {
      globalBusiTrackNo: item.globalBusiTrackNo || '',
      zoneVal: item.zoneVal || '',
      lmtAmtCheckObjAttrbVal: item.lmtAmtCheckObjAttrbVal || '',
      currCode: item.currCode || '',
      txAmt: item.txAmt || '',
      convtEquivaleRmb: item.convtEquivaleRmb || '',
      sceneNo: item.sceneNo || '',
      sceneName: item.sceneName || '',
      limitTxSumInfoOutputList: []
    };
    
    // 处理控制项信息
    if (item.limitTxSumInfoOutputList && Array.isArray(item.limitTxSumInfoOutputList) && item.limitTxSumInfoOutputList.length > 0) {
      // 直接使用 limitTxSumInfoOutputList 数组
      mainData.limitTxSumInfoOutputList = item.limitTxSumInfoOutputList.map((controlItem: any) => ({
        lmtAmtCtrlItemNo: controlItem.lmtAmtCtrlItemNo || '',
        lmtAmtCtrlItemName: controlItem.lmtAmtCtrlItemName || '',
        lmtAmtCtrlItemSumVal: controlItem.lmtAmtCtrlItemSumVal || '',
        lmtAmtCtrlItemVal: controlItem.lmtAmtCtrlItemVal || ''
      }));
      console.log('处理控制项数据:', item.globalBusiTrackNo, mainData.limitTxSumInfoOutputList);
    } else if (item.lmtAmtCtrlItemNo || item.lmtAmtCtrlItemName || 
        item.lmtAmtCtrlItemSumVal || item.lmtAmtCtrlItemVal) {
      // 兼容旧的数据结构，如果控制项信息直接在主记录中
      mainData.limitTxSumInfoOutputList.push({
        lmtAmtCtrlItemNo: item.lmtAmtCtrlItemNo || '',
        lmtAmtCtrlItemName: item.lmtAmtCtrlItemName || '',
        lmtAmtCtrlItemSumVal: item.lmtAmtCtrlItemSumVal || '',
        lmtAmtCtrlItemVal: item.lmtAmtCtrlItemVal || ''
      });
    } else {
      // 即使没有控制项信息，也添加一个空记录，确保可以展开
      mainData.limitTxSumInfoOutputList.push({
        lmtAmtCtrlItemNo: '无控制项',
        lmtAmtCtrlItemName: '无控制项名称',
        lmtAmtCtrlItemSumVal: '-',
        lmtAmtCtrlItemVal: '-'
      });
    }
    
    return mainData;
  };

  // 将 columns 变量重命名为 mainColumns，并在 ProTable 中使用它
  const mainColumns: ProColumns<LimitTrackData>[] = [
    {
      title: "流水号",
      dataIndex: "globalBusiTrackNo",
      valueType: "text",
      hideInSearch: false,
      width: 220,
      align: "center",
      fieldProps: {
        style: {
          width: "300px",
          borderRadius: "8px",  
        },
        placeholder: "请输入流水号",
        onKeyPress: (e: React.KeyboardEvent<HTMLInputElement>) => {
          const regex = /[a-zA-Z0-9_\.]/;
          const key = String.fromCharCode(e.charCode);
          if (!regex.test(key)) {
            e.preventDefault();
          }
        },
        onChange: (e: React.ChangeEvent<HTMLInputElement>) => {
          const value = e.target.value;
          if (value && !/^[a-zA-Z0-9_\.]*$/.test(value)) {
            e.target.value = value.replace(/[^a-zA-Z0-9_\.]/g, '');
          }
        },
      },
      formItemProps: {
        rules: [
          {
            pattern: /^[a-zA-Z0-9_\.]*$/,
            message: '流水号只能包含大小写字母、数字',
          },
          {
            required: true,
            message: '请输入流水号',
          },
        ],
      },
      // 添加必填标记
      render: (_, record) => record.globalBusiTrackNo,
    },
    {
      title: "客户编号",
      dataIndex: "zoneVal",
      valueType: "text",
      hideInSearch: false,
      width: 180,
      align: "center",
      fieldProps: {
        style: {
          width: "300px",
          borderRadius: "8px",  
        },
        placeholder: "请输入客户编号",
      },
      formItemProps: {
        rules: [
          {
            required: true,
            message: '请输入客户编号',
          },
        ],
      },
      // 添加必填标记
      render: (_, record) => record.zoneVal,
    },
    {
      title: "介质号",
      dataIndex: "lmtAmtCheckObjAttrbVal",
      valueType: "text",
      hideInSearch: true,
      width: 180,
      align: "center",
    },
    {
      title: "币种代码",
      dataIndex: "currCode",
      valueType: "text", 
      hideInSearch: true,
      width: 150,
      align: "center",
      render: (_, record) => {
        const currCode = record.currCode;
        return currCodeMap[currCode]?.text || currCode;
      },
    },
    {
      title: "交易金额",
      dataIndex: "txAmt",
      valueType: "text",
      hideInSearch: true,
      width: 120,
      align: "center",
    },
    {
      title: "等值人民币",
      dataIndex: "convtEquivaleRmb",
      valueType: "text",
      hideInSearch: true,
      width: 120,
      align: "center",
    },
    {
      title: "限额场景编码",
      dataIndex: "sceneNo",
      valueType: "text",
      hideInSearch: true,
      width: 180,
      align: "center",
    },
    {
      title: "限额场景小类名称",
      dataIndex: "sceneName",
      valueType: "text",
      hideInSearch: true,
      width: 210,
      align: "center",
    }
  ];

  // 子表格列定义
  const expandedColumns: ColumnType<LimitTxSumInfoOutputVo>[] = [
    {
      title: "限额控制项编码",
      dataIndex: "lmtAmtCtrlItemNo",
      key: "lmtAmtCtrlItemNo",
      width: 180,
      align: "center",
    },
    {
      title: "限额控制项名称",
      dataIndex: "lmtAmtCtrlItemName",
      key: "lmtAmtCtrlItemName",
      width: 220,
      align: "center",
    },
    {
      title: "交易前已累计值",
      dataIndex: "lmtAmtCtrlItemSumVal",
      key: "lmtAmtCtrlItemSumVal",
      width: 120,
      align: "center",
    },
    {
      title: "限额控制项标数值",
      dataIndex: "lmtAmtCtrlItemVal",
      key: "lmtAmtCtrlItemVal",
      width: 120,
      align: "center",
    },
  ];

  // 可展开行的渲染函数
  const expandedRowRender = (record: LimitTrackData) => {
    console.log('展开行数据:', record.globalBusiTrackNo, record.limitTxSumInfoOutputList);
    return (
      <div className={styles.expandedTable}>
        <Table
          columns={expandedColumns}
          dataSource={record.limitTxSumInfoOutputList}
          pagination={false}
          rowKey={(item, index) => `${item.lmtAmtCtrlItemNo}_${index}`} // 使用更可靠的行键
          size="small"
          bordered
        />
      </div>
    );
  };

  return (
    <KeepAlive name="limitGloTrackBussNoQuery" saveScrollPosition="screen">
      <WrapContent>
        <div className={styles.container}>
          <Card className={styles.card}>
            <div className={styles.titleContainer}>
              <Title level={4}>限额流水查询</Title>
            </div>
            <ProTable<LimitTrackData>
              headerTitle="限额流水号列表"
              actionRef={actionRef}
              formRef={formTableRef}
              rowKey={(record) => `${record.globalBusiTrackNo}_${record.sceneNo}`}
              columns={mainColumns}
              expandable={{
                expandedRowRender,
                expandRowByClick: true,
                rowExpandable: (record) => record.limitTxSumInfoOutputList && record.limitTxSumInfoOutputList.length > 0,
                defaultExpandAllRows: true,
              }}
              search={{
                labelWidth: 120,
                defaultCollapsed: false,
                span: 8,
                // 添加以下配置以确保必填项显示星号
                optionRender: ({ searchText, resetText }, { form }) => [
                  <Button
                    key="search"
                    type="primary"
                    onClick={() => handleSearch()}
                    icon={<SearchOutlined />}
                    className={styles.roundButton}
                  >
                    查询
                  </Button>,
                  <Button
                    key="reset"
                    onClick={() => handleReset(form)}
                    icon={<ReloadOutlined />}
                    className={styles.roundButton}
                  >
                    重置
                  </Button>,
                ],
              }}
              manualRequest={false}
              form={{
                validateMessages: {
                  required: '${label}为必填项',
                },  
                initialValues: formValues,
                // 添加以下配置以确保表单验证生效
                requiredMark: true,
              }}
              toolBarRender={() => []}
              request={handleRequest}
              rowSelection={false}
              params={searchForm}
              pagination={{ 
                pageSize: 10,
                showQuickJumper: true,
                showSizeChanger: true,
              }}
              // 修改滚动配置，增加水平滚动宽度以适应所有列的总宽度
              scroll={{ x: 1800 }}
              // 添加表格样式，优化滚动体验
              tableClassName={styles.scrollableTable}
              options={{
                density: true,
                fullScreen: true,
                setting: true,
              }}
              dateFormatter="string"
              className={styles.table}
            />
          </Card>
        </div>
      </WrapContent>
    </KeepAlive>
  );
};

export default LimitGloTrackBussNoQuery;