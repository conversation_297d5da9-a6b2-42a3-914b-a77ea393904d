.container {
  padding: 16px;
  background-color: #f0f2f5;
  min-height: calc(100vh - 120px);
  animation: fadeInUp 0.5s ease-out; // 添加容器动画
  
  .card {
    box-shadow:
      0 1px 2px -2px rgba(0, 0, 0, 0.16),
 
      0 3px 6px 0 rgba(0, 0, 0, 0.12),
 
      0 5px 12px 4px rgba(0, 0, 0, 0.09);
    border-radius: 8px;
    transition: all 0.3s; // 添加过渡效果
    
    &:hover {
      box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12); // 添加悬停效果
    }
    
    .titleContainer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding-bottom: 16px;
      border-bottom: 1px solid #f0f0f0;
      animation: slideInLeft 0.5s ease-out; // 添加标题动画
    }
    
    .table {
      margin-top: 16px;
      animation: fadeIn 0.5s ease-out; // 添加表格动画
      
      :global {
        .ant-pro-table-search {
          margin-bottom: 16px;
          padding: 24px 24px 0;
          background-color: #fff;
          border-radius: 8px;
          animation: fadeIn 0.5s ease-out; // 添加搜索表单动画
        }
        
        .ant-table-thead > tr > th {
          background-color: #fafafa;
          font-weight: 500;
        }
        
        .ant-btn-link {
          padding: 0 8px;
        }
        
        .ant-form-item-label > label {
          font-weight: 500;
        }
        
        .ant-select-selector,
        .ant-input,
        .ant-picker {
          border-radius: 8px;
        }
        
        .ant-table-container {
          border-radius: 8px;
          overflow: hidden;
        }
        
        .ant-pagination {
          margin-top: 16px;
        }
      }
    }
  }
}

// 圆角按钮样式
.roundButton {
  border-radius: 12px;
  margin-right: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

// 响应式调整
@media screen and (max-width: 768px) {
  .container {
    padding: 8px;
    
    .card {
      .table {
        :global {
          .ant-pro-table-search {
            padding: 16px 16px 0;
          }
        }
      }
    }
  }
}

.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  
  :global {
    .ant-spin-text {
      margin-top: 8px;
      color: #1890ff;
      font-size: 14px;
    }
  }
}

.noDataContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  color: #999;
  font-size: 14px;
}

// 添加动画关键帧
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}
// 在文件末尾添加以下样式

// 优化表格滚动
.scrollableTable {
  :global {
    .ant-table-body {
      overflow-x: auto !important;
      -webkit-overflow-scrolling: touch; // 增强移动端滑动体验
      scrollbar-width: thin; // 细滚动条
      
      &::-webkit-scrollbar {
        height: 6px; // 水平滚动条高度
      }
      
      &::-webkit-scrollbar-thumb {
        background-color: rgba(0, 0, 0, 0.2);
        border-radius: 3px;
      }
      
      &::-webkit-scrollbar-track {
        background-color: rgba(0, 0, 0, 0.05);
      }
    }
    
    // 固定首列时的样式优化
    .ant-table-cell-fix-left,
    .ant-table-cell-fix-right {
      z-index: 2;
    }
  }
}

// 子表格样式
.expandedTable {
  margin: 0 16px 16px 16px;
  background-color: #fafafa;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
  animation: fadeIn 0.3s ease-out;
  
  :global {
    .ant-table {
      margin: 0;
      background-color: transparent;
    }
    
    .ant-table-thead > tr > th {
      background-color: #f0f0f0;
      font-size: 13px;
    }
    
    .ant-table-tbody > tr > td {
      font-size: 13px;
      background-color: #fafafa;
    }
    
    .ant-table-tbody > tr:hover > td {
      background-color: #f0f0f0;
    }
  }
}

// 可展开行样式
:global {
  .ant-table-row-expand-icon {
    background-color: #f0f0f0;
    border-radius: 4px;
    transition: all 0.3s;
    
    &:hover {
      background-color: #e0e0e0;
    }
  }
  
  .ant-table-row-expanded {
    background-color: #f5f5f5;
  }
}