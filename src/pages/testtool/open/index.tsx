// 从antd库中导入需要的组件和类型
import type { FormInstance } from 'antd';
import { Button, message } from 'antd';
// 导入React核心库和Hook
import React, { useRef, useState } from 'react';
// 导入国际化相关组件
import { useIntl, FormattedMessage } from 'umi';
// 导入ProTable相关类型
import type { ProColumns, ActionType } from '@ant-design/pro-table';
// 导入ProTable组件
import ProTable from '@ant-design/pro-table';
// 导入布局组件
import WrapContent from '@/components/WrapContent';
// 导入API服务
import { openAccount } from './service';
import type { OpenAccountParams } from './data.d'; // 导入开户参数的接口类型

// 定义表单验证错误的接口类型
interface ValidationError {
  errorFields?: {
    errors: string[];
  }[];
}

// 定义开户列表组件
const OpenAccountList: React.FC = () => {
  // 创建表单引用，用于操作表单实例
  const formTableRef = useRef<FormInstance>();
  // 创建表格动作引用，用于操作表格
  const actionRef = useRef<ActionType>();
  // 定义提交状态，控制按钮loading状态
  const [submitting, setSubmitting] = useState<boolean>(false);
  // 定义表格数据状态
  const [tableData, setTableData] = useState<any[]>([]);
  // 获取国际化实例
  const intl = useIntl();

  // 处理表单提交的函数
  const handleSubmit = async (fields: OpenAccountParams) => {
    // 如果正在提交中，直接返回
    if (submitting) {
      return;
    }

    // 设置提交状态为true
    setSubmitting(true);
    // 显示加载提示
    message.loading('正在提交...', 0);

    try {
      // 处理证件类型和卡品种，只发送数字部分
      const params = {
        ...fields,
        idType: fields.idType.split('-')[0], // 分割字符串，获取证件类型编号
        cardType: fields.cardType.split('-')[0], // 分割字符串，获取卡品种编号
      };
      // 调用开户API
      const resp = await openAccount(params);
      // 清除loading消息
      message.destroy();

      if (resp.code === 200) {
        // 开户成功，显示成功提示
        message.success('开户成功');
        // 构造表格数据
        const resultData = {
          ...resp.data,
          id: Date.now(), // 添加唯一的key
          // 恢复显示格式
          cardType: fields.cardType,
          idType: fields.idType,
        };
        // 更新表格数据
        setTableData([resultData]);
      } else {
        // 显示错误信息
        message.error(resp.msg);
      }
    } catch (error) {
      // 清除loading消息
      message.destroy();
      // 显示错误提示
      message.error('开户失败请重试！');
    }

    // 3秒后才能再次提交
    setTimeout(() => {
      setSubmitting(false);
    }, 3000);
  };

  // 定义表格列配置
  const columns: ProColumns<OpenAccountParams>[] = [
    {
      title: '卡品种',
      dataIndex: 'cardType',
      valueType: 'select', // 使用下拉选择框
      valueEnum: {
        // 下拉选项
        '01-测试1': { text: '01-测试1' },
        '02-测试2': { text: '02-测试2' },
        '03-测试3': { text: '03-测试3' },
        '04-测试4': { text: '04-测试4' },
      },
      formItemProps: {
        rules: [{ required: true, message: '请选择卡品种' }], // 设置必填规则
      },
    },
    {
      title: '卡类别',
      dataIndex: 'cardCategory',
      valueType: 'text', // 文本输入框
      formItemProps: {
        rules: [{ required: true, message: '请输入卡类别' }],
      },
    },
    {
      title: '可售产品编码',
      dataIndex: 'productCode',
      valueType: 'text',
      formItemProps: {
        rules: [{ required: true, message: '请输入可售产品编码' }],
      },
    },
    {
      title: '用户名称',
      dataIndex: 'userName',
      valueType: 'text',
      formItemProps: {
        rules: [{ required: true, message: '请输入用户名称' }],
      },
    },
    {
      title: '证件类型',
      dataIndex: 'idType',
      valueType: 'select',
      valueEnum: {
        '1010-身份证': { text: '1010-身份证' },
        '1011-临时身份证': { text: '1011-临时身份证' },
      },
      formItemProps: {
        rules: [{ required: true, message: '请选择证件类型' }],
      },
    },
    {
      title: '证件号码',
      dataIndex: 'idNumber',
      valueType: 'text',
      formItemProps: {
        rules: [{ required: true, message: '请输入证件号码' }],
      },
    },
    {
      title: '账户类别',
      dataIndex: 'accountType',
      valueType: 'text',
      formItemProps: {
        rules: [{ required: true, message: '请输入账户类别' }],
      },
    },
  ];

  // 渲染组件
  return (
    <WrapContent>
      {/* 使用ProTable组件创建表单和表格 */}
      <ProTable<OpenAccountParams>
        headerTitle="开户结果"
        actionRef={actionRef}
        formRef={formTableRef} // 绑定表单引用
        form={{
          ignoreRules: false, // 不忽略表单验证规则
        }}
        rowKey="id"
        search={{
          labelWidth: 120,
          defaultCollapsed: false,
          // 自定义操作按钮
          optionRender: () => [
            // 提交按钮
            <Button
              key="submit"
              type="primary"
              loading={submitting}
              onClick={async () => {
                try {
                  // 验证表单字段
                  const values = await formTableRef.current?.validateFields();
                  if (values) {
                    // 提交表单数据
                    await handleSubmit(values);
                  }
                } catch (error: any) {
                  // 处理验证错误
                  const validationError = error as ValidationError;
                  if (validationError.errorFields) {
                    message.error('必输项未输入');
                  }
                }
              }}
            >
              <FormattedMessage id="pages.submit" defaultMessage="开户" />
            </Button>,
            // 重置按钮
            <Button
              key="reset"
              onClick={() => {
                // 重置表单字段
                formTableRef.current?.resetFields();
                // 清空表格数据
                setTableData([]);
              }}
            >
              重置
            </Button>,
          ],
        }}
        options={false} // 禁用表格工具栏
        dataSource={tableData} // 设置表格数据源
        columns={columns} // 设置表格列
        pagination={false} // 禁用分页
        toolBarRender={false} // 禁用工具栏
      />
    </WrapContent>
  );
};

// 导出组件
export default OpenAccountList;
