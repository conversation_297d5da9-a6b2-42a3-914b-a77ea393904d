import { downLoadXlsx } from '@/utils/downloadfile';
import request from '@/utils/request';
import type { LimitSumInfoListParams, LimitSumInfoExportParams } from './data.d';

/* *
 *
 * <AUTHOR> zhu
 * @datetime  2025/02/9
 * 
 * */


// 查询限额累计信息
export async function LimitSumInfoQueryDataList(params: Partial<LimitSumInfoListParams>) {
    // 设置默认值
    const defaultParams: LimitSumInfoListParams = {
        lmtAmtCheckObjAttrbVal: params.lmtAmtCheckObjAttrbVal || '',
        custNo: params.custNo || '',
        isCustomFlag: params.isCustomFlag?.charAt(0) || '',
        current: 1,
        pageSize: 10,
        ...params,  // 使用传入的参数覆盖默认值
    };
  
    // 将所有参数转换为字符串类型
    const convertedParams: Record<string, string> = {};
    Object.keys(defaultParams).forEach(key => {
      const value = defaultParams[key as keyof LimitSumInfoListParams];
      convertedParams[key] = value?.toString() || '';
    });
    
    const queryString = new URLSearchParams(convertedParams).toString();
    return request(`/testtool/limitSumInfoQuery/list?${queryString}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json;charset=UTF-8',
      },
    });
  }

// 导出限额累计信息
export function exportLimitSumInfo (params: LimitSumInfoExportParams) {
    return downLoadXlsx(
      `/testtool/limitSumInfoQuery/export`,
      {
        method: 'POST',
        data: params.list, // 直接传递数据数组
      },
      `account_${new Date().getTime()}.xlsx`,
    );
  }
