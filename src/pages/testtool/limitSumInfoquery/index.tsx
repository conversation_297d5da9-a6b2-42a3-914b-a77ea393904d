import { PlusOutlined } from "@ant-design/icons";
import WrapContent from "@/components/WrapContent";
import { KeepAlive } from "react-activation";
import type { FormInstance } from "antd";
import { Button, message, Row, Col } from "antd";
import { useIntl, FormattedMessage, useAccess } from "umi";
import React, { useState, useRef, useEffect } from "react";
import type { ProColumns, ActionType } from "@ant-design/pro-table";
import ProTable from "@ant-design/pro-table";
import type { LimitSumInfoQueryData } from "./data.d";
import { LimitSumInfoQueryDataList, exportLimitSumInfo } from "./service";
import { storage } from "@/utils/storageUtils";

// 添加自定义样式
import "./style.less";

interface FormValueType {
  lmtAmtCheckObjAttrbVal: string; // 介质号
  custNo?: string; // 客户编号
  isCustomFlag: string; // 是否自定义限额
  sceneNo?: string; // 限额场景编码
  sceneName?: string; // 限额场景名称
  lmtAmtKindName?: string; // 限额场景大类名称
  lmtAmtCheckObjSubtypeCd?: string; // 累计账户类型：22-主账户，21-非主账户
  currCode?: string; // 币种代码
  lmtAmtCtrlItemVal1?: string; // 限额控制项值1
  lmtAmtCtrlItemVal2?: string; // 限额控制项值2
  lmtAmtCtrlItemVal3?: string; // 限额控制项值3
  lmtAmtCtrlItemVal4?: string; // 限额控制项值4
  lmtAmtCtrlItemVal5?: string; // 限额控制项值5
  staticLmtAmtCtrlItemVal1?: string; // 限额控制项值1-标数
  staticLmtAmtCtrlItemVal2?: string; // 限额控制项值2-标数
  staticLmtAmtCtrlItemVal3?: string; // 限额控制项值3-标数
  staticLmtAmtCtrlItemVal4?: string; // 限额控制项值4-标数
  staticLmtAmtCtrlItemVal5?: string; // 限额控制项值5-标数
  lmtAmtCtrlItemName1?: string; // 限额控制项名称1
  lmtAmtCtrlItemName2?: string; // 限额控制项名称2
  lmtAmtCtrlItemName3?: string; // 限额控制项名称3
  lmtAmtCtrlItemName4?: string; // 限额控制项名称4
  lmtAmtCtrlItemName5?: string; // 限额控制项名称5
}

const LimitSumInfoQueryList: React.FC = () => {
  const formTableRef = useRef<FormInstance>();
  const actionRef = useRef<ActionType>();
  const [selectedRowsState, setSelectedRows] = useState<
    LimitSumInfoQueryData[]
  >([]);
  const [formValues, setFormValues] = useState<FormValueType>({
    lmtAmtCheckObjAttrbVal: "",
    custNo: "",
    isCustomFlag: "",
  });
  const [hasCustomerNumber, setHasCustomerNumber] = useState(false);
  const [tableData, setTableData] = useState<LimitSumInfoQueryData[]>([]);
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const [isFirstRequest, setIsFirstRequest] = useState(true);

  const access = useAccess();
  const intl = useIntl();

  // 搜索表单状态初始化
  const [searchForm, setSearchForm] = useState(() => {
    const savedSearch = storage.get("limitSumInfo_search");
    return savedSearch || {};
  });

  // 加载缓存数据的函数
  const loadCacheData = () => {
    try {
      const savedData = storage.get("limitSumInfo_data");
      const savedSearch = storage.get("limitSumInfo_search");
      console.log("初始化数据:", savedData, savedSearch);

      if (savedData?.records && savedSearch) {
        const records = Array.isArray(savedData.records)
          ? savedData.records
          : [];
        // 先设置表单值
        setFormValues(savedSearch);
        // 设置客户编号状态
        setHasCustomerNumber(!!savedSearch.custNo);
        // 延迟设置表格数据
        setTimeout(() => {
          if (formTableRef.current) {
            formTableRef.current.setFieldsValue(savedSearch);
          }
          // 再设置表格数据
          setTableData(records);
        }, 100);
      }
    } catch (error) {
      console.error("加载缓存数据失败:", error);
    }
    setIsInitialLoad(false);
  };

  // 初始化数据
  useEffect(() => {
    loadCacheData();
    return () => {
      setIsFirstRequest(true);
      setIsInitialLoad(true);
    };
  }, []);

  // 处理重置
  const handleReset = (form: FormInstance | undefined) => {
    // 重置所有状态
    setFormValues({
      lmtAmtCheckObjAttrbVal: "",
      custNo: "",
      isCustomFlag: "",
    });
    setHasCustomerNumber(false);
    setSelectedRows([]);
    setTableData([]);
    setSearchForm({});

    // 重置表单 - 使用 setTimeout 确保状态更新后再重置表单
    setTimeout(() => {
      form?.resetFields();
      formTableRef.current?.resetFields();

      // 清除表格选中状态
      if (actionRef.current?.clearSelected) {
        actionRef.current.clearSelected();
      }

      // 清除本地存储
      storage.set("limitSumInfo_data", null, 0);
      storage.set("limitSumInfo_search", null, 0);
    }, 0);
  };

  // 处理导出
  const handleExport = async () => {
    if (selectedRowsState.length === 0) {
      message.warning("请选择数据进行导出");
      return;
    }
    const hide = message.loading("正在导出");
    try {
      await exportLimitSumInfo({
        list: selectedRowsState,
      });
      hide();
      message.success("导出成功");
      return true;
    } catch (error) {
      hide();
      message.error("导出失败，请重试");
      return false;
    }
  };

  // 处理请求
  const handleRequest = async (params: any) => {
    const { current = 1, pageSize = 10, ...searchParams } = params;
    const requestParams = {
      current: parseInt(current), // 确保 current 是数字类型
      pageSize: parseInt(pageSize),
      lmtAmtCheckObjAttrbVal: searchParams.lmtAmtCheckObjAttrbVal || "",
      custNo: searchParams.custNo || "",
      isCustomFlag: searchParams.isCustomFlag
        ? searchParams.isCustomFlag.charAt(0)
        : "",
    };

    try {
      // 在发起新请求前，先清空表格数据
      setTableData([]);

      // 只有在非首次请求时才更新缓存
      if (!isFirstRequest) {
        setSearchForm({
          ...requestParams,
          current: parseInt(current), // 确保缓存中的 current 也是正确的
          pageSize: parseInt(pageSize),
        });
        storage.set("limitSumInfo_search", requestParams, 30 * 60);
      }
      setIsFirstRequest(false);

      const res = await LimitSumInfoQueryDataList(requestParams);
      if (res.code === 200) {
        // 解析返回的数据
        let records = [];
        try {
          // 如果 res.data 是字符串，则解析它
          records =
            typeof res.data === "string" ? JSON.parse(res.data) : res.data;
          // 确保 records 是数组
          records = Array.isArray(records) ? records : [];
        } catch (error) {
          console.error("数据解析错误:", error);
          records = [];
        }
        // 构造分页数据结构
        const responseData = {
          records: records,
          total: records.length,
          current: parseInt(current),
          pageSize: parseInt(pageSize),
        };

        // 只有在非首次请求时才更新缓存
        if (!isFirstRequest) {
          storage.set("limitSumInfo_data", responseData, 30 * 60);
        }
        setTableData(records);
        return {
          data: records,
          total: records.length,
          success: true,
          current: parseInt(current),
          pageSize: parseInt(pageSize),
        };
      }
      if (res.code === 500) {
        message.error(res.msg);
      }
      return {
        data: [],
        total: 0,
        success: false,
      };
    } catch (error) {
      console.error("请求失败:", error);
      return {
        data: [],
        total: 0,
        success: false,
      };
    }
  };

  const columns: ProColumns<LimitSumInfoQueryData>[] = [
    {
      title: (
        <FormattedMessage
          id="autoTest.accquery.lmtAmtCheckObjAttrbVal"
          defaultMessage="介质编号"
        />
      ),
      dataIndex: "lmtAmtCheckObjAttrbVal",
      valueType: "textarea",
      hideInSearch: false,
      width: 200, // 添加这一行，限制列宽
      fixed: "left", // 添加这一行，固定在左侧
      align: "center", // 添加这一行，实现居中对齐
      fieldProps: (form) => {
        return {
          style: {
            width: "220px",
            borderRadius: "8px", // 添加圆角样式
            margin: "0", // 移除所有边距
          }, // 设置搜索框宽度
          placeholder: "请输入介质编号",
          maxLength: 50,
          disabled: hasCustomerNumber, // 使用状态变量控制禁用
          onKeyPress: (e: React.KeyboardEvent<HTMLInputElement>) => {
            const pattern = /^[A-Za-z0-9]$/;
            if (!pattern.test(e.key)) {
              e.preventDefault();
            }
          },
          onChange: (e: React.ChangeEvent<HTMLInputElement>) => {
            const value = e.target.value;
            e.target.value = value.replace(/[^A-Za-z0-9]/g, "");
            // 如果输入了介质编号，则清空客户编号
            if (value && form?.getFieldValue?.("custNo")) {
              form?.setFieldsValue?.({ custNo: "" });
              setHasCustomerNumber(false);
            }
          },
        };
      },
      // formItemProps: {
      //   rules: [
      //     {
      //       required: true,
      //       message: "请输入介质编号",
      //     },
      //     {
      //       pattern: /^[A-Za-z0-9]*$/,
      //       message: "只能输入字母和数字",
      //     },
      //   ],
      // },
    },
    {
      title: (
        <FormattedMessage
          id="autoTest.accquery.custNo"
          defaultMessage="客户编号"
        />
      ),
      dataIndex: "custNo",
      valueType: "textarea",
      hideInSearch: false,
      hideInTable: true,
      width: 200,
      align: "center",
      fieldProps: (form) => {
        return {
          style: {
            width: "200px",
            borderRadius: "8px",
            margin: "0", // 移除所有边距
          },
          placeholder: "请输入客户编号",
          maxLength: 50,
          disabled: !!formValues.lmtAmtCheckObjAttrbVal,
          onKeyPress: (e: React.KeyboardEvent<HTMLInputElement>) => {
            const pattern = /^[A-Za-z0-9]$/;
            if (!pattern.test(e.key)) {
              e.preventDefault();
            }
          },
          onChange: (e: React.ChangeEvent<HTMLInputElement>) => {
            const value = e.target.value;
            e.target.value = value.replace(/[^A-Za-z0-9]/g, "");

            // 如果输入了客户编号，则清空介质编号，并强制设置isCustomFlag为"2"
            if (value) {
              form?.setFieldsValue?.({
                lmtAmtCheckObjAttrbVal: "",
                isCustomFlag: "2",
              });
              setHasCustomerNumber(true);
            } else {
              setHasCustomerNumber(false);
            }
          },
        };
      },
      // formItemProps: {
      //   rules: [
      //     {
      //       required: false,
      //       message: "请输入客户编号",
      //     },
      //     {
      //       pattern: /^[A-Za-z0-9]*$/,
      //       message: "只能输入字母和数字",
      //     },
      //   ],
      // },
    },
    {
      title: (
        <FormattedMessage
          id="autoTest.accquery.isCustomFlag"
          defaultMessage="是否查询客户自定义限额"
        />
      ),
      dataIndex: "isCustomFlag",
      valueType: "select",
      valueEnum: {
        "0": "0-否",
        "1": "1-是",
        "2": "2-所有",
      },
      width: 180, // 添加这一行，限制列宽
      hideInSearch: false,
      align: "center", // 添加这一行，实现居中对齐
      fieldProps: (form) => {
        return {
          style: {
            width: "180px", // 修正宽度值，添加单位
            borderRadius: "8px", // 添加圆角样式
            overflow: "hidden", // 确保内容不会超出圆角
            margin: "0", // 移除所有边距
          },
          dropdownStyle: {
            borderRadius: "8px",
            overflow: "hidden",
          },
          placeholder: "请选择是否查询客户自定义限额",
          disabled: hasCustomerNumber, // 使用状态变量控制禁用
          onChange: (value: string) => {
            // 如果用户先选择了"isCustomFlag"，然后再输入客户编号，确保isCustomFlag变为"2"
            if (hasCustomerNumber && value !== "2") {
              setTimeout(() => {
                // 安全地访问form
                form?.setFieldsValue?.({ isCustomFlag: "2" });
              }, 0);
            }
          },
        };
      },
      formItemProps: {
        rules: [
          {
            required: true,
            message: "请输入是否查询客户自定义限额",
          },
        ],
      },
    },
    {
      title: (
        <FormattedMessage
          id="autoTest.accquery.sceneNo"
          defaultMessage="限额场景编码"
        />
      ),
      dataIndex: "sceneNo",
      valueType: "text",
      hideInSearch: true,
      ellipsis: true, // 添加这一行，使用省略号
      width: 180, // 添加这一行，限制列宽
      align: "center", // 添加这一行，实现居中对齐
    },
    {
      title: (
        <FormattedMessage
          id="autoTest.accquery.sceneName"
          defaultMessage="限额场景名称"
        />
      ),
      dataIndex: "sceneName",
      valueType: "textarea",
      hideInSearch: true,
      width: 200, // 添加这一行，限制列宽
      ellipsis: true, // 添加这一行，使用省略号
      align: "center", // 添加这一行，实现居中对齐
    },
    {
      title: (
        <FormattedMessage
          id="autoTest.accquery.lmtAmtKindName"
          defaultMessage="限额场景大类名称"
        />
      ),
      dataIndex: "lmtAmtKindName",
      valueType: "textarea",
      hideInSearch: true,
      ellipsis: true, // 添加这一行，使用省略号
      width: 150, // 添加这一行，限制列宽
      align: "center", // 添加这一行，实现居中对齐
    },
    {
      title: (
        <FormattedMessage
          id="autoTest.accquery.lmtAmtCheckObjSubtypeCd"
          defaultMessage="累计账户类型"
        />
      ),
      dataIndex: "lmtAmtCheckObjSubtypeCd",
      valueType: "select",
      valueEnum: {
        "31": "31-客户",
        "22": "22-主账户",
        "21": "21-非主账户",
      },
      width: 160, // 添加这一行，限制列宽
      hideInSearch: true,
      align: "center", // 添加这一行，实现居中对齐
    },
    {
      title: (
        <FormattedMessage
          id="autoTest.accquery.currCode"
          defaultMessage="币种代码"
        />
      ),
      dataIndex: "currCode",
      valueType: "select",
      hideInSearch: true,
      width: 180, // 添加这一行，限制列宽
      align: "center", // 添加这一行，实现居中对齐
      valueEnum: {
        "036": "036-澳大利亚元",
        "124": "124-加元",
        "344": "344-香港元",
        "392": "392-日元",
        "826": "826-英镑",
        "840": "840-美元",
        "978": "978-欧元（EUR）",
        "156": "156-人民币元",
      },
    },
    {
      title: (
        <FormattedMessage
          id="autoTest.accquery.lmtAmtCtrlItemName1"
          defaultMessage="限额控制项名称1"
        />
      ),
      dataIndex: "lmtAmtCtrlItemName1",
      valueType: "textarea",
      hideInSearch: true, // 在搜索表单中隐藏该列
      width: 170, // 添加这一行，限制列宽
      align: "center", // 添加这一行，实现居中对齐
    },
    {
      title: (
        <FormattedMessage
          id="autoTest.accquery.staticLmtAmtCtrlItemVal1"
          defaultMessage="限额控制项值1-标数"
        />
      ),
      dataIndex: "staticLmtAmtCtrlItemVal1",
      valueType: "textarea",
      hideInSearch: true, // 在搜索表单中隐藏该列
      width: 220, // 添加这一行，限制列宽
      align: "center", // 添加这一行，实现居中对齐
    },
    {
      title: (
        <FormattedMessage
          id="autoTest.accquery.lmtAmtCtrlItemVal1"
          defaultMessage="限额控制项值1"
        />
      ),
      dataIndex: "lmtAmtCtrlItemVal1",
      valueType: "textarea",
      hideInSearch: true, // 在搜索表单中隐藏该列
      width: 220, // 添加这一行，限制列宽
      align: "center", // 添加这一行，实现居中对齐
    },
    {
      title: (
        <FormattedMessage
          id="autoTest.accquery.lmtAmtCtrlItemName2"
          defaultMessage="限额控制项名称2"
        />
      ),
      dataIndex: "lmtAmtCtrlItemName2",
      valueType: "textarea",
      hideInSearch: true, // 在搜索表单中隐藏该列
      width: 170, // 添加这一行，限制列宽
      align: "center", // 添加这一行，实现居中对齐
    },
    {
      title: (
        <FormattedMessage
          id="autoTest.accquery.staticLmtAmtCtrlItemVal2"
          defaultMessage="限额控制项值2-标数"
        />
      ),
      dataIndex: "staticLmtAmtCtrlItemVal2",
      valueType: "textarea",
      hideInSearch: true, // 在搜索表单中隐藏该列
      width: 220, // 添加这一行，限制列宽
      align: "center", // 添加这一行，实现居中对齐
    },
    {
      title: (
        <FormattedMessage
          id="autoTest.accquery.lmtAmtCtrlItemVal2"
          defaultMessage="限额控制项值2"
        />
      ),
      dataIndex: "lmtAmtCtrlItemVal2",
      valueType: "textarea",
      hideInSearch: true, // 在搜索表单中隐藏该列
      width: 220, // 添加这一行，限制列宽
      align: "center", // 添加这一行，实现居中对齐
    },
    {
      title: (
        <FormattedMessage
          id="autoTest.accquery.lmtAmtCtrlItemName3"
          defaultMessage="限额控制项名称3"
        />
      ),
      dataIndex: "lmtAmtCtrlItemName3",
      valueType: "textarea",
      hideInSearch: true, // 在搜索表单中隐藏该列
      width: 170, // 添加这一行，限制列宽
      align: "center", // 添加这一行，实现居中对齐
    },
    {
      title: (
        <FormattedMessage
          id="autoTest.accquery.staticLmtAmtCtrlItemVal3"
          defaultMessage="限额控制项值3-标数"
        />
      ),
      dataIndex: "staticLmtAmtCtrlItemVal3",
      valueType: "textarea",
      hideInSearch: true, // 在搜索表单中隐藏该列
      width: 220, // 添加这一行，限制列宽
      align: "center", // 添加这一行，实现居中对齐
    },
    {
      title: (
        <FormattedMessage
          id="autoTest.accquery.lmtAmtCtrlItemVal3"
          defaultMessage="限额控制项值3"
        />
      ),
      dataIndex: "lmtAmtCtrlItemVal3",
      valueType: "textarea",
      hideInSearch: true, // 在搜索表单中隐藏该列
      width: 220, // 添加这一行，限制列宽
      align: "center", // 添加这一行，实现居中对齐
    },
    {
      title: (
        <FormattedMessage
          id="autoTest.accquery.lmtAmtCtrlItemName4"
          defaultMessage="限额控制项名称4"
        />
      ),
      dataIndex: "lmtAmtCtrlItemName4",
      valueType: "textarea",
      hideInSearch: true, // 在搜索表单中隐藏该列
      width: 170, // 添加这一行，限制列宽
      align: "center", // 添加这一行，实现居中对齐
    },
    {
      title: (
        <FormattedMessage
          id="autoTest.accquery.staticLmtAmtCtrlItemVal4"
          defaultMessage="限额控制项值4-标数"
        />
      ),
      dataIndex: "staticLmtAmtCtrlItemVal4",
      valueType: "textarea",
      hideInSearch: true, // 在搜索表单中隐藏该列
      width: 220, // 添加这一行，限制列宽
      align: "center", // 添加这一行，实现居中对齐
    },
    {
      title: (
        <FormattedMessage
          id="autoTest.accquery.lmtAmtCtrlItemVal4"
          defaultMessage="限额控制项值4"
        />
      ),
      dataIndex: "lmtAmtCtrlItemVal4",
      valueType: "textarea",
      hideInSearch: true, // 在搜索表单中隐藏该列
      width: 220, // 添加这一行，限制列宽
      align: "center", // 添加这一行，实现居中对齐
    },
    {
      title: (
        <FormattedMessage
          id="autoTest.accquery.lmtAmtCtrlItemName5"
          defaultMessage="限额控制项名称5"
        />
      ),
      dataIndex: "lmtAmtCtrlItemName5",
      valueType: "textarea",
      hideInSearch: true, // 在搜索表单中隐藏该列
      width: 170, // 添加这一行，限制列宽
      align: "center", // 添加这一行，实现居中对齐
    },
    {
      title: (
        <FormattedMessage
          id="autoTest.accquery.staticLmtAmtCtrlItemVal5"
          defaultMessage="限额控制项值5-标数"
        />
      ),
      dataIndex: "staticLmtAmtCtrlItemVal5",
      valueType: "textarea",
      hideInSearch: true, // 在搜索表单中隐藏该列
      width: 220, // 添加这一行，限制列宽
      align: "center", // 添加这一行，实现居中对齐
    },
    {
      title: (
        <FormattedMessage
          id="autoTest.accquery.lmtAmtCtrlItemVal5"
          defaultMessage="限额控制项值5"
        />
      ),
      dataIndex: "lmtAmtCtrlItemVal5",
      valueType: "textarea",
      hideInSearch: true, // 在搜索表单中隐藏该列
      width: 220, // 添加这一行，限制列宽
      align: "center", // 添加这一行，实现居中对齐
    },
    {
      title: (
        <FormattedMessage
          id="autoTest.accquery.operation"
          defaultMessage="操作"
        />
      ),
      dataIndex: "option",
      width: "120px",
      valueType: "option",
      fixed: "right", // 添加这一行，固定在右侧
      align: "center", // 添加这一行，实现居中对齐
      render: (_, record) => [
        <Button type="link" size="small" key="resetpwd" hidden={true}>
          <FormattedMessage
            id="autoTest.accquery.operation"
            defaultMessage="操作"
          />
        </Button>,
        <Button
          type="link"
          size="small"
          key="edit"
          disabled={
            !access.hasEnvPerms(localStorage.getItem("currentEnv") as string)
          }
          // hidden={true}
          onClick={() => {
            // 编辑操作
          }}
        >
          <FormattedMessage id="pages.searchTable.edit" defaultMessage="编辑" />
        </Button>,
        <Button type="link" size="small" danger key="batchRemove" hidden={true}>
          <FormattedMessage
            id="pages.searchTable.delete"
            defaultMessage="删除"
          />
        </Button>,
      ],
    },
  ];

  return (
    <KeepAlive
      saveScrollPosition={false}
      when={() => true}
      cacheKey="limitSumInfo"
      onActivate={() => {
        setIsFirstRequest(true);
        // 使用 requestAnimationFrame 确保在下一帧渲染
        requestAnimationFrame(() => {
          loadCacheData();
        });
      }}
    >
      <WrapContent>
        <Row gutter={[16, 24]}>
          <Col lg={24} md={24}>
            <ProTable<LimitSumInfoQueryData>
              headerTitle={intl.formatMessage({
                id: "pages.searchTable.title",
                defaultMessage: "信息",
              })}
              actionRef={actionRef}
              formRef={formTableRef}
              className="compact-search-form" // 添加自定义类名
              form={{
                ignoreRules: false,
                onReset: () => {
                  handleReset(formTableRef.current);
                },
                onValuesChange: (_, values: Record<string, any>) => {
                  // 检查是否输入了客户编号
                  const hasCustNo = !!values.custNo;
                  setHasCustomerNumber(hasCustNo);

                  // 如果输入了客户编号，确保isCustomFlag设置为"2"
                  if (hasCustNo && values.isCustomFlag !== "2") {
                    formTableRef.current?.setFieldsValue({ isCustomFlag: "2" });
                    values.isCustomFlag = "2";
                  }

                  // 更新表单值
                  setFormValues(values as FormValueType);
                },
                // 添加初始值
                initialValues: searchForm,
              }}
              rowKey={(record) => record.lmtAmtCheckObjAttrbVal} // 修改为使用 mediumNo 作为唯一标识
              key="accList"
              search={{
                labelWidth: 150,
                span: {
                  xs: 8,
                  sm: 8,
                  md: 8, // 修改为8，使搜索项在一行显示
                  lg: 8, // 修改为8，使搜索项在一行显示
                  xl: 8, // 修改为8，使搜索项在一行显示
                  xxl: 8, // 在超大屏幕上可以三列显示
                },
                layout: "horizontal", // 改为水平布局
                defaultCollapsed: false,
                // 添加搜索表单的样式
                searchGutter: [8, 0], // 减小表单项之间的间距
                optionRender: ({ searchText, form }) => [
                  <Button
                    type="primary"
                    key="search"
                    size="middle"
                    style={{ borderRadius: "8px", marginRight: "4px" }}
                    onClick={() => {
                      setIsFirstRequest(false);
                      form?.submit();
                    }}
                  >
                    {searchText}
                  </Button>,
                  <Button
                    key="reset"
                    size="middle"
                    style={{ marginLeft: 4, borderRadius: "8px" }}
                    onClick={() => {
                      handleReset(form);
                      // 强制更新表单状态
                      form?.setFieldsValue({});
                    }}
                  >
                    重置
                  </Button>,
                ],
              }}
              toolBarRender={() => [
                <Button
                  type="primary"
                  key="export"
                  style={{ borderRadius: "8px" }}
                  onClick={handleExport}
                  disabled={true}
                >
                  <PlusOutlined />
                  <FormattedMessage
                    id="pages.searchTable.export"
                    defaultMessage="导出"
                  />
                </Button>,
              ]}
              pagination={{
                defaultPageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                current:
                  parseInt(storage.get("limitSumInfo_search")?.current) || 1,
                pageSize:
                  parseInt(storage.get("limitSumInfo_search")?.pageSize) || 10,
                onChange: (page, pageSize) => {
                  const currentSearchParams =
                    formTableRef.current?.getFieldsValue();
                  const params = {
                    ...currentSearchParams,
                    current: page,
                    pageSize: pageSize,
                  };
                  // 直接调用 handleRequest 而不是使用 reload
                  handleRequest(params).then((result) => {
                    if (result.success) {
                      // 更新分页信息到缓存
                      storage.set(
                        "limitSumInfo_search",
                        {
                          ...storage.get("limitSumInfo_search"),
                          current: page,
                          pageSize: pageSize,
                        },
                        30 * 60
                      );
                    }
                  });
                },
              }}
              request={async (params) => {
                // 如果有缓存数据且是首次加载，直接返回缓存数据
                if (isFirstRequest && tableData.length > 0) {
                  const savedData = storage.get("limitSumInfo_data");
                  setIsFirstRequest(false);
                  return {
                    data: tableData,
                    success: true,
                    total: savedData?.total || tableData.length,
                    current:
                      parseInt(storage.get("limitSumInfo_search")?.current) ||
                      1,
                  };
                }
                // 否则执行正常的请求逻辑
                return handleRequest(params);
              }}
              dataSource={tableData}
              rowSelection={{
                type: "checkbox",
                preserveSelectedRowKeys: true,
                getCheckboxProps: (record) => ({
                  name: record.lmtAmtCheckObjAttrbVal, // 使用 lmtAmtCheckObjAttrbVal 作为 checkbox 的 name
                }),
                selectedRowKeys: selectedRowsState.map(
                  (row) => row.lmtAmtCheckObjAttrbVal
                ), // 使用 lmtAmtCheckObjAttrbVal 作为选中的 key
                onChange: (selectedRowKeys, selectedRows) => {
                  setSelectedRows(selectedRows);
                },
                selections: [
                  {
                    key: "clear",
                    text: "清空选择",
                    onSelect: () => {
                      setSelectedRows([]);
                    },
                  },
                ],
              }}
              scroll={{ x: 3500 }}
              columnEmptyText="-"
              columns={columns}
            />
          </Col>
        </Row>
      </WrapContent>
    </KeepAlive>
  );
};
export default LimitSumInfoQueryList;
