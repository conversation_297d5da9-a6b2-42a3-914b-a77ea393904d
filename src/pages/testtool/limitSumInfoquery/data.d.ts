/* *
 *
 * <AUTHOR> zhu
 * @datetime  2025/02/9
 * 
 * */
export type LimitSumInfoQueryData = {
    isCustomFlag: string; // 是否自定义限额
    lmtAmtCheckObjAttrbVal: string;     // 介质号
    sceneNo: string;    // 限额场景编码
    sceneName: string;  // 限额场景名称
    lmtAmtKindName: string; // 限额场景大类名称
    lmtAmtCheckObjSubtypeCd: string;    // 累计账户类型：22-主账户，21-非主账户
    currCode: string;   // 币种代码
    lmtAmtCtrlItemVal1: string; // 限额控制项值1
    lmtAmtCtrlItemVal2: string; // 限额控制项值2
    lmtAmtCtrlItemVal3: string; // 限额控制项值3
    lmtAmtCtrlItemVal4: string; // 限额控制项值4
    lmtAmtCtrlItemVal5: string;   // 限额控制项值5
    staticLmtAmtCtrlItemVal1: string;   // 限额控制项值1-标数
    staticLmtAmtCtrlItemVal2: string;   // 限额控制项值2-标数
    staticLmtAmtCtrlItemVal3: string;   // 限额控制项值3-标数
    staticLmtAmtCtrlItemVal4: string;   // 限额控制项值4-标数
    staticLmtAmtCtrlItemVal5: string;   // 限额控制项值5-标数
    lmtAmtCtrlItemName1: string;    // 限额控制项名称1
    lmtAmtCtrlItemName2: string;    // 限额控制项名称2
    lmtAmtCtrlItemName3: string;    // 限额控制项名称3
    lmtAmtCtrlItemName4: string;    // 限额控制项名称4
    lmtAmtCtrlItemName5: string;    // 限额控制项名称5
};

// LimitSumInfoListPagination 定义了账户列表的分页信息
export type LimitSumInfoListPagination = {
    total: number; // 总数
    pageSize: number; // 每页大小
    current: number; // 当前页码
  };
  
// LimitSumInfoListData 定义了账户列表的数据结构
export type LimitSumInfoListData = {
    list: LimitSumInfoQueryData[]; // 用户列表
    pagination: Partial<LimitSumInfoListPagination>; // 分页信息
};
  
  // LimitSumInfoListParams 定义了账户列表的请求参数
  export type LimitSumInfoListParams = {
    lmtAmtCheckObjAttrbVal: string;     // 介质号
    custNo?: string;                     // 客户编号
    isCustomFlag?: string;  // 是否自定义限额
    sceneNo?: string;    // 限额场景编码
    sceneName?: string;  // 限额场景名称
    lmtAmtKindName?: string; // 限额场景大类名称
    lmtAmtCheckObjSubtypeCd?: string;    // 累计账户类型：22-主账户，21-非主账户
    currCode?: string;   // 币种代码
    lmtAmtCtrlItemVal1?: string; // 限额控制项值1
    lmtAmtCtrlItemVal2?: string; // 限额控制项值2
    lmtAmtCtrlItemVal3?: string; // 限额控制项值3
    lmtAmtCtrlItemVal4?: string; // 限额控制项值4
    lmtAmtCtrlItemVal5?: string;   // 限额控制项值5
    staticLmtAmtCtrlItemVal1?: string;   // 限额控制项值1-标数
    staticLmtAmtCtrlItemVal2?: string;   // 限额控制项值2-标数
    staticLmtAmtCtrlItemVal3?: string;   // 限额控制项值3-标数
    staticLmtAmtCtrlItemVal4?: string;   // 限额控制项值4-标数
    staticLmtAmtCtrlItemVal5?: string;   // 限额控制项值5-标数
    lmtAmtCtrlItemName1?: string;    // 限额控制项名称1
    lmtAmtCtrlItemName2?: string;    // 限额控制项名称2
    lmtAmtCtrlItemName3?: string;    // 限额控制项名称3
    lmtAmtCtrlItemName4?: string;    // 限额控制项名称4
    lmtAmtCtrlItemName5?: string;    // 限额控制项名称5
    current: number;       // 改为必填
    pageSize: number;      // 改为必填
};
  
  // LimitSumInfoExportParams 定义了导出功能的请求参数
export type LimitSumInfoExportParams = {
    list: LimitSumInfoQueryData[]; // 直接使用选中的数据对象数组
};