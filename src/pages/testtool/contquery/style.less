.container {
  padding: 16px;
  background-color: #f0f2f5;
  min-height: calc(100vh - 120px);
  animation: fadeInUp 0.5s ease-out; // 添加容器动画
  
  .card {
    box-shadow:
      0 1px 2px -2px rgba(0, 0, 0, 0.16),
 
      0 3px 6px 0 rgba(0, 0, 0, 0.12),
 
      0 5px 12px 4px rgba(0, 0, 0, 0.09);
    border-radius: 8px;
    transition: all 0.3s; // 添加过渡效果
    margin-bottom: 16px;
    
    &:hover {
      box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12); // 添加悬停效果
    }
    
    .titleContainer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding-bottom: 16px;
      border-bottom: 1px solid #f0f0f0;
      animation: slideInLeft 0.5s ease-out; // 添加标题动画
    }
  
    .formContainer {
      animation: fadeIn 0.5s ease-out; // 添加表单动画
      
      :global {
        .ant-form-item-label > label {
          font-weight: 500;
          text-align: center; // 表单标签居中
        }
        
        .ant-select-selector,
        .ant-input,
        .ant-picker {
          border-radius: 6px; // 统一圆角边框
          text-align: left; // 输入框内容靠左对齐
          padding-left: 12px; // 添加左侧内边距
        }
        
        .ant-btn {
          border-radius: 8px;
          margin-right: 8px;
        }
        
        // 修改客户编号输入框样式，移除特殊样式
        .ant-form-item-control-input-content .ant-input {
          border-radius: 6px; // 使用统一的圆角
          border: 1px solid #d9d9d9; // 使用默认边框颜色
          box-shadow: none; // 移除阴影效果
          width: 100%; // 确保宽度为100%
          height: 32px; // 增加高度
          text-align: left; // 输入框内容靠左对齐
          padding-left: 12px; // 添加左侧内边距
          
          &:focus, &:hover {
            border-color: #40a9ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
          }
        }
        
        // 移除外层容器的边框和背景
        .ant-form-item-control-input {
          background: transparent;
          border: none;
          box-shadow: none;
        }
        
        // 修复双层输入框问题
        .ant-input-affix-wrapper {
          border: none !important; // 移除外层边框
          box-shadow: none !important; // 移除外层阴影
          padding: 0 !important; // 移除内边距
          background: transparent !important; // 背景透明
          width: 100%; // 确保宽度为100%
          height: 32px; // 增加高度
          
          .ant-input {
            border: 1px solid #d9d9d9 !important; // 只保留内层输入框的边框
            width: 100%; // 确保宽度为100%
            height: 32px; // 增加高度
            border-radius: 6px !important; // 减小圆角
            text-align: left !important; // 输入框内容靠左对齐
            padding-left: 12px !important; // 添加左侧内边距
          }
          
          &:hover, &:focus, &-focused {
            .ant-input {
              border-color: #40a9ff !important;
            }

            border: none !important; // 确保悬停时外层无边框
            box-shadow: none !important; // 确保悬停时外层无阴影
          }

          // 调整清除按钮位置
          .ant-input-suffix {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
          }
        }
      }
    }
    
    .descriptionsContainer {
      animation: fadeIn 0.6s ease-out; // 添加描述列表动画
      
      :global {
        .ant-descriptions-item-label {
          font-weight: 500;
          color: rgba(0, 0, 0, 0.85);
          text-align: center; // 标签居中
          background-color: #f5f7fa; // 轻微背景色区分
        }
        
        .ant-descriptions-item-content {
          color: rgba(0, 0, 0, 0.65);
          text-align: center; // 内容居中
        }
      }
    }
    
    .table {
      margin-top: 16px;
      animation: fadeIn 0.7s ease-out; // 添加表格动画
      
      :global {
        .ant-pro-table-search {
          margin-bottom: 16px;
          padding: 24px 24px 0;
          background-color: #fff;
          border-radius: 8px;
        }
        
        .ant-table-thead > tr > th {
          background-color: #fafafa;
          font-weight: 500;
          text-align: center; // 表头居中
        }
        
        .ant-table-tbody > tr > td {
          text-align: center; // 表格内容居中
        }
        
        .ant-btn-link {
          padding: 0 8px;
        }
        
        .ant-table-container {
          border-radius: 8px;
          overflow: hidden;
        }
        
        .ant-pagination {
          margin-top: 16px;
        }
      }
    }
  }
}

// 圆角按钮样式
.roundButton {
  border-radius: 12px;
  margin-right: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

// 响应式调整
@media screen and (max-width: 768px) {
  .container {
    padding: 8px;
    
    .card {
      .formContainer,
      .descriptionsContainer,
      .table {
        :global {
          .ant-pro-table-search {
            padding: 16px 16px 0;
          }
        }
      }
    }
  }
}

.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  
  :global {
    .ant-spin-text {
      margin-top: 8px;
      color: #1890ff;
    }
  }
}

// 动画关键帧定义
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// 表单项动画
.formItem {
  animation: fadeIn 0.5s ease-out;
  animation-fill-mode: both;
}

// 为表单项添加延迟动画
.formItem:nth-child(1) { animation-delay: 0.1s; }
.formItem:nth-child(2) { animation-delay: 0.2s; }
.formItem:nth-child(3) { animation-delay: 0.3s; }
.formItem:nth-child(4) { animation-delay: 0.4s; }
.formItem:nth-child(5) { animation-delay: 0.5s; }

// 空状态样式
.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 0;
  color: rgba(0, 0, 0, 0.45);
  
  .emptyIcon {
    font-size: 48px;
    margin-bottom: 16px;
  }
  
  .emptyText {
    font-size: 16px;
  }
}

// 覆盖自定义类名的样式
.custom-tooltip {
  .ant-tooltip-inner {
    border-radius: 8px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15);
    width: auto;
    min-width: 300px;  // 确保最小宽度
  }
}

// 自定义 Tooltip 样式
.customTooltip {
  :global {
    .ant-tooltip-inner {
      background-color: #fff;
      color: rgba(0, 0, 0, 0.85);
      border-radius: 8px;
      box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15);
      padding: 12px 16px;
      
      p {
        font-weight: bold;
        margin-bottom: 8px;
      }
      
      ul {
        padding-left: 16px;
        margin-bottom: 0;
      }
      
      li {
        margin-bottom: 4px;
        line-height: 1.5;
      }
    }
    
    .ant-tooltip-arrow-content {
      background-color: #fff;
    }
  }
}