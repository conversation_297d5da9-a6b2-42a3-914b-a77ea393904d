// 子合约查询相关数据类型定义

// 分页响应类型
export interface PageResponse<T> {
  records: T[];              // 记录列表
  total: number;             // 总记录数
  size: number;              // 每页记录数
  current: number;           // 当前页码
  pages: number;             // 总页数
}

// 查询响应类型
export interface ContQueryResponse {
  code: number;              // 响应代码
  msg: string;               // 响应消息
  data: string;              // 响应数据，JSON字符串
}
//合约信息类型
export interface ContInfo {
  accAvalBal?: string;
  accBal?: string;
  currCode?: string;
  custNo?: string;
  custName?: string;
  mediumNo?: string;         // 介质号
  mediumTpCd?: string;       // 介质类型代码
  mainContrNo?: string;      // 主合约编号
  mainPersInnerAccno?: string;
  perCertTpCd?: string;
  persDepAccKindCd?: string;
  persDepAccTpCd?: string;
  personalCertNo?: string;   // 证件号码
  prodtContractName?: string;
  saccnoSeqNo?: string;
  shardingId?: string;
  subInstNo?: string;
  subPersInnerAccno?: string;
  subProdtContractNo?: string;
}
// 查询参数类型
export interface ContQueryParams {
  tableIndex?: string;            
  mediumTpCd?: string;
  dpContrTpCd?: string;
  pageSize?: number;         // 每页记录数
  current?: number;          // 当前页码
}