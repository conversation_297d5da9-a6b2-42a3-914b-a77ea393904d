import request from '@/utils/request';
import type { ContQueryParams, ContQueryResponse } from './data.d';

// 查询客户信息
export async function queryContInfo(params: ContQueryParams): Promise<ContQueryResponse> {
  console.log('service接收到的参数:', params);
  
  // 设置默认值
  const defaultParams: ContQueryParams = {
    pageSize: 10,
    current: 1,
  };
  
  // 合并参数，确保传入的参数优先级更高
  const queryParams: ContQueryParams = {
    ...params,
    ...defaultParams
  };
  
  console.log('最终请求参数:', queryParams);
  
  try {
    // 使用GET方法请求后台接口
    const response = await request('/testtool/subContQuery', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json;charset=UTF-8',
      },
      data: queryParams,
    });
    
    return response;
  } catch (error) {
    console.error('请求错误:', error);
    throw error;
  }
}
