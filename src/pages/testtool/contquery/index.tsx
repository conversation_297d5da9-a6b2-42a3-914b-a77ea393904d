import React, { useState, useEffect, useRef } from "react";
import {
  Card,
  Typography,
  message,
  Row,
  Col,
  Button
} from "antd";
import {
  SearchOutlined,
  ReloadOutlined,
} from "@ant-design/icons";
import { KeepAlive, useActivate, useUnactivate } from "react-activation";
import { storage } from "@/utils/storageUtils";
import WrapContent from "@/components/WrapContent";
import ProForm, { ProFormSelect } from "@ant-design/pro-form";
import ProTable from "@ant-design/pro-table";
import type { ProColumns, ActionType } from "@ant-design/pro-table";
import type { FormInstance } from "antd";
import type {
  ContInfo,
  ContQueryParams,
} from "./data.d";
import { queryContInfo } from "./service";
import styles from "./style.less";
import { getCurrCodeText, getPerCertTpCdText, getPersDepAccKindCdText } from "@/utils/constants"
const { Title } = Typography;

const ContQuery: React.FC = () => {
  // 表单和表格引用
  const formRef = useRef<FormInstance>();
  const actionRef = useRef<ActionType>();


  // 状态管理
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [isVisible, setIsVisible] = useState<boolean>(false);

  // 数据状态
  const [contList, setContList] = useState<ContInfo[]>([]);
  const [searchParams, setSearchParams] = useState<ContQueryParams>({
    // const savedSearch = storage.get("contQuery_search");
    // return savedSearch;
  });

  // 初始化加载
  useEffect(() => {
    // 从缓存加载数据
    const savedResult = storage.get("contQuery_result");
    if (savedResult) {
      setContList(savedResult || []);
    }
    // 从缓存加载搜索参数
    const savedSearch = storage.get("contQuery_search");
    if (savedSearch && formRef.current) {
      formRef.current.setFieldsValue(savedSearch);
      setSearchParams(savedSearch);
    }
    // 添加动画触发
    setTimeout(() => {
      setIsVisible(true);
    }, 100);
  }, []);
    // 当查询结果更新时保存到localStorage
    useEffect(() => {
      if (contList) {
        storage.set("contQuery_result", contList, 30 * 60); // 保存30分钟
      }
    }, [contList]);

  // 当搜索参数更新时保存到localStorage
  useEffect(() => {
    if (searchParams) {
      storage.set("contQuery_search", searchParams, 30 * 60); // 保存30分钟
    }
  }, [searchParams]);

  // 组件激活时处理
  useActivate(() => {
    // setTimeout(() => {
    //   const savedResult = storage.get("custQuery_result");
    //   if (savedResult) {
    //     setCustomerInfo(savedResult.customerInfo);
    //     setMediumList(savedResult.mediumList || []);
    //   }

    //   const savedSearch = storage.get("custQuery_search");
    //   if (savedSearch && formRef.current) {
    //     formRef.current.setFieldsValue(savedSearch);
    //     setSearchParams(savedSearch);
    //   }

    //   setIsVisible(true);
    //   setFormKey(Date.now());
    // }, 0);
  });

  // 组件失活时处理
  useUnactivate(() => {
    // setLoading(false);
    // setError(null);
    // setIsVisible(false);
  });

  // 查询合约信息
  const handleSearch = async (values: ContQueryParams) => {
    setLoading(true);
    setError(null);
    setSearchParams(values);

    try {
      const response = await queryContInfo(values);

      if (response.code === 200 && response.data) {
        // 解析后端返回的JSON字符串
        const rawData = JSON.parse(response.data);

        setContList(rawData);
        message.success("查询成功");

        // 刷新表格
        if (actionRef.current) {
          actionRef.current.reload();
        }
      } else {
        setError(response.msg || "查询失败");
        message.error(response.msg || "查询失败");
      }
    } catch (error) {
      console.error("查询出错:", error);
      setError("查询出错，请稍后重试");
      message.error("查询出错，请稍后重试");
    } finally {
      setLoading(false);
    }
  };

  // 重置表单
  const handleReset = () => {
    if (formRef.current) {
      formRef.current.resetFields();
    }
    setSearchParams({})
    setContList([]);
    storage.remove("custQuery_search");
    storage.remove("custQuery_result");
    // setError(null);
  };

  // 介质表格列定义
  const columns: ProColumns<ContInfo>[] = [
    {
      title: "介质编号",
      dataIndex: "mediumNo",
      ellipsis: true,
      width: 200,
      align: "center"
    },
    {
      title: "子账号序号",
      dataIndex: "saccnoSeqNo",
      ellipsis: true,
      width: 100,
      align: "center"
    },
    {
      title: "主合约编号",
      dataIndex: "mainContrNo",
      ellipsis: true,
      width: 220,
      align: "center"
    },
    {
      title: "主账号",
      dataIndex: "mainPersInnerAccno",
      ellipsis: true,
      width: 200,
      align: "center"
    },
    {
      title: "子合约编号",
      dataIndex: "subProdtContractNo",
      ellipsis: true,
      width: 220,
      align: "center"
    },
    {
      title: "子账号",
      dataIndex: "subPersInnerAccno",
      ellipsis: true,
      width: 200,
      align: "center"
    },
    {
      title: "客户号",
      dataIndex: "custNo",
      ellipsis: true,
      width: 150,
      align: "center"
    },
    {
      title: "客户名称",
      dataIndex: "custName",
      ellipsis: true,
      width: 120,
      align: "center"
    },
    {
      title: "证件类型",
      dataIndex: "perCertTpCd",
      ellipsis: true,
      width: 140,
      align: "center",
     render: (_, record) => getPerCertTpCdText(record.perCertTpCd || ''),
    },
    {
      title: "证件号码",
      dataIndex: "personalCertNo",
      ellipsis: true,
      width: 180,
      align: "center"
    },
    {
      title: "合约名称",
      dataIndex: "prodtContractName",
      ellipsis: true,
      width: 120,
      align: "center"
    },
    {
      title: "账户余额",
      dataIndex: "accBal",
      ellipsis: true,
      width: 120,
      align: "center"
    },
    {
      title: "可用余额",
      dataIndex: "accAvalBal",
      ellipsis: true,
      width: 120,
      align: "center"
    },
    {
      title: "开立机构号",
      dataIndex: "subInstNo",
      ellipsis: true,
      width: 120,
      align: "center"
    },
    {
      title: "币种",
      dataIndex: "currCode",
      ellipsis: true,
      width: 120,
      align: "center",
      render: (_, record) => getCurrCodeText(record.currCode || ''),
    },
    {
      title: "账户种类",
      dataIndex: "persDepAccKindCd",
      ellipsis: true,
      width: 80,
      align: "center",
      render: (_, record) => getPersDepAccKindCdText(record.persDepAccKindCd || ''),
    }
  ];

  return (
    <WrapContent>
      <div className={styles.container} style={{ opacity: isVisible ? 1 : 0 }}>
        <Card className={styles.card}>
          <div className={styles.titleContainer}>
            <Title level={5}>子合约查询</Title>
          </div>

          <div className={styles.formContainer}>
            <ProForm
              formRef={formRef}
              layout="horizontal"
              onFinish={handleSearch}
              submitter={{
                render: (props) => {
                  return (
                    <Row justify="end" gutter={16}>
                      <Col>
                        <Button
                          type="primary"
                          icon={<SearchOutlined />}
                          onClick={() => props.form?.submit()}
                          loading={loading}
                          className={styles.roundButton}
                        >
                          查询
                        </Button>
                      </Col>
                      <Col>
                        <Button
                          icon={<ReloadOutlined />}
                          onClick={handleReset}
                          className={styles.roundButton}
                        >
                          重置
                        </Button>
                      </Col>
                    </Row>
                  );
                },
              }}
            >
               <ProForm.Group style={{ display: 'flex', flexDirection: 'row', gap: '24px' }}>
                  <ProFormSelect
                    name="mediumTpCd"
                    label="介质类型代码"
                    placeholder="请选择"
                    rules={[
                      { required: true, message: "请选择介质类型" }
                    ]}
                    options={[
                      { value: '0202', label: '0202-绿卡通' },
                      { value: '0305', label: '0305-本外币活期一本通' },
                      { value: '0306', label: '0306-本外币定期一本通' },
                      { value: '0309', label: '0309-本币定期一本通' }
                    ]}
                    fieldProps={{
                      showSearch: true,
                      bordered: true,
                      style: {
                        borderRadius: "6px",
                        textAlign: "left",
                        width: 200  // 保持与原来输入框相同的宽度
                      }
                    }}
                    className={styles.formItem}
                  />
                  <ProFormSelect
                    name="dpContrTpCd"
                    label="合约类型代码"
                    placeholder="请选择"
                    rules={[
                      { required: true, message: "请选择合约类型" }
                    ]}
                    options={[
                      { value: "1001", label: "1001-人民币活期储蓄合约" },
                      { value: "1002", label: "1002-人民币活期结算合约" },
                      { value: "1003", label: "1003-外币活期储蓄合约" },
                      { value: "1004", label: "1004-外币活期结算合约" },
                      { value: "1005", label: "1005-本外币合一结算合约" },
                      { value: "2001", label: "2001-整存整取储蓄存款合约" },
                      { value: "2002", label: "2002-整存整取协议存款合约" },
                      { value: "2003", label: "2003-提前付息定期存款合约" },
                      { value: "2004", label: "2004-定活两便储蓄存款合约" },
                      { value: "2005", label: "2005-整存零取储蓄存款合约" },
                      { value: "2006", label: "2006-存本取息储蓄存款合约" },
                      { value: "2007", label: "2007-零存整取储蓄存款合约" },
                      { value: "2008", label: "2008-通知存款合约" },
                      { value: "2009", label: "2009-结构性存款合约" },
                      { value: "2010", label: "2010-递增计息合约" },
                      { value: "2011", label: "2011-梦想加邮站合约" },
                      { value: "2012", label: "2012-大额存单合约" },
                      { value: "2013", label: "2013-礼仪存单合约" },
                      { value: "2014", label: "2014-邮智存合约" },
                      { value: "3001", label: "3001-行业应用子账户合约" },
                      { value: "3002", label: "3002-电子现金账户合约" },
                      { value: "4001", label: "4001-副卡合约" },
                      { value: "4002", label: "4002-映射卡合约" },
                      { value: "4003", label: "4003-本外币定期一本通合约" },
                    ]}
                    fieldProps={{
                      showSearch: true,
                      bordered: true,
                      style: {
                        borderRadius: "6px",
                        textAlign: "left",
                        width: 250  // 保持与原来输入框相同的宽度
                      }
                    }}
                    className={styles.formItem}
                  />
                  <ProFormSelect
                    name="tableIndex"
                    label="分片值"
                    placeholder="请选择"
                    rules={[
                      { required: true, message: "请选择分片值" }
                    ]}
                    options={[
                      { value: '1', label: '0001' },
                      { value: '2', label: '0002' },
                      { value: '3', label: '0003' },
                      { value: '4', label: '0004' },
                      { value: '5', label: '0005' },
                      { value: '6', label: '0006' },
                      { value: '7', label: '0007' },
                      { value: '8', label: '0008' },
                      { value: '9', label: '0009' },
                      { value: '10', label: '0010' },
                      { value: '11', label: '0011' },
                      { value: '12', label: '0012' },
                      { value: '13', label: '0013' },
                      { value: '14', label: '0014' },
                      { value: '15', label: '0015' },
                      { value: '16', label: '0016' }
                    ]}
                    fieldProps={{
                      showSearch: true,
                      bordered: true,
                      style: {
                        borderRadius: "6px",
                        textAlign: "left",
                        width: 150  // 保持与原来输入框相同的宽度
                      }
                    }}
                    className={styles.formItem}
                  />
                </ProForm.Group>
            </ProForm>
          </div>
        </Card>
        <Card
            className={styles.card}>
            <div className={styles.formTable}>
              <ProTable<ContInfo>
                actionRef={actionRef}
                search={false}
                options={{
                  density: false,
                  fullScreen: false,
                  setting: false,
                  reload: false,
                }}
                pagination={{
                  showSizeChanger: true,
                  showQuickJumper: true,
                  defaultPageSize: 10,
                }}
                dataSource={contList}
                columns={columns}
                loading={loading}
                scroll={{ x: "max-content" }}
              />
            </div>
        </Card>
      </div>
    </WrapContent>
  );
};

export default () => (
  <KeepAlive>
    <ContQuery />
  </KeepAlive>
);
