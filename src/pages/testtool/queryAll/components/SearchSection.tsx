import React from "react";
import { Input, <PERSON><PERSON>, Card, Row, Col, message, Tooltip } from "antd";
import { SearchOutlined, ReloadOutlined, RedoOutlined, ExclamationCircleOutlined, CloseSquareOutlined } from "@ant-design/icons";
import "../style.less";
import {dacCac} from "../../../tinytools/dac/service";
import{ oneKeyClose, cacelOneKeyClose } from "../service";
import { storage } from "@/utils/storageUtils";
import { useAccess } from "umi";

/*
 * 介质信息查询搜索框区域
 */
interface SearchSectionProps {
  onSearch: (mediumNo: string) => void;
}


export const SearchSection: React.FC<SearchSectionProps> = ({ onSearch }) => {

  const access = useAccess();

  const [searchValue, setSearchValue] = React.useState(() => {
    const cachedSearch = storage.get("queryAll_search");
    return cachedSearch?.mediumNo || "";
  });

  const handleSearch = () => {
    if (searchValue.trim()) {
      onSearch(searchValue.trim());
    } else {
      message.warning('请输入介质号');
    }
  };

  const handleReset = () => {
    setSearchValue("");
  };

  // 添加 loading 状态
  const [dacLoading, setDacLoading] = React.useState(false);
  const [closeLoading, setCloseLoading] = React.useState(false);
  const [cancelCloseLoading, setCancelCloseLoading] = React.useState(false);
  // 重置DAC
  const handleResetDac = async () => {
    
    if (searchValue.trim()) {
      setDacLoading(true);
      try {
        const requestData = {
          mediumNo: searchValue.trim(), // 添加 trim 处理
          flag: "0",
        };
        const res = await dacCac(requestData);
        if(res.code !== 200) {
          message.error('DAC重置失败');
        } else {
          message.success('DAC重置成功');
        }
      } catch (error) {
        message.error('DAC重置失败');
      } finally {
        setDacLoading(false);
      }
    } else {
      message.warning('请输入介质号');
    }
  };
  // 一键销户
  const handleOneKeyClose = async () => {
    if (searchValue.trim()) {
      setCloseLoading(true);
      try {
        const res = await oneKeyClose({ mediumNo: searchValue.trim() });
        if(res.code !== 200) {
          message.error('一键销户失败');
        } else {
          message.success('一键销户成功');
          onSearch(searchValue.trim());
        }
      } catch (error) {
        message.error('一键销户失败');
      } finally {
        setCloseLoading(false);
      }
    } else {
      message.warning('请输入介质号');
    }
  }
  // 取消一键销户
  const handleCancelOneKeyClose = async () => {
    if (searchValue.trim()) {
      setCancelCloseLoading(true);
      try {
        const res = await cacelOneKeyClose({ mediumNo: searchValue.trim() });
        if(res.code !== 200) {
          message.error('取消一键销户失败');
        } else {
          message.success('取消一键销户成功');
          onSearch(searchValue.trim());
        }
      } catch (error) {
        message.error('取消一键销户失败');
      } finally {
        setCancelCloseLoading(false);
      }
    } else {
      message.warning('请输入介质号');
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      handleSearch();
    }
  };

  // 添加错误状态
  const [error, setError] = React.useState<string>("");

  return (
    <Card
      className="search-section"
      bordered={false}
      style={{
        margin: "0 auto",
        width: "100%",
        maxWidth: "1200px",  // 从 1400px 改为 1200px
        padding: "20px",     // 从 24px 改为 20px
        overflow: "hidden"
      }}
    >
      <Row gutter={[-12, 16]} align="middle">
        <Col xs={24} sm={4} style={{ paddingRight: 0 }}>
          <span className="search-label" style={{ marginRight: '-8px' }}>介质号</span>
        </Col>
        <Col xs={24} sm={6} style={{ paddingLeft: 0 }}>
          <Input
            placeholder="请输入介质号"
            value={searchValue}
            style={{ borderRadius: '12px' }} 
            onChange={(e) => {
              const value = e.target.value.replace(/[^a-zA-Z0-9]/g, '');
              setSearchValue(value.slice(0, 30));
              setError("");
            }}
            onKeyPress={handleKeyPress}
            allowClear
            required
            status={error ? "error" : ""}
          />
        </Col>
        {/* 添加一个空的 Col 作为间距 */}
        <Col xs={0} sm={2} />
        <Col xs={24} sm={12}>
          <div 
            className="search-buttons"
            style={{ 
              display: 'flex', 
              justifyContent: 'flex-start'
            }}
          >
            <Button
              type="primary"
              icon={<SearchOutlined />}
              onClick={handleSearch}
            >
              查询
            </Button>
            <Button
              icon={<ReloadOutlined />}
              onClick={handleReset}
            >
              重置输入
            </Button>
            <Tooltip 
              title="仅重置当前介质号对应的介质、主合约、主账户DAC" 
              placement="top"
              overlayInnerStyle={{
                backgroundColor: '#fff',
                color: '#333',
                border: '1px solid #ffcdd2',
                boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
                padding: '8px 12px',
                borderRadius: '6px'
              }}
            >
              <Button
                icon={<RedoOutlined />}
                onClick={handleResetDac}
                loading={dacLoading}
                style={
                  {
                    backgroundColor: '#ffebee',  // 浅红色背景
                    borderColor: '#ffcdd2',      // 浅红色边框
                    color: '#d32f2f',           // 红色文字
                  }
                }
                disabled= {
                  !access.hasEnvPerms(
                    localStorage.getItem("currentEnv") as string
                  )
                }
              >
                重置DAC
              </Button>
            </Tooltip>
            <Tooltip 
              title="仅对当前介质号对应的介质、主合约、主账户进行销户" 
              placement="top"
              overlayInnerStyle={{
                backgroundColor: '#fff',
                color: '#333',
                border: '1px solid #c8e6c9',
                boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
                padding: '8px 12px',
                borderRadius: '6px'
              }}
            >
              <Button
                icon={<ExclamationCircleOutlined />}
                onClick={handleOneKeyClose}
                loading={closeLoading}
                style={{
                  backgroundColor: '#e8f5e9',  // 浅绿色背景
                  borderColor: '#c8e6c9',      // 浅绿色边框
                  color: '#2e7d32',           // 绿色文字
                }}
                disabled= {
                  !access.hasEnvPerms(
                    localStorage.getItem("currentEnv") as string
                  )
                }
              >
                一键销户
              </Button>
            </Tooltip>
            <Tooltip 
              title="仅对当前介质号对应的介质、主合约、主账户进行取消销户" 
              placement="top"
              overlayInnerStyle={{
                backgroundColor: '#fff',
                color: '#333',
                border: '1px solid #ffe082',
                boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
                padding: '8px 12px',
                borderRadius: '6px'
              }}
            >
              <Button
                icon={<CloseSquareOutlined />}
                onClick={handleCancelOneKeyClose}
                loading={cancelCloseLoading}
                style={{
                  backgroundColor: '#fff8e1',  // 浅黄色背景
                  borderColor: '#ffe082',      // 浅黄色边框
                  color: '#f9a825',           // 黄色文字
                }}
                disabled= {
                  !access.hasEnvPerms(
                    localStorage.getItem("currentEnv") as string
                  )
                }
              >
                取消一键销户
              </Button>
            </Tooltip>
          </div>
        </Col>
      </Row>
    </Card>
  );
};