import React, { useState } from 'react';
import ProTable from '@ant-design/pro-table';
import { message } from 'antd';
import type { 
  DataItem, 
  TbDpmstMedium, 
  TbDpmstCurtCont,
  TbDpmstCurtAcc,
  TbDpmstFixCont,
  TbDpmstFixAcc 
} from '../data.d';
import { 
  dpmstMediumUpdate,
  dpmstCurtContUpdate,
  dpmstCurtAccUpdate,
  dpmstFixContUpdate,
  dpmstFixAccUpdate
} from '../service';
import { EditModal } from './EditModal';
import FlagModal from './FlagModal';
import '../style.less';

interface DataSectionProps {
  data: DataItem[];
  loading: boolean;
  columns: any[];
  onRefresh?: () => void;
  tabKey: string; // 添加标签页标识
  pagination?: {
    total: number;
    pageSize: number;
    current: number;
    onChange?: (page: number, pageSize: number) => void;
    showSizeChanger?: boolean;
    showQuickJumper?: boolean;
  };
}

export const DataSection: React.FC<DataSectionProps> = ({ data, loading, columns, onRefresh, tabKey, pagination }) => {
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [flagModalVisible, setFlagModalVisible] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<DataItem | null>(null);
  const [selectedFlag, setSelectedFlag] = useState<string>('');
  const [selectedDataIndex, setSelectedDataIndex] = useState<string>('');

  // const handleEdit = (record: DataItem) => {
  //   setSelectedRecord(record);
  //   setEditModalVisible(true);
  // };

  const handleFlagClick = (flag: string | undefined, record: any, dataIndex: string) => {
    if (!dataIndex) return;
    // console.log('点击标志位 - flag:', flag, 'dataIndex:', dataIndex);
    // console.log('完整记录:', record);

    // 根据不同的dataIndex获取对应的flag值
    let flagValue = '';
    if (dataIndex === 'relMediumTypeFlagCd') {    // 关联介质类型标志码
      flagValue = record.relMediumTypeFlagCd || '';
    } else if (dataIndex === 'statusFlagCd') {    // 介质状态标志码
      flagValue = record.statusFlagCd || '';
    } else if (dataIndex === 'attrbuteFlagCd') {  // 介质属性标志码
      flagValue = record.attrbuteFlagCd || '';
    } else if (dataIndex === 'crtModeFlagCd') { // 介质认证方式标志码
      flagValue = record.crtModeFlagCd || '';
    } else if (dataIndex === 'mContrFlagCd') {  // 主合约控制标志码
      flagValue = record.mContrFlagCd || '';
    } else if (dataIndex === 'contrCtrlFlagCd') { // 合约控制标志码
      flagValue = record.contrCtrlFlagCd || '';
    } else if (dataIndex === 'contrAttrFgCd') { // 合约属性标志码
      flagValue = record.contrAttrFgCd || '';
    } else if (dataIndex === 'contrStaFgCd') { // 合约状态标志码
      flagValue = record.contrStaFgCd || '';
    } else if (dataIndex === 'accStatusFlagCd') { // 账户状态标志码
      flagValue = record.accStatusFlagCd || '';
    } else if (dataIndex === 'accFlagCd') { // 账户标志码
      flagValue = record.accFlagCd || '';
    }

    // console.log('解析得到的标志值:', flagValue);
    // console.log('标志值长度:', flagValue.length);
    // console.log('标志值的每一位:', flagValue.split(''));

    setSelectedFlag(flagValue);
    setSelectedRecord(record);
    setSelectedDataIndex(dataIndex);
    setFlagModalVisible(true);
  };

  const updatedColumns = columns.map((col: any) => {
    const baseCol = {
      ...col,
      ellipsis: true,
      onHeaderCell: () => ({
        style: {
          whiteSpace: 'nowrap',
          overflow: 'hidden',
          textOverflow: 'ellipsis'
        }
      })
    };

    if (col.dataIndex === 'relMediumTypeFlagCd' 
      || col.dataIndex === 'statusFlagCd' 
      || col.dataIndex === 'attrbuteFlagCd' 
      || col.dataIndex === 'crtModeFlagCd' 
      || col.dataIndex === 'mContrFlagCd'
      || col.dataIndex === 'contrCtrlFlagCd'
      || col.dataIndex === 'contrAttrFgCd'
      || col.dataIndex === 'contrStaFgCd'
      || col.dataIndex === 'accStatusFlagCd'
      || col.dataIndex === 'accFlagCd'
    ) {
      return {
        ...baseCol,
        render: (text: string, record: any) => (
          <span
            onClick={() => handleFlagClick(text, record, col.dataIndex)}
            style={{
              whiteSpace: 'nowrap',
              width: 'auto',
              color: '#1890ff !important',  // 添加 !important
              // color: 'rgba(24, 144, 255, 1)',
              textDecoration: 'underline',
              cursor: 'pointer',
              fontWeight: 500,
              display: 'inline-block'
            }}
            className="flag-text"  // 添加自定义类名
          >
            {text}
          </span>
        ),
      };
    }
    return baseCol;
  });

  return (
    <>
      <ProTable<DataItem>
        columns={updatedColumns}
        dataSource={data}
        loading={loading}
        rowKey="id"
        search={false}
        options={{
          density: true,
          fullScreen: false,
          reload: true,
          setting: true,
        }}
        pagination={
          pagination
            ? {
                total: pagination.total,
                pageSize: pagination.pageSize,
                current: pagination.current,
                onChange: pagination.onChange,
                showSizeChanger: true,
                showQuickJumper: true,
              }
            : {
                defaultPageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
              }
        }
        scroll={{ x: 'max-content' }}
        cardBordered={false}
        cardProps={{
          bodyStyle: { padding: '0 24px 24px 24px', overflowX: 'auto' }
        }}
        tableStyle={{
          tableLayout: 'fixed',
        }}
        className="data-section-table"
      />

      {selectedRecord && (
        <EditModal
          visible={editModalVisible}
          record={selectedRecord}
          onCancel={() => setEditModalVisible(false)}
          onSubmit={(values) => {
            setEditModalVisible(false);
          }}
        />
      )}

      <FlagModal
        visible={flagModalVisible}
        flag={selectedFlag}
        dataIndex={selectedDataIndex}
        onCancel={() => setFlagModalVisible(false)}
        onSubmit={async (values) => {
          try {
            if (!selectedRecord) {
              message.error('数据异常');
              return;
            }
            let res: any = {};

            // dataIndex为介质主档表的字段
            if (selectedDataIndex === 'relMediumTypeFlagCd'
              || selectedDataIndex === 'statusFlagCd'
              || selectedDataIndex === 'attrbuteFlagCd'
              || selectedDataIndex === 'crtModeFlagCd'
            ) {
              const params: TbDpmstMedium = {
                mediumNo: selectedRecord.mediumNo,
                zoneVal: selectedRecord.zoneVal,
              };
              if (selectedDataIndex === 'relMediumTypeFlagCd') {
                params.relMediumTypeFlagCd = values.flag;
              } else if (selectedDataIndex === 'statusFlagCd') {
                params.statusFlagCd = values.flag;
              } else if (selectedDataIndex === 'attrbuteFlagCd') {
                params.attrbuteFlagCd = values.flag;
              } else if (selectedDataIndex === 'crtModeFlagCd') {
                params.crtModeFlagCd = values.flag;
              }
              res = await dpmstMediumUpdate(params);
            }

            // dataIndex为合约主档表的字段
            if (selectedDataIndex === 'mContrFlagCd'
              || selectedDataIndex === 'contrCtrlFlagCd'
              || selectedDataIndex === 'contrAttrFgCd'
              || selectedDataIndex === 'contrStaFgCd'
            ) {

              if (tabKey === '2' || tabKey === '7') {
                const params: TbDpmstCurtCont = {
                  prodtContractNo: selectedRecord.prodtContractNo,
                  zoneVal: selectedRecord.zoneVal,
                };
                if (selectedDataIndex === 'mContrFlagCd') {
                  params.mContrFlagCd = values.flag;
                } else if (selectedDataIndex === 'contrCtrlFlagCd') {
                  params.contrCtrlFlagCd = values.flag;
                } else if (selectedDataIndex === 'contrAttrFgCd') {
                  params.contrAttrFgCd = values.flag;
                } else if (selectedDataIndex === 'contrStaFgCd') {
                  params.contrStaFgCd = values.flag;
                }
                res = await dpmstCurtContUpdate(params);
              } else if (tabKey === '4' || tabKey === '9') {
                const params: TbDpmstFixCont = {
                  prodtContractNo: selectedRecord.prodtContractNo,
                  zoneVal: selectedRecord.zoneVal,
                };
                if (selectedDataIndex === 'mContrFlagCd') {
                  params.mContrFlagCd = values.flag;
                } else if (selectedDataIndex === 'contrCtrlFlagCd') {
                  params.contrCtrlFlagCd = values.flag;
                } else if (selectedDataIndex === 'contrAttrFgCd') {
                  params.contrAttrFgCd = values.flag;
                } else if (selectedDataIndex === 'contrStaFgCd') {
                  params.contrStaFgCd = values.flag;
                }
                res = await dpmstFixContUpdate(params);
              }
            }
            // dataIndex为账户主档表的字段
            if (selectedDataIndex === 'accStatusFlagCd'
              || selectedDataIndex === 'accFlagCd'
            ) {
              if (tabKey === '3' || tabKey === '8') {
                const params: TbDpmstCurtAcc = {
                  persInnerAccno: selectedRecord.persInnerAccno,
                  zoneVal: selectedRecord.zoneVal,
                };
                if (selectedDataIndex === 'accStatusFlagCd') {
                  params.accStatusFlagCd = values.flag;
                } else if (selectedDataIndex === 'accFlagCd') {
                  params.accFlagCd = values.flag;
                }
                res = await dpmstCurtAccUpdate(params);
              } else if (tabKey === '5' || tabKey === '10') {
                const params: TbDpmstFixAcc = {
                  persInnerAccno: selectedRecord.persInnerAccno,
                  zoneVal: selectedRecord.zoneVal,
                };
                if (selectedDataIndex === 'accStatusFlagCd') {
                  params.accStatusFlagCd = values.flag;
                } else if (selectedDataIndex === 'accFlagCd') {
                  params.accFlagCd = values.flag;
                }
                res = await dpmstFixAccUpdate(params);
              }
            }
            if (res.code === 200) {
              message.success('更新成功');
              // 延迟1秒后刷新数据
              setTimeout(() => {
                onRefresh?.();
              }, 1000);
            } else {
              message.error(res.msg || '更新失败');
            }
          } catch (error) {
            message.error('更新失败，请重试');
          } finally {
            setFlagModalVisible(false);
          }
        }}
      />
    </>
  );
};
