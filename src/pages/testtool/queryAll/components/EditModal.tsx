import React from 'react';
import { Modal, Form, Input, Select } from 'antd';
import type { DataItem } from '../data.d';

interface EditModalProps {
  visible: boolean;
  record: DataItem;
  onCancel: () => void;
  onSubmit: (values: DataItem) => void;
}

export const EditModal: React.FC<EditModalProps> = ({
  visible,
  record,
  onCancel,
  onSubmit,
}) => {
  const [form] = Form.useForm();

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      onSubmit({ ...record, ...values });
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  return (
    <Modal
      title="编辑数据"
      open={visible}
      onCancel={onCancel}
      onOk={handleSubmit}
      destroyOnClose
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={record}
      >
        {Object.keys(record).map(key => {
          if (key === 'name') {
            return (
              <Form.Item
                key={key}
                name={key}
                label="名称"
                rules={[{ required: true, message: '请输入名称' }]}
              >
                <Input />
              </Form.Item>
            );
          } else if (key === 'description') {
            return (
              <Form.Item
                key={key}
                name={key}
                label="描述"
                rules={[{ required: true, message: '请输入描述' }]}
              >
                <Input.TextArea rows={4} />
              </Form.Item>
            );
          } else if (key === 'status') {
            return (
              <Form.Item
                key={key}
                name={key}
                label="状态"
                rules={[{ required: true, message: '请选择状态' }]}
              >
                <Select>
                  <Select.Option value="active">活跃</Select.Option>
                  <Select.Option value="inactive">非活跃</Select.Option>
                  <Select.Option value="pending">待处理</Select.Option>
                </Select>
              </Form.Item>
            );
          } else {
            return (
              <Form.Item key={key} label={key} initialValue={(record as any)[key]}>
                <Input disabled />
              </Form.Item>
            );
          }
        })}
      </Form>
    </Modal>
  );
};
