import React, { useState, useEffect } from 'react';
import { Modal, Form, Radio, Space, message } from 'antd';
import type { TbDpmstFixAcc } from '../data';
import { dpmstFixAccUpdate, queryAll } from '../service';
import { flagMeaningsConfig } from './FlagModal';


interface AccStatusFlagCdEditProps {
  visible: boolean;
  onCancel: () => void;
  record: any;
  mediumNo: string; // 添加介质号属性
  persInnerAccno: string; // 添加个人内部账号属性
  onSuccess: () => void;
}

// 合约属性标志码
const FixAccStatusFlagCdEdit: React.FC<AccStatusFlagCdEditProps> = ({
  visible,
  onCancel,
  record,
  mediumNo, // 传入介质号
  persInnerAccno, // 接收个人内部账号
  onSuccess,
}) => {
  const [loading, setLoading] = useState(false);

  const [editedValues, setEditedValues] = useState<Record<number, string>>({});
  const accStatusFlagCdConfig = flagMeaningsConfig.accStatusFlagCd;

  useEffect(() => {
    if (visible && record) {
      const initialValues: Record<number, string> = {};

      if (record.accStatusFlagCd) {
        const bits = record.accStatusFlagCd.split('');
        accStatusFlagCdConfig.forEach(item => {
          initialValues[item.position] = bits[item.position - 1] || '0';
        });
      } else {
        accStatusFlagCdConfig.forEach(item => {
          initialValues[item.position] = '0';
        });
      }

      setEditedValues(initialValues);
    }
  }, [visible, record]);

  const handleValueChange = (position: number, value: string) => {
    setEditedValues(prev => ({
      ...prev,
      [position]: value
    }));
  };

  const handleOk = async () => {
    setLoading(true);
    try {
      // 生成32位标志码
      const flagArray = new Array(32).fill('0');
      Object.entries(editedValues).forEach(([position, value]) => {
        const index = parseInt(position) - 1;
        // 只对前2位赋值
        if (index >= 0 && index < 2) {
          flagArray[index] = value;
        }
      });

      const accStatusFlagCd = flagArray.join('');
      const params: TbDpmstFixAcc = {
        persInnerAccno: persInnerAccno, // 使用传入的个人内部账号
        zoneVal: record.zoneVal,
        accStatusFlagCd,
      };

      const res = await dpmstFixAccUpdate(params);
      if (res.code === 200) {
        message.success('更新成功');
        // 调用查询方法重新获取数据
        await queryAll(mediumNo);
        onCancel();
      } else {
        message.error(res.msg || '更新失败');
      }
    } catch (error) {
      message.error('更新失败，请重试');
    } finally {
      setLoading(false);
    }
  };
  return (
    <Modal
      title="编辑账户状态标志码"
      open={visible}
      onOk={handleOk}
      onCancel={onCancel}
      confirmLoading={loading}
      width={550}
    >
      <Space direction="vertical" style={{ width: '100%' }} size="large">
        {accStatusFlagCdConfig.map(item => (
          <Form.Item key={item.key} label={item.title}>
            <Radio.Group
              value={editedValues[item.position]}
              onChange={(e) => handleValueChange(item.position, e.target.value)}
            >
              <Space>
                {item.options.map(option => (
                  <Radio key={option.value} value={option.value}>
                    {option.label}
                  </Radio>
                ))}
              </Space>
            </Radio.Group>
          </Form.Item>
        ))}
      </Space>
    </Modal>
  );
};

export default FixAccStatusFlagCdEdit;
