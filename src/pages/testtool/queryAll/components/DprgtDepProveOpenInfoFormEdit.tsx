import React, { useEffect, useState, useCallback } from "react";
import { Mo<PERSON>, message, <PERSON><PERSON>, Spin } from "antd";
import { ProFormRadio, EditableProTable } from "@ant-design/pro-components";
import type { ProColumns } from "@ant-design/pro-components";
import {
  queryTbDprgtDepProveOpenInfo,
  tbDprgtDepProveOpenInfoAdd,
  tbDprgtDepProveOpenInfoMod,
  tbDprgtDepProveOpenInfoDel,
} from "../service";
import type { TbDprgtDepProveOpenInfo, PageResponse } from "../data";

/*
 * @Description: 存款证明开立信息登记表编辑表单
 * @Author: taylor zhu
 * @Date: 2024-08-07 10:00:00
 */

interface PaginationType {
  current: number;
  pageSize: number;
  total: number;
}

export type TbDprgtDepProveOpenInfoFormProps = {
  onCancel: () => void;
  open: boolean;
  data: PageResponse<TbDprgtDepProveOpenInfo> | null;
};

const DEFAULT_PAGE_SIZE = 10;
const TABLE_SCROLL = { x: 2000 } as const;
const MODAL_BODY_STYLE = {
  maxHeight: "70vh",
  overflow: "auto",
  padding: "24px",
  borderRadius: "10px",
} as const;

const MODAL_STYLE = {
  borderRadius: "10px",
  overflow: "hidden",
} as const;

const INITIAL_PAGINATION: PaginationType = {
  current: 1,
  pageSize: DEFAULT_PAGE_SIZE,
  total: 0,
};

const DprgtDepProveOpenInfoFormEdit: React.FC<
  TbDprgtDepProveOpenInfoFormProps
> = (props) => {
  const [dataSource, setDataSource] = useState<
    readonly TbDprgtDepProveOpenInfo[]
  >([]);
  const [editableKeys, setEditableKeys] = useState<React.Key[]>([]);
  const [isEditing, setIsEditing] = useState(false);
  const [editingRecord, setEditingRecord] =
    useState<TbDprgtDepProveOpenInfo | null>(null);
  const [pagination, setPagination] =
    useState<PaginationType>(INITIAL_PAGINATION);
  const [refresh, setRefresh] = useState(0);
  const [loading, setLoading] = useState(false);
  const [depProveOpenInfoInfo, setDepProveOpenInfoInfo] = useState<{
    deptProveNo?: string;
    zoneVal?: string;
  } | null>(null);
  const [deptProveNo, setDeptProveNo] = useState<string | undefined>(undefined);
  const [zoneVal, setZoneVal] = useState<string | undefined>(undefined);

  useEffect(() => {
    if (props.open) {
      if (props.data?.loading !== undefined) {
        setLoading(props.data.loading);
      } else if (props.data) {
        // If we have data but no loading property, ensure loading is turned off
        setLoading(false);
      }

      // 当有data数据时，清空分页并设置数据
      if (props.data?.records) {
        setPagination({
          current: 1,
          pageSize: 10,
          total: props.data.total || props.data.records.length,
        });
        setDataSource(props.data.records);

        // 重要：设置账号信息，确保分页功能正常工作
        if (props.data.records.length > 0) {
          const firstRecord = props.data.records[0];

          // 从记录中提取账号信息
          setDeptProveNo(firstRecord.deptProveNo);
          setZoneVal(firstRecord.zoneVal);

          // 同时更新accountInfo，作为备份
          setDepProveOpenInfoInfo({
            deptProveNo: firstRecord.deptProveNo,
            zoneVal: firstRecord.zoneVal,
          });
        }
      }
    } else {
      // 当弹窗关闭时，清空数据和分页
      setDataSource([]);
      setPagination({
        current: 1,
        pageSize: 10,
        total: 0,
      });
      // Also ensure loading is reset when modal closes
      setLoading(false);
    }
  }, [props.open, props.data]);

  const refreshData = useCallback(
    async (
      current: number = pagination.current,
      pageSize: number = pagination.pageSize
    ) => {
      // 使用当前状态或depProveOpenInfoInfo作为后备
      const provNo = depProveOpenInfoInfo?.deptProveNo;
      const zoneValue = depProveOpenInfoInfo?.zoneVal;

      if (!provNo || !zoneValue) {
        // 向用户显示错误消息，便于调试
        console.error("查询参数不完整:", { provNo, zoneValue });
        message.error("分页查询失败：账号信息不完整，请重新打开弹窗");
        return;
      }

      setLoading(true);
      console.log("正在查询数据:", { provNo, zoneValue, current, pageSize });

      try {
        const result = await queryTbDprgtDepProveOpenInfo(
          provNo,
          zoneValue,
          "DP_RGT_DEP_PROVE_OPEN_INFO_ALL_QUERYPAGES",
          {
            current,
            pageSize,
          }
        );

        if (result && result.data) {
          const detailData = JSON.parse(result.data);
          const newPagination = {
            current: current,
            pageSize: pageSize,
            total: detailData.total || detailData.records.length,
          };

          setPagination(newPagination);
          setDataSource(detailData.records || []);
          console.log("查询成功，数据条数:", detailData.records?.length);
        }
      } catch (error) {
        console.error("获取数据失败:", error);
        message.error("获取存款证明开立信息登记数据失败");
      } finally {
        // Ensure loading state is always cleared
        setLoading(false);
      }
    },
    [deptProveNo, zoneVal, pagination.current, pagination.pageSize]
  );

  // 定义表格列
  const [position, setPosition] = useState<"top" | "bottom" | "hidden">(
    "bottom"
  );

  const columns: ProColumns<TbDprgtDepProveOpenInfo>[] = [
    {
      title: "存款证明编号",
      dataIndex: "deptProveNo",
      width: 160,
      ellipsis: true,
      align: "center",
      fixed: "left",
      editable: () => false,
    },
    {
      title: "个人内部账号",
      dataIndex: "persInnerAccno",
      width: 220,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "介质编号",
      dataIndex: "mediumNo",
      width: 220,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "子账号序号",
      dataIndex: "saccnoSeqNo",
      width: 120,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "产品合约编号",
      dataIndex: "prodtContractNo",
      width: 260,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "开户日期",
      dataIndex: "openaccDate",
      width: 120,
      ellipsis: true,
      align: "center",
      editable: () => false,
      valueType: "date",
      render: (_, entity) => entity.openaccDate || "-",
    },
    {
      title: "类别标识代码",
      dataIndex: "categFlagCd",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => false,
      valueType: "select",
      valueEnum: {
        "01": { text: "01-活期" },
        "02": { text: "02-定期" },
      },
    },
    {
      title: "储种中类代码",
      dataIndex: "savTypeMclassCode",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => false,
      valueType: "select",
      valueEnum: {
        "010100": { text: "010100-储蓄活期" },
        "010200": { text: "010200-结算活期" },
        "020100": { text: "020100-保值储蓄" },
        "020200": { text: "020200-整存整取" },
        "020300": { text: "020300-整存零取" },
        "020400": { text: "020400-零存整取" },
        "020500": { text: "020500-存本取息" },
        "020600": { text: "020600-定额定期" },
        "020700": { text: "020700-定期协议利率存款" },
        "020800": { text: "020800-大额存单" },
        "030100": { text: "030100-不固定定活两便" },
        "030200": { text: "030200-固定定活两便" },
        "040100": { text: "040100-老通知" },
        "040200": { text: "040200-新通知" },
        "050100": { text: "050100-一本通" },
        "070100": { text: "070100-结构性存款" },
        "990100": { text: "990100-小额支付账户" },
        "990200": { text: "990200-行业应用子账户（计息）" },
        "990300": { text: "990300-行业应用子账户（不计息）" },
        "990400": { text: "990400-行业应用子账户（分段）" },
        "999900": { text: "999900-其它" },
      },
    },
    {
      title: "止付号",
      dataIndex: "stopPayNo",
      width: 180,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "存款证明金额",
      dataIndex: "depProveAmt",
      width: 160,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "币种代码",
      dataIndex: "currCode",
      width: 120,
      ellipsis: true,
      align: "center",
      editable: () => false,
      valueType: "select",
      valueEnum: {
        "156": { text: "156-人民币" },
        "840": { text: "840-美元" },
        "036": { text: "036-澳大利亚元" },
      },
    },
    {
      title: "钞汇类别代码",
      dataIndex: "cashExgVatgCd",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => false,
      valueType: "select",
      valueEnum: {
        "1": { text: "1-钞" },
        "2": { text: "2-汇" },
      },
    },
    {
      title: "基础产品编码",
      dataIndex: "baseProdtNo",
      width: 160,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "关系状态代码",
      dataIndex: "relatStaCd",
      width: 120,
      ellipsis: true,
      align: "center",
      editable: () => false,
      valueType: "select",
      valueEnum: {
        "1": { text: "1-已生效" },
        "2": { text: "2-已失效" },
      },
    },
    {
      title: "最后交易日期",
      dataIndex: "lastTxDate",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: false,
      valueType: "date",
      render: (_, entity) => entity.lastTxDate || "-",
    },
    {
      title: "记录状态代码",
      dataIndex: "recordStaCd",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => false,
      valueType: "select",
      valueEnum: {
        "0": { text: "0-无效" },
        "1": { text: "1-有效" },
      },
    },
    {
      title: "分片值",
      dataIndex: "zoneVal",
      width: 260,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "创建时间戳",
      dataIndex: "createStamp",
      width: 180,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "最后修改时间戳",
      dataIndex: "lastModStamp",
      width: 180,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "操作",
      valueType: "option",
      width: 150,
      align: "center",
      render: (text, record: TbDprgtDepProveOpenInfo, _, action) => {
        const isRowEditing = record.deptProveNo
          ? editableKeys.includes(record.deptProveNo)
          : false;
        return [
          isRowEditing ? (
            <>
              <Button
                key="confirm"
                type="link"
                onClick={async () => {
                  try {
                    if (record.deptProveNo) {
                      await action?.saveEditable?.(record.deptProveNo);
                      setIsEditing(false);
                    }
                  } catch (err) {
                    console.error("保存失败:", err);
                  }
                }}
              >
                确定
              </Button>
              <Button
                key="cancel"
                type="link"
                onClick={() => {
                  if (record.deptProveNo) {
                    action?.cancelEditable?.(record.deptProveNo);
                    setEditableKeys([]);
                    setIsEditing(false);
                  }
                }}
                style={{ marginLeft: 8 }}
              >
                取消
              </Button>
            </>
          ) : (
            <Button
              key="editable"
              type="link"
            //   disabled={isEditing}
              disabled={true}
              onClick={async () => {
                if (!record.deptProveNo) return;
                const originalRecord = { ...record };
                setEditingRecord(originalRecord);
                setIsEditing(true);
                await action?.startEditable?.(record.deptProveNo);
              }}
              style={{ padding: 0 }}
            >
              编辑
            </Button>
          ),
          <Button
            key="delete"
            type="link"
            // disabled={isEditing}
            disabled={true}
            onClick={async () => {
              if (!record.deptProveNo) return;
              Modal.confirm({
                title: "确认删除",
                content: "确定要删除这条记录吗？",
                onOk: async () => {
                  try {
                    const res = await tbDprgtDepProveOpenInfoDel(record);
                    if (res.code === 200) {
                      message.success("删除成功");
                    } else {
                      message.error(res.msg || "删除失败");
                    }
                  } catch (error) {
                    message.error("删除失败");
                  } finally {
                    await refreshData();
                  }
                },
                okText: "确认",
                cancelText: "取消",
              });
            }}
            style={{ marginLeft: 8 }}
          >
            删除
          </Button>,
        ];
      },
    },
  ];

  const handleAdd = async (record: TbDprgtDepProveOpenInfo) => {
    try {
      // 设置必要的默认字段
      const newRecord = {
        ...record,
        deptProveNo: deptProveNo || depProveOpenInfoInfo?.deptProveNo,
        zoneVal: zoneVal || depProveOpenInfoInfo?.zoneVal,
        recordStaCd: "1", // 默认有效
      };

      setLoading(true);
      const res = await tbDprgtDepProveOpenInfoAdd(newRecord);
      if (res.code === 200) {
        message.success("添加成功");
        refreshData();
      } else {
        message.error(res.msg || "添加失败");
      }
    } catch (error) {
      console.error("添加失败:", error);
      message.error("添加失败");
    } finally {
      // 添加完成后重置状态
      setLoading(false);
      setRefresh((prev) => prev + 1);
      setEditableKeys([]);
      setIsEditing(false);
    }
  };

  return (
    <Modal
      width={1200}
      title="存款证明开立信息登记"
      open={props.open}
      destroyOnClose
      onCancel={props.onCancel}
      footer={null}
      bodyStyle={MODAL_BODY_STYLE}
      style={MODAL_STYLE}
    >
      <Spin spinning={loading} tip="加载数据中..." size="large">
        <div style={{ marginBottom: 16 }}>
          <p>
            数据状态:{" "}
            {loading ? (
              <span>
                <Spin size="small" style={{ marginRight: 8 }} />
                加载中...
              </span>
            ) : (
              `已加载 ${dataSource.length} 条记录, 总共 ${pagination.total} 条`
            )}
            {depProveOpenInfoInfo && (
              <span style={{ marginLeft: 8 }}>
                | 存款证明编号: {depProveOpenInfoInfo.deptProveNo}
              </span>
            )}
          </p>
        </div>

        <EditableProTable<TbDprgtDepProveOpenInfo>
          key={refresh}
          rowKey={(record) => record.deptProveNo || ""}
          headerTitle="存款证明开立信息登记表"
          scroll={TABLE_SCROLL}
          loading={loading}
          toolBarRender={() => [
            <ProFormRadio.Group
              key="positionRender"
              fieldProps={{
                value: position,
                onChange: (e: any) => setPosition(e.target.value),
              }}
              options={[
                { label: "添加到顶部", value: "top" },
                { label: "添加到底部", value: "bottom" },
                { label: "隐藏", value: "hidden" },
              ]}
            />,
          ]}
          recordCreatorProps={{
            position: position === "hidden" ? "bottom" : position,
            record: (index, dataSource) => {
              // 直接创建新记录，不检查是否有正在编辑的行
              return {
                deptProveNo: deptProveNo || depProveOpenInfoInfo?.deptProveNo,
                zoneVal: zoneVal || depProveOpenInfoInfo?.zoneVal,
                lastTxDate: new Date().toISOString().split("T")[0], // 最后交易日期
                recordStaCd: "1", // 记录状态代码：有效
              } as TbDprgtDepProveOpenInfo;
            },
            creatorButtonText: "添加一行数据",
            newRecordType: "dataSource",
          }}
          columns={columns}
          request={async (params, sorter, filter) => {
            // 直接返回当前数据源，不进行额外的API调用
            return {
              data: dataSource as TbDprgtDepProveOpenInfo[],
              total: pagination.total,
              success: true,
            };
          }}
          value={dataSource}
          onChange={setDataSource}
          editable={{
            type: "multiple", // 支持同时编辑多行
            editableKeys,
            onSave: async (rowKey, data, row) => {
              try {
                // 如果是新添加的记录则调用新增接口
                if (String(rowKey).startsWith("NEW_")) {
                  await handleAdd(data);
                } else {
                  // 否则调用更新接口
                  setLoading(true);
                  const res = await tbDprgtDepProveOpenInfoMod(data);
                  if (res.code === 200) {
                    message.success("更新成功");
                    refreshData();
                  } else {
                    message.error(res.msg || "更新失败");
                  }
                }
              } catch (error) {
                console.error("保存失败:", error);
                message.error("保存失败");
              } finally {
                // 保存后从编辑状态中移除当前行
                setEditableKeys((keys) => keys.filter((key) => key !== rowKey));
                if (editableKeys.length <= 1) {
                  setIsEditing(false);
                }
                setLoading(false);
              }
            },
            onChange: setEditableKeys,
            onCancel: async (key) => {
              // 取消编辑时只清除当前行的编辑状态
              setEditableKeys((keys) => keys.filter((k) => k !== key));
              if (editableKeys.length <= 1) {
                setIsEditing(false);
              }
              return true;
            },
            actionRender: (row, config, dom) => [dom.save, dom.cancel],
          }}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条`,
            onChange: async (page, pageSize) => {
              console.log("分页改变:", {
                page,
                pageSize,
                currentPage: pagination.current,
                isEditing,
              });

              if (isEditing) {
                message.warn("请先完成编辑操作再切换页面");
                return;
              }

              // 仅当页码或页大小变化时才刷新数据
              if (
                page !== pagination.current ||
                pageSize !== pagination.pageSize
              ) {
                // 更新分页状态
                setPagination({
                  ...pagination,
                  current: page,
                  pageSize: pageSize,
                });

                // 直接调用刷新数据，不做额外判断
                await refreshData(page, pageSize);
              }
            },
          }}
        />
      </Spin>
    </Modal>
  );
};

export default DprgtDepProveOpenInfoFormEdit;
