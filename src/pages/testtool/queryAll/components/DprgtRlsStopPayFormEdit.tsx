import React, { useEffect, useState } from "react";
import { Modal, message } from "antd";
import {
  TbDprgtRlsStopPayUpdate,
  getTbDprgtRlsStopPay,
  insertTbDprgtRlsStopPay,
  tbDprgtRlsStopPayDelete,
} from "../service";
import type { TbDprgtRlsStopPay, PageResponse } from "../data";
import type { ProColumns } from "@ant-design/pro-components";
import { EditableProTable, ProFormRadio } from "@ant-design/pro-components";

/*
 * @Description: 解止付登记簿编辑表单
 * @Author: zhujinwei
 * @Date: 2025-01-17 10:00:00
 */

interface PaginationType {
  current: number;
  pageSize: number;
  total: number;
}

export type TbDprgtRlsStopPayFormProps = {
  onCancel: () => void;
  open: boolean;
  data: PageResponse<TbDprgtRlsStopPay> | null;
  //   refresh: number;
};

// 表格相关常量
const DEFAULT_PAGE_SIZE = 10;
const TABLE_SCROLL = { x: 1300 } as const;
const TABLE_OPTIONS = {
  density: true,
  fullScreen: true,
  setting: true,
} as const;
const TABLE_PAGINATION = {
  showSizeChanger: true,
  showQuickJumper: true,
} as const;

// Modal相关常量
const MODAL_BODY_STYLE = {
  maxHeight: "70vh",
  overflow: "auto",
  padding: "24px",
  borderRadius: "10px",
} as const;

const MODAL_STYLE = {
  borderRadius: "10px",
  overflow: "hidden",
} as const;

// 工具函数
const formatAmount = (value: string | number | null | undefined): string => {
  if (!value) return "0.00";
  const num = typeof value === "string" ? parseFloat(value) : value;
  return isNaN(num) ? "0.00" : num.toFixed(2);
};

// 分页相关常量
const INITIAL_PAGINATION: PaginationType = {
  current: 1,
  pageSize: DEFAULT_PAGE_SIZE,
  total: 0,
};

const DprgtRlsStopPayFormEdit: React.FC<TbDprgtRlsStopPayFormProps> = (props) => {
  const [dataSource, setDataSource] = useState<readonly TbDprgtRlsStopPay[]>([]);
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);
  const [isEditing, setIsEditing] = useState(false);
  const [editingRecord, setEditingRecord] = useState<TbDprgtRlsStopPay | null>(null);
  const [pagination, setPagination] =
    useState<PaginationType>(INITIAL_PAGINATION);
  const [refresh, setRefresh] = useState(0);
  const [loading, setLoading] = useState(false);

  // 刷新数据方法
  const refreshData = async () => {
    if (!props.data?.records?.[0]) return;

    const TbDprgtRlsStopPay = props.data.records[0];
    if (!TbDprgtRlsStopPay?.stopPayNo || !TbDprgtRlsStopPay?.zoneVal) return;

    setLoading(true);
    try {
      const res = await getTbDprgtRlsStopPay(
        TbDprgtRlsStopPay.stopPayNo,
        TbDprgtRlsStopPay.zoneVal,
        "tbDprgtRlsStopPayPageInfo",
        {
          current: pagination.current,
          pageSize: pagination.pageSize,
        }
      );

      if (res.code === 200 && res.data) {
        const detailData = JSON.parse(res.data);
        setDataSource(detailData.records || []);
        setPagination({
          current: detailData.current || INITIAL_PAGINATION.current,
          pageSize: detailData.size || INITIAL_PAGINATION.pageSize,
          total: detailData.total || INITIAL_PAGINATION.total,
        });
        setRefresh(refresh + 1);
      } else {
        message.error(res.msg || "获取明细失败");
      }
    } catch (error) {
      message.error("查询明细失败");
    } finally {
      setLoading(false);
    }
  };

  // 初始化数据
  useEffect(() => {
    let isMounted = true;

    const initData = async () => {
      if (props.open && props.data) {
        const { records, current, size, total } = props.data;
        if (isMounted) {
          setDataSource(records || []);
          setPagination({
            current: current || INITIAL_PAGINATION.current,
            pageSize: size || INITIAL_PAGINATION.pageSize,
            total: total || INITIAL_PAGINATION.total,
          });
        }
      } else if (isMounted) {
        setDataSource([]);
        setPagination(INITIAL_PAGINATION);
      }
    };

    initData();

    return () => {
      isMounted = false;
    };
  }, [props.open, props.data]);

  // 定义表格列
  const [position, setPosition] = useState<"top" | "bottom" | "hidden">(
    "bottom"
  );

  const columns: ProColumns<TbDprgtRlsStopPay>[] = [
    {
      title: "解止付日期",
      dataIndex: "relsStopDate",
      width: 180,
      ellipsis: true,
      align: "center",
      editable: () => true,
      render: (_, entity) => entity.relsStopDate || "-",
      valueType: "date",
    },
    {
      title: "解止付全局业务跟踪号",
      dataIndex: "rlPayGloTracNo",
      width: 200,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "解止付子交易序号",
      dataIndex: "relPaySubtxNo",
      width: 200,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "止付号",
      dataIndex: "stopPayNo",
      width: 200,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "解止付金额",
      dataIndex: "relsStopAmt",
      width: 200,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "解止付执行文号",
      dataIndex: "relPayFileNo",
      width: 200,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "解止付有权机关名称",
      dataIndex: "relPayPowInstName",
      width: 200,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "联系电话",
      dataIndex: "contTel",
      width: 200,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "解止付方式代码",
      dataIndex: "relPayModeCd",
      width: 200,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "select",
      valueEnum: {
        "01": { text: "01-自动" },
        "02": { text: "02-人工" },
      },
    },
    {
      title: "解止付类别代码",
      dataIndex: "rlPayVatgCd",
      width: 200,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "select",
      valueEnum: {
        "1": { text: "1-全部解止付" },
        "2": { text: "2-部分解止付" },
      },
    },
    {
      title: "解止付渠道种类代码",
      dataIndex: "relPayChnlKindCd",
      width: 200,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "select",
      valueEnum: {
        "01": { text: "01-网点柜面" },
        "10": { text: "10-网上银行" },
        "12": { text: "12-个人网银" },
        "13": { text: "13-电视银行" },
        "14": { text: "14-电话银行" },
        "15": { text: "15-手机银行" },
        "16": { text: "16-企业网银" },
        "17": { text: "17-自助设备" },
        "18": { text: "18-POS" },
        "20": { text: "20-超级网银" },
        "21": { text: "21-大小额支付" },
        "22": { text: "22-银联前置" },
        "24": { text: "24-管理端" },
        "25": { text: "25-交易端" },
        "26": { text: "26-商易通" },
        "27": { text: "27-助农通" },
        "29": { text: "29-外部系统" },
        "30": { text: "30-系统自动" },
        "31": { text: "31-电子汇兑系统" },
        "32": { text: "32-理财规划终端" },
        "34": { text: "34-网汇通" },
        "35": { text: "35-同城支付" },
        "36": { text: "36-移动终端-TSM（可信服务管理）" },
        "37": { text: "37-移动终端-移动展业" },
        "38": { text: "38-直销银行" },
        "39": { text: "39-短信" },
        "40": { text: "40-专属APP" },
        "41": { text: "41-第三方线上渠道" },
        "42": { text: "42-国际支付前置" },
        "43": { text: "43-智能柜员机（ITM）" },
        "44": { text: "44-邮储经营" },
        "45": { text: "45-银银前置系统" },
        "46": { text: "46-U链供应链" },
        "47": { text: "47-油料保障结算系统" },
        "48": { text: "48-银企直联" },
        "91": { text: "91-微信银行" },
        "99": { text: "99-其他" },
      },
    },
    {
      title: "发起系统或组件编码",
      dataIndex: "startSysOrCmptNo",
      width: 120,
      ellipsis: true,
      align: "center",
    },
    {
      title: "解止付机构号",
      dataIndex: "relPayInstNo",
      width: 120,
      ellipsis: true,
      align: "center",
    },
    {
      title: "解止付柜员号",
      dataIndex: "relPayTellerNo",
      width: 120,
      ellipsis: true,
      align: "center",
    },
    {
      title: "解止付授权柜员号",
      dataIndex: "rlPayAuthTellerNo",
      width: 120,
      ellipsis: true,
      align: "center",
    },
    {
      title: "解止付原因",
      dataIndex: "rlPayReason",
      width: 200,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
     {
      title: "交易时间",
      dataIndex: "txTime",
      width: 120,
      ellipsis: false,
      align: "center",
    },
    {
      title: "最后交易日期",
      dataIndex: "lastTxDate",
      width: 120,
      ellipsis: true,
      align: "center",
       render: (_, entity) => entity.lastTxDate || "-",
      valueType: "date",
    },
    {
      title: "记录状态代码",
      dataIndex: "recordStaCd",
      width: 200,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "select",
      valueEnum: {
        "0": { text: "0-无效" },
        "1": { text: "1-有效" },
      },
    },
    {
      title: "分片值",
      dataIndex: "zoneVal",
      width: 120,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "操作",
      valueType: "option",
      width: 150,
      fixed: "right",
      align: "center",
      render: (text, record: TbDprgtRlsStopPay, _, action) => {
        const isEditing = record.stopPayNo
          ? editableKeys.includes(record.stopPayNo)
          : false;
        return [
          isEditing ? (
            <>
              <a
                key="confirm"
                onClick={async () => {
                  try {
                    if (record.stopPayNo) {
                      await action?.saveEditable?.(record.stopPayNo);
                      setIsEditing(false);
                    }
                  } catch (err) {
                    console.error("保存失败:", err);
                  }
                }}
              >
                确定
              </a>
              <a
                key="cancel"
                onClick={() => {
                  if (record.stopPayNo) {
                    action?.cancelEditable?.(record.stopPayNo);
                    setEditableRowKeys([]);
                    setIsEditing(false);
                  }
                }}
                style={{ marginLeft: 8 }}
              >
                取消
              </a>
            </>
          ) : (
            <a
              key="editable"
              onClick={async () => {
                if (!record.stopPayNo) return;
                // 先保存原始数据，再进入编辑状态
                const originalRecord = { ...record };
                setEditingRecord(originalRecord);
                setIsEditing(true);
                await action?.startEditable?.(record.stopPayNo);
              }}
            >
              编辑
            </a>
          ),
          <a
            key="delete"
            onClick={async () => {
              if (!record.stopPayNo) return;
              Modal.confirm({
                title: "确认删除",
                content: "确定要删除这条记录吗？",
                onOk: async () => {
                  try {
                    const res = await tbDprgtRlsStopPayDelete(record);
                    if (res.code === 200) {
                      message.success("删除成功");
                    } else {
                      message.error(res.msg || "删除失败");
                    }
                  } catch (error) {
                    message.error("删除失败");
                  } finally {
                    await refreshData();
                  }
                },
                okText: "确认",
                cancelText: "取消",
              });
            }}
            style={{ marginLeft: 8 }}
          >
            删除
          </a>,
        ];
      },
    },
  ];

  return (
    <Modal
      width={1200}
      title="查询解止付登记簿"
      open={props.open}
      destroyOnClose
      onCancel={props.onCancel}
      footer={null}
      bodyStyle={MODAL_BODY_STYLE}
      style={MODAL_STYLE}
    >
      <EditableProTable<TbDprgtRlsStopPay>
        key={refresh}
        rowKey={(record) => record.stopPayNo || ""}
        headerTitle="解止付登记簿"
        scroll={TABLE_SCROLL}
        recordCreatorProps={
          position === "hidden"
            ? false
            : {
                position: position,
                creatorButtonText: "新增一行解止付登记簿记录",
                onClick: async () => {
                  // 获取当前页数据
                  const startIndex =
                    (pagination.current - 1) * pagination.pageSize;
                  const endIndex = startIndex + pagination.pageSize;

                  const currentPage = pagination.current; // 当前页码
                  const pageSize = pagination.pageSize; // 每页显示的数据量
                  // 计算当前页面中数据量
                  const currentPageDataCount = Math.min(
                    pageSize, // 每页最大显示数
                    pagination.total - (currentPage - 1) * pageSize // 剩余数据量
                  );

                  // 检查当前页是否已满
                  if (
                    currentPage !== 1 &&
                    currentPageDataCount >= pagination.pageSize
                  ) {
                    Modal.error({
                      title: "提示",
                      content:
                        "当前页面数据已满，请切换至第一页或者是未满的页面进行新增",
                    });
                    return false;
                  }

                  // 创建新记录
                  const newRecord = {
                    relsStopDate: new Date().toISOString().split("T")[0],
                    rlPayGloTracNo: "",
                    relPaySubtxNo: "",
                    stopPayNo: "000000000000000000",
                    relsStopAmt: "",
                    relPayFileNo: "",
                    relPayPowInstName: "",
                    contTel: "",
                    relPayModeCd: "01",
                    rlPayVatgCd: "1",
                    relPayChnlKindCd: "01",
                    startSysOrCmptNo: "",
                    relPayInstNo: "",
                    relPayTellerNo: "",
                    rlPayAuthTellerNo: "",
                    rlPayReason: "",
                    txTime: "",
                    lastTxDate: new Date().toISOString().split("T")[0],
                    zoneVal: props.data?.records?.[0]?.zoneVal ?? "",
                  };

                  // 在当前页末尾添加新记录
                  const newDataSource = [...dataSource];
                  newDataSource.splice(endIndex, 0, newRecord);

                  setDataSource(newDataSource);
                  setIsEditing(true);
                  setEditableRowKeys(["00000000000000"]);
                },
                record: () => {
                  const formattedDate = new Date().toISOString().split("T")[0];
                  return {
                    relsStopDate: props.data?.records?.[0]?.relsStopDate ?? "",
                    rlPayGloTracNo: props.data?.records?.[0]?.rlPayGloTracNo ?? "",
                    relPaySubtxNo: props.data?.records?.[0]?.relPaySubtxNo ?? "",
                    stopPayNo: props.data?.records?.[0]?.stopPayNo ?? "",
                    relsStopAmt: props.data?.records?.[0]?.relsStopAmt ?? "",
                    relPayFileNo: props.data?.records?.[0]?.relPayFileNo ?? "",
                    relPayPowInstName: props.data?.records?.[0]?.relPayPowInstName ?? "",
                    contTel: props.data?.records?.[0]?.contTel ?? "",
                    relPayModeCd: props.data?.records?.[0]?.relPayModeCd ?? "",
                    rlPayVatgCd: props.data?.records?.[0]?.rlPayVatgCd ?? "",
                    relPayChnlKindCd: props.data?.records?.[0]?.relPayChnlKindCd ?? "",
                    startSysOrCmptNo: props.data?.records?.[0]?.startSysOrCmptNo ?? "",
                    relPayInstNo: props.data?.records?.[0]?.relPayInstNo ?? "",
                    relPayTellerNo: props.data?.records?.[0]?.relPayTellerNo ?? "",
                    rlPayAuthTellerNo: props.data?.records?.[0]?.rlPayAuthTellerNo ?? "",
                    rlPayReason: props.data?.records?.[0]?.rlPayReason ?? "",
                    txTime: props.data?.records?.[0]?.txTime ?? "",
                    lastTxDate: props.data?.records?.[0]?.lastTxDate ?? "",
                    zoneVal: props.data?.records?.[0]?.zoneVal ?? "",
                  } as TbDprgtRlsStopPay;
                },
              }
        }
        loading={loading}
        toolBarRender={() => [
          <ProFormRadio.Group
            key="render"
            fieldProps={{
              value: position,
              onChange: (e: any) => setPosition(e.target.value),
            }}
            options={[
              {
                label: "添加到顶部",
                value: "top",
              },
              {
                label: "添加到底部",
                value: "bottom",
              },
              {
                label: "隐藏",
                value: "hidden",
              },
            ]}
          />,
        ]}
        columns={columns}
        request={async (params, sorter, filter) => {
          // 如果正在编辑，直接返回当前数据
          if (isEditing) {
            return {
              data: dataSource,
              total: pagination.total,
              success: true,
            };
          }

          setLoading(true);
          try {
            if (
              !props.data ||
              !props.data.records ||
              props.data.records.length === 0
            ) {
              return {
                data: [],
                total: 0,
                success: true,
              };
            }
            const dprgtRlsStopPay = props.data.records[0];
            if (!dprgtRlsStopPay?.rlPayGloTracNo || !dprgtRlsStopPay?.zoneVal) {
              return {
                data: [],
                total: 0,
                success: true,
              };
            }
            const { current, pageSize } = params;
            const res = await getTbDprgtRlsStopPay(
                dprgtRlsStopPay.rlPayGloTracNo,
                dprgtRlsStopPay.zoneVal,
                "dprgtRlsStopPayPageInfo",
              {
                current: current || 1,
                pageSize: pageSize || DEFAULT_PAGE_SIZE,
              }
            );
            if (res.code === 200 && res.data) {
              const detailData = JSON.parse(res.data);
              setDataSource(detailData.records || []);
              setPagination({
                current: detailData.current || 1,
                pageSize: detailData.size || DEFAULT_PAGE_SIZE,
                total: detailData.total || 0,
              });
              return {
                data: detailData.records || [],
                total: detailData.total || 0,
                success: true,
              };
            } else {
              message.error(res.msg || "获取解止付登记簿失败");
              return {
                data: [],
                total: 0,
                success: false,
              };
            }
          } catch (error) {
            message.error("查询解止付登记簿失败");
            return {
              data: [],
              total: 0,
              success: false,
            };
          } finally {
            setLoading(false);
          }
        }}
        dataSource={dataSource}
        pagination={{
          ...TABLE_PAGINATION,
          current: pagination.current,
          pageSize: pagination.pageSize,
          total: pagination.total,
          showTotal: (total) => `总共 ${total} 条`,
          onChange: (current, pageSize) => {
            setPagination((prev) => ({ ...prev, current, pageSize }));
          },
        }}
        editable={{
          type: "multiple",
          editableKeys,
          onSave: async (rowKey, data) => {
            console.log("保存数据:", rowKey, data);
            if (data.rlPayGloTracNo === "0000000000000000000") {
              // 新增数据
              try {
                const newData = {
                  ...data,
                  rlPayGloTracNo: "0000000000000000000", // 生成唯一ID
                };
                console.log("保存新增数据:", newData);
                const res = await insertTbDprgtRlsStopPay(newData);
                if (res.code === 200) {
                  message.success("新增成功");
                  // 新增成功后刷新数据并跳转到最后一页
                  setEditableRowKeys([]);
                  setIsEditing(false);
                  setEditingRecord(null);

                  // 先获取最新的数据总数
                  const dprgtRlsStopPay = props.data?.records?.[0];
                  if (dprgtRlsStopPay?.rlPayGloTracNo && dprgtRlsStopPay?.zoneVal) {
                    const res = await getTbDprgtRlsStopPay(
                        dprgtRlsStopPay.rlPayGloTracNo,
                        dprgtRlsStopPay.zoneVal,
                      "dprgtRlsStopPayPageInfo",
                      {
                        current: 1,
                        pageSize: 1,
                      }
                    );

                    if (res.code === 200 && res.data) {
                      const detailData = JSON.parse(res.data);
                      const total = detailData.total || 0;
                      const lastPage = Math.ceil(total / pagination.pageSize);

                      setPagination((prev) => ({
                        ...prev,
                        current: lastPage,
                        total: total,
                      }));
                    }
                  }

                  await refreshData();
                  return true;
                } else {
                  message.error(res.msg || "新增失败");
                  await refreshData();
                  return false;
                }
              } catch (error) {
                message.error("新增失败");
                return false;
              }
            }

            if (!editingRecord || !rowKey) {
              message.error("编辑记录不存在");
              return false;
            }

            let originalData: TbDprgtRlsStopPay | null = null;
            try {
              // 只上送可编辑字段
              const updateData = {
                relsStopDate: data.relsStopDate,
                    rlPayGloTracNo: data.rlPayGloTracNo,
                    relPaySubtxNo: data.relPaySubtxNo,
                    stopPayNo: data.stopPayNo,
                    relsStopAmt: data.relsStopAmt,
                    relPayFileNo: data.relPayFileNo,
                    relPayPowInstName: data.relPayPowInstName,
                    contTel: data.contTel,
                    relPayModeCd: data.relPayModeCd,
                    rlPayVatgCd: data.rlPayVatgCd,
                    relPayChnlKindCd: data.relPayChnlKindCd,
                    startSysOrCmptNo: data.startSysOrCmptNo,
                    relPayInstNo: data.relPayInstNo,
                    relPayTellerNo: data.relPayTellerNo,
                    rlPayAuthTellerNo: data.rlPayAuthTellerNo,
                    rlPayReason: data.rlPayReason,
                    lastTxDate: data.lastTxDate,
                    zoneVal: data.zoneVal,
              };

              // 保存当前数据
              originalData =
                dataSource.find((item) => item.stopPayNo === rowKey) || null;

              const res = await TbDprgtRlsStopPayUpdate(updateData);

              if (res.code === 200) {
                // 成功时更新数据
                setDataSource((prevDataSource) =>
                  prevDataSource.map((item) =>
                    item.stopPayNo === rowKey ? { ...data } : item
                  )
                );
                message.success("保存成功");
              } else {
                // API调用失败，回滚数据
                message.error(res.msg || "保存失败");
                if (originalData) {
                  setDataSource((prevDataSource) => {
                    return prevDataSource.map((item) =>
                      item.rlPayGloTracNo === rowKey ? originalData : item
                    ) as TbDprgtRlsStopPay[];
                  });
                }
              }
            } catch (error) {
              // 发生错误，回滚数据
              message.error("保存失败");
              if (originalData) {
                setDataSource((prevDataSource) => {
                  return prevDataSource.map((item) =>
                    item.stopPayNo === rowKey ? originalData : item
                  ) as TbDprgtRlsStopPay[];
                });
              }
            } finally {
              // 退出编辑状态
              setEditableRowKeys([]);
              setIsEditing(false);
              setEditingRecord(null);
              if (props.data) {
                try {
                  const dprgtRlsStopPay =
                    props.data.records && props.data.records[0];
                  if (dprgtRlsStopPay?.stopPayNo && dprgtRlsStopPay?.zoneVal) {
                    setLoading(true);
                    try {
                      const res = await getTbDprgtRlsStopPay(
                        dprgtRlsStopPay.stopPayNo,
                        dprgtRlsStopPay.zoneVal,
                        "dprgtRlsStopPayPageInfo",
                        {
                          current: pagination.current,
                          pageSize: pagination.pageSize,
                        }
                      );
                      if (res.code === 200 && res.data) {
                        const detailData = JSON.parse(res.data);
                        setDataSource(detailData.records || []);
                        setPagination({
                          current:
                            detailData.current || INITIAL_PAGINATION.current,
                          pageSize:
                            detailData.size || INITIAL_PAGINATION.pageSize,
                          total: detailData.total || INITIAL_PAGINATION.total,
                        });
                        setRefresh(refresh + 1);
                      } else {
                        message.error(res.msg || "获取解止付登记簿失败");
                      }
                    } catch (error) {
                      message.error("查询解止付登记簿失败");
                    } finally {
                      setLoading(false);
                    }
                  }
                } catch (error) {
                  message.error("查询解止付登记簿失败");
                }
              }
            }
          },
          onChange: setEditableRowKeys,
          onCancel: async (rowKey) => {
            if (editingRecord) {
              setDataSource((prevDataSource) =>
                prevDataSource.map((item) =>
                  item.rlPayGloTracNo === editingRecord.rlPayGloTracNo
                    ? { ...editingRecord }
                    : item
                )
              );
            }
            // 退出编辑状态
            setEditableRowKeys([]);
            setIsEditing(false);
            setEditingRecord(null);
            return true;
          },
        }}
      />
    </Modal>
  );
};

export default DprgtRlsStopPayFormEdit;