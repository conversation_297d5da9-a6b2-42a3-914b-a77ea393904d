import React, { useEffect, useState } from "react";
import { Mo<PERSON>, message, <PERSON><PERSON>, Toolt<PERSON> } from "antd";
import { InfoCircleOutlined } from "@ant-design/icons";
import {
  TbDprgtSignAddUpdate,
  getTbDprgtSignAdd,
  insertTbDprgtSignAdd,
  tbDprgtSignAddDelete,
} from "../service";
import type { TbDprgtSignAdd, PageResponse } from "../data";
import type { ProColumns } from "@ant-design/pro-components";
import { EditableProTable, ProFormRadio } from "@ant-design/pro-components";

/*
 * @Description: 签约加办关系登记簿编辑表单
 * @Author: zhujinwei
 * @Date: 2025-01-17 10:00:00
 */

interface PaginationType {
  current: number;
  pageSize: number;
  total: number;
}

export type TbDprgtSignAddFormProps = {
  onCancel: () => void;
  open: boolean;
  data: PageResponse<TbDprgtSignAdd> | null;
  //   refresh: number;
};

// 表格相关常量
const DEFAULT_PAGE_SIZE = 10;
const TABLE_SCROLL = { x: 1300 } as const;
const TABLE_OPTIONS = {
  density: true,
  fullScreen: true,
  setting: true,
} as const;
const TABLE_PAGINATION = {
  showSizeChanger: true,
  showQuickJumper: true,
} as const;

// Modal相关常量
const MODAL_BODY_STYLE = {
  maxHeight: "70vh",
  overflow: "auto",
  padding: "24px",
  borderRadius: "10px",
} as const;

const MODAL_STYLE = {
  borderRadius: "10px",
  overflow: "hidden",
} as const;

// 工具函数
const formatAmount = (value: string | number | null | undefined): string => {
  if (!value) return "0.00";
  const num = typeof value === "string" ? parseFloat(value) : value;
  return isNaN(num) ? "0.00" : num.toFixed(2);
};

// 分页相关常量
const INITIAL_PAGINATION: PaginationType = {
  current: 1,
  pageSize: DEFAULT_PAGE_SIZE,
  total: 0,
};

const DprgtSignAddFormEdit: React.FC<TbDprgtSignAddFormProps> = (props) => {
  const [dataSource, setDataSource] = useState<readonly TbDprgtSignAdd[]>([]);
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);
  const [isEditing, setIsEditing] = useState(false);
  const [editingRecord, setEditingRecord] = useState<TbDprgtSignAdd | null>(
    null
  );
  const [pagination, setPagination] =
    useState<PaginationType>(INITIAL_PAGINATION);
  const [refresh, setRefresh] = useState(0);
  const [loading, setLoading] = useState(false);

  // 刷新数据方法
  const refreshData = async () => {
    if (!props.data?.records?.[0]) return;

    const TbDprgtSignAdd = props.data.records[0];
    if (!TbDprgtSignAdd?.mediumNo || !TbDprgtSignAdd?.zoneVal) return;

    setLoading(true);
    try {
      const res = await getTbDprgtSignAdd(
        TbDprgtSignAdd.mediumNo,
        TbDprgtSignAdd.zoneVal,
        "tbDprgtSignAddPageInfo",
        {
          current: pagination.current,
          pageSize: pagination.pageSize,
        }
      );

      if (res.code === 200 && res.data) {
        const detailData = JSON.parse(res.data);
        setDataSource(detailData.records || []);
        setPagination({
          current: detailData.current || INITIAL_PAGINATION.current,
          pageSize: detailData.size || INITIAL_PAGINATION.pageSize,
          total: detailData.total || INITIAL_PAGINATION.total,
        });
        setRefresh(refresh + 1);
      } else {
        message.error(res.msg || "获取签约加办关系登记失败");
      }
    } catch (error) {
      message.error("查询签约加办关系登记失败");
    } finally {
      setLoading(false);
    }
  };

  // 初始化数据
  useEffect(() => {
    let isMounted = true;

    const initData = async () => {
      if (props.open && props.data) {
        const { records, current, size, total } = props.data;
        if (isMounted) {
          setDataSource(records || []);
          setPagination({
            current: current || INITIAL_PAGINATION.current,
            pageSize: size || INITIAL_PAGINATION.pageSize,
            total: total || INITIAL_PAGINATION.total,
          });
        }
      } else if (isMounted) {
        setDataSource([]);
        setPagination(INITIAL_PAGINATION);
      }
    };

    initData();

    return () => {
      isMounted = false;
    };
  }, [props.open, props.data]);

  // 定义表格列
  const [position, setPosition] = useState<"top" | "bottom" | "hidden">(
    "bottom"
  );

  const columns: ProColumns<TbDprgtSignAdd>[] = [
    {
      title: "介质编号",
      dataIndex: "mediumNo",
      width: 200,
      fixed: true,
      // ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "签约对方系统编码",
      dataIndex: "signOpsSysNo",
      width: 180,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "签约种类代码",
      dataIndex: "signKindCd",
      width: 200,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "select",
      valueEnum: {
        "1000": { text: "1000-电子银行" },
        "1100": { text: "1100-贵金属合约" },
        "1200": { text: "1200-短信合约" },
        "1300": { text: "1300-理财类合约" },
        "1400": { text: "1400-三方存管合约" },
        "1500": { text: "1500-协助查控系统加办" },
        "1600": { text: "1600-信用卡还款合约" },
        "1700": { text: "1700-信贷合约" },
        "1800": { text: "1800-代理保险委托扣款签约" },
        "1900": { text: "1900-代收付业务合约" },
        "2000": { text: "2000-ATM刷脸交易" },
        "2100": { text: "2100-ATM现金汇款" },
        "2200": { text: "2200-存款类合约" },
        "2300": { text: "2300-汇款类合约" },
        "2400": { text: "2400-银联无卡支付" },
        "2500": { text: "2500-邮政汇兑合约" },
        "2600": { text: "2600-外币协议储蓄" },
        "3400": { text: "3400-柜面刷脸办" },
        "3500": { text: "3500-银联前置映射卡" },
      },
    },
    {
      title: "签约子类代码",
      dataIndex: "signSubtypeCd",
      width: 200,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "select",
      valueEnum: {
        "1000000001": { text: "1000000001-电话银行" },
        "1000000002": { text: "1000000002-个人网银" },
        "1000000003": { text: "1000000003-手机银行" },
        "1000000006": { text: "1000000006-手机银行动账通知" },
        "1000000007": { text: "1000000007-微信银行动账通知" },
        "1100000001": { text: "1100000001-代理金交易" },
        "1100000002": { text: "1100000002-黄金积存" },
        "1100000003": { text: "1100000003-实物金交易" },
        "1200000000": { text: "1200000000-短信合约" },
        "1300000000": { text: "1300000000-理财类合约" },
        "1400000001": { text: "1400000001-银证存管" },
        "1400000002": { text: "1400000002-银期存管" },
        "1400000003": { text: "1400000003-银商存管" },
        "1400000004": { text: "1400000004-金融机构存管" },
        "1400000005": { text: "1400000005-银彩通" },
        "1500000000": { text: "1500000000-协助查控系统加办" },
        "1600000000": { text: "1600000000-信用卡还款合约" },
        "1700000001": { text: "1700000001-信贷卡折加办" },
        "1700000002": { text: "1700000002-信贷子账户加办（卡贷通）" },
        "1800000000": { text: "1800000000-代理保险委托扣款签约" },
        "1900100000": {
          text: "1900100000-代收类",
        },
        "1900101000": {
          text: "1900101000-公共事业类",
        },
        "1900101010": {
          text: "1900101010-水费",
        },
        "1900101020": {
          text: "1900101020-电费",
        },
        "1900101030": {
          text: "1900101030-燃气费",
        },
        "1900101040": {
          text: "1900101040-有线电视费",
        },
        "1900101050": {
          text: "1900101050-热力费",
        },
        "1900101060": {
          text: "1900101060-物业费",
        },
        "1900101061": {
          text: "1900101061-停车费",
        },
        "1900101062": {
          text: "1900101062-市场管理费",
        },
        "1900101063": {
          text: "1900101063-水电费",
        },
        "1900101070": {
          text: "1900101070-房租费",
        },
        "1900102000": {
          text: "1900102000-通讯类",
        },
        "1900102010": {
          text: "1900102010-宽带费",
        },
        "1900102020": {
          text: "1900102020-移动通讯费",
        },
        "1900102030": {
          text: "1900102030-联通通讯费",
        },
        "1900102040": {
          text: "1900102040-电信通讯费",
        },
        "1900103000": {
          text: "1900103000-代收社保类",
        },
        "1900103010": {
          text: "1900103010-代收社保",
        },
        "1900103011": {
          text: "1900103011-社保税银",
        },
        "1900103020": {
          text: "1900103020-代收城镇职工基本养老保险",
        },
        "1900103030": {
          text: "1900103030-代收城乡居民基本养老保险",
        },
        "1900103040": {
          text: "1900103040-代收失业保险",
        },
        "1900103050": {
          text: "1900103050-代收工伤保险",
        },
        "1900103060": {
          text: "1900103060-代收职业年金",
        },
        "1900103070": {
          text: "1900103070-大病医疗保险",
        },
        "1900104000": {
          text: "1900104000-非税类",
        },
        "1900104010": {
          text: "1900104010-非税",
        },
        "1900104020": {
          text: "1900104020-交通罚没款",
        },
        "1900104021": {
          text: "1900104021-跨省交通罚没款",
        },
        "1900104030": {
          text: "1900104030-维修基金",
        },
        "1900104040": {
          text: "1900104040-诉讼费",
        },
        "1900104050": {
          text: "1900104050-执行款",
        },
        "1900104060": {
          text: "1900104060-非税其他",
        },
        "1900104080": {
          text: "1900104080-垃圾处理费",
        },
        "1900104090": {
          text: "1900104090-林业调查费",
        },
        "1900105000": {
          text: "1900105000-四川能源营业款",
        },
        "1900105010": {
          text: "1900105010-代收职工基本医疗保险",
        },
        "1900105020": {
          text: "1900105020-代收城乡居民基本医疗保险",
        },
        "1900106000": {
          text: "1900106000-彩票代收类",
        },
        "1900106010": {
          text: "1900106010-福彩营业款",
        },
        "1900106020": {
          text: "1900106020-体彩营业款",
        },
        "1900106030": {
          text: "1900106030-福彩定投",
        },
        "1900106040": {
          text: "1900106040-体彩定投",
        },
        "1900107000": {
          text: "1900107000-烟草代收类",
        },
        "1900107010": {
          text: "1900107010-卷烟销售款",
        },
        "1900107020": {
          text: "1900107020-烟用物资",
        },
        "1900107030": {
          text: "1900107030-烟苗款及无人机植保款",
        },
        "1900108000": {
          text: "1900108000-营业款类",
        },
        "1900108010": {
          text: "1900108010-营业款",
        },
        "1900108020": {
          text: "1900108020-物流营业款",
        },
        "1900108021": {
          text: "1900108021-邮政营业款",
        },
        "1900108022": {
          text: "1900108022-韵达物流营业款",
        },
        "1900108023": {
          text: "1900108023-韵达对公物流营业款",
        },
        "1900108024": {
          text: "1900108024-速递物流快递费",
        },
        "1900108025": {
          text: "1900108025-邮政寄递费",
        },
        "1900108030": {
          text: "1900108030-代收货款",
        },
        "1900108031": {
          text: "1900108031-租赁营业款",
        },
        "1900108040": {
          text: "1900108040-移动营业款",
        },
        "1900108041": {
          text: "1900108041-联通营业款",
        },
        "1900108042": {
          text: "1900108042-电信营业款",
        },
        "1900108043": {
          text: "1900108043-中石化营业款",
        },
        "1900108044": {
          text: "1900108044-中石油营业款",
        },
        "1900108045": {
          text: "1900108045-电力营业款",
        },
        "1900108046": {
          text: "1900108046-报刊营业款",
        },
        "1900108050": {
          text: "1900108050-伙食费",
        },
        "1900108060": {
          text: "1900108060-代收营业款",
        },
        "1900108990": {
          text: "1900108990-邮政便民服务站",
        },
        "1900109000": {
          text: "1900109000-医疗类",
        },
        "1900109010": {
          text: "1900109010-医疗",
        },
        "1900109020": {
          text: "1900109020-养老院",
        },
        "1900109030": {
          text: "1900109030-挂号费",
        },
        "1900109040": {
          text: "1900109040-医事服务费",
        },
        "1900109050": {
          text: "1900109050-住院费",
        },
        "1900110000": {
          text: "1900110000-教育类",
        },
        "1900110010": {
          text: "1900110010-学费",
        },
        "1900110020": {
          text: "1900110020-代收杂费",
        },
        "1900110030": {
          text: "1900110030-培训费",
        },
        "1900110040": {
          text: "1900110040-住宿费",
        },
        "1900110050": {
          text: "1900110050-餐费",
        },
        "1900110060": {
          text: "1900110060-代收费",
        },
        "1900110070": {
          text: "1900110070-保育费",
        },
        "1900110080": {
          text: "1900110080-餐杂费",
        },
        "1900110090": {
          text: "1900110090-午托费",
        },
        "1900110100": {
          text: "1900110100-学生体检费",
        },
        "1900110110": {
          text: "1900110110-教材费",
        },
        "1900110120": {
          text: "1900110120-学杂及考试报名费",
        },
        "1900111000": {
          text: "1900111000-IC卡圈存充值",
        },
        "1900111010": {
          text: "1900111010-校园卡圈存",
        },
        "1900111020": {
          text: "1900111020-公交卡充值",
        },
        "1900111030": {
          text: "1900111030-市民卡充值",
        },
        "1900111110": {
          text: "1900111110-ETC",
        },
        "1900112000": {
          text: "1900112000-融资租赁类",
        },
        "1900112010": {
          text: "1900112010-融资租赁",
        },
        "1900113000": {
          text: "1900113000-代收消费金融类",
        },
        "1900113010": {
          text: "1900113010-汽车金融",
        },
        "1900113011": {
          text: "1900113011-汽车租金",
        },
        "1900113020": {
          text: "1900113020-消费金融还款",
        },
        "1900113030": {
          text: "1900113030-中邮消费还款",
        },
        "1900114000": {
          text: "1900114000-理财类",
        },
        "1900114010": {
          text: "1900114010-基金申购",
        },
        "1900115000": {
          text: "1900115000-邮票预订",
        },
        "1900115010": {
          text: "1900115010-邮票预定",
        },
        "1900116000": {
          text: "1900116000-公积金代收类",
        },
        "1900116010": {
          text: "1900116010-住房公积金",
        },
        "1900116011": {
          text: "1900116011-住建部公积金",
        },
        "1900116020": {
          text: "1900116020-公积金委托贷款还款",
        },
        "1900117000": {
          text: "1900117000-保证金类",
        },
        "1900117010": {
          text: "1900117010-担保购机",
        },
        "1900117020": {
          text: "1900117020-购房验资",
        },
        "1900117030": {
          text: "1900117030-房屋资金监管扣款",
        },
        "1900117040": {
          text: "1900117040-招投标保证金",
        },
        "1900118000": {
          text: "1900118000-信用卡分期",
        },
        "1900118010": {
          text: "1900118010-信用卡汽车分期",
        },
        "1900118020": {
          text: "1900118020-信用卡教育分期",
        },
        "1900118030": {
          text: "1900118030-信用卡供暖分期",
        },
        "1900119000": {
          text: "1900119000-团体组织类",
        },
        "1900119010": {
          text: "1900119010-党费",
        },
        "1900119020": {
          text: "1900119020-工会费",
        },
        "1900119030": {
          text: "1900119030-团费",
        },
        "1900119040": {
          text: "1900119040-会员费",
        },
        "1900120000": {
          text: "1900120000-服务费代收类",
        },
        "1900120010": {
          text: "1900120010-代收咨询服务费",
        },
        "1900195000": {
          text: "1900195000-国库代收类",
        },
        "1900195010": {
          text: "1900195010-国库联网缴税",
        },
        "1900196000": {
          text: "1900196000-贷款还款类",
        },
        "1900196010": {
          text: "1900196010-三农联合贷还款",
        },
        "1900196020": {
          text: "1900196020-抵押还款",
        },
        "1900197000": {
          text: "1900197000-公益代收类",
        },
        "1900197010": {
          text: "1900197010-捐款",
        },
        "1900197020": {
          text: "1900197020-爱心捐款",
        },
        "1900197980": {
          text: "1900197980-邮爱基金",
        },
        "1900197990": {
          text: "1900197990-绿动邮你",
        },
        "1900198000": {
          text: "1900198000-集中代收类",
        },
        "1900198010": {
          text: "1900198010-人行同城代收",
        },
        "1900199000": {
          text: "1900199000-行内特殊收款",
        },
        "1900199010": {
          text: "1900199010-欠费补扣",
        },
        "1900199020": {
          text: "1900199020-其他查询",
        },
        "1900200000": {
          text: "1900200000-代付类",
        },
        "1900201000": {
          text: "1900201000-工资类",
        },
        "1900201010": {
          text: "1900201010-代发工资",
        },
        "1900201020": {
          text: "1900201020-农民工工资",
        },
        "1900201021": {
          text: "1900201021-农民工工资监管",
        },
        "1900201030": {
          text: "1900201030-代发薪酬",
        },
        "1900201040": {
          text: "1900201040-代发津贴",
        },
        "1900201050": {
          text: "1900201050-代发奖金",
        },
        "1900201051": {
          text: "1900201051-代发奖学金",
        },
        "1900201060": {
          text: "1900201060-劳务费",
        },
        "1900201070": {
          text: "1900201070-稿费",
        },
        "1900201080": {
          text: "1900201080-军保卡",
        },
        "1900201090": {
          text: "1900201090-国库工资",
        },
        "1900201110": {
          text: "1900201110-差旅费",
        },
        "1900201120": {
          text: "1900201120-慰问金",
        },
        "1900201130": {
          text: "1900201130-住房补贴",
        },
        "1900201140": {
          text: "1900201140-代付杂费",
        },
        "1900201150": {
          text: "1900201150-大龄补贴",
        },
        "1900201160": {
          text: "1900201160-报销款",
        },
        "1900202000": {
          text: "1900202000-代付社保",
        },
        "1900202010": {
          text: "1900202010-代付城镇职工基本养老保险",
        },
        "1900202011": {
          text: "1900202011-企业统筹养老金",
        },
        "1900202012": {
          text: "1900202012-企业非统筹养老金",
        },
        "1900202013": {
          text: "1900202013-机关事业养老保险",
        },
        "1900202014": {
          text: "1900202014-企业养老保险",
        },
        "1900202020": {
          text: "1900202020-代付城乡居民基本养老保险",
        },
        "1900202021": {
          text: "1900202021-新农保",
        },
        "1900202022": {
          text: "1900202022-超龄养老金",
        },
        "1900202023": {
          text: "1900202023-老年生活保障金",
        },
        "1900202030": {
          text: "1900202030-代付失业保险",
        },
        "1900202040": {
          text: "1900202040-代付工伤保险",
        },
        "1900202050": {
          text: "1900202050-灵活就业保险",
        },
        "1900202210": {
          text: "1900202210-代发医疗保险",
        },
        "1900203000": {
          text: "1900203000-财政类",
        },
        "1900203010": {
          text: "1900203010-学生资助资金",
        },
        "1900203011": {
          text: "1900203011-代发中职助学金",
        },
        "1900203012": {
          text: "1900203012-普通高中助学金",
        },
        "1900203020": {
          text: "1900203020-财政贴息",
        },
        "1900203021": {
          text: "1900203021-扶贫贴息",
        },
        "1900203022": {
          text: "1900203022-工贷贴息",
        },
        "1900203023": {
          text: "1900203023-创业贷款贴息",
        },
        "1900203030": {
          text: "1900203030-拆迁补贴",
        },
        "1900203040": {
          text: "1900203040-保障性补贴",
        },
        "1900203041": {
          text: "1900203041-财政补贴",
        },
        "1900203042": {
          text: "1900203042-车辆报废补贴",
        },
        "1900203043": {
          text: "1900203043-民办教师补贴",
        },
        "1900203050": {
          text: "1900203050-优抚金",
        },
        "1900203060": {
          text: "1900203060-低保",
        },
        "1900203061": {
          text: "1900203061-五保",
        },
        "1900203070": {
          text: "1900203070-特殊扶助金",
        },
        "1900203080": {
          text: "1900203080-粮食补贴",
        },
        "1900203090": {
          text: "1900203090-家电下乡补贴",
        },
        "1900203100": {
          text: "1900203100-退耕还林补贴",
        },
        "1900203110": {
          text: "1900203110-土地补贴",
        },
        "1900203120": {
          text: "1900203120-移民补助",
        },
        "1900203130": {
          text: "1900203130-渔船燃油补助",
        },
        "1900203131": {
          text: "1900203131-汽车燃油补贴",
        },
        "1900203140": {
          text: "1900203140-惠民惠农补贴",
        },
        "1900203141": {
          text: "1900203141-惠民惠农一卡通",
        },
        "1900203150": {
          text: "1900203150-退役军人补贴",
        },
        "1900203160": {
          text: "1900203160-计生奖励",
        },
        "1900203170": {
          text: "1900203170-涉农补贴",
        },
        "1900203180": {
          text: "1900203180-生活补贴",
        },
        "1900203190": {
          text: "1900203190-光伏扶贫",
        },
        "1900204000": {
          text: "1900204000-国库代付类",
        },
        "1900204010": {
          text: "1900204010-国库集中支付",
        },
        "1900205000": {
          text: "1900205000-医保代付类",
        },
        "1900205010": {
          text: "1900205010-代付职工基本医疗保险",
        },
        "1900205020": {
          text: "1900205020-代付城乡居民基本医疗保险",
        },
        "1900205030": {
          text: "1900205030-医保报销",
        },
        "1900205050": {
          text: "1900205050-医药费报销",
        },
        "1900206000": {
          text: "1900206000-彩票代付类",
        },
        "1900206010": {
          text: "1900206010-福彩中奖",
        },
        "1900206020": {
          text: "1900206020-体彩中奖",
        },
        "1900207000": {
          text: "1900207000-烟草代付类",
        },
        "1900207010": {
          text: "1900207010-烟叶收购款",
        },
        "1900207020": {
          text: "1900207020-烟农补贴",
        },
        "1900208000": {
          text: "1900208000-结算款类",
        },
        "1900208010": {
          text: "1900208010-物流货款",
        },
        "1900208011": {
          text: "1900208011-韵达物流款",
        },
        "1900208012": {
          text: "1900208012-农副产品交易款",
        },
        "1900208020": {
          text: "1900208020-收单商户奖励",
        },
        "1900208030": {
          text: "1900208030-商户结算款",
        },
        "1900208040": {
          text: "1900208040-代发茧款",
        },
        "1900208120": {
          text: "1900208120-国库集中支付结算",
        },
        "1900209000": {
          text: "1900209000-代付消费金融类",
        },
        "1900209010": {
          text: "1900209010-消费金融放款",
        },
        "1900209990": {
          text: "1900209990-中邮消费放款",
        },
        "1900210000": {
          text: "1900210000-个人网银代发",
        },
        "1900210010": {
          text: "1900210010-个人网银批量代发",
        },
        "1900211000": {
          text: "1900211000-公积金代付类",
        },
        "1900211010": {
          text: "1900211010-公积金提取",
        },
        "1900211020": {
          text: "1900211020-公积金放款",
        },
        "1900212000": {
          text: "1900212000-房屋交易资金",
        },
        "1900212010": {
          text: "1900212010-房屋资金监管付款",
        },
        "1900295000": {
          text: "1900295000-公务卡类",
        },
        "1900295010": {
          text: "1900295010-公务卡代付",
        },
        "1900296000": {
          text: "1900296000-公益代付类",
        },
        "1900297000": {
          text: "1900297000-退款类",
        },
        "1900297010": {
          text: "1900297010-餐费退款",
        },
        "1900297020": {
          text: "1900297020-退保证金",
        },
        "1900297030": {
          text: "1900297030-租金退款",
        },
        "1900297040": {
          text: "1900297040-退评估费",
        },
        "1900298000": {
          text: "1900298000-集中代付类",
        },
        "1900298010": {
          text: "1900298010-人行同城代付",
        },
        "1900299000": {
          text: "1900299000-行内特殊付款",
        },
        "1900300000": {
          text: "1900300000-同城通存通兑",
        },
        "1900301000": {
          text: "1900301000-人行通存通兑类",
        },
        "1900301010": {
          text: "1900301010-人行通存通兑",
        },
        "1900400000": {
          text: "1900400000-信息服务类",
        },
        "1900401000": {
          text: "1900401000-对公账户",
        },
        "1900401010": {
          text: "1900401010-易窗通",
        },
        "1900402000": {
          text: "1900402000-社保卡类",
        },
        "1900402010": {
          text: "1900402010-社保卡代收付",
        },
        "1900108071": {
          text: "1900108071-电商平台卡表电力",
        },
        "1900108072": {
          text: "1900108072-电商平台抄表电力",
        },
        "1900108999": {
          text: "1900108999-个人外贸结算",
        },
        "1900121000": {
          text: "1900121000-商业保险类",
        },
        "1900196030": {
          text: "1900196030-不良贷款回款",
        },
        "1900201081": {
          text: "1900201081-民兵补贴",
        },
        "1900202026": {
          text: "1900202026-机关事业职业年金",
        },
        "1900202041": {
          text: "1900202041-职业伤害险",
        },
        "1900203013": {
          text: "1900203013-贫困补助",
        },
        "1900203152": {
          text: "1900203152-军人资助金",
        },
        "1900213000": {
          text: "1900213000-保险费代付",
        },
        "1900214001": {
          text: "1900214001-基金快赎",
        },
        "1900214002": {
          text: "1900214002-理财快赎",
        },
        "1900101011": {
          text: "1900101011-代收水资源费",
        },
        "1900101021": {
          text: "1900101021-代收卡表电费",
        },
        "1900101031": {
          text: "1900101031-代收抄表燃气费",
        },
        "1900101032": {
          text: "1900101032-代收芯片卡表燃气费",
        },
        "1900101033": {
          text: "1900101033-物联网居民燃气",
        },
        "1900101034": {
          text: "1900101034-物联网对公燃气",
        },
        "1900101035": {
          text: "1900101035-北京平谷燃气",
        },
        "1900101064": {
          text: "1900101064-房屋监管资金",
        },
        "1900102050": {
          text: "1900102050-铁通通讯费",
        },
        "1900103012": {
          text: "1900103012-社保临柜缴费",
        },
        "1900104041": {
          text: "1900104041-代收法院款项",
        },
        "1900104070": {
          text: "1900104070-代扣民政资金",
        },
        "1900104100": {
          text: "1900104100-代收印花税",
        },
        "1900104110": {
          text: "1900104110-代收个人所得税",
        },
        "1900104120": {
          text: "1900104120-暂扣押款",
        },
        "1900104130": {
          text: "1900104130-仲裁费",
        },
        "1900104140": {
          text: "1900104140-考试费",
        },
        "1900104150": {
          text: "1900104150-棚户区改造款",
        },
        "1900104160": {
          text: "1900104160-政策性水稻保险",
        },
        "1900105030": {
          text: "1900105030-代收灵活就业人员医疗保险",
        },
        "1900105040": {
          text: "1900105040-代收医疗保险",
        },
        "1900107040": {
          text: "1900107040-代扣卷烟款",
        },
        "1900108070": {
          text: "1900108070-电子商务营业款",
        },
        "1900108080": {
          text: "1900108080-邮政电商便民汇款",
        },
        "1900110130": {
          text: "1900110130-校园刷脸",
        },
        "1900110140": {
          text: "1900110140-研学费",
        },
        "1900110150": {
          text: "1900110150-助学读物费",
        },
        "1900110160": {
          text: "1900110160-教培资金监管",
        },
        "1900110170": {
          text: "1900110170-课后服务费",
        },
        "1900111120": {
          text: "1900111120-高速收费结算代收",
        },
        "1900111130": {
          text: "1900111130-借记卡选装",
        },
        "1900111140": {
          text: "1900111140-信用卡选装",
        },
        "1900115020": {
          text: "1900115020-邮政增值业务代收",
        },
        "1900115030": {
          text: "1900115030-中邮阅读网代扣",
        },
        "1900120020": {
          text: "1900120020-保管箱费用",
        },
        "1900120021": {
          text: "1900120021-保管箱保证金",
        },
        "1900121010": {
          text: "1900121010-保险费代收",
        },
        "1900121011": {
          text: "1900121011-代收担保费",
        },
        "1900198020": {
          text: "1900198020-银联代收",
        },
        "1900202001": {
          text: "1900202001-社会保险费批量返还",
        },
        "1900202015": {
          text: "1900202015-农转非养老金",
        },
        "1900202016": {
          text: "1900202016-机关事业养老保险试点",
        },
        "1900202017": {
          text: "1900202017-军转干补贴",
        },
        "1900202018": {
          text: "1900202018-代发工龄补缴",
        },
        "1900202019": {
          text: "1900202019-功勋荣誉津贴",
        },
        "1900202024": {
          text: "1900202024-征地养老",
        },
        "1900202025": {
          text: "1900202025-代付城乡居民补充养老保险",
        },
        "1900202031": {
          text: "1900202031-职业技能提升补贴",
        },
        "1900202051": {
          text: "1900202051-享受补贴人员退费",
        },
        "1900202052": {
          text: "1900202052-个人委托存档三险退费",
        },
        "1900203051": {
          text: "1900203051-抚恤金",
        },
        "1900203151": {
          text: "1900203151-军人安置费",
        },
        "1900203181": {
          text: "1900203181-市场租房补贴",
        },
        "1900203182": {
          text: "1900203182-公租房补贴",
        },
        "1900205011": {
          text: "1900205011-个人委托存档医疗报销",
        },
        "1900208050": {
          text: "1900208050-股份分红",
        },
        "1900208060": {
          text: "1900208060-土地租赁款结算",
        },
        "1900208070": {
          text: "1900208070-房屋租赁款结算",
        },
        "1900208080": {
          text: "1900208080-高速收费结算代付",
        },
        "1900296010": {
          text: "1900296010-救济金",
        },
        "1900297050": {
          text: "1900297050-邮政增值业务退款",
        },
        "1900297060": {
          text: "1900297060-涉案款返还",
        },
        "1900297070": {
          text: "1900297070-诉讼费退款",
        },
        "1900298020": {
          text: "1900298020-银联代付",
        },
        "1900401020": {
          text: "1900401020-破产管理人账户查询",
        },
        "2000000000": { text: "2000000000-ATM刷脸交易" },
        "2100000000": { text: "2100000000-ATM现金汇款" },
        "2200000001": { text: "2200000001-约定转账" },
        "2200000003": { text: "2200000003-递增计息预约转入" },
        "2300000001": { text: "2300000001-预约汇款" },
        "2300000002": { text: "2300000002-汇款资金归集" },
        "2300000003": { text: "2300000003-预约汇款（电子银行）" },
        "2300000004": { text: "2300000004-跨境理财通（南向通）合约" },
        "2400000000": { text: "2400000000-银联无卡支付" },
        "2500000001": { text: "2500000001-邮政汇兑逾期退款" },
        "2600000000": { text: "2600000000-外币协议储蓄" },
        "3400000000": { text: "3400000000-柜面刷脸办" },
        "3500000000": { text: "3500000000-手机pay(2.0)" },
      },
    },
    {
      title: "产品合约编号",
      dataIndex: "prodtContractNo",
      width: 200,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "签约描述",
      dataIndex: "signDesc",
      width: 200,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "签约加办次数",
      dataIndex: "signAddoffTimes",
      width: 200,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "签约状态代码",
      dataIndex: "signContStatus",
      width: 200,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "select",
      valueEnum: {
        "0": { text: "0-签约" },
        "1": { text: "1-解约" },
      },
    },
    {
      title: "签约生效日期",
      dataIndex: "signContEffDate",
      width: 180,
      ellipsis: true,
      align: "center",
      editable: () => true,
      render: (_, entity) => entity.signContEffDate || "-",
      valueType: "date",
    },
    {
      title: "签约时间",
      dataIndex: "signTime",
      width: 180,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "签约日期",
      dataIndex: "signContDate",
      width: 180,
      ellipsis: true,
      align: "center",
      editable: () => true,
      render: (_, entity) => entity.signContDate || "-",
      valueType: "date",
    },
    {
      title: "登记全局业务跟踪号",
      dataIndex: "enrollGloTracNo",
      width: 220,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "登记子交易序号",
      dataIndex: "enrollSubtxNo",
      width: 200,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "签约手机号",
      dataIndex: "signMobileNo",
      width: 200,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "签约柜员号",
      dataIndex: "signTellerNo",
      width: 200,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "签约机构号",
      dataIndex: "signContInstNo",
      width: 200,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "授权柜员号",
      dataIndex: "authTellerNo",
      width: 200,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "签约失效日期",
      dataIndex: "signInvalDt",
      width: 180,
      ellipsis: true,
      align: "center",
      editable: () => true,
      render: (_, entity) => entity.signInvalDt || "-",
      valueType: "date",
    },
    {
      title: () => (
        <Tooltip
          title={
            <div style={{ width: "100%" }}>
              <p>签约信息标志码说明</p>
              <ul>
                <li>
                  <strong>第1位：数据切换类型代码： </strong>0-未切换，1-已切换
                </li>
                <li>
                  <strong>第2位：数据一次性批量处理标志： </strong>0-未处理，1-一次性批量交易处理
                </li>
              </ul>
            </div>
          }
          placement="top"
          overlayClassName="custom-tooltip"
          color="#fff"
          overlayStyle={{ minWidth: "350px" }}
        >
          <span>
          签约信息标志码{" "}
            <InfoCircleOutlined
              style={{ marginLeft: 4, color: "#1890ff" }}
            />
          </span>
        </Tooltip>
      ),
      dataIndex: "signInfoFlagCd",
      width: 200,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "解约时间",
      dataIndex: "cacContTime",
      width: 180,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "解约日期",
      dataIndex: "cacContDt",
      width: 180,
      ellipsis: true,
      align: "center",
      editable: () => true,
      render: (_, entity) => entity.cacContDt || "-",
      valueType: "date",
    },
    {
      title: "解约全局业务跟踪号",
      dataIndex: "cacContGloTracNo",
      width: 200,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "解约子交易序号",
      dataIndex: "cacContSubtxNo",
      width: 200,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "解约机构号",
      dataIndex: "cacContInstNo",
      width: 200,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "解约柜员号",
      dataIndex: "cacContTellerNo",
      width: 200,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "解约授权柜员号",
      dataIndex: "cacContAuthTellerNo",
      width: 200,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "最后交易日期",
      dataIndex: "lastTxDate",
      width: 120,
      ellipsis: true,
      align: "center",
      render: (_, entity) => entity.lastTxDate || "-",
      valueType: "date",
    },
    {
      title: "分片值",
      dataIndex: "zoneVal",
      width: 150,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "操作",
      valueType: "option",
      width: 150,
      fixed: "right",
      align: "center",
      render: (text, record: TbDprgtSignAdd, _, action) => {
        const isEditing = record.enrollGloTracNo
          ? editableKeys.includes(record.enrollGloTracNo)
          : false;
        return [
          isEditing ? (
            <>
              <a
                key={`confirm-${record.enrollGloTracNo}`}
                onClick={async () => {
                  try {
                    if (record.enrollGloTracNo) {
                      await action?.saveEditable?.(record.enrollGloTracNo);
                      setIsEditing(false);
                    }
                  } catch (err) {
                    console.error("保存失败:", err);
                  }
                }}
              >
                确定
              </a>
              <a
                key={`cancel-${record.enrollGloTracNo}`}
                onClick={() => {
                  if (record.mediumNo) {
                    action?.cancelEditable?.(record.mediumNo);
                    setEditableRowKeys([]);
                    setIsEditing(false);
                  }
                }}
                style={{ marginLeft: 8 }}
              >
                取消
              </a>
            </>
          ) : (
            <Button
              key={`editable-${record.enrollGloTracNo}`}
              type="link"
              disabled={true}
              onClick={async () => {
                if (!record.enrollGloTracNo) return;
                // 先保存原始数据，再进入编辑状态
                const originalRecord = { ...record };
                setEditingRecord(originalRecord);
                setIsEditing(true);
                setEditableRowKeys([record.enrollGloTracNo]);
                await action?.startEditable?.(record.enrollGloTracNo);
              }}
              style={{ padding: 0 }}
            >
              编辑
            </Button>
          ),
          <Button
            key={`delete-${record.enrollGloTracNo}`}
            type="link"
            disabled={true}
            onClick={async () => {
              if (!record.enrollGloTracNo) return;
              Modal.confirm({
                title: "确认删除",
                content: "确定要删除这条记录吗？",
                onOk: async () => {
                  try {
                    const res = await tbDprgtSignAddDelete(record);
                    if (res.code === 200) {
                      message.success("删除成功");
                    } else {
                      message.error(res.msg || "删除失败");
                    }
                  } catch (error) {
                    message.error("删除失败");
                  } finally {
                    await refreshData();
                  }
                },
                okText: "确认",
                cancelText: "取消",
              });
            }}
            style={{ marginLeft: 8 }}
          >
            删除
          </Button>,
        ];
      },
    },
  ];

  return (
    <Modal
      width={1200}
      title="查询签约加办关系登记簿"
      open={props.open}
      destroyOnClose
      onCancel={props.onCancel}
      footer={null}
      bodyStyle={MODAL_BODY_STYLE}
      style={MODAL_STYLE}
    >
      <EditableProTable<TbDprgtSignAdd>
        key={refresh}
        rowKey={(record) => record.enrollGloTracNo || ""}
        headerTitle="签约加办关系登记簿"
        scroll={TABLE_SCROLL}
        recordCreatorProps={
          position === "hidden"
            ? false
            : {
                position: position,
                creatorButtonText: "新增一行签约加办关系登记记录",
                onClick: async () => {
                  // 获取当前页数据
                  const startIndex =
                    (pagination.current - 1) * pagination.pageSize;
                  const endIndex = startIndex + pagination.pageSize;

                  const currentPage = pagination.current; // 当前页码
                  const pageSize = pagination.pageSize; // 每页显示的数据量
                  // 计算当前页面中数据量
                  const currentPageDataCount = Math.min(
                    pageSize, // 每页最大显示数
                    pagination.total - (currentPage - 1) * pageSize // 剩余数据量
                  );

                  // 检查当前页是否已满
                  if (
                    currentPage !== 1 &&
                    currentPageDataCount >= pagination.pageSize
                  ) {
                    Modal.error({
                      title: "提示",
                      content:
                        "当前页面数据已满，请切换至第一页或者是未满的页面进行新增",
                    });
                    return false;
                  }

                  const formattedDate = new Date().toISOString().split("T")[0];
                  // 创建新记录
                  const newRecord = {
                    mediumNo: props.data?.records?.[0]?.mediumNo ?? "",
                    signOpsSysNo: "",
                    signKindCd: "",
                    signSubtypeCd: "",
                    prodtContractNo:
                      props.data?.records?.[0]?.prodtContractNo ?? "",
                    signDesc: "",
                    signAddoffTimes: "",
                    signContStatus: "0",
                    signContEffDate: formattedDate,
                    signTime: "",
                    signContDate: formattedDate,
                    enrollGloTracNo: Math.random()
                      .toString(36)
                      .substring(2, 15),
                    enrollSubtxNo: Math.random().toString(36).substring(2, 15),
                    signMobileNo: "",
                    signTellerNo: "",
                    signContInstNo: "",
                    authTellerNo: "",
                    signInvalDt: formattedDate,
                    signInfoFlagCd: "000000",
                    cacContTime: "",
                    cacContDt: formattedDate,
                    cacContGloTracNo: "",
                    cacContSubtxNo: "",
                    cacContInstNo: "",
                    cacContTellerNo: "",
                    cacContAuthTellerNo: "",
                    lastTxDate: formattedDate,
                    zoneVal: props.data?.records?.[0]?.zoneVal ?? "",
                  };

                  // 在当前页末尾添加新记录
                  const newDataSource = [...dataSource];
                  newDataSource.splice(endIndex, 0, newRecord);

                  setDataSource(newDataSource);
                  setIsEditing(true);
                  setEditableRowKeys(["XXXXXXXXXXXXXXX"]);
                },
                record: () => {
                  const today = new Date();
                  const year = today.getFullYear();
                  const month = String(today.getMonth() + 1).padStart(2, "0");
                  const day = String(today.getDate()).padStart(2, "0");
                  const formattedDate = `${year}-${month}-${day}`;

                  const newId = Math.random().toString(36).substring(2, 15);
                  return {
                    mediumNo: props.data?.records?.[0]?.mediumNo ?? "",
                    signOpsSysNo: "",
                    signKindCd: "1000",
                    signSubtypeCd: "1000000001",
                    prodtContractNo:
                      props.data?.records?.[0]?.prodtContractNo ?? "",
                    signDesc: "",
                    signAddoffTimes: "0",
                    signContStatus: "0",
                    signContEffDate: formattedDate,
                    signTime: "",
                    signContDate: formattedDate,
                    enrollGloTracNo: newId,
                    enrollSubtxNo: newId,
                    signMobileNo: "",
                    signTellerNo: "",
                    signContInstNo: "",
                    authTellerNo: "",
                    signInvalDt: formattedDate,
                    signInfoFlagCd: "000000",
                    cacContTime: "",
                    cacContDt: formattedDate,
                    cacContGloTracNo: "",
                    cacContSubtxNo: "",
                    cacContInstNo: "",
                    cacContTellerNo: "",
                    cacContAuthTellerNo: "",
                    lastTxDate: formattedDate,
                    recordStaCd: "1",
                    zoneVal: props.data?.records?.[0]?.zoneVal ?? "",
                  } as TbDprgtSignAdd;
                },
              }
        }
        loading={loading}
        toolBarRender={() => [
          <ProFormRadio.Group
            key="render"
            fieldProps={{
              value: position,
              onChange: (e: any) => setPosition(e.target.value),
            }}
            options={[
              {
                label: "添加到顶部",
                value: "top",
              },
              {
                label: "添加到底部",
                value: "bottom",
              },
              {
                label: "隐藏",
                value: "hidden",
              },
            ]}
          />,
        ]}
        columns={columns}
        request={async (params, sorter, filter) => {
          // 如果正在编辑，直接返回当前数据
          if (isEditing) {
            return {
              data: dataSource,
              total: pagination.total,
              success: true,
            };
          }

          setLoading(true);
          try {
            if (
              !props.data ||
              !props.data.records ||
              props.data.records.length === 0
            ) {
              return {
                data: [],
                total: 0,
                success: true,
              };
            }
            const dprgtSignAdd = props.data.records[0];
            if (!dprgtSignAdd?.mediumNo || !dprgtSignAdd?.zoneVal) {
              return {
                data: [],
                total: 0,
                success: true,
              };
            }
            const { current, pageSize } = params;
            const res = await getTbDprgtSignAdd(
              dprgtSignAdd.mediumNo,
              dprgtSignAdd.zoneVal,
              "dprgtSignAddPageInfo",
              {
                current: current || 1,
                pageSize: pageSize || DEFAULT_PAGE_SIZE,
              }
            );
            if (res.code === 200 && res.data) {
              const detailData = JSON.parse(res.data);
              setDataSource(detailData.records || []);
              setPagination({
                current: detailData.current || 1,
                pageSize: detailData.size || DEFAULT_PAGE_SIZE,
                total: detailData.total || 0,
              });
              return {
                data: detailData.records || [],
                total: detailData.total || 0,
                success: true,
              };
            } else {
              message.error(res.msg || "获取签约加办关系登记簿失败");
              return {
                data: [],
                total: 0,
                success: false,
              };
            }
          } catch (error) {
            message.error("查询签约加办关系登记簿失败");
            return {
              data: [],
              total: 0,
              success: false,
            };
          } finally {
            setLoading(false);
          }
        }}
        dataSource={dataSource}
        pagination={{
          ...TABLE_PAGINATION,
          current: pagination.current,
          pageSize: pagination.pageSize,
          total: pagination.total,
          showTotal: (total) => `总共 ${total} 条`,
          onChange: (current, pageSize) => {
            setPagination((prev) => ({ ...prev, current, pageSize }));
          },
        }}
        editable={{
          type: "multiple",
          editableKeys,
          onSave: async (rowKey, data) => {
            console.log("保存数据:", rowKey, data);
            if (data.enrollGloTracNo === "XXXXXXXXXXXXXXXXXXX") {
              // 新增数据
              try {
                const newData = {
                  ...data,
                  enrollGloTracNo: "XXXXXXXXXXXXXXXXXXX", // 生成唯一ID
                };
                console.log("保存新增数据:", newData);
                const res = await insertTbDprgtSignAdd(newData);
                if (res.code === 200) {
                  message.success("新增成功");
                  // 新增成功后刷新数据并跳转到最后一页
                  setEditableRowKeys([]);
                  setIsEditing(false);
                  setEditingRecord(null);

                  // 先获取最新的数据总数
                  const dprgtSignAdd = props.data?.records?.[0];
                  if (dprgtSignAdd?.mediumNo && dprgtSignAdd?.zoneVal) {
                    const res = await getTbDprgtSignAdd(
                      dprgtSignAdd.mediumNo,
                      dprgtSignAdd.zoneVal,
                      "dprgtSignAddPageInfo",
                      {
                        current: 1,
                        pageSize: 1,
                      }
                    );

                    if (res.code === 200 && res.data) {
                      const detailData = JSON.parse(res.data);
                      const total = detailData.total || 0;
                      const lastPage = Math.ceil(total / pagination.pageSize);

                      setPagination((prev) => ({
                        ...prev,
                        current: lastPage,
                        total: total,
                      }));
                    }
                  }

                  await refreshData();
                  return true;
                } else {
                  message.error(res.msg || "新增失败");
                  await refreshData();
                  return false;
                }
              } catch (error) {
                message.error("新增失败");
                return false;
              }
            }

            if (!editingRecord || !rowKey) {
              message.error("编辑记录不存在");
              return false;
            }

            let originalData: TbDprgtSignAdd | null = null;
            try {
              // 只上送可编辑字段
              const updateData = {
                mediumNo: data.mediumNo,
                signOpsSysNo: data.signOpsSysNo,
                signKindCd: data.signKindCd,
                signSubtypeCd: data.signSubtypeCd,
                signDesc: data.signDesc,
                signAddoffTimes: data.signAddoffTimes,
                signContStatus: data.signContStatus,
                signContEffDate: data.signContEffDate,
                signContDate: data.signContDate,
                enrollGloTracNo: data.enrollGloTracNo,
                enrollSubtxNo: data.enrollSubtxNo,
                signMobileNo: data.signMobileNo,
                signTellerNo: data.signTellerNo,
                signContInstNo: data.signContInstNo,
                authTellerNo: data.authTellerNo,
                signInvalDt: data.signInvalDt,
                signInfoFlagCd: data.signInfoFlagCd,
                cacContTime: data.cacContTime,
                cacContDt: data.cacContDt,
                cacContGloTracNo: data.cacContGloTracNo,
                cacContSubtxNo: data.cacContSubtxNo,
                cacContInstNo: data.cacContInstNo,
                cacContTellerNo: data.cacContTellerNo,
                cacContAuthTellerNo: data.cacContAuthTellerNo,
                lastTxDate: data.lastTxDate,
                zoneVal: data.zoneVal,
              };

              // 保存当前数据
              originalData =
                dataSource.find((item) => item.enrollGloTracNo === rowKey) ||
                null;

              const res = await TbDprgtSignAddUpdate(updateData);

              if (res.code === 200) {
                // 成功时更新数据
                setDataSource((prevDataSource) =>
                  prevDataSource.map((item) =>
                    item.enrollGloTracNo === rowKey ? { ...data } : item
                  )
                );
                message.success("保存成功");
              } else {
                // API调用失败，回滚数据
                message.error(res.msg || "保存失败");
                if (originalData) {
                  setDataSource((prevDataSource) => {
                    return prevDataSource.map((item) =>
                      item.enrollGloTracNo === rowKey ? originalData : item
                    ) as TbDprgtSignAdd[];
                  });
                }
              }
            } catch (error) {
              // 发生错误，回滚数据
              message.error("保存失败");
              if (originalData) {
                setDataSource((prevDataSource) => {
                  return prevDataSource.map((item) =>
                    item.enrollGloTracNo === rowKey ? originalData : item
                  ) as TbDprgtSignAdd[];
                });
              }
            } finally {
              // 退出编辑状态
              setEditableRowKeys([]);
              setIsEditing(false);
              setEditingRecord(null);
              if (props.data) {
                try {
                  const dprgtSignAdd =
                    props.data.records && props.data.records[0];
                  if (dprgtSignAdd?.mediumNo && dprgtSignAdd?.zoneVal) {
                    setLoading(true);
                    try {
                      const res = await getTbDprgtSignAdd(
                        dprgtSignAdd.mediumNo,
                        dprgtSignAdd.zoneVal,
                        "dprgtSignAddPageInfo",
                        {
                          current: pagination.current,
                          pageSize: pagination.pageSize,
                        }
                      );
                      if (res.code === 200 && res.data) {
                        const detailData = JSON.parse(res.data);
                        setDataSource(detailData.records || []);
                        setPagination({
                          current:
                            detailData.current || INITIAL_PAGINATION.current,
                          pageSize:
                            detailData.size || INITIAL_PAGINATION.pageSize,
                          total: detailData.total || INITIAL_PAGINATION.total,
                        });
                        setRefresh(refresh + 1);
                      } else {
                        message.error(res.msg || "获取签约加办关系登记簿失败");
                      }
                    } catch (error) {
                      message.error("查询签约加办关系登记簿失败");
                    } finally {
                      setLoading(false);
                    }
                  }
                } catch (error) {
                  message.error("查询签约加办关系登记簿失败");
                }
              }
            }
          },
          onChange: setEditableRowKeys,
          onCancel: async (rowKey) => {
            if (editingRecord) {
              setDataSource((prevDataSource) =>
                prevDataSource.map((item) =>
                  item.enrollGloTracNo === editingRecord.enrollGloTracNo
                    ? { ...editingRecord }
                    : item
                )
              );
            }
            // 退出编辑状态
            setEditableRowKeys([]);
            setIsEditing(false);
            setEditingRecord(null);
            return true;
          },
        }}
      />
    </Modal>
  );
};

export default DprgtSignAddFormEdit;
