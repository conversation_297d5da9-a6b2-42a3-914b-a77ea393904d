import React, { useEffect } from "react";
import moment from "moment";
import {
  ProFormText,
  ProFormSelect,
  ProFormDatePicker,
} from "@ant-design/pro-form";
import { Form, Modal, Row, Col, Spin } from "antd";
import type { TbDpmstFixAcc } from "../data";

/*
 * @Description: 定期账户主档表编辑表单
 * @Author: zhujinwei
 * @Date: 2025-01-17 10:00:00
 */

export type DpmstFixAccFormValueType = Record<string, unknown> &
  Partial<TbDpmstFixAcc>;

export type DpmstFixAccFormProps = {
  onCancel: (flag?: boolean, formVals?: DpmstFixAccFormValueType) => void;
  onSubmit: (values: DpmstFixAccFormValueType) => Promise<void>;
  visible: boolean;
  values: Partial<TbDpmstFixAcc>;
  loading?: boolean; // 添加loading属性
  submitting?: boolean; // 添加提交状态属性
};

const DpmstFixAccForm: React.FC<DpmstFixAccFormProps> = (props) => {
  const [form] = Form.useForm();

  useEffect(() => {
    if (props.visible && props.values) {
      console.log("表单设置值:", props.values);
      form.resetFields();
      form.setFieldsValue({
        persInnerAccno: props.values.persInnerAccno, // 个人内部账号
        persDepAccTpCd: props.values.persDepAccTpCd, // 个人存款账户类型代码
        prodtContractNo: props.values.prodtContractNo, // 产品合约编号
        currCode: props.values.currCode,                // 币种代码
        cashExgVatgCd: props.values.cashExgVatgCd,  // 钞汇类别代码
        termUnitCode: props.values.termUnitCode, // 期限单位代码
        openaccDate: props.values.openaccDate, // 开户日期
        openAccInstNo: props.values.openAccInstNo, // 合约签约日期
        clsaccDate: props.values.clsaccDate, // 销户日期
        clsAccInstNo: props.values.clsAccInstNo, // 销户机构号
        openaccAmt: props.values.openaccAmt, // 开户金额
        accBal: props.values.accBal, // 账户余额
        accAvalBal: props.values.accAvalBal, // 账户可用余额
        calIntAccu: props.values.calIntAccu, // 计息积数
        intRate: props.values.intRate, // 利率
        intRateNo: props.values.intRateNo, // 利率号
        bgnIntDate: props.values.bgnIntDate, // 起息日期
        dueDate: props.values.dueDate,  // 到期日期
        lastTmTxDt: props.values.lastTmTxDt, // 上次交易日期
        lastdayBal: props.values.lastdayBal, // 上日余额
        curDtlNo: props.values.curDtlNo, // 当前明细序号
        campTellerNo: props.values.campTellerNo, // 营销柜员号
        lastTxDate: props.values.lastTxDate, // 最后交易日期
        lastModStamp: props.values.lastModStamp, // 最后修改时间戳
        zoneVal: props.values.zoneVal,          // 客户编号
      });
    }
  }, [form, props.visible, props.values]);

  const handleOk = () => {
    form.submit();
  };

  const handleCancel = () => {
    props.onCancel();
    form.resetFields();
  };

  const handleFinish = async (values: Record<string, any>) => {
    // 转换所有日期字段为yyyyMMdd格式
    const dateFields = [
      "openaccDate",
      "clsaccDate",
      "bgnIntDate",
      "dueDate",
      "lastTmTxDt",
      "lastTxDate",
    ];
    dateFields.forEach((field) => {
      if (values[field]) {
        values[field] = moment(values[field]).format("YYYYMMDD");
      }
    });
    props.onSubmit(values as DpmstFixAccFormValueType);
    return true;
  };

  return (
    <Modal
      width={800}
      title="编辑定期账户信息"
      visible={props.visible}
      destroyOnClose
      onOk={handleOk}
      onCancel={handleCancel}
      bodyStyle={{ 
        maxHeight: '70vh', 
        overflow: 'auto', 
        padding: '24px',
        borderRadius: '10px',  // 添加圆角
      }}
      style={{
        borderRadius: '10px',  // Modal整体圆角
        overflow: 'hidden'    // 确保内容不超出圆角范围
      }}
    >
      <Spin 
        spinning={(props.loading || props.submitting) || false} 
        tip={props.submitting ? "正在提交数据，请稍后..." : "一大波数据正在路上..."}
      >
      <Form
        form={form}
        onFinish={handleFinish}
        initialValues={props.values}
        layout="vertical"
        className="custom-form" // 添加自定义类名
      >
        <style>
        {`
          .custom-form .ant-form-item-label > label {
            font-weight: 600;
          }
        `}
        </style>
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <ProFormText
              name="persInnerAccno"
              label="个人内部账号"
              width="xl"
              placeholder="请输入个人内部账号"
              disabled
              rules={[
                {
                  required: true,
                  message: "请输入个人内部账号！",
                },
              ]}
            />
          </Col>
          <Col span={12}>
            <ProFormText
              name="zoneVal"
              label="分片值"
              width="xl"
              placeholder="请输入分片值"
              disabled
              rules={[
                {
                  required: true,
                  message: "请输入分片值！",
                },
              ]}
            />
          </Col>
        </Row>
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <ProFormSelect
              name="persDepAccTpCd"
              label="个人存款账户类型代码"
              width="xl"
              placeholder="请输入个人存款账户类型代码"
              options={
                [
                  {value: '1001', label: '1001-人民币活期储蓄合约'},
                  {value: '1002', label: '1002-人民币活期结算合约'},
                  {value: '1003', label: '1003-外币活期储蓄合约'},
                  {value: '1004', label: '1004-外币活期结算合约'},
                  {value: '1005', label: '1005-本外币合一结算合约'},
                  {value: '2001', label: '2001-整存整取储蓄存款合约'},
                  {value: '2002', label: '2002-整存整取协议存款合约'},
                  {value: '2003', label: '2003-提前付息定期存款合约'},
                  {value: '2004', label: '2004-定活两便储蓄存款合约'},
                  {value: '2005', label: '2005-整存零取储蓄存款合约'},
                  {value: '2006', label: '2006-存本取息储蓄存款合约'},
                  {value: '2007', label: '2007-零存整取储蓄存款合约'},
                  {value: '2008', label: '2008-通知存款合约'},
                  {value: '2009', label: '2009-结构性存款合约'},
                  {value: '2010', label: '2010-递增计息合约'},
                  {value: '2011', label: '2011-梦想加邮站合约'},
                  {value: '2012', label: '2012-大额存单合约'},
                  {value: '2013', label: '2013-礼仪存单合约'},
                  {value: '2014', label: '2014-邮智存合约'},
                  {value: '3001', label: '3001-行业应用子账户合约'},
                  {value: '3002', label: '3002-电子现金账户合约'},
                  {value: '4001', label: '4001-副卡合约'},
                  {value: '4002', label: '4002-映射卡合约'},
                  {value: '4003', label: '4003-本外币定期一本通合约'},
                ]
              }
              fieldProps={{
                optionItemRender: (item: { value: string; label: string }) => {
                  const mediumTypeMap: { [key: string]: string } = {
                    '1001': '人民币活期储蓄合约',
                    '1002': '人民币活期结算合约',
                    '1003': '外币活期储蓄合约',
                    '1004': '外币活期结算合约',
                    '1005': '本外币合一结算合约',
                    '2001': '整存整取储蓄存款合约',
                    '2002': '整存整取协议存款合约',
                    '2003': '提前付息定期存款合约',
                    '2004': '定活两便储蓄存款合约',
                    '2005': '整存零取储蓄存款合约',
                    '2006': '存本取息储蓄存款合约',
                    '2007': '零存整取储蓄存款合约',
                    '2008': '通知存款合约',
                    '2009': '结构性存款合约',
                    '2010': '递增计息合约',
                    '2011': '梦想加邮站合约',
                    '2012': '大额存单合约',
                    '2013': '礼仪存单合约',
                    '2014': '邮智存合约',
                    '3001': '行业应用子账户合约',
                    '3002': '电子现金账户合约',
                    '4001': '副卡合约',
                    '4002': '映射卡合约',
                    '4003': '本外币定期一本通合约',
                  };
                  return `${item.value}-${mediumTypeMap[item.value] || item.label}`;
                },
              }}
            />
          </Col>
          <Col span={12}>
            <ProFormText
              name="prodtContractNo"
              label="产品合约编号"
              width="xl"
              placeholder="请输入产品合约编号"
            />
          </Col>
        </Row>
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <ProFormSelect
              name="currCode"
              label="币种代码"
              width="xl"
              placeholder="请输入币种代码"
              options={
                [
                  {value: '036', label: '036-澳大利亚元'},
                  {value: '124', label: '124-加元'},
                  {value: '344', label: '344-香港元'},
                  {value: '392', label: '392-日元'},
                  {value: '826', label: '826-英镑'},
                  {value: '840', label: '840-美元'},
                  {value: '978', label: '978-欧元（EUR）'},
                  {value: '156', label: '156-人民币元'},
                ]
              }
              fieldProps={{
                optionItemRender: (item: { value: string; label: string }) => {
                  const mediumTypeMap: { [key: string]: string } = {
                    '036': '澳大利亚元',
                    '124': '加元',
                    '344': '香港元',
                    '392': '日元',
                    '826': '英镑',
                    '840': '美元',
                    '978': '欧元（EUR）',
                    '156': '人民币元',
                  };
                  return `${item.value}-${mediumTypeMap[item.value] || item.label}`;
                },
              }}
            />
          </Col>
          <Col span={12}>
            <ProFormSelect
              name="cashExgVatgCd"
              label="钞汇类别代码"
              width="xl"
              placeholder="请输入钞汇类别代码"
              options={
                [
                  {value: '2', label: '2-钞'},
                  {value: '3', label: '3-汇'},
                ]
              }
              fieldProps={{
                optionItemRender: (item: { value: string; label: string }) => {
                  const mediumTypeMap: { [key: string]: string } = {
                    '2': '钞',
                    '3': '汇',
                  };
                  return `${item.value}-${mediumTypeMap[item.value] || item.label}`;
                },
              }}
            />
          </Col>
        </Row>
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <ProFormSelect
              name="termUnitCode"
              label="期限单位代码"
              width="xl"
              placeholder="请输入期限单位代码"
              options={
                [
                  {value: '1', label: '1-日'},
                  {value: '2', label: '2-周'},
                  {value: '3', label: '3-月'},
                  {value: '4', label: '4-季'},
                  {value: '5', label: '5-年'},
                ]
              }
              fieldProps={{
                optionItemRender: (item: { value: string; label: string }) => {
                  const mediumTypeMap: { [key: string]: string } = {
                    '1': '日',
                    '2': '周',
                    '3': '月',
                    '4': '季',
                    '5': '年',
                  };
                  return `${item.value}-${mediumTypeMap[item.value] || item.label}`;
                },
              }}
            />
          </Col>
          <Col span={12}>
            <ProFormText
              name="deptTerm"
              label="存款期限"
              width="xl"
              placeholder="请输入存款期限"
            />
          </Col>
        </Row>
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <ProFormDatePicker
              name="openaccDate"
              label="开户日期"
              width="xl"
              placeholder="请输入开户日期"
            />
          </Col>
          <Col span={12}>
            <ProFormText
              name="openAccInstNo"
              label="开户机构号"
              width="xl"
              placeholder="请输入开户机构号"
            />
          </Col>
        </Row>
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <ProFormDatePicker
              name="clsaccDate"
              label="销户日期"
              width="xl"
              placeholder="请输入销户日期"
            />
          </Col>
          <Col span={12}>
            <ProFormText
              name="clsAccInstNo"
              label="销户机构号"
              width="xl"
              placeholder="请输入销户机构号"
            />
          </Col>
        </Row>
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <ProFormText
              name="openaccAmt"
              label="开户金额"
              width="xl"
              placeholder="请输入开户金额"
            />
          </Col>
          <Col span={12}>
            <ProFormText
              name="accBal"
              label="账户余额"
              width="xl"
              placeholder="请输入账户余额"
            />
          </Col>
        </Row>
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <ProFormText
              name="accAvalBal"
              label="账户可用余额"
              width="xl"
              placeholder="请输入账户可用余额"
            />
          </Col>
          <Col span={12}>
            <ProFormText
              name="calIntAccu"
              label="计息积数"
              width="xl"
              placeholder="请输入计息积数"
            />
          </Col>
        </Row>
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <ProFormText
              name="intRate"
              label="利率"
              width="xl"
              placeholder="请输入利率"
            />
          </Col>
          <Col span={12}>
            <ProFormText
              name="intRateNo"
              label="利率号"
              width="xl"
              placeholder="请输入利率号"
            />
          </Col>
        </Row>
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <ProFormText
              name="fixDeptOvdueIntRateNo"
              label="定期存款逾期利率号"
              width="xl"
              placeholder="请输入定期存款逾期利率号"
            />
          </Col>
          <Col span={12}>
            <ProFormText
              name="prodTpNo"
              label="产品类型编码"
              width="xl"
              placeholder="请输入产品类型编码"
            />
          </Col>
        </Row>
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <ProFormDatePicker
              name="bgnIntDate"
              label="起息日期"
              width="xl"
              placeholder="请输入起息日期"
            />
          </Col>
          <Col span={12}>
            <ProFormDatePicker
              name="dueDate"
              label="到期日期"
              width="xl"
              placeholder="请输入到期日期"
            />
          </Col>
        </Row>
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <ProFormText
              name="prtExtdTimes"
              label="已部提次数"
              width="xl"
              placeholder="请输入已部提次数"
            />
          </Col>
          <Col span={12}>
            <ProFormText
              name="lastdayBal"
              label="上日余额"
              width="xl"
              placeholder="请输入上日余额"
            />
          </Col>
        </Row>
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <ProFormDatePicker
              name="lastTmTxDt"
              label="上次交易日期"
              width="xl"
              placeholder="请输入上次交易日期"
            />
          </Col>
          <Col span={12}>
            <ProFormText
              name="curDtlNo"
              label="当前明细序号"
              width="xl"
              placeholder="请输入当前明细序号"
            />
          </Col>
        </Row>
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <ProFormSelect
              name="execCycleCd"
              label="执行周期代码"
              width="xl"
              placeholder="请输入执行周期代码"
              options={
                [
                  {value: '1', label: '1-日'},
                  {value: '2', label: '2-周'},
                  {value: '3', label: '3-月'},
                  {value: '4', label: '4-季'},
                  {value: '5', label: '5-年'},
                ]
              }
              fieldProps={{
                optionItemRender: (item: { value: string; label: string }) => {
                  const mediumTypeMap: { [key: string]: string } = {
                    '1': '日',
                    '2': '周',
                    '3': '月',
                    '4': '季',
                    '5': '年',
                  };
                  return `${item.value}-${mediumTypeMap[item.value] || item.label}`;
                },
              }}
            />
          </Col>
          <Col span={12}>
            <ProFormText
              name="execCycVal"
              label="执行周期值"
              width="xl"
              placeholder="请输入执行周期值"
            />
          </Col>
        </Row>
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <ProFormDatePicker
              name="lastTxDate"
              label="最后交易日期"
              width="xl"
              placeholder="请输入最后交易日期"
            />
          </Col>
        </Row>
      </Form>
      </Spin>
    </Modal>
  );
};

export default DpmstFixAccForm;
