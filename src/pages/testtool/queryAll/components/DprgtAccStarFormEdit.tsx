import React, { useEffect, useState, useCallback } from "react";
import { Mo<PERSON>, message, <PERSON><PERSON>, Spin } from "antd";
import { 
  ProFormRadio, 
  EditableProTable 
} from "@ant-design/pro-components";
import type { ProColumns } from "@ant-design/pro-components";
import {
  getDprgtAccStar,
  insertDprgtAccStar,
  updateDprgtAccStar,
  deleteDprgtAccStar,
} from "../service";
import type { DprgtAccStar, PageResponse } from "../data";

/*
 * @Description: 星级登记簿编辑表单
 * @Author: taylor zhu
 * @Date: 2024-08-03 10:00:00
 */

interface PaginationType {
  current: number;
  pageSize: number;
  total: number;
}

export type DprgtAccStarFormProps = {
  onCancel: () => void;
  open: boolean;
  data: PageResponse<DprgtAccStar> | null;
};

const DEFAULT_PAGE_SIZE = 10;
const TABLE_SCROLL = { x: 2000 } as const;
const MODAL_BODY_STYLE = {
  maxHeight: "70vh",
  overflow: "auto",
  padding: "24px",
  borderRadius: "10px",
} as const;

const MODAL_STYLE = {
  borderRadius: "10px",
  overflow: "hidden",
} as const;

const INITIAL_PAGINATION: PaginationType = {
  current: 1,
  pageSize: DEFAULT_PAGE_SIZE,
  total: 0,
};

const DprgtAccStarFormEdit: React.FC<DprgtAccStarFormProps> = (props) => {
  const [dataSource, setDataSource] = useState<readonly DprgtAccStar[]>([]);
  const [editableKeys, setEditableKeys] = useState<React.Key[]>([]);
  const [isEditing, setIsEditing] = useState(false);
  const [editingRecord, setEditingRecord] = useState<DprgtAccStar | null>(null);
  const [pagination, setPagination] = useState<PaginationType>(INITIAL_PAGINATION);
  const [refresh, setRefresh] = useState(0);
  const [loading, setLoading] = useState(false);
  const [accountInfo, setAccountInfo] = useState<{persInnerAccno?: string, zoneVal?: string} | null>(null);
  const [persInnerAccno, setPersInnerAccno] = useState<string | undefined>(undefined);
  const [zoneVal, setZoneVal] = useState<string | undefined>(undefined);

  useEffect(() => {
    if (props.open) {
      if (props.data?.loading !== undefined) {
        setLoading(props.data.loading);
      } else if (props.data) {
        // If we have data but no loading property, ensure loading is turned off
        setLoading(false);
      }
      
      // 当有data数据时，清空分页并设置数据
      if (props.data?.records) {
        setPagination({
          current: 1,
          pageSize: 10,
          total: props.data.total || props.data.records.length,
        });
        setDataSource(props.data.records);
        
        // 重要：设置账号信息，确保分页功能正常工作
        if (props.data.records.length > 0) {
          const firstRecord = props.data.records[0];
          
          // 从记录中提取账号信息
          setPersInnerAccno(firstRecord.persInnerAccno);
          setZoneVal(firstRecord.zoneVal);
          
          // 同时更新accountInfo，作为备份
          setAccountInfo({
            persInnerAccno: firstRecord.persInnerAccno,
            zoneVal: firstRecord.zoneVal
          });
        }
      }
    } else {
      // 当弹窗关闭时，清空数据和分页
      setDataSource([]);
      setPagination({
        current: 1,
        pageSize: 10,
        total: 0,
      });
      // Also ensure loading is reset when modal closes
      setLoading(false);
    }
  }, [props.open, props.data]);

  const refreshData = useCallback(
    async (current: number = pagination.current, pageSize: number = pagination.pageSize) => {
      // 使用当前状态或accountInfo作为后备
      const accountNo = persInnerAccno || accountInfo?.persInnerAccno;
      const zoneValue = zoneVal || accountInfo?.zoneVal;
      
      if (!accountNo || !zoneValue) {
        // 向用户显示错误消息，便于调试
        console.error('查询参数不完整:', { accountNo, zoneValue });
        message.error('分页查询失败：账号信息不完整，请重新打开弹窗');
        return;
      }
      
      setLoading(true);
      console.log('正在查询数据:', { accountNo, zoneValue, current, pageSize });
      
      try {
        const result = await getDprgtAccStar(
          accountNo,
          zoneValue,
          'DP_RGT_ACC_STAR_ALL_QUERYPAGES',
          {
            current,
            pageSize,
          },
        );
        
        if (result && result.data) {
          const detailData = JSON.parse(result.data);
          const newPagination = {
            current: current,
            pageSize: pageSize,
            total: detailData.total || detailData.records.length,
          };
          
          setPagination(newPagination);
          setDataSource(detailData.records || []);
          console.log('查询成功，数据条数:', detailData.records?.length);
        }
      } catch (error) {
        console.error('获取数据失败:', error);
        message.error('获取账户星级登记簿数据失败');
      } finally {
        // Ensure loading state is always cleared
        setLoading(false);
      }
    },
    [persInnerAccno, zoneVal, accountInfo, pagination.current, pagination.pageSize],
  );

  // 定义表格列
  const [position, setPosition] = useState<"top" | "bottom" | "hidden">("bottom");

  const columns: ProColumns<DprgtAccStar>[] = [
    {
      title: '个人内部账号',
      dataIndex: 'persInnerAccno',
      width: 260,
	    ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: '服务体系类型代码',
      dataIndex: 'servSysTpCd',
      width: 220,
      ellipsis: true,
      align: "center",
      editable: () => false,
		  valueType: "select",
      valueEnum: {
        "01": { text: "01-星级服务体系" },
      },
    },
    {
      title: '账户星级代码',
      dataIndex: 'accStarCd',
      width: 120,
      ellipsis: true,
      align: "center",
      editable: () => false,
	    valueType: "select",
      valueEnum: {
        "01": { text: "01-准一星" },
        "02": { text: "02-一星" },
        "03": { text: "03-二星" },
        "04": { text: "04-三星" },
      },
    },
    {
      title: '定价协议编号',
      dataIndex: 'pricingAgrNo',
      width: 240,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: '加办类型代码',
      dataIndex: 'addoffTpCd',
      width: 180,
      ellipsis: true,
      align: "center",
      editable: () => false,
	    valueType: "select",
      valueEnum: {
        "00": { text: "00-批量开户默认加办" },
        "01": { text: "01-单笔人工加办" },
        "02": { text: "02-批量人工加办" },
        "03": { text: "03-系统加办" },
        "04": { text: "04-换卡加办" },
      },
    },
    {
      title: '优惠到期日期',
      dataIndex: 'disDueDt',
      width: 120,
      ellipsis: true,
      align: "center",
      editable: () => false,
      valueType: "date",
    },
    {
      title: '单笔加办次数',
      dataIndex: 'perAddoffTimes',
      width: 120,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: '批量加办次数',
      dataIndex: 'batchAddoffTimes',
      width: 120,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: '变更日期',
      dataIndex: 'chgDate',
      width: 120,
      ellipsis: true,
      align: "center",
      editable: () => false,
      valueType: "date",
    },
    {
      title: '交易机构号',
      dataIndex: 'txInstNo',
      width: 120,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: '交易柜员号',
      dataIndex: 'txTellerNo',
      width: 120,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: '授权柜员号',
      dataIndex: 'authTellerNo',
      width: 120,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: '交易时间',
      dataIndex: 'txTime',
      width: 160,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: '生效状态代码',
      dataIndex: 'effStaCd',
      width: 120,
      ellipsis: true,
      align: "center",
      editable: () => false,
	valueType: "select",
      valueEnum: {
        "1": { text: "1-已生效" },
        "2": { text: "2-已失效" },
      },
    },
    {
      title: '最后交易日期',
      dataIndex: 'lastTxDate',
      width: 120,
      ellipsis: true,
      align: "center",
      editable: () => false,
		  valueType: "date",
    },
    {
      title: '记录状态代码',
      dataIndex: 'recordStaCd',
      width: 120,
      ellipsis: true,
      align: "center",
      editable: () => false,
	    valueType: "select",
      valueEnum: {
        "0": { text: "0-无效" },
        "1": { text: "1-有效" },
      },
    },
    {
      title: "操作",
      valueType: "option",
      width: 150,
      fixed: "right",
      align: "center",
      render: (text, record: DprgtAccStar, _, action) => {
        const isRowEditing = record.persInnerAccno
          ? editableKeys.includes(record.persInnerAccno)
          : false;
        return [
          isRowEditing ? (
            <>
              <Button
                key="confirm"
                type="link"
                onClick={async () => {
                  try {
                    if (record.persInnerAccno) {
                      await action?.saveEditable?.(record.persInnerAccno);
                      setIsEditing(false);
                    }
                  } catch (err) {
                    console.error("保存失败:", err);
                  }
                }}
              >
                确定
              </Button>
              <Button
                key="cancel"
                type="link"
                onClick={() => {
                  if (record.persInnerAccno) {
                    action?.cancelEditable?.(record.persInnerAccno);
                    setEditableKeys([]);
                    setIsEditing(false);
                  }
                }}
                style={{ marginLeft: 8 }}
              >
                取消
              </Button>
            </>
          ) : (
            <Button
              key="editable"
              type="link"
            //   disabled={isEditing}
              disabled={true}
              onClick={async () => {
                if (!record.persInnerAccno) return;
                const originalRecord = { ...record };
                setEditingRecord(originalRecord);
                setIsEditing(true);
                await action?.startEditable?.(record.persInnerAccno);
              }}
              style={{ padding: 0 }}
            >
              编辑
            </Button>
          ),
          <Button
            key="delete"
            type="link"
            // disabled={isEditing}
            disabled={true}
            onClick={async () => {
              if (!record.persInnerAccno) return;
              Modal.confirm({
                title: "确认删除",
                content: "确定要删除这条记录吗？",
                onOk: async () => {
                  try {
                    const res = await deleteDprgtAccStar(record);
                    if (res.code === 200) {
                      message.success("删除成功");
                    } else {
                      message.error(res.msg || "删除失败");
                    }
                  } catch (error) {
                    message.error("删除失败");
                  } finally {
                    await refreshData();
                  }
                },
                okText: "确认",
                cancelText: "取消",
              });
            }}
            style={{ marginLeft: 8 }}
          >
            删除
          </Button>,
        ];
      },
    },
  ];

  const handleAdd = async (record: DprgtAccStar) => {
    try {
      // 设置必要的默认字段
      const newRecord = {
        ...record,
        persInnerAccno: persInnerAccno || accountInfo?.persInnerAccno,
        zoneVal: zoneVal || accountInfo?.zoneVal,
        recordStaCd: "1", // 默认有效
      };
      
      setLoading(true);
      const res = await insertDprgtAccStar(newRecord);
      if (res.code === 200) {
        message.success('添加成功');
        refreshData();
      } else {
        message.error(res.msg || '添加失败');
      }
    } catch (error) {
      console.error('添加失败:', error);
      message.error('添加失败');
    } finally {
      // 添加完成后重置状态
      setLoading(false);
      setRefresh(prev => prev + 1);
      setEditableKeys([]);
      setIsEditing(false);
    }
  };

  return (
    <Modal
      width={1200}
      title="查询账户星级登记簿信息"
      open={props.open}
      destroyOnClose
      onCancel={props.onCancel}
      footer={null}
      bodyStyle={MODAL_BODY_STYLE}
      style={MODAL_STYLE}
    >
      <Spin spinning={loading} tip="加载数据中..." size="large">
        <div style={{ marginBottom: 16 }}>
          <p>
            数据状态: {loading ? (
              <span>
                <Spin size="small" style={{ marginRight: 8 }} />
                加载中...
              </span>
            ) : (
              `已加载 ${dataSource.length} 条记录, 总共 ${pagination.total} 条`
            )}
            {accountInfo && (
              <span style={{ marginLeft: 8 }}>
                | 个人内部账号: {accountInfo.persInnerAccno}
              </span>
            )}
          </p>
        </div>
        
        <EditableProTable<DprgtAccStar>
          key={refresh}
          rowKey={(record) => record.persInnerAccno || ""}
          headerTitle="账户星级登记簿信息"
          scroll={TABLE_SCROLL}
          loading={loading}
          toolBarRender={() => [
            <ProFormRadio.Group
              key="positionRender"
              fieldProps={{
                value: position,
                onChange: (e: any) => setPosition(e.target.value),
              }}
              options={[
                { label: "添加到顶部", value: "top" },
                { label: "添加到底部", value: "bottom" },
                { label: "隐藏", value: "hidden" },
              ]}
            />,
          ]}
          recordCreatorProps={{
            position: position === 'hidden' ? 'bottom' : position,
            record: (index, dataSource) => {
              // 直接创建新记录，不检查是否有正在编辑的行
              return {
                persInnerAccno: persInnerAccno || accountInfo?.persInnerAccno,
                servSysTpCd: "01", // 默认为星级服务体系
                addoffTpCd: "01", // 默认为单笔人工加办
                zoneVal: zoneVal || accountInfo?.zoneVal,
                effStaCd: "1", // 默认有效
                recordStaCd: "1", // 默认有效
                lastTxDate: new Date().toISOString().split('T')[0], // 最后交易日期
              } as DprgtAccStar;
            },
            creatorButtonText: '添加一行数据',
            newRecordType: 'dataSource',
          }}
          columns={columns}
          request={async (params, sorter, filter) => {
            // 直接返回当前数据源，不进行额外的API调用
            return {
              data: dataSource as DprgtAccStar[],
              total: pagination.total,
              success: true,
            };
          }}
          value={dataSource}
          onChange={setDataSource}
          editable={{
            type: 'multiple', // 支持同时编辑多行
            editableKeys,
            onSave: async (rowKey, data, row) => {
              try {
                // 如果是新添加的记录则调用新增接口
                if (String(rowKey).startsWith('NEW_')) {
                  await handleAdd(data);
                } else {
                  // 否则调用更新接口
                  setLoading(true);
                  const res = await updateDprgtAccStar(data);
                  if (res.code === 200) {
                    message.success('更新成功');
                    refreshData();
                  } else {
                    message.error(res.msg || '更新失败');
                  }
                }
              } catch (error) {
                console.error('保存失败:', error);
                message.error('保存失败');
              } finally {
                // 保存后从编辑状态中移除当前行
                setEditableKeys(keys => keys.filter(key => key !== rowKey));
                if (editableKeys.length <= 1) {
                  setIsEditing(false);
                }
                setLoading(false);
              }
            },
            onChange: setEditableKeys,
            onCancel: async (key) => {
              // 取消编辑时只清除当前行的编辑状态
              setEditableKeys(keys => keys.filter(k => k !== key));
              if (editableKeys.length <= 1) {
                setIsEditing(false);
              }
              return true;
            },
            actionRender: (row, config, dom) => [dom.save, dom.cancel],
          }}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条`,
            onChange: async (page, pageSize) => {
              console.log('分页改变:', { page, pageSize, currentPage: pagination.current, isEditing });
              
              if (isEditing) {
                message.warn('请先完成编辑操作再切换页面');
                return;
              }
              
              // 仅当页码或页大小变化时才刷新数据
              if (page !== pagination.current || pageSize !== pagination.pageSize) {
                // 更新分页状态
                setPagination({
                  ...pagination,
                  current: page,
                  pageSize: pageSize,
                });
                
                // 直接调用刷新数据，不做额外判断
                await refreshData(page, pageSize);
              }
            },
          }}
        />
      </Spin>
    </Modal>
  );
};

export default DprgtAccStarFormEdit;
