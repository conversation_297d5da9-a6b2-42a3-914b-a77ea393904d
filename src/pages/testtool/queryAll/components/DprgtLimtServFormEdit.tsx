import React, { useEffect, useState } from "react";
import { Modal, message, Button } from "antd";
import {
  updateLimtServ,
  getLimtServ,
  insertLimtServ,
  deleteLimtServ,
} from "../service";
import type { TbDprgtLimtServ, PageResponse } from "../data";
import type { ProColumns } from "@ant-design/pro-components";
import { EditableProTable, ProFormRadio } from "@ant-design/pro-components";

interface PaginationType {
  current: number;
  pageSize: number;
  total: number;
}

export type DprgtLimtServFormProps = {
  onCancel: () => void;
  open: boolean;
  data: PageResponse<TbDprgtLimtServ> | null;
};

const DEFAULT_PAGE_SIZE = 10;
const TABLE_SCROLL = { x: 3000 } as const;
const TABLE_PAGINATION = {
  showSizeChanger: true,
  showQuickJumper: true,
} as const;
const MODAL_BODY_STYLE = {
  maxHeight: "70vh",
  overflow: "auto",
  padding: "24px",
  borderRadius: "10px",
} as const;
const MODAL_STYLE = {
  borderRadius: "10px",
  overflow: "hidden",
} as const;
const INITIAL_PAGINATION: PaginationType = {
  current: 1,
  pageSize: DEFAULT_PAGE_SIZE,
  total: 0,
};

const DprgtLimtServFormEdit: React.FC<DprgtLimtServFormProps> = (props) => {
  const [dataSource, setDataSource] = useState<readonly TbDprgtLimtServ[]>([]);
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);
  const [isEditing, setIsEditing] = useState(false);
  const [editingRecord, setEditingRecord] = useState<TbDprgtLimtServ | null>(null);
  const [pagination, setPagination] = useState<PaginationType>(INITIAL_PAGINATION);
  const [refresh, setRefresh] = useState(0);
  const [loading, setLoading] = useState(false);
  const [position, setPosition] = useState<"top" | "bottom" | "hidden">("bottom");

  // 刷新数据方法
  const refreshData = async () => {
    if (!props.data?.records?.[0]) return;
    const record = props.data.records[0];
    if (!record?.prodtContractNo || !record?.zoneVal) return;

    setLoading(true);
    try {
      const res = await getLimtServ(
        record.prodtContractNo,
        record.zoneVal,
        "limtServPageInfo",
        {
          current: pagination.current,
          pageSize: pagination.pageSize,
        }
      );
      if (res.code === 200 && res.data) {
        const detailData = JSON.parse(res.data);
        setDataSource(detailData.records || []);
        setPagination({
          current: detailData.current || INITIAL_PAGINATION.current,
          pageSize: detailData.size || INITIAL_PAGINATION.pageSize,
          total: detailData.total || INITIAL_PAGINATION.total,
        });
        setRefresh(refresh + 1);
      } else {
        message.error(res.msg || "获取明细失败");
      }
    } catch (error) {
      message.error("查询明细失败");
    } finally {
      setLoading(false);
    }
  };

  // 初始化数据
  useEffect(() => {
    let isMounted = true;
    const initData = async () => {
      if (props.open && props.data) {
        const { records, current, size, total } = props.data;
        if (isMounted) {
          setDataSource(records || []);
          setPagination({
            current: current || INITIAL_PAGINATION.current,
            pageSize: size || INITIAL_PAGINATION.pageSize,
            total: total || INITIAL_PAGINATION.total,
          });
        }
      } else if (isMounted) {
        setDataSource([]);
        setPagination(INITIAL_PAGINATION);
      }
    };
    initData();
    return () => {
      isMounted = false;
    };
  }, [props.open, props.data]);

  // 定义表格列
  const columns: ProColumns<TbDprgtLimtServ>[] = [
    {
      title: "登记日期",
      dataIndex: "enrollDate",
      width: 120,
      editable: () => true,
      valueType: "date",
      render: (_, entity) => entity.enrollDate || "-",
      align: "center",
    },
    {
      title: "全局业务跟踪号",
      dataIndex: "globalBusiTrackNo",
      width: 280,
      ellipsis: true,
      editable: () => true,
      align: "center",
    },
    {
      title: "子交易序号",
      dataIndex: "subtxNo",
      width: 280,
      ellipsis: true,
      editable: () => true,
      align: "center",
    },
    {
      title: "产品合约编号",
      dataIndex: "prodtContractNo",
      width: 220,
      ellipsis: true,
      editable: () => true,
      align: "center",
    },
    {
      title: "登记时间",
      dataIndex: "enrollTime",
      width: 160,
      ellipsis: true,
      editable: () => true,
      render: (_, entity) => entity.enrollTime || "-",
      align: "center",
    },
    {
      title: "限制类型代码",
      dataIndex: "limtTpCd",
      width: 220,
      ellipsis: true,
      editable: () => true,
      valueType: "select",
      valueEnum: {
        "10": { text: "10-停用" },
        "20": { text: "20-中止" },
        "30": { text: "30-暂停非柜面" },
        "40": { text: "40-长期不动户" },
        "50": { text: "50-非柜面渠道专项管控" },
      },
      align: "center",
    },
    {
      title: "介质编号",
      dataIndex: "mediumNo",
      width: 280,
      ellipsis: true,
      editable: () => false,
      align: "center",
    },
    {
      title: "类别标识代码",
      dataIndex: "categFlagCd",
      width: 100,
      ellipsis: true,
      editable: () => true,
      valueType: "select",
      valueEnum: {
        "01": { text: "01-活期" },
        "02": { text: "02-定期" },
      },
      align: "center",
    },
    {
      title: "客户编号",
      dataIndex: "custNo",
      width: 140,
      ellipsis: true,
      editable: () => true,
      align: "center",
    },
    {
      title: "客户名称",
      dataIndex: "custNm",
      width: 180,
      ellipsis: true,
      editable: () => true,
      align: "center",
    },
    {
      title: "个人证件类型代码",
      dataIndex: "perCertTpCd",
      width: 180,
      ellipsis: true,
      editable: () => true,
      valueType: "select",
      valueEnum: {
        "1010": { text: "1010-居民身份证" },
        "1011": { text: "1011-临时居民身份证" },
        "1020": { text: "1020-军人身份证件" },
        "1021": { text: "1021-士兵证" },
        "1022": { text: "1022-军官证" },
        "1023": { text: "1023-文职干部证" },
        "1024": { text: "1024-军官退休证" },
        "1025": { text: "1025-文职干部退休证" },
        "1030": { text: "1030-武警身份证件" },
        "1031": { text: "1031-武警士兵证" },
        "1032": { text: "1032-警官证" },
        "1033": { text: "1033-武警文职干部证" },
        "1034": { text: "1034-武警军官退休证" },
        "1035": { text: "1035-武警文职干部退休证" },
        "1040": { text: "1040-户口簿" },
        "1050": { text: "1050-中国护照" },
        "1051": { text: "1051-外国护照" },
        "1060": { text: "1060-学生证" },
        "1070": { text: "1070-港澳居民来往内地通行证" },
        "1071": { text: "1071-往来港澳通行证" },
        "1080": { text: "1080-台湾居民来往大陆通行证" },
        "1090": { text: "1090-执行公务证" },
        "1100": { text: "1100-机动车驾驶证" },
        "1110": { text: "1110-社会保障卡" },
        "1120": { text: "1120-外国人居留证" },
        "1121": { text: "1121-外国人永久居留证" },
        "1130": { text: "1130-旅行证件" },
        "1140": { text: "1140-香港居民身份证" },
        "1150": { text: "1150-澳门居民身份证" },
        "1160": { text: "1160-台湾居民身份证" },
        "1170": { text: "1170-边民证" },
        "1180": { text: "1180-港澳台居民居住证" },
        "1181": { text: "1181-港澳居民居住证" },
        "1182": { text: "1182-台湾居民居住证" },
        "1190": { text: "1190-外国身份证" },
        "1998": { text: "1998-其他（原98类）" },
        "1999": { text: "1999-其他证件（个人）" },
      },
      align: "center",
    },
    {
      title: "个人证件号码",
      dataIndex: "personalCertNo",
      width: 180,
      ellipsis: true,
      editable: () => true,
      align: "center",
    },
    {
      title: "开户日期",
      dataIndex: "openAccDate",
      width: 120,
      ellipsis: true,
      editable: () => true,
      valueType: "date",
      render: (_, entity) => entity.openAccDate || "-",
      align: "center",
    },
    {
      title: "账户余额",
      dataIndex: "accBal",
      width: 120,
      ellipsis: true,
      editable: () => true,
      align: "center",
    },
    {
      title: "最后动户交易日",
      dataIndex: "lastMobilityTxDate",
      width: 140,
      ellipsis: true,
      editable: () => true,
      valueType: "date",
      render: (_, entity) => entity.lastMobilityTxDate || "-",
      align: "center",
    },
    {
      title: "交易机构号",
      dataIndex: "txInstNo",
      width: 140,
      ellipsis: true,
      editable: () => true,
      align: "center",
    },
    {
      title: "交易柜员号",
      dataIndex: "txTellerNo",
      width: 140,
      ellipsis: true,
      editable: () => true,
      align: "center",
    },
    {
      title: "授权柜员号",
      dataIndex: "authTellerNo",
      width: 140,
      ellipsis: true,
      editable: () => true,
      align: "center",
    },
    {
      title: "渠道种类代码",
      dataIndex: "chnlKindCode",
      width: 120,
      ellipsis: true,
      editable: () => true,
      valueType: "select",
      valueEnum: {
        '01': { text: '01-网点柜面' },
        '10': { text: '10-网上银行' },
        '12': { text: '12-个人网银' },
        '13': { text: '13-电视银行' },
        '14': { text: '14-电话银行' },
        '15': { text: '15-手机银行' },
        '16': { text: '16-企业网银' },
        '17': { text: '17-自助设备' },
        '18': { text: '18-POS' },
        '20': { text: '20-超级网银' },
        '21': { text: '21-大小额支付' },
        '22': { text: '22-银联前置' },
        '24': { text: '24-管理端' },
        '25': { text: '25-交易端' },
        '26': { text: '26-商易通' },
        '27': { text: '27-助农通' },
        '29': { text: '29-外部系统' },
        '30': { text: '30-系统自动' },
        '31': { text: '31-电子汇兑系统' },
        '32': { text: '32-理财规划终端' },
        '34': { text: '34-网汇通' },
        '35': { text: '35-同城支付' },
        '36': { text: '36-移动终端-TSM（可信服务管理）' },
        '37': { text: '37-移动终端-移动展业' },
        '38': { text: '38-直销银行' },
        '39': { text: '39-短信' },
        '40': { text: '40-专属APP' },
        '41': { text: '41-第三方线上渠道' },
        '43': { text: '43-智能柜员机（ITM）' },
        '42': { text: '42-国际支付前置' },
        '44': { text: '44-邮储经营' },
        '45': { text: '45-银银前置系统' },
        '46': { text: '46-U链供应链' },
        '47': { text: '47-油料保障结算系统' },
        '48': { text: '48-银企直联' },
        '91': { text: '91-微信银行' },
        '99': { text: '99-其他' },
      },
      align: "center",
    },
    {
      title: "发起系统或组件编码",
      dataIndex: "startSysOrCmptNo",
      width: 150,
      ellipsis: true,
      editable: () => true,
      align: "center",
    },
    {
      title: "限制编号",
      dataIndex: "limtNo",
      width: 180,
      ellipsis: true,
      editable: () => false,
      align: "center",
    },
    {
      title: "限制原因代码",
      dataIndex: "limtReasnCd",
      width: 140,
      ellipsis: true,
      editable: () => true,
      valueType: "select",
      valueEnum: {
        "1001": { text: "1001-冒名开立账户停用" },
        "1002": { text: "1002-有权机关停用" },
        "1003": { text: "1003-本人申请停用" },
        "1004": { text: "1004-A涉案账户停用" },
        "1005": { text: "1005-反洗钱账户停用" },
        "1006": { text: "1006-可疑风险账户停用" },
        "1007": { text: "1007-B涉案账户停用（反欺诈系统）" },
        "1008": { text: "1008-C涉案账户停用（反洗钱系统停用）" },
        "1009": { text: "1009-客户III类账户双边收付达5万7天未核实" },
        "1010": { text: "1010-假名匿名" },
        "1011": { text: "1011-账户/卡数量超限停用" },
        "1012": { text: "1012-可疑风险账户停用(行内事中模型)" },
        "1013": { text: "1013-假名匿名" },
        "2001": { text: "2001-客户信息不符合要求" },
        "3001": { text: "3001-A涉案客户名下其他账户" },
        "3002": { text: "3002-惩戒客户" },
        "3003": { text: "3003-开户6个月无交易" },
        "3004": { text: "3004-电话号码对应客户有误" },
        "3005": { text: "3005-存量无法核实" },
        "3006": { text: "3006-客户信息不合规" },
        "3007": { text: "3007-退役部门发起暂停" },
        "3008": { text: "3008-账户/卡数量超限" },
        "3009": { text: "3009-B涉案客户名下其他账户（反欺诈系统）" },
        "3010": { text: "3010-C涉案客户名下其他账户（反洗钱系统）" },
        "3011": { text: "3011-惩戒账户暂停（非法买卖的账户）" },
        "3012": { text: "3012-可疑风险账户" },
        "3013": { text: "3013-可疑风险账户（灰名单）" },
        "3014": { text: "3014-反洗钱账户暂停" },
        "3015": { text: "3015-虚拟货币暂停" },
        "3016": { text: "3016-不活跃账户" },
        "3017": { text: "3017-可疑风险账户(行内事中模型)" },
        "3018": { text: "3018-客户信息不全或不符暂非" },
        "3019": { text: "3019-落实当地监管要求(暂停非柜面)" },
        "3023": { text: "3023-疑似受害人(行内事中模型)" },
        "4001": { text: "4001-长期不动户（指客户账户三年无交易且账户余额小于等于10元时，判断为不活跃账户）" },
        "5001": { text: "5001-监管、联席办或政府文件要求" },
        "5002": { text: "5002-可疑风险账户管控(非柜面渠道专项管控)" },
        "5003": { text: "5003-批量重新约定限额" },
        "5004": { text: "5004-客户临柜设置" },
        "5005": { text: "5005-临时调额" },
        "5006": { text: "5006-VIP客户线上提额" },
        "5007": { text: "5007-白名单账户提额" },
        "5008": { text: "5008-客户线上预约提额" },
        "6001": { text: "6001-存在洗钱风险" },
        "9999": { text: "9999-其他" },
      },
      align: "center",
    },
    {
      title: "限制原因补充描述",
      dataIndex: "limtReasonSupplemDesc",
      width: 180,
      ellipsis: true,
      editable: () => true,
      align: "center",
    },
    {
      title: "限制执行文号",
      dataIndex: "limtFileNo",
      width: 180,
      ellipsis: true,
      editable: () => true,
      align: "center",
    },
    {
      title: "限制有权机关名称",
      dataIndex: "limtPowInstName",
      width: 200,
      ellipsis: true,
      editable: () => true,
      align: "center",
    },
    {
      title: "公安联系人名称",
      dataIndex: "policeConterName",
      width: 140,
      ellipsis: true,
      editable: () => true,
      align: "center",
    },
    {
      title: "公安联系人电话",
      dataIndex: "policeConterTel",
      width: 140,
      ellipsis: true,
      editable: () => true,
      align: "center",
    },
    {
      title: "生效状态代码",
      dataIndex: "effStaCd",
      width: 120,
      ellipsis: true,
      editable: () => true,
      valueType: "select",
      valueEnum: {
        "1": { text: "1-生效" },
        "0": { text: "0-未生效" },
      },
      align: "center",
    },
    {
      title: "限制终止日期",
      dataIndex: "limtEndDt",
      width: 120,
      ellipsis: true,
      editable: () => true,
      valueType: "date",
      render: (_, entity) => entity.limtEndDt || "-",
      align: "center",
    },
    {
      title: "变更渠道种类代码",
      dataIndex: "chgChnlKindCode",
      width: 140,
      ellipsis: true,
      editable: () => true,
      align: "center",
    },
    {
      title: "变更系统编码",
      dataIndex: "chgSysNo",
      width: 140,
      ellipsis: true,
      editable: () => true,
      align: "center",
    },
    {
      title: "变更主机日期",
      dataIndex: "chgHostDt",
      width: 120,
      ellipsis: true,
      editable: () => true,
      valueType: "date",
      render: (_, entity) => entity.chgHostDt || "-",
      align: "center",
    },
    {
      title: "解除日期",
      dataIndex: "relsDt",
      width: 120,
      ellipsis: true,
      editable: () => true,
      valueType: "date",
      render: (_, entity) => entity.relsDt || "-",
      align: "center",
    },
    {
      title: "解除时间",
      dataIndex: "relsTime",
      width: 160,
      ellipsis: true,
      editable: () => true,
      align: "center",
    },
    {
      title: "最后交易日期",
      dataIndex: "lastTxDate",
      width: 120,
      ellipsis: true,
      editable: () => true,
      valueType: "date",
      render: (_, entity) => entity.lastTxDate || "-",
      align: "center",
    },
    {
      title: "记录状态代码",
      dataIndex: "recordStaCd",
      width: 120,
      ellipsis: true,
      editable: () => true,
      valueType: "select",
      valueEnum: {
        "0": { text: "0-无效" },
        "1": { text: "1-有效" },
      },
      align: "center",
    },
    {
      title: "分片值",
      dataIndex: "zoneVal",
      width: 160,
      ellipsis: true,
      editable: () => false,
      align: "center",
    },
    {
      title: "操作",
      valueType: "option",
      width: 150,
      // fixed: "right",
      align: "center",
      render: (text, record: TbDprgtLimtServ, _, action) => {
        const isRecordEditing = record.limtNo
          ? editableKeys.includes(record.limtNo)
          : false;
        return [
          isRecordEditing ? (
            <>
              <Button
                key="confirm"
                type="link"
                onClick={async () => {
                  try {
                    if (record.limtNo) {
                      await action?.saveEditable?.(record.limtNo);
                      setIsEditing(false);
                    }
                  } catch (err) {
                    console.error("保存失败:", err);
                  }
                }}
              >
                确定
              </Button>
              <Button
                key="cancel"
                type="link"
                onClick={() => {
                  if (record.limtNo) {
                    action?.cancelEditable?.(record.limtNo);
                    setEditableRowKeys([]);
                    setIsEditing(false);
                  }
                }}
                style={{ marginLeft: 8 }}
              >
                取消
              </Button>
            </>
          ) : (
            <Button
              key="editable"
              type="link"
              disabled={true}
              onClick={async () => {
                if (!record.limtNo) return;
                const originalRecord = { ...record };
                setEditingRecord(originalRecord);
                setIsEditing(true);
                await action?.startEditable?.(record.limtNo);
              }}
            >
              编辑
            </Button>
          ),
          <Button
            key="delete"
            type="link"
            danger
            disabled={true}
            onClick={async () => {
              if (!record.limtNo) return;
              Modal.confirm({
                title: "确认删除",
                content: "确定要删除这条记录吗？",
                onOk: async () => {
                  try {
                    const res = await deleteLimtServ(record);
                    if (res.code === 200) {
                      message.success("删除成功");
                    } else {
                      message.error(res.msg || "删除失败");
                    }
                  } catch (error) {
                    message.error("删除失败");
                  } finally {
                    await refreshData();
                  }
                },
                okText: "确认",
                cancelText: "取消",
              });
            }}
            style={{ marginLeft: 8 }}
          >
            删除
          </Button>,
        ];
      },
    },
  ];
  
  return (
    <Modal
      title="限制服务登记簿"
      open={props.open}
      onCancel={props.onCancel}
      footer={null}
      width={1280}
      bodyStyle={MODAL_BODY_STYLE}
      style={MODAL_STYLE}
      destroyOnClose
      maskClosable={false}
    >
      <div style={{ marginBottom: 16 }}>
        <p>数据状态: {loading ? '加载中' : `已加载 ${dataSource.length} 条记录`}</p>
      </div>
      <EditableProTable<TbDprgtLimtServ>
        columns={columns}
        rowKey="limtNo"
        scroll={TABLE_SCROLL}
        value={dataSource}
        onChange={setDataSource}
        pagination={{
          ...TABLE_PAGINATION,
          ...pagination,
          onChange: (page, pageSize) => {
            setPagination({
              ...pagination,
              current: page,
              pageSize,
            });
          },
        }}
        headerTitle="限制服务登记簿信息"
        recordCreatorProps={position === "hidden" ? false : {
          position: position,
          creatorButtonText: "新增一行限制服务登记信息",
          onClick: async () => {
            const startIndex = (pagination.current - 1) * pagination.pageSize;
            const endIndex = startIndex + pagination.pageSize;
            const currentPageDataCount = Math.min(
              pagination.pageSize,
              pagination.total - (pagination.current - 1) * pagination.pageSize
            );
            if (pagination.current !== 1 && currentPageDataCount >= pagination.pageSize) {
              Modal.error({
                title: "提示",
                content: "当前页面数据已满，请切换至第一页或者未满的页面进行新增",
              });
              return false;
            }
            
            // 获取当前日期
            const today = new Date();
            const formattedDate = today.toISOString().split("T")[0];
            
            // 使用类型断言安全地获取值
            const contractNo = (props.data?.records?.[0] as any)?.prodtContractNo || '';
            const zoneVal = (props.data?.records?.[0] as any)?.zoneVal || '';
            const mediumNo = (props.data?.records?.[0] as any)?.mediumNo || '';
            
            // 构建新记录
            const newRecord = {
              limtNo: `NEW_${Date.now()}`,
              enrollDate: formattedDate,
              enrollTime: today.toTimeString().split(' ')[0],
              prodtContractNo: contractNo,
              mediumNo: mediumNo,
              zoneVal: zoneVal,
              ...props.data?.records?.[0],
            };
            
            const newDataSource = [...dataSource];
            newDataSource.splice(endIndex, 0, newRecord);
            setDataSource(newDataSource);
            setIsEditing(true);
            setEditableRowKeys([newRecord.limtNo]);
          },
          record: () => {
            const today = new Date();
            const formattedDate = today.toISOString().split("T")[0];
            
            return {
              limtNo: `NEW_${Date.now()}`,
              enrollDate: formattedDate,
              enrollTime: today.toTimeString().split(' ')[0],
              ...props.data?.records?.[0],
            } as TbDprgtLimtServ;
          },
        }}
        toolBarRender={() => [
          <ProFormRadio.Group
            key="render"
            fieldProps={{
              value: position,
              onChange: (e: any) => setPosition(e.target.value),
            }}
            options={[
              { label: "添加到顶部", value: "top" },
              { label: "添加到底部", value: "bottom" },
              { label: "隐藏", value: "hidden" },
            ]}
          />,
          <a
            key="refresh"
            onClick={() => {
              refreshData();
            }}
          >
            刷新
          </a>,
        ]}
        editable={{
          type: 'single',
          editableKeys,
          actionRender: (row, config, defaultDom) => {
            return [defaultDom.save, defaultDom.cancel];
          },
          onSave: async (rowKey, data, row) => {
            const isNewRecord = String(rowKey).startsWith('NEW_');
            try {
              const response = isNewRecord
                ? await insertLimtServ(data)
                : await updateLimtServ(data);
              
              if (response.code === 200) {
                message.success(isNewRecord ? '添加成功' : '更新成功');
                await refreshData();
              } else {
                message.error(response.msg || (isNewRecord ? '添加失败' : '更新失败'));
              }
            } catch (error) {
              console.error(isNewRecord ? '添加出错:' : '更新出错:', error);
              message.error(isNewRecord ? '添加失败' : '更新失败');
            }
          },
          onChange: setEditableRowKeys,
        }}
        loading={loading}
      />
    </Modal>
  );
};

export default DprgtLimtServFormEdit;