import React, { useEffect, useState } from "react";
import { Mo<PERSON>, message,But<PERSON>, Toolt<PERSON> } from "antd";
import {
  dprgtPersDepLifeCycUpdate,
  getDprgtPersDepLifeCyc,
  insertDprgtPersDepLifeCyc,
  dprgtPersDepLifeCycDelete,
} from "../service";
import type { TbDprgtPersDepLifeCyc, PageResponse } from "../data";
import type { ProColumns } from "@ant-design/pro-components";
import { InfoCircleOutlined } from "@ant-design/icons";
import { EditableProTable, ProFormRadio } from "@ant-design/pro-components";

/*
 * @Description: 个人存款生命周期表编辑表单
 * @Author: zhujinwei
 * @Date: 2025-01-17 10:00:00
 */

interface PaginationType {
  current: number;
  pageSize: number;
  total: number;
}

export type DprgtPersDepLifeCycFormProps = {
  onCancel: () => void;
  open: boolean;
  data: PageResponse<TbDprgtPersDepLifeCyc> | null;
  // refresh: number;
};

// 表格相关常量
const DEFAULT_PAGE_SIZE = 10;
const TABLE_SCROLL = { x: 1300 } as const;
const TABLE_OPTIONS = {
  density: true,
  fullScreen: true,
  setting: true,
} as const;
const TABLE_PAGINATION = {
  showSizeChanger: true,
  showQuickJumper: true,
} as const;

// Modal相关常量
const MODAL_BODY_STYLE = {
  maxHeight: "70vh",
  overflow: "auto",
  padding: "24px",
  borderRadius: "10px",
} as const;

const MODAL_STYLE = {
  borderRadius: "10px",
  overflow: "hidden",
} as const;


// 工具函数
const formatAmount = (value: string | number | null | undefined): string => {
  if (!value) return "0.00";
  const num = typeof value === "string" ? parseFloat(value) : value;
  return isNaN(num) ? "0.00" : num.toFixed(2);
};

// 分页相关常量
const INITIAL_PAGINATION: PaginationType = {
  current: 1,
  pageSize: DEFAULT_PAGE_SIZE,
  total: 0,
};

const DprgtPersDepLifeCycEdit: React.FC<DprgtPersDepLifeCycFormProps> = (props) => {
  const [dataSource, setDataSource] = useState<readonly TbDprgtPersDepLifeCyc[]>([]);
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);
  const [isEditing, setIsEditing] = useState(false);
  const [editingRecord, setEditingRecord] = useState<TbDprgtPersDepLifeCyc | null>(
    null
  );
  const [pagination, setPagination] =
    useState<PaginationType>(INITIAL_PAGINATION);
  const [refresh, setRefresh] = useState(0);
  const [loading, setLoading] = useState(false);

  // 刷新数据方法
  const refreshData = async () => {
    if (!props.data?.records?.[0]) return;

    const dpmstMedium = props.data.records[0];
    if (!dpmstMedium?.mediumNo || !dpmstMedium?.zoneVal) return;

    setLoading(true);
    try {
      const res = await getDprgtPersDepLifeCyc(
        dpmstMedium.mediumNo,
        dpmstMedium.zoneVal,
        "dprgtPersDepLifeCycPageInfo",
        {
          current: pagination.current,
          pageSize: pagination.pageSize,
        }
      );

      if (res.code === 200 && res.data) {
        const detailData = JSON.parse(res.data);
        setDataSource(detailData.records || []);
        setPagination({
          current: detailData.current || INITIAL_PAGINATION.current,
          pageSize: detailData.size || INITIAL_PAGINATION.pageSize,
          total: detailData.total || INITIAL_PAGINATION.total,
        });
        setRefresh(refresh + 1);
      } else {
        message.error(res.msg || "获取个人存款生命周期失败");
      }
    } catch (error) {
      message.error("查询个人存款生命周期失败");
    } finally {
      setLoading(false);
    }
  };

  // 初始化数据
  useEffect(() => {
    let isMounted = true;

    const initData = async () => {
      if (props.open && props.data) {
        const { records, current, size, total } = props.data;
        if (isMounted) {
          setDataSource(records || []);
          setPagination({
            current: current || INITIAL_PAGINATION.current,
            pageSize: size || INITIAL_PAGINATION.pageSize,
            total: total || INITIAL_PAGINATION.total,
          });
        }
      } else if (isMounted) {
        setDataSource([]);
        setPagination(INITIAL_PAGINATION);
      }
    };

    initData();

    return () => {
      isMounted = false;
    };
  }, [props.open, props.data]);

  // 定义表格列
  const [position, setPosition] = useState<"top" | "bottom" | "hidden">(
    "bottom"
  );

  const columns: ProColumns<TbDprgtPersDepLifeCyc>[] = [
    {
      title: "登记日期",
      dataIndex: "enrollDate",
      width: 200,
      ellipsis: true,
      align: 'center',
      render: (_, entity) => entity.enrollDate || "-",
      editable: () => true,
      valueType: 'date',
    },
    {
      title: "全局业务跟踪号",
      dataIndex: "globalBusiTrackNo",
      width: 260,
      ellipsis: true,
      align: 'center',
      editable: () => true,
    },
    {
      title: "子交易序号",
      dataIndex: "subtxNo",
      width: 260,
      ellipsis: true,
      align: 'center',
      editable: () => true,
    },
    {
      title: "介质编号",
      dataIndex: "mediumNo",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => false,
    },
    {
      title: "子账号序号",
      dataIndex: "saccnoSeqNo",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => false,
    },
    {
      title: "合约账户类型代码",
      dataIndex: "contrAccTpCd",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => true,
      valueType: 'select',
      valueEnum: {
      '0000': { text: '0000-无账户' },
      '1001': { text: '1001-正常账户' },
      },
    },
    {
      title: "个人存款合约类型代码",
      dataIndex: "dpContrTpCd",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => true,
      valueType: 'select',
      valueEnum: {
      '1001': { text: '1001-人民币活期储蓄合约' },
      '1002': { text: '1002-人民币活期结算合约' },
      '1003': { text: '1003-外币活期储蓄合约' },
      '1004': { text: '1004-外币活期结算合约' },
      '1005': { text: '1005-本外币合一结算合约' },
      '2001': { text: '2001-整存整取储蓄存款合约' },
      '2002': { text: '2002-整存整取协议存款合约' },
      '2003': { text: '2003-提前付息定期存款合约' },
      '2004': { text: '2004-定活两便储蓄存款合约' },
      '2005': { text: '2005-整存零取储蓄存款合约' },
      '2006': { text: '2006-存本取息储蓄存款合约' },
      '2007': { text: '2007-零存整取储蓄存款合约' },
      '2008': { text: '2008-通知存款合约' },
      '2009': { text: '2009-结构性存款合约' },
      '2010': { text: '2010-递增计息合约' },
      '2011': { text: '2011-梦想加邮站合约' },
      '2012': { text: '2012-大额存单合约' },
      '2013': { text: '2013-礼仪存单合约' },
      '2014': { text: '2014-邮智存合约' },
      '3001': { text: '3001-行业应用子账户合约' },
      '3002': { text: '3002-电子现金账户合约' },
      '4003': { text: '4003-本外币定期一本通合约' },
      },
    },
    {
      title: () => (
        <Tooltip
          title={
            <div style={{ width: "100%" }}>
              <p>执行层级标志码：</p>
              <ul>
                <li>
                  <strong>介质层：0-否，1-是</strong>
                </li>
                <li>
                  <strong>合约层：0-否，1-是</strong>
                </li>
                <li>
                  <strong>账户层：0-否，1-是</strong>
                </li>
              </ul>
            </div>
          }
          placement="top"
          overlayClassName="custom-tooltip"
          color="#fff"
          overlayStyle={{ minWidth: "350px" }}
        >
          <span>
            执行层级标志码{" "}
            <InfoCircleOutlined
              style={{ marginLeft: 4, color: "#1890ff" }}
            />
          </span>
        </Tooltip>
      ),
      // title: "执行层级标志码",
      dataIndex: "execLvlFlagCd",
      width: 220,
      ellipsis: true,
      align: 'center',
      editable: () => true,
    },
    {
      title: "状态变更原因代码",
      dataIndex: "statChgReasnCd",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => true,
      valueType: 'select',
      valueEnum: {
      '1001': { text: '1001-正常开户' },
      '1002': { text: '1002-部支新开' },
      '1003': { text: '1003-扣划新开' },
      '1004': { text: '1004-开户冲正' },
      '1005': { text: '1005-销户取消' },
      '1006': { text: '1006-挂失补发' },
      '1007': { text: '1007-配发介质' },
      '1008': { text: '1008-更换介质(异号)' },
      '1009': { text: '1009-转存开户' },
      '1010': { text: '1010-账户移入' },
      '1011': { text: '1011-受让新开' },
      '2001': { text: '2001-正常销户' },
      '2002': { text: '2002-部支销户' },
      '2003': { text: '2003-挂失销户' },
      '2004': { text: '2004-划扣销户' },
      '2005': { text: '2005-欠费销户' },
      '2006': { text: '2006-开户取消' },
      '2007': { text: '2007-注销介质' },
      '2009': { text: '2009-更换介质取消' },
      '2010': { text: '2010-转存销户' },
      '2011': { text: '2011-账户移出' },
      '2012': { text: '2012-冒名账户强制销户' },
      '2013': { text: '2013-本人账户强制销户' },
      '2014': { text: '2014-出让销户' },
      '2016': { text: '2016-凭证丢失销户' },
      '3001': { text: '3001-介质激活' },
      },
    },
    {
      title: "产品合约编号",
      dataIndex: "prodtContractNo",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => false,
    },
    {
      title: "个人内部账号",
      dataIndex: "persInnerAccno",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => false,
    },
    {
      title: "可售产品编码",
      dataIndex: "vendibiProdtNo",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => true,
    },
    {
      title: "可售产品名称",
      dataIndex: "vendibiProdtName",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => true,
    },
    {
      title: "基础产品编码",
      dataIndex: "baseProdtNo",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => true,
    },
    {
      title: "储种中类代码",
      dataIndex: "savTypeMclassCode",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => true,
      valueType: 'select',
      valueEnum: {
      '010100': { text: '010100-储蓄活期' },
      '010200': { text: '010200-结算活期' },
      '020100': { text: '020100-保值储蓄' },
      '020200': { text: '020200-整存整取' },
      '020300': { text: '020300-整存零取' },
      '020400': { text: '020400-零存整取' },
      '020500': { text: '020500-存本取息' },
      '020600': { text: '020600-定额定期' },
      '020700': { text: '020700-定期协议利率存款' },
      '020800': { text: '020800-大额存单' },
      '030100': { text: '030100-不固定定活两便' },
      '030200': { text: '030200-固定定活两便' },
      '040100': { text: '040100-老通知' },
      '040200': { text: '040200-新通知' },
      '050100': { text: '050100-一本通' },
      '070100': { text: '070100-结构性存款' },
      '990100': { text: '990100-小额支付账户' },
      '990200': { text: '990200-行业应用子账户（计息）' },
      '990300': { text: '990300-行业应用子账户（不计息）' },
      '990400': { text: '990400-行业应用子账户（分段）' },
      '999900': { text: '999900-其它' },
      },
    },
    {
      title: "类别标识代码",
      dataIndex: "categFlagCd",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => true,
      valueType: 'select',
      valueEnum: {
      '01': { text: '01-活期' },
      '02': { text: '02-定期' },
      },
    },
    {
      title: "币种代码",
      dataIndex: "currCode",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => true,
      valueType: 'select',
      valueEnum: {
      '036': { text: '036-澳大利亚元' },
      '124': { text: '124-加元' },
      '344': { text: '344-香港元' },
      '392': { text: '392-日元' },
      '826': { text: '826-英镑' },
      '840': { text: '840-美元' },
      '978': { text: '978-欧元（EUR）' },
      '156': { text: '156-人民币元' },
      },
    },
    {
      title: "钞汇类别代码",
      dataIndex: "cashExgVatgCd",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => true,
    valueType: 'select',
          valueEnum: {
            '2': { text: '2-钞' },
            '3': { text: '3-汇' },
          },
    },
    {
      title: "交易金额",
      dataIndex: "txAmt",
      width: 200,
      ellipsis: true,
      align: 'center',
      render: (_, entity) => formatAmount(entity.txAmt),
      editable: () => true,
    },
    {
      title: "期限单位代码",
      dataIndex: "termUnitCode",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => true,
    valueType: "select",
            valueEnum: {
                "1": { text: "1-日" },
              "2": { text: "2-周" },
              "3": { text: "3-月" },
              "4": { text: "4-季" },
              "5": { text: "5-年" },
            },
    },
    {
      title: "存款期限",
      dataIndex: "deptTerm",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => true,
    },
    {
      title: "客户编号",
      dataIndex: "custNo",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => true,
    },
    {
      title: "客户名称",
      dataIndex: "custNm",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => true,
    },
    {
      title: "个人证件类型代码",
      dataIndex: "perCertTpCd",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => true,
      valueType: "select",
      valueEnum: {
      '1010': { text: '1010-居民身份证' },
      '1011': { text: '1011-临时居民身份证' },
      '1020': { text: '1020-军人身份证件' },
      '1021': { text: '1021-士兵证' },
      '1022': { text: '1022-军官证' },
      '1023': { text: '1023-文职干部证' },
      '1024': { text: '1024-军官退休证' },
      '1025': { text: '1025-文职干部退休证' },
      '1030': { text: '1030-武警身份证件' },
      '1031': { text: '1031-武警士兵证' },
      '1032': { text: '1032-警官证' },
      '1033': { text: '1033-武警文职干部证' },
      '1034': { text: '1034-武警军官退休证' },
      '1035': { text: '1035-武警文职干部退休证' },
      '1040': { text: '1040-户口簿' },
      '1050': { text: '1050-中国护照' },
      '1051': { text: '1051-外国护照' },
      '1060': { text: '1060-学生证' },
      '1070': { text: '1070-港澳居民来往内地通行证' },
      '1071': { text: '1071-往来港澳通行证' },
      '1080': { text: '1080-台湾居民来往大陆通行证' },
      '1090': { text: '1090-执行公务证' },
      '1100': { text: '1100-机动车驾驶证' },
      '1110': { text: '1110-社会保障卡' },
      '1120': { text: '1120-外国人居留证' },
      '1121': { text: '1121-外国人永久居留证' },
      '1130': { text: '1130-旅行证件' },
      '1140': { text: '1140-香港居民身份证' },
      '1150': { text: '1150-澳门居民身份证' },
      '1160': { text: '1160-台湾居民身份证' },
      '1170': { text: '1170-边民证' },
      '1180': { text: '1180-港澳台居民居住证' },
      '1181': { text: '1181-港澳居民居住证' },
      '1182': { text: '1182-台湾居民居住证' },
      '1190': { text: '1190-外国身份证' },
      '1998': { text: '1998-其他（原98类）' },
      '1999': { text: '1999-其他证件（个人）' },
      },
    },
    {
      title: "个人证件号码",
      dataIndex: "personalCertNo",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => true,
    },
    {
      title: "生命周期状态代码",
      dataIndex: "lifeCycStaCd",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => true,
      valueType: 'select',
      valueEnum: {
      '01': { text: '01-配发/签约/开户' },
      '02': { text: '02-注销/解约/销户' },
      '03': { text: '03-激活' },
      },
    },
    {
      title: "渠道种类代码",
      dataIndex: "chnlKindCode",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => true,
      valueType: 'select',
      valueEnum: {
      '01': { text: '01-网点柜面' },
      '10': { text: '10-网上银行' },
      '12': { text: '12-个人网银' },
      '13': { text: '13-电视银行' },
      '14': { text: '14-电话银行' },
      '15': { text: '15-手机银行' },
      '16': { text: '16-企业网银' },
      '17': { text: '17-自助设备' },
      '18': { text: '18-POS' },
      '20': { text: '20-超级网银' },
      '21': { text: '21-大小额支付' },
      '22': { text: '22-银联前置' },
      '24': { text: '24-管理端' },
      '25': { text: '25-交易端' },
      '26': { text: '26-商易通' },
      '27': { text: '27-助农通' },
      '29': { text: '29-外部系统' },
      '30': { text: '30-系统自动' },
      '31': { text: '31-电子汇兑系统' },
      '32': { text: '32-理财规划终端' },
      '34': { text: '34-网汇通' },
      '35': { text: '35-同城支付' },
      '36': { text: '36-移动终端-TSM（可信服务管理）' },
      '37': { text: '37-移动终端-移动展业' },
      '38': { text: '38-直销银行' },
      '39': { text: '39-短信' },
      '40': { text: '40-专属APP' },
      '41': { text: '41-第三方线上渠道' },
      '43': { text: '43-智能柜员机（ITM）' },
      '42': { text: '42-国际支付前置' },
      '44': { text: '44-邮储经营' },
      '45': { text: '45-银银前置系统' },
      '46': { text: '46-U链供应链' },
      '47': { text: '47-油料保障结算系统' },
      '48': { text: '48-银企直联' },
      '91': { text: '91-微信银行' },
      '99': { text: '99-其他' },
      },
    },
    {
      title: "发起系统或组件编码",
      dataIndex: "startSysOrCmptNo",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => true,
    },
    {
      title: "批次号",
      dataIndex: "batchNo",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => true,
    },
    {
      title: "开户机构号",
      dataIndex: "openAccInstNo",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => true,
    },
    {
      title: "销户机构号",
      dataIndex: "clsAccInstNo",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => true,
    },
    {
      title: "交易机构号",
      dataIndex: "txInstNo",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => true,
    },
    {
      title: "交易柜员号",
      dataIndex: "txTellerNo",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => true,
    },
    {
      title: "授权柜员号",
      dataIndex: "authTellerNo",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => true,
    },
    {
      title: "营销柜员号",
      dataIndex: "campTellerNo",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => true,
    },
    {
      title: "账户开立用途分类代码",
      dataIndex: "settaccUsageClsCd",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => true,
      valueType: 'select',
      valueEnum: {
      '1': { text: '1-储蓄' },
      '2': { text: '2-投资' },
      '3': { text: '3-贷款' },
      '4': { text: '4-其他' },
      '5': { text: '5-消费缴费' },
      '6': { text: '6-代发工资' },
      },
    },
    {
      title: "其他用途说明",
      dataIndex: "othUsageDesc",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => true,
    },
    {
      title: "有密标志",
      dataIndex: "hvPsFlag",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => true,
      valueType: 'select',
      valueEnum: {
      '0': { text: '0-否' },
      '1': { text: '1-是' },
      },
    },
    {
      title: "联名账户标志",
      dataIndex: "jointNaAccFlag",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => true,
      valueType: 'select',
      valueEnum: {
      '0': { text: '0-否' },
      '1': { text: '1-是' },
      },
    },
    {
      title: () => (
        <Tooltip
          title={
            <div style={{ width: "100%" }}>
              <p>生命周期标志码</p>
              <ul>
                <li>
                  <strong>第1位：代理人代办标志：0-否，1-是</strong>
                </li>
                <li>
                  <strong>第2位：合约属性：</strong>
                </li>
                <li>0-无关</li>
                <li>1-人民币结算合约</li>
                <li>2-本外币合一结算合约</li>
                <li>3-外币结算合约</li>
                <li>4-个人储蓄合约</li>
                <li>
                  <strong>第3位：分配介质编号标志： </strong>
                </li>
                <li>0-已分配</li>
                <li>1-未分配</li>
                <li>
                  <strong>第4位：收付款方式：</strong>
                </li>
                <li>0-无关</li>
                <li>1-现金</li>
                <li>2-转账（仅指活转活）</li>
                <li>3-转存</li>
                <li>4-支票</li>
                <li>5-活转定</li>
                <li>6-定转活</li>
                <li>7-定转定</li>
              </ul>
            </div>
          }
          placement="top"
          overlayClassName="custom-tooltip"
          color="#fff"
          overlayStyle={{ minWidth: "350px" }}
        >
          <span>
            生命周期标志码{" "}
            <InfoCircleOutlined
              style={{ marginLeft: 4, color: "#1890ff" }}
            />
          </span>
        </Tooltip>
      ),
      // title: "生命周期标志码",
      dataIndex: "lifeCycFlagCd",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => true,
    },
    {
      title: "原银行外币提钞单据/入境申报单编号",
      dataIndex: "obankFcurrDrawDocEntryNo",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => true,
    },
    {
      title: "批处理标志",
      dataIndex: "batDealFlag",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => true,
      valueType: 'select',
      valueEnum: {
      '0': { text: '0-否' },
      '1': { text: '1-是' },
      },
    },
    {
      title: "是否处理标志",
      dataIndex: "dealFlag",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => true,
    valueType: 'select',
      valueEnum: {
      '0': { text: '0-否' },
      '1': { text: '1-是' },
      },
    },
    {
      title: "外国政要审批信息",
      dataIndex: "forCounGoveMemApproveInfo",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => true,
    },
    {
      title: "组织证件类型代码",
      dataIndex: "orgCertTypeCode",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => true,
      valueType: 'select',
      valueEnum: {
      '2000': { text: '2000-统一社会信用代码' },
      '2010': { text: '2010-营业执照' },
      },
    },
    {
      title: "组织证件号码",
      dataIndex: "orgCertNo",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => true,
    },
    {
      title: "最后交易日期",
      dataIndex: "lastTxDate",
      width: 200,
      ellipsis: true,
      align: 'center',
      render: (_, entity) => entity.enrollDate || "-",
      editable: () => true,
      valueType: 'date',
    },
    {
      title: "分片值",
      dataIndex: "zoneVal",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => false,
    },    
    {
      title: "操作",
      valueType: "option",
      width: 150,
      fixed: "right",
      align: 'center',
      render: (text, record: TbDprgtPersDepLifeCyc, _, action) => {
        const isEditing = record.globalBusiTrackNo
          ? editableKeys.includes(record.globalBusiTrackNo)
          : false;
        return [
          isEditing ? (
            <>
              <a
                key="confirm"
                onClick={async () => {
                  try {
                    if (record.globalBusiTrackNo) {
                      await action?.saveEditable?.(record.globalBusiTrackNo);
                      setIsEditing(false);
                    }
                  } catch (err) {
                    console.error("保存失败:", err);
                  }
                }}
              >
                确定
              </a>
              <a
                key="cancel"
                onClick={() => {
                  if (record.globalBusiTrackNo) {
                    action?.cancelEditable?.(record.globalBusiTrackNo);
                    setEditableRowKeys([]);
                    setIsEditing(false);
                  }
                }}
                style={{ marginLeft: 8 }}
              >
                取消
              </a>
            </>
          ) : (
            <Button
              key="editable"
              type="link"
            //   disabled={isEditing}
              disabled={true}
              onClick={async () => {
                if (!record.globalBusiTrackNo) return;
                // 先保存原始数据，再进入编辑状态
                const originalRecord = { ...record };
                setEditingRecord(originalRecord);
                setIsEditing(true);
                await action?.startEditable?.(record.globalBusiTrackNo);
              }}
              style={{ padding: 0 }}
            >
              编辑
            </Button>
          ),
          <Button
            key="delete"
            type="link"
            // disabled={isEditing}
            disabled={true}
            onClick={async () => {
              if (!record.globalBusiTrackNo) return;
              Modal.confirm({
                title: "确认删除",
                content: "确定要删除这条记录吗？",
                onOk: async () => {
                  try {
                    const res = await dprgtPersDepLifeCycDelete(record);
                    if (res.code === 200) {
                      message.success("删除成功");
                    } else {
                      message.error(res.msg || "删除失败");
                    }
                  } catch (error) {
                    message.error("删除失败");
                  } finally {
                    await refreshData();
                  }
                },
                okText: "确认",
                cancelText: "取消",
              });
            }}
            style={{ marginLeft: 8 }}
          >
            删除
          </Button>,
        ];
      },
    },
  ];

  return (
    <Modal
      width={1200}
      title="查询个人存款生命周期"
      open={props.open}
      destroyOnClose
      onCancel={props.onCancel}
      footer={null}
      bodyStyle={MODAL_BODY_STYLE}
      style={MODAL_STYLE}
    >
      <EditableProTable<TbDprgtPersDepLifeCyc>
        key={refresh}
        rowKey={(record) => record.globalBusiTrackNo || ""}
        headerTitle="个人存款生命周期"
        scroll={TABLE_SCROLL}
        recordCreatorProps={
          position === "hidden"
            ? false
            : {
                position: position,
                creatorButtonText: "新增一行个人存款生命周期记录",
                onClick: async () => {
                  // 获取当前页数据
                  const startIndex = (pagination.current - 1) * pagination.pageSize;
                  const endIndex = startIndex + pagination.pageSize;

                  const currentPage = pagination.current; // 当前页码
                  const pageSize = pagination.pageSize; // 每页显示的数据量
                  // 计算当前页面中数据量
                  const currentPageDataCount = Math.min(
                    pageSize, // 每页最大显示数
                    pagination.total - (currentPage - 1) * pageSize // 剩余数据量
                  );

                  // 检查当前页是否已满
                  if (currentPage !== 1 && currentPageDataCount >= pagination.pageSize) {
                    Modal.error({
                      title: '提示',
                      content: '当前页面数据已满，请切换至第一页或者是未满的页面进行新增',
                    });
                    return false;
                  }

                  // 创建新记录
                  const newRecord = {
                    enrollDate: new Date().toISOString().split('T')[0],
                    globalBusiTrackNo: "0000000000000000000000000000000000",
                    subtxNo: "0000000000000000000000000000000000",
                    mediumNo: props.data?.records?.[0]?.mediumNo ?? "",
                    saccnoSeqNo: "",
                    contrAccTpCd: "1001",
                    dpContrTpCd: "1001",
                    execLvlFlagCd: "00000000000000000000000000000000",
                    statChgReasnCd: "1001",
                    prodtContractNo: props.data?.records?.[0]?.prodtContractNo ?? "",
                    persInnerAccno: props.data?.records?.[0]?.persInnerAccno ?? "",
                    vendibiProdtNo: props.data?.records?.[0]?.vendibiProdtNo ?? "",
                    vendibiProdtName: props.data?.records?.[0]?.vendibiProdtName ?? "",
                    baseProdtNo: props.data?.records?.[0]?.baseProdtNo ?? "",
                    savTypeMclassCode: "010100",
                    categFlagCd: "01",
                    currCode: "156",
                    cashExgVatgCd: "2",
                    txAmt: "0.00",
                    termUnitCode: "1",
                    deptTerm: "0",
                    custNo: props.data?.records?.[0]?.custNo ?? "",
                    custNm: props.data?.records?.[0]?.custNm ?? "",
                    perCertTpCd: props.data?.records?.[0]?.perCertTpCd ?? "",
                    personalCertNo: props.data?.records?.[0]?.personalCertNo ?? "",
                    lifeCycStaCd: "01",
                    chnlKindCode: "01",
                    startSysOrCmptNo: "1022199",
                    batchNo: "",
                    openAccInstNo: "",
                    clsAccInstNo: "",
                    txInstNo: "",
                    txTellerNo: "",
                    authTellerNo: "",
                    campTellerNo: "",
                    settaccUsageClsCd: "1",
                    othUsageDesc: "",
                    hvPsFlag: "1",
                    jointNaAccFlag: "0",
                    lifeCycFlagCd: "00000000000000000000000000000000",
                    obankFcurrDrawDocEntryNo: "",
                    batDealFlag: "0",
                    dealFlag: "0",
                    forCounGoveMemApproveInfo: "",
                    orgCertTypeCode: "2000",
                    orgCertNo: "",
                    lastTxDate: new Date().toISOString().split('T')[0],
                    zoneVal: props.data?.records?.[0]?.zoneVal ?? "",
                  };

                  // 在当前页末尾添加新记录
                  const newDataSource = [...dataSource];
                  newDataSource.splice(endIndex, 0, newRecord);

                  setDataSource(newDataSource);
                  setIsEditing(true);
                  setEditableRowKeys(["9999"]);
                },
                record: () => {
                  const today = new Date();
                  const year = today.getFullYear();
                  const month = String(today.getMonth() + 1).padStart(2, '0');
                  const day = String(today.getDate()).padStart(2, '0');
                  const formattedDate = `${year}-${month}-${day}`;
                
                  return {
                    enrollDate: formattedDate,
                    globalBusiTrackNo: props.data?.records?.[0]?.globalBusiTrackNo ?? "",
                    subtxNo: props.data?.records?.[0]?.subtxNo ?? "",
                    contrAccTpCd: props.data?.records?.[0]?.contrAccTpCd ?? "",
                    dpContrTpCd: props.data?.records?.[0]?.dpContrTpCd ?? "",
                    execLvlFlagCd: props.data?.records?.[0]?.execLvlFlagCd ?? "",
                    statChgReasnCd: props.data?.records?.[0]?.statChgReasnCd ?? "",
                    savTypeMclassCode: props.data?.records?.[0]?.savTypeMclassCode ?? "",
                    categFlagCd: props.data?.records?.[0]?.categFlagCd ?? "",
                    currCode: props.data?.records?.[0]?.currCode ?? "",
                    cashExgVatgCd: props.data?.records?.[0]?.cashExgVatgCd ?? "",
                    txAmt: props.data?.records?.[0]?.txAmt ?? "",
                    termUnitCode: props.data?.records?.[0]?.termUnitCode ?? "",
                    deptTerm: props.data?.records?.[0]?.deptTerm ?? "",
                    lifeCycStaCd: props.data?.records?.[0]?.lifeCycStaCd ?? "",
                    chnlKindCode: props.data?.records?.[0]?.chnlKindCode ?? "",
                    startSysOrCmptNo: props.data?.records?.[0]?.startSysOrCmptNo ?? "",
                    batchNo: props.data?.records?.[0]?.batchNo ?? "",
                    openAccInstNo: props.data?.records?.[0]?.openAccInstNo ?? "",
                    clsAccInstNo: props.data?.records?.[0]?.clsAccInstNo ?? "",
                    txInstNo: props.data?.records?.[0]?.txInstNo ?? "",
                    txTellerNo: props.data?.records?.[0]?.txTellerNo ?? "",
                    authTellerNo: props.data?.records?.[0]?.authTellerNo ?? "",
                    campTellerNo: props.data?.records?.[0]?.campTellerNo ?? "",
                    settaccUsageClsCd: props.data?.records?.[0]?.settaccUsageClsCd ?? "",
                    othUsageDesc: props.data?.records?.[0]?.othUsageDesc ?? "",
                    hvPsFlag: props.data?.records?.[0]?.hvPsFlag ?? "",
                    jointNaAccFlag: props.data?.records?.[0]?.jointNaAccFlag ?? "",
                    lifeCycFlagCd: props.data?.records?.[0]?.lifeCycFlagCd ?? "",
                    obankFcurrDrawDocEntryNo: props.data?.records?.[0]?.obankFcurrDrawDocEntryNo ?? "",
                    batDealFlag: props.data?.records?.[0]?.batDealFlag ?? "",
                    dealFlag: props.data?.records?.[0]?.dealFlag ?? "",
                    forCounGoveMemApproveInfo: props.data?.records?.[0]?.forCounGoveMemApproveInfo ?? "",
                    orgCertTypeCode: props.data?.records?.[0]?.orgCertTypeCode ?? "",
                    orgCertNo: props.data?.records?.[0]?.orgCertNo ?? "",
                    lastTxDate: props.data?.records?.[0]?.lastTxDate ?? "",
                  } as TbDprgtPersDepLifeCyc;
                }
              }
        }
        
        loading={loading}
        toolBarRender={() => [
          <ProFormRadio.Group
            key="render"
            fieldProps={{
              value: position,
              onChange: (e: any) => setPosition(e.target.value),
            }}
            options={[
              {
                label: "添加到顶部",
                value: "top",
              },
              {
                label: "添加到底部",
                value: "bottom",
              },
              {
                label: "隐藏",
                value: "hidden",
              },
            ]}
          />,
        ]}
        columns={columns}
        request={async (params, sorter, filter) => {
          // 如果正在编辑，直接返回当前数据
          if (isEditing) {
            return {
              data: dataSource,
              total: pagination.total,
              success: true
            };
          }

          setLoading(true);
          try {
            if (
              !props.data ||
              !props.data.records ||
              props.data.records.length === 0
            ) {
              return {
                data: [],
                total: 0,
                success: true,
              };
            }
            const dpmstMedium = props.data.records[0];
            if (!dpmstMedium?.mediumNo || !dpmstMedium?.zoneVal) {
              return {
                data: [],
                total: 0,
                success: true,
              };
            }
            const { current, pageSize } = params;
            const res = await getDprgtPersDepLifeCyc(
              dpmstMedium.mediumNo,
              dpmstMedium.zoneVal,
              "dprgtPersDepLifeCycPageInfo",
              {
                current: current || 1,
                pageSize: pageSize || DEFAULT_PAGE_SIZE,
              }
            );
            if (res.code === 200 && res.data) {
              const detailData = JSON.parse(res.data);
              setDataSource(detailData.records || []);
              setPagination({
                current: detailData.current || 1,
                pageSize: detailData.size || DEFAULT_PAGE_SIZE,
                total: detailData.total || 0,
              });
              return {
                data: detailData.records || [],
                total: detailData.total || 0,
                success: true,
              };
            } else {
              message.error(res.msg || "获取个人存款生命周期失败");
              return {
                data: [],
                total: 0,
                success: false,
              };
            }
          } catch (error) {
            message.error("查询个人存款生命周期失败");
            return {
              data: [],
              total: 0,
              success: false,
            };
          } finally {
            setLoading(false);
          }
        }}
        dataSource={dataSource}
        pagination={{
          ...TABLE_PAGINATION,
          current: pagination.current,
          pageSize: pagination.pageSize,
          total: pagination.total,
          showTotal: (total) => `总共 ${total} 条`,
          onChange: (current, pageSize) => {
            setPagination((prev) => ({ ...prev, current, pageSize }));
          },
        }}
        editable={{
          type: "multiple",
          editableKeys,
          onSave: async (rowKey, data) => {
            console.log("保存数据:", rowKey, data);
            if (data.globalBusiTrackNo === "9999") {
              // 新增数据
              try {
                const newData = {
                  ...data,
                  dtlSeqNo: "9999", // 生成唯一ID
                };
                console.log("保存新增数据:", newData);
                const res = await insertDprgtPersDepLifeCyc(newData);
                if (res.code === 200) {
                  message.success("新增成功");
                  // 新增成功后刷新数据并跳转到最后一页
                  setEditableRowKeys([]);
                  setIsEditing(false);
                  setEditingRecord(null);
                  
                  // 先获取最新的数据总数
                  const dpmstMedium = props.data?.records?.[0];
                  if (dpmstMedium?.mediumNo && dpmstMedium?.zoneVal) {
                    const res = await getDprgtPersDepLifeCyc(
                      dpmstMedium.mediumNo,
                      dpmstMedium.zoneVal,
                      "dprgtPersDepLifeCycPageInfo",
                      {
                        current: 1,
                        pageSize: 1
                      }
                    );
                    
                    if (res.code === 200 && res.data) {
                      const detailData = JSON.parse(res.data);
                      const total = detailData.total || 0;
                      const lastPage = Math.ceil(total / pagination.pageSize);
                      
                      setPagination(prev => ({
                        ...prev,
                        current: lastPage,
                        total: total
                      }));
                    }
                  }
                  
                  await refreshData();
                  return true;
                } else {
                  message.error(res.msg || "新增失败");
                  await refreshData();
                  return false;
                }
              } catch (error) {
                message.error("新增失败");
                return false;
              }
            }

            if (!editingRecord || !rowKey) {
              message.error("编辑记录不存在");
              return false;
            }

            let originalData: TbDprgtPersDepLifeCyc | null = null;
            try {
              // 只上送可编辑字段
              const updateData = {
                enrollDate: data.enrollDate,
                globalBusiTrackNo: data.globalBusiTrackNo,
                subtxNo: data.subtxNo,
                contrAccTpCd: data.contrAccTpCd,
                dpContrTpCd: data.dpContrTpCd,
                execLvlFlagCd: data.execLvlFlagCd,
                statChgReasnCd: data.statChgReasnCd,
                vendibiProdtNo: data.vendibiProdtNo,
                vendibiProdtName: data.vendibiProdtName,
                baseProdtNo: data.baseProdtNo,
                savTypeMclassCode: data.savTypeMclassCode,
                categFlagCd: data.categFlagCd,
                currCode: data.currCode,
                cashExgVatgCd: data.cashExgVatgCd,
                txAmt: data.txAmt,
                termUnitCode: data.termUnitCode,
                deptTerm: data.deptTerm,
                perCertTpCd: data.perCertTpCd,
                personalCertNo: data.personalCertNo,
                lifeCycStaCd: data.lifeCycStaCd,
                chnlKindCode: data.chnlKindCode,
                startSysOrCmptNo: data.startSysOrCmptNo,
                batchNo: data.batchNo,
                openAccInstNo: data.openAccInstNo,
                clsAccInstNo: data.clsAccInstNo,
                txInstNo: data.txInstNo,
                txTellerNo: data.txTellerNo,
                authTellerNo: data.authTellerNo,
                campTellerNo: data.campTellerNo,
                settaccUsageClsCd: data.settaccUsageClsCd,
                othUsageDesc: data.othUsageDesc,
                hvPsFlag: data.hvPsFlag,
                jointNaAccFlag: data.jointNaAccFlag,
                lifeCycFlagCd: data.lifeCycFlagCd,
                obankFcurrDrawDocEntryNo: data.obankFcurrDrawDocEntryNo,
                batDealFlag: data.batDealFlag,
                dealFlag: data.dealFlag,
                forCounGoveMemApproveInfo: data.forCounGoveMemApproveInfo,
                orgCertTypeCode: data.orgCertTypeCode,
                orgCertNo: data.orgCertNo,
                lastTxDate: data.lastTxDate,
              };

              // 保存当前数据
              originalData =
                dataSource.find((item) => item.globalBusiTrackNo === rowKey) || null;

              const res = await dprgtPersDepLifeCycUpdate(updateData);

              if (res.code === 200) {
                // 成功时更新数据
                setDataSource((prevDataSource) =>
                  prevDataSource.map((item) =>
                    item.globalBusiTrackNo === rowKey ? { ...data } : item
                  )
                );
                message.success("保存成功");
              } else {
                // API调用失败，回滚数据
                message.error(res.msg || "保存失败");
                if (originalData) {
                  setDataSource((prevDataSource) => {
                    return prevDataSource.map((item) =>
                      item.globalBusiTrackNo === rowKey ? originalData : item
                    ) as TbDprgtPersDepLifeCyc[];
                  });
                }
              }
            } catch (error) {
              // 发生错误，回滚数据
              message.error("保存失败");
              if (originalData) {
                setDataSource((prevDataSource) => {
                  return prevDataSource.map((item) =>
                    item.globalBusiTrackNo === rowKey ? originalData : item
                  ) as TbDprgtPersDepLifeCyc[];
                });
              }
            } finally {
              // 退出编辑状态
              setEditableRowKeys([]);
              setIsEditing(false);
              setEditingRecord(null);
              if (props.data) {
                try {
                  const dpmstMedium =
                    props.data.records && props.data.records[0];
                  if (
                    dpmstMedium?.mediumNo &&
                    dpmstMedium?.zoneVal
                  ) {
                    setLoading(true);
                    try {
                      const res = await getDprgtPersDepLifeCyc(
                        dpmstMedium.mediumNo,
                        dpmstMedium.zoneVal,
                        "dprgtPersDepLifeCycPageInfo",
                        {
                          current: pagination.current,
                          pageSize: pagination.pageSize,
                        }
                      );
                      if (res.code === 200 && res.data) {
                        const detailData = JSON.parse(res.data);
                        console.log("解析后的个人存款生命周期数据:", detailData);
                        setDataSource(detailData.records || []);
                        setPagination({
                          current:
                            detailData.current || INITIAL_PAGINATION.current,
                          pageSize:
                            detailData.size || INITIAL_PAGINATION.pageSize,
                          total: detailData.total || INITIAL_PAGINATION.total,
                        });
                        setRefresh(refresh + 1);
                      } else {
                        message.error(res.msg || "获取个人存款生命周期失败");
                      }
                    } catch (error) {
                      message.error("查询个人存款生命周期失败");
                    } finally {
                      setLoading(false);
                    }
                  }
                } catch (error) {
                  message.error("查询个人存款生命周期失败");
                }
              }
            }
          },
          onChange: setEditableRowKeys,
          onCancel: async (rowKey) => {
            if (editingRecord) {
              setDataSource((prevDataSource) =>
                prevDataSource.map((item) =>
                  item.globalBusiTrackNo === editingRecord.globalBusiTrackNo ? { ...editingRecord } : item
                )
              );
            }
            // 退出编辑状态
            setEditableRowKeys([]);
            setIsEditing(false);
            setEditingRecord(null);
            return true;
          },
        }}
      />
    </Modal>
  );
};

export default DprgtPersDepLifeCycEdit;
