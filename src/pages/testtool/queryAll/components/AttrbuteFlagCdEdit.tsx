import React, { useState, useEffect } from 'react';
import { Modal, Form, Radio, Space, message } from 'antd';
import type { TbDpmstMedium } from '../data';
import { dpmstMediumUpdate, queryAll } from '../service';
import { flagMeaningsConfig } from './FlagModal';


interface AttrbuteFlagCdEditProps {
  visible: boolean;
  onCancel: () => void;
  record: any;
  mediumNo: string; // 添加介质号属性
  onSuccess: () => void;
}

const AttrbuteFlagCdEdit: React.FC<AttrbuteFlagCdEditProps> = ({
  visible,
  onCancel,
  record,
  mediumNo, // 接收介质号
  onSuccess,
}) => {
//   const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const [editedValues, setEditedValues] = useState<Record<number, string>>({});
  const attrbuteFlagCdConfig = flagMeaningsConfig.attrbuteFlagCd;

//   const [options, setOptions] = useState([
//     { label: '介质状态代码', value: 0 },
//     { label: '挂失状态代码', value: 1 },
//     { label: '激活状态代码', value: 2 },
//     { label: '吞没状态代码', value: 3 },
//     { label: '可疑状态代码', value: 4 },
//     { label: '密码状态代码', value: 5 },
//     { label: '需要更换折/单标志', value: 6 },
//     { label: '已换卡标志', value: 7 },
//   ]);
  const [checkedValues, setCheckedValues] = useState<boolean[]>([]);
  useEffect(() => {
    if (visible && record) {
      const initialValues: Record<number, string> = {};

      if (record.attrbuteFlagCd) {
        const bits = record.attrbuteFlagCd.split('');
        attrbuteFlagCdConfig.forEach(item => {
          initialValues[item.position] = bits[item.position - 1] || '0';
        });
      } else {
        attrbuteFlagCdConfig.forEach(item => {
          initialValues[item.position] = '0';
        });
      }

      setEditedValues(initialValues);
    }
  }, [visible, record]);

  const handleValueChange = (position: number, value: string) => {
    setEditedValues(prev => ({
      ...prev,
      [position]: value
    }));
  };

  const handleOk = async () => {
    setLoading(true);
    try {
      // 生成32位标志码
      const flagArray = new Array(32).fill('0');
      Object.entries(editedValues).forEach(([position, value]) => {
        const index = parseInt(position) - 1;
        // 只对前5位赋值
        if (index >= 0 && index < 5) {
          flagArray[index] = value;
        }
      });

      const attrbuteFlagCd = flagArray.join('');
      const params: TbDpmstMedium = {
        mediumNo: mediumNo, // 使用传入的介质号
        zoneVal: record.zoneVal,
        attrbuteFlagCd,
      };

      const res = await dpmstMediumUpdate(params);
      if (res.code === 200) {
        message.success('更新成功');
        // 调用查询方法重新获取数据
        await queryAll(mediumNo);
        onCancel();
      } else {
        message.error(res.msg || '更新失败');
      }
    } catch (error) {
      message.error('更新失败，请重试');
    } finally {
      setLoading(false);
    }
  };

//   const handleCancel = () => {
//     onCancel();
//   };

//   const handleCheckboxChange = (index: number, checked: boolean) => {
//     const newCheckedValues = [...checkedValues];
//     newCheckedValues[index] = checked;
//     setCheckedValues(newCheckedValues);
//   };
  return (
    <Modal
      title="编辑介质属性标志码"
      open={visible}
      onOk={handleOk}
      onCancel={onCancel}
      confirmLoading={loading}
      width={550}
    >
      <Space direction="vertical" style={{ width: '100%' }} size="large">
        {attrbuteFlagCdConfig.map(item => (
          <Form.Item key={item.key} label={item.title}>
            <Radio.Group
              value={editedValues[item.position]}
              onChange={(e) => handleValueChange(item.position, e.target.value)}
            >
              <Space>
                {item.options.map(option => (
                  <Radio key={option.value} value={option.value}>
                    {option.label}
                  </Radio>
                ))}
              </Space>
            </Radio.Group>
          </Form.Item>
        ))}
      </Space>
    </Modal>
  );
};

export default AttrbuteFlagCdEdit;
