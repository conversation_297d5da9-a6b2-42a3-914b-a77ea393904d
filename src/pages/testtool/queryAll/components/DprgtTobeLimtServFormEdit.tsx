import React, { useEffect, useState } from "react";
import { Modal, message, Button } from "antd";
import {
  updateTobeLimtServ,
  getTobeLimtServ,
  insertTobeLimtServ,
  deleteTobeLimtServ,
} from "../service";
import type { TbDprgtTobeLimtServ, PageResponse } from "../data";
import type { ProColumns } from "@ant-design/pro-components";
import { EditableProTable, ProFormRadio } from "@ant-design/pro-components";

/*
 * @Description: 待限制服务登记簿编辑表单
 * @Author: 自动生成示例
 * @Date: 2025-01-17 10:00:00
 */

interface PaginationType {
  current: number;
  pageSize: number;
  total: number;
}

export type DprgtTobeLimtServFormProps = {
  onCancel: () => void;
  open: boolean;
  data: PageResponse<TbDprgtTobeLimtServ> | null;
};

const DEFAULT_PAGE_SIZE = 10;
const TABLE_SCROLL = { x: 2500 } as const;
const TABLE_PAGINATION = {
  showSizeChanger: true,
  showQuickJumper: true,
} as const;

const MODAL_BODY_STYLE = {
  maxHeight: "70vh",
  overflow: "auto",
  padding: "24px",
  borderRadius: "10px",
} as const;

const MODAL_STYLE = {
  borderRadius: "10px",
  overflow: "hidden",
} as const;

const INITIAL_PAGINATION: PaginationType = {
  current: 1,
  pageSize: DEFAULT_PAGE_SIZE,
  total: 0,
};

const DprgtTobeLimtServFormEdit: React.FC<DprgtTobeLimtServFormProps> = (props) => {
  const [dataSource, setDataSource] = useState<readonly TbDprgtTobeLimtServ[]>([]);
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);
  const [isEditing, setIsEditing] = useState(false);
  const [editingRecord, setEditingRecord] = useState<TbDprgtTobeLimtServ | null>(null);
  const [pagination, setPagination] = useState<PaginationType>(INITIAL_PAGINATION);
  const [loading, setLoading] = useState(false);
  const [key, setKey] = useState<string>(`tobeLimtServ-table-${Date.now()}`);

  // 刷新数据方法
  const refreshData = async () => {
    setLoading(true);

    // 检查数据结构和字段
    let prodtContract = '';
    let zone = '';

    if (props.data && props.data.records && props.data.records.length > 0) {
      const firstRecord = props.data.records[0] as any;
      
      // 直接获取值并保存在变量中
      prodtContract = firstRecord.prodtContractNo || firstRecord.mainContrNo || '';
      zone = firstRecord.zoneVal || '';
    } else {
      console.log('没有记录数据或数据结构不正确');
      setLoading(false);
      return;
    }

    // 确保有需要的参数
    if (!prodtContract || !zone) {
      console.error('缺少必要参数: 产品合约编号或分片值');
      message.error('获取待限制服务信息失败: 缺少必要参数');
      setLoading(false);
      return;
    }

    try {
      // 发送请求
      const res = await getTobeLimtServ(
        prodtContract,
        zone,
        "tobeLimtServPageInfo",
        {
          current: pagination.current || 1,
          pageSize: pagination.pageSize || 10
        }
      );
    

      // 解析和验证响应
      if (res && res.code === 200 && res.data) {
        // 尝试解析数据
        let parsedData;
        try {
          parsedData = typeof res.data === 'string' ? JSON.parse(res.data) : res.data;
        } catch (parseError) {
          console.error('数据解析错误:', parseError);
          message.error('数据格式错误');
          setDataSource([]);
          setLoading(false);
          return;
        }

        // 确保每条记录都有唯一ID
        if (parsedData && Array.isArray(parsedData.records)) {
          const processedData = parsedData.records.map((item: any, index: number) => {
            return { ...item, key: item.custNo || item.id || item.sysId || `${index}-${Date.now()}` };
          });
          
          setDataSource(processedData);
          
          // 更新分页信息
          setPagination({
            ...pagination,
            current: parsedData.current || 1,
            pageSize: parsedData.size || 10,
            total: parsedData.total || 0,
          });
        } else {
          console.error('API响应格式不正确:', parsedData);
          message.error('获取待限制服务信息失败: 响应格式不正确');
          setDataSource([]);
        }
      } else {
        console.error('API响应异常:', res);
        message.error(res.msg || '获取待限制服务信息失败');
        setDataSource([]);
      }
    } catch (error) {
      console.error('获取待限制服务数据出错:', error);
      message.error('获取待限制服务信息失败');
      setDataSource([]);
    } finally {
      setLoading(false);
    }
  };

  // 初始化数据
  useEffect(() => {
    let isMounted = true;

    const initData = async () => {
      if (props.open && props.data) {
        const { records, current, size, total } = props.data;
        
        // 确保记录有唯一标识
        const processedRecords = (records || []).map((record, index) => ({
          ...record,
          key: record.custNo || `record-${index}`,
        }));
        
        
        if (isMounted) {
          setDataSource(processedRecords);
          setPagination({
            current: current || INITIAL_PAGINATION.current,
            pageSize: size || INITIAL_PAGINATION.pageSize,
            total: total || INITIAL_PAGINATION.total,
          });
          
          // 强制重新渲染表格
          setKey(`tobeLimtServ-table-${Date.now()}`);
        }
      } else if (isMounted) {
        setDataSource([]);
        setPagination(INITIAL_PAGINATION);
      }
    };

    initData();
    return () => {
      isMounted = false;
    };
  }, [props.open, props.data]);

  useEffect(() => {
    if (props.open) {
      
      // 检查数据结构和字段
      if (props.data && props.data.records && props.data.records.length > 0) {
        const firstRecord = props.data.records[0];
        
        // 使用try-catch避免访问不存在的属性导致的错误
        try {
          
          // 检查数据可能存在于其他字段
          for (const key in firstRecord) {
            if (typeof (firstRecord as any)[key] === 'string' && 
                (key.toLowerCase().includes('contr') || key.toLowerCase().includes('contract'))) {
              console.log(`合约编号字段 ${key}:`, (firstRecord as any)[key]);
            }
          }
        } catch (error) {
          console.error('访问记录字段时出错:', error);
        }
      } else {
        console.log('没有记录数据或数据结构不正确');
      }
    }
  }, [props.open, props.data]);

  // 记录新增位置选择
  const [position, setPosition] = useState<"top" | "bottom" | "hidden">("bottom");

  // 定义表格列
  const columns: ProColumns<TbDprgtTobeLimtServ>[] = [
    {
      title: "登记日期",
      dataIndex: "enrollDate",
      width: 120,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "date",
      render: (_, entity) => entity.enrollDate || "-",
    },
    {
      title: "全局业务跟踪号",
      dataIndex: "globalBusiTrackNo",
      width: 280,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "子交易序号",
      dataIndex: "subtxNo",
      width: 280,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "产品合约编号",
      dataIndex: "prodtContractNo",
      width: 200,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "登记时间",
      dataIndex: "enrollTime",
      width: 160,
      ellipsis: true,
      align: "center",
      editable: () => true,
      render: (_, entity) => entity.enrollTime || "-",
    },
    {
      title: "限制类型代码",
      dataIndex: "limtTpCd",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "select",
      valueEnum: {
        "10": { text: "10-停用" },
        "20": { text: "20-中止" },
        "30": { text: "30-暂停非柜面" },
        "40": { text: "40-长期不动户" },
        "50": { text: "50-非柜面渠道专项管控" },
      },
    },
    {
      title: "介质编号",
      dataIndex: "mediumNo",
      width: 200,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "类别标识代码",
      dataIndex: "categFlagCd",
      width: 120,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "select",
      valueEnum: {
        "01": { text: "01-活期" },
        "02": { text: "02-定期" },
      },
    },
    {
      title: "客户编号",
      dataIndex: "custNo",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "客户名称",
      dataIndex: "custNm",
      width: 180,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "个人证件类型代码",
      dataIndex: "perCertTpCd",
      width: 160,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "select",
      valueEnum: {
        "1010": { text: "1010-居民身份证" },
        "1011": { text: "1011-临时居民身份证" },
        "1020": { text: "1020-军人身份证件" },
        "1021": { text: "1021-士兵证" },
        "1022": { text: "1022-军官证" },
        "1023": { text: "1023-文职干部证" },
        "1024": { text: "1024-军官退休证" },
        "1025": { text: "1025-文职干部退休证" },
        "1030": { text: "1030-武警身份证件" },
        "1031": { text: "1031-武警士兵证" },
        "1032": { text: "1032-警官证" },
        "1033": { text: "1033-武警文职干部证" },
        "1034": { text: "1034-武警军官退休证" },
        "1035": { text: "1035-武警文职干部退休证" },
        "1040": { text: "1040-户口簿" },
        "1050": { text: "1050-中国护照" },
        "1051": { text: "1051-外国护照" },
        "1060": { text: "1060-学生证" },
        "1070": { text: "1070-港澳居民来往内地通行证" },
        "1071": { text: "1071-往来港澳通行证" },
        "1080": { text: "1080-台湾居民来往大陆通行证" },
        "1090": { text: "1090-执行公务证" },
        "1100": { text: "1100-机动车驾驶证" },
        "1110": { text: "1110-社会保障卡" },
        "1120": { text: "1120-外国人居留证" },
        "1121": { text: "1121-外国人永久居留证" },
        "1130": { text: "1130-旅行证件" },
        "1140": { text: "1140-香港居民身份证" },
        "1150": { text: "1150-澳门居民身份证" },
        "1160": { text: "1160-台湾居民身份证" },
        "1170": { text: "1170-边民证" },
        "1180": { text: "1180-港澳台居民居住证" },
        "1181": { text: "1181-港澳居民居住证" },
        "1182": { text: "1182-台湾居民居住证" },
        "1190": { text: "1190-外国身份证" },
        "1998": { text: "1998-其他（原98类）" },
        "1999": { text: "1999-其他证件（个人）" },
      },
    },
    {
      title: "个人证件号码",
      dataIndex: "personalCertNo",
      width: 180,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "开户日期",
      dataIndex: "openaccDate",
      width: 120,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "date",
      render: (_, entity) => entity.openaccDate || "-",
    },
    {
      title: "开户机构号",
      dataIndex: "openAccInstNo",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "交易机构号",
      dataIndex: "txInstNo",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "交易柜员号",
      dataIndex: "txTellerNo",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "授权柜员号",
      dataIndex: "authTellerNo",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "渠道种类代码",
      dataIndex: "chnlKindCode",
      width: 160,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "select",
      valueEnum: {
        '01': { text: '01-网点柜面' },
        '10': { text: '10-网上银行' },
        '12': { text: '12-个人网银' },
        '13': { text: '13-电视银行' },
        '14': { text: '14-电话银行' },
        '15': { text: '15-手机银行' },
        '16': { text: '16-企业网银' },
        '17': { text: '17-自助设备' },
        '18': { text: '18-POS' },
        '20': { text: '20-超级网银' },
        '21': { text: '21-大小额支付' },
        '22': { text: '22-银联前置' },
        '24': { text: '24-管理端' },
        '25': { text: '25-交易端' },
        '26': { text: '26-商易通' },
        '27': { text: '27-助农通' },
        '29': { text: '29-外部系统' },
        '30': { text: '30-系统自动' },
        '31': { text: '31-电子汇兑系统' },
        '32': { text: '32-理财规划终端' },
        '34': { text: '34-网汇通' },
        '35': { text: '35-同城支付' },
        '36': { text: '36-移动终端-TSM（可信服务管理）' },
        '37': { text: '37-移动终端-移动展业' },
        '38': { text: '38-直销银行' },
        '39': { text: '39-短信' },
        '40': { text: '40-专属APP' },
        '41': { text: '41-第三方线上渠道' },
        '43': { text: '43-智能柜员机（ITM）' },
        '42': { text: '42-国际支付前置' },
        '44': { text: '44-邮储经营' },
        '45': { text: '45-银银前置系统' },
        '46': { text: '46-U链供应链' },
        '47': { text: '47-油料保障结算系统' },
        '48': { text: '48-银企直联' },
        '91': { text: '91-微信银行' },
        '99': { text: '99-其他' },
      },
    },
    {
      title: "发起系统或组件编码",
      dataIndex: "startSysOrCmptNo",
      width: 180,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "限制原因代码",
      dataIndex: "limtReasnCd",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "select",
      valueEnum: {
        "1001": { text: "1001-冒名开立账户停用" },
        "1002": { text: "1002-有权机关停用" },
        "1003": { text: "1003-本人申请停用" },
        "1004": { text: "1004-A涉案账户停用" },
        "1005": { text: "1005-反洗钱账户停用" },
        "1006": { text: "1006-可疑风险账户停用" },
        "1007": { text: "1007-B涉案账户停用（反欺诈系统）" },
        "1008": { text: "1008-C涉案账户停用（反洗钱系统停用）" },
        "1009": { text: "1009-客户III类账户双边收付达5万7天未核实" },
        "1010": { text: "1010-假名匿名" },
        "1011": { text: "1011-账户/卡数量超限停用" },
        "1012": { text: "1012-可疑风险账户停用(行内事中模型)" },
        "1013": { text: "1013-假名匿名" },
        "2001": { text: "2001-客户信息不符合要求" },
        "3001": { text: "3001-A涉案客户名下其他账户" },
        "3002": { text: "3002-惩戒客户" },
        "3003": { text: "3003-开户6个月无交易" },
        "3004": { text: "3004-电话号码对应客户有误" },
        "3005": { text: "3005-存量无法核实" },
        "3006": { text: "3006-客户信息不合规" },
        "3007": { text: "3007-退役部门发起暂停" },
        "3008": { text: "3008-账户/卡数量超限" },
        "3009": { text: "3009-B涉案客户名下其他账户（反欺诈系统）" },
        "3010": { text: "3010-C涉案客户名下其他账户（反洗钱系统）" },
        "3011": { text: "3011-惩戒账户暂停（非法买卖的账户）" },
        "3012": { text: "3012-可疑风险账户" },
        "3013": { text: "3013-可疑风险账户（灰名单）" },
        "3014": { text: "3014-反洗钱账户暂停" },
        "3015": { text: "3015-虚拟货币暂停" },
        "3016": { text: "3016-不活跃账户" },
        "3017": { text: "3017-可疑风险账户(行内事中模型)" },
        "3018": { text: "3018-客户信息不全或不符暂非" },
        "3019": { text: "3019-落实当地监管要求(暂停非柜面)" },
        "3023": { text: "3023-疑似受害人(行内事中模型)" },
        "4001": { text: "4001-长期不动户（指客户账户三年无交易且账户余额小于等于10元时，判断为不活跃账户）" },
        "5001": { text: "5001-监管、联席办或政府文件要求" },
        "5002": { text: "5002-可疑风险账户管控(非柜面渠道专项管控)" },
        "5003": { text: "5003-批量重新约定限额" },
        "5004": { text: "5004-客户临柜设置" },
        "5005": { text: "5005-临时调额" },
        "5006": { text: "5006-VIP客户线上提额" },
        "5007": { text: "5007-白名单账户提额" },
        "5008": { text: "5008-客户线上预约提额" },
        "6001": { text: "6001-存在洗钱风险" },
        "9999": { text: "9999-其他" },
      },
    },
    {
      title: "限制执行文号",
      dataIndex: "limtFileNo",
      width: 180,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "限制有权机关名称",
      dataIndex: "limtPowInstName",
      width: 200,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "预计限制日期",
      dataIndex: "expectLimtDt",
      width: 120,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "date",
      render: (_, entity) => entity.expectLimtDt || "-",
    },
    {
      title: "当前状态代码",
      dataIndex: "curStatusCode",
      width: 120,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "select",
      valueEnum: {
        "1": { text: "1-已处理" },
        "2": { text: "2-未处理" },
        "4": { text: "4-已撤销" },
      },
    },
    {
      title: "变更渠道种类代码",
      dataIndex: "chgChnlKindCode",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "select",
      valueEnum: {
        '01': { text: '01-网点柜面' },
        '10': { text: '10-网上银行' },
        '12': { text: '12-个人网银' },
        '13': { text: '13-电视银行' },
        '14': { text: '14-电话银行' },
        '15': { text: '15-手机银行' },
        '16': { text: '16-企业网银' },
        '17': { text: '17-自助设备' },
        '18': { text: '18-POS' },
        '20': { text: '20-超级网银' },
        '21': { text: '21-大小额支付' },
        '22': { text: '22-银联前置' },
        '24': { text: '24-管理端' },
        '25': { text: '25-交易端' },
        '26': { text: '26-商易通' },
        '27': { text: '27-助农通' },
        '29': { text: '29-外部系统' },
        '30': { text: '30-系统自动' },
        '31': { text: '31-电子汇兑系统' },
        '32': { text: '32-理财规划终端' },
        '34': { text: '34-网汇通' },
        '35': { text: '35-同城支付' },
        '36': { text: '36-移动终端-TSM（可信服务管理）' },
        '37': { text: '37-移动终端-移动展业' },
        '38': { text: '38-直销银行' },
        '39': { text: '39-短信' },
        '40': { text: '40-专属APP' },
        '41': { text: '41-第三方线上渠道' },
        '43': { text: '43-智能柜员机（ITM）' },
        '42': { text: '42-国际支付前置' },
        '44': { text: '44-邮储经营' },
        '45': { text: '45-银银前置系统' },
        '46': { text: '46-U链供应链' },
        '47': { text: '47-油料保障结算系统' },
        '48': { text: '48-银企直联' },
        '91': { text: '91-微信银行' },
        '99': { text: '99-其他' },
      },
    },
    {
      title: "变更系统编码",
      dataIndex: "chgSysNo",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "解除日期",
      dataIndex: "relsDt",
      width: 120,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "date",
      render: (_, entity) => entity.relsDt || "-",
    },
    {
      title: "解除时间",
      dataIndex: "relsTime",
      width: 160,
      ellipsis: true,
      align: "center",
      editable: () => true,
      render: (_, entity) => entity.relsTime || "-",
    },
    {
      title: "解除全局业务跟踪号",
      dataIndex: "relsGloTracNo",
      width: 280,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "解除子交易序号",
      dataIndex: "relsSubtxNo",
      width: 280,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "解除执行文号",
      dataIndex: "relsFileNo",
      width: 180,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "解除有权机关名称",
      dataIndex: "relsPowInstName",
      width: 200,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "解除限制原因代码",
      dataIndex: "relsLimtRe",
      width: 160,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "select",
      valueEnum: {
        "01": { text: "01-客户申请解除" },
        "02": { text: "02-银行申请解除" },
      },
    },
    {
      title: "最后交易日期",
      dataIndex: "lastTxDate",
      width: 120,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "date",
      render: (_, entity) => entity.lastTxDate || "-",
    },
    {
      title: "记录状态代码",
      dataIndex: "recordStaCd",
      width: 120,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "select",
      valueEnum: {
        "0": { text: "0-无效" },
        "1": { text: "1-有效" },
      },
    },
    {
      title: "分片值",
      dataIndex: "zoneVal",
      width: 160,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "操作",
      valueType: "option",
      width: 150,
      // fixed: "right",
      align: "center",
      render: (text, record: TbDprgtTobeLimtServ, _, action) => {
        const isRecordEditing = record.custNo
          ? editableKeys.includes(record.custNo)
          : false;
        return [
          isRecordEditing ? (
            <>
              <Button
                key="confirm"
                type="link"
                onClick={async () => {
                  try {
                    if (record.custNo) {
                      await action?.saveEditable?.(record.custNo);
                      setIsEditing(false);
                    }
                  } catch (err) {
                    console.error("保存失败:", err);
                  }
                }}
              >
                确定
              </Button>
              <Button
                key="cancel"
                type="link"
                onClick={() => {
                  if (record.custNo) {
                    action?.cancelEditable?.(record.custNo);
                    setEditableRowKeys([]);
                    setIsEditing(false);
                  }
                }}
                style={{ marginLeft: 8 }}
              >
                取消
              </Button>
            </>
          ) : (
            <Button
              key="editable"
              type="link"
              disabled={true}
              onClick={async () => {
                if (!record.custNo) return;
                const originalRecord = { ...record };
                setEditingRecord(originalRecord);
                setIsEditing(true);
                await action?.startEditable?.(record.custNo);
              }}
            >
              编辑
            </Button>
          ),
          <Button
            key="delete"
            type="link"
            danger
            disabled={true}
            onClick={async () => {
              if (!record.custNo) return;
              Modal.confirm({
                title: "确认删除",
                content: "确定要删除这条记录吗？",
                onOk: async () => {
                  try {
                    const res = await deleteTobeLimtServ(record);
                    if (res.code === 200) {
                      message.success("删除成功");
                    } else {
                      message.error(res.msg || "删除失败");
                    }
                  } catch (error) {
                    message.error("删除失败");
                  } finally {
                    await refreshData();
                  }
                },
                okText: "确认",
                cancelText: "取消",
              });
            }}
            style={{ marginLeft: 8 }}
          >
            删除
          </Button>,
        ];
      },
    },
  ];

  return (
    <Modal
      width={1200}
      title="查询待限制服务登记簿信息"
      open={props.open}
      destroyOnClose
      onCancel={props.onCancel}
      footer={null}
      bodyStyle={MODAL_BODY_STYLE}
      style={MODAL_STYLE}
    >
      <div style={{ marginBottom: 16 }}>
        <p>数据状态: {loading ? '加载中' : `已加载 ${dataSource.length} 条记录`}</p>
      </div>

      <EditableProTable<TbDprgtTobeLimtServ>
        key={key}
        rowKey="custNo"
        headerTitle="待限制服务登记簿信息"
        scroll={TABLE_SCROLL}
        recordCreatorProps={position === "hidden" ? false : {
          position: position,
          creatorButtonText: "新增一行待限制服务登记信息",
          onClick: async () => {
            const startIndex = (pagination.current - 1) * pagination.pageSize;
            const endIndex = startIndex + pagination.pageSize;
            const currentPageDataCount = Math.min(
              pagination.pageSize,
              pagination.total - (pagination.current - 1) * pagination.pageSize
            );
            if (pagination.current !== 1 && currentPageDataCount >= pagination.pageSize) {
              Modal.error({
                title: "提示",
                content: "当前页面数据已满，请切换至第一页或者未满的页面进行新增",
              });
              return false;
            }
            
            // 获取当前日期
            const today = new Date();
            const formattedDate = today.toISOString().split("T")[0];
            
            // 使用类型断言安全地获取值
            const contractNo = (props.data?.records?.[0] as any)?.prodtContractNo || '';
            const zoneVal = (props.data?.records?.[0] as any)?.zoneVal || '';
            const mediumNo = (props.data?.records?.[0] as any)?.mediumNo || '';
            
            // 构建新记录，注意各字段默认值可按需要调整
            const newRecord = {
              enrollDate: formattedDate,
              globalBusiTrackNo: "",
              subtxNo: "",
              prodtContractNo: contractNo,
              enrollTime: today.toISOString().substr(0, 14),
              limtTpCd: "10",
              mediumNo: mediumNo,
              categFlagCd: "01",
              custNo: "NEW", // 新增记录标识，由后端生成唯一ID
              custNm: "",
              perCertTpCd: "1010",
              personalCertNo: "",
              openaccDate: formattedDate,
              openAccInstNo: "",
              txInstNo: "",
              txTellerNo: "",
              authTellerNo: "",
              chnlKindCode: "01",
              startSysOrCmptNo: "",
              limtReasnCd: "",
              limtFileNo: "",
              limtPowInstName: "",
              expectLimtDt: formattedDate,
              curStatusCode: "2",
              chgChnlKindCode: "",
              chgSysNo: "",
              relsDt: "",
              relsTime: "",
              relsGloTracNo: "",
              relsSubtxNo: "",
              relsFileNo: "",
              relsPowInstName: "",
              relsLimtRe: "",
              lastTxDate: formattedDate,
              recordStaCd: "1",
              zoneVal: zoneVal,
            };
            
            const newDataSource = [...dataSource];
            newDataSource.splice(endIndex, 0, newRecord);
            setDataSource(newDataSource);
            setIsEditing(true);
            setEditableRowKeys(["NEW"]);
          },
          record: () => {
            const today = new Date();
            const formattedDate = today.toISOString().split("T")[0];
            
            // 使用类型断言安全地获取值
            const contractNo = (props.data?.records?.[0] as any)?.prodtContractNo || '';
            const zoneVal = (props.data?.records?.[0] as any)?.zoneVal || '';
            const mediumNo = (props.data?.records?.[0] as any)?.mediumNo || '';
            
            return {
              enrollDate: formattedDate,
              globalBusiTrackNo: "",
              subtxNo: "",
              prodtContractNo: contractNo,
              enrollTime: today.toISOString().substr(0, 14),
              limtTpCd: "10",
              mediumNo: mediumNo,
              categFlagCd: "01",
              custNo: "NEW", // 新增记录标识，由后端生成唯一ID
              custNm: "",
              perCertTpCd: "1010",
              personalCertNo: "",
              openaccDate: formattedDate,
              openAccInstNo: "",
              txInstNo: "",
              txTellerNo: "",
              authTellerNo: "",
              chnlKindCode: "01",
              startSysOrCmptNo: "",
              limtReasnCd: "",
              limtFileNo: "",
              limtPowInstName: "",
              expectLimtDt: formattedDate,
              curStatusCode: "2",
              chgChnlKindCode: "",
              chgSysNo: "",
              relsDt: "",
              relsTime: "",
              relsGloTracNo: "",
              relsSubtxNo: "",
              relsFileNo: "",
              relsPowInstName: "",
              relsLimtRe: "",
              lastTxDate: formattedDate,
              recordStaCd: "1",
              zoneVal: zoneVal,
            } as TbDprgtTobeLimtServ;
          },
        }}
        loading={loading}
        toolBarRender={() => [
          <ProFormRadio.Group
            key="render"
            fieldProps={{
              value: position,
              onChange: (e: any) => setPosition(e.target.value),
            }}
            options={[
              { label: "添加到顶部", value: "top" },
              { label: "添加到底部", value: "bottom" },
              { label: "隐藏", value: "hidden" },
            ]}
          />,
        ]}
        columns={columns}
        request={async (params, sorter, filter) => {
          if (isEditing) {
            return {
              data: dataSource,
              total: pagination.total,
              success: true,
            };
          }

          setLoading(true);
          try {
            if (!props.data || !props.data.records || props.data.records.length === 0) {
              console.log('没有初始数据，返回空数组');
              return { data: [], total: 0, success: true };
            }
            
            const record = props.data.records[0];
            // 使用类型断言访问可能不在类型定义中的字段
            const contractNo = (record as any).prodtContractNo || (record as any).mainContrNo || '';
            const zoneVal = (record as any).zoneVal || '';
            
            if (!contractNo || !zoneVal) {
              console.log('缺少查询参数', { record, contractNo, zoneVal });
              message.error('缺少必要的查询参数：产品合约编号或分片值');
              return { data: [], total: 0, success: true };
            }
            
            const { current, pageSize } = params;
            const res = await getTobeLimtServ(
              contractNo,
              zoneVal,
              "tobeLimtServPageInfo",
              {
                current: current || 1,
                pageSize: pageSize || DEFAULT_PAGE_SIZE,
              }
            );

            if (res?.code === 200 && res.data) {
              let resData;
              try {
                resData = typeof res.data === 'string' ? JSON.parse(res.data) : res.data;
              } catch (error) {
                console.error('解析数据失败:', error);
                return { data: [], total: 0, success: false };
              }

              // 处理数据
              if (resData && Array.isArray(resData.records)) {
                const processedData = resData.records.map((item: any, index: number) => ({
                  ...item,
                  key: item.custNo || item.id || item.sysId || `${index}-${Date.now()}`
                }));
                
                return {
                  data: processedData,
                  total: resData.total || 0,
                  success: true,
                };
              }
            }
            
            message.error(res?.msg || '获取数据失败');
            return { data: [], total: 0, success: false };
          } catch (error) {
            console.error("数据加载错误:", error);
            message.error("查询待限制服务登记簿信息失败");
            return { data: [], total: 0, success: false };
          } finally {
            setLoading(false);
          }
        }}
        dataSource={dataSource}
        pagination={{
          ...TABLE_PAGINATION,
          current: pagination.current,
          pageSize: pagination.pageSize,
          total: pagination.total,
          showTotal: (total) => `总共 ${total} 条`,
          onChange: (current, pageSize) => {
            setPagination((prev) => ({ ...prev, current, pageSize }));
          },
        }}
        editable={{
          type: "multiple",
          editableKeys,
          onChange: setEditableRowKeys,
          onSave: async (rowKey, data) => {
            if (data.custNo === "NEW") {
              try {
                const res = await insertTobeLimtServ(data);
                if (res.code === 200) {
                  message.success("新增成功");
                  setEditableRowKeys([]);
                  setIsEditing(false);
                  setEditingRecord(null);
                  // 刷新数据并跳转到最后一页
                  const record = props.data?.records?.[0];
                  if (record?.prodtContractNo && record?.zoneVal) {
                    const res = await getTobeLimtServ(
                      record.prodtContractNo,
                      record.zoneVal,
                      "tobeLimtServPageInfo",
                      { current: 1, pageSize: 1 }
                    );
                    if (res.code === 200 && res.data) {
                      const detailData = JSON.parse(res.data);
                      const total = detailData.total || 0;
                      const lastPage = Math.ceil(total / pagination.pageSize);
                      setPagination((prev) => ({ ...prev, current: lastPage, total }));
                    }
                  }
                  await refreshData();
                  return true;
                } else {
                  message.error(res.msg || "新增失败");
                  await refreshData();
                  return false;
                }
              } catch (error) {
                message.error("新增失败");
                return false;
              }
            }

            if (!editingRecord || !rowKey) {
              message.error("编辑记录不存在");
              return false;
            }
            let originalData: TbDprgtTobeLimtServ | null = null;
            try {
              originalData = dataSource.find((item) => item.custNo === rowKey) || null;
              const res = await updateTobeLimtServ(data);
              if (res.code === 200) {
                setDataSource((prev) =>
                  prev.map((item) =>
                    item.custNo === rowKey ? { ...data } : item
                  )
                );
                message.success("保存成功");
              } else {
                message.error(res.msg || "保存失败");
                if (originalData) {
                  setDataSource((prev) =>
                    prev.map((item) =>
                      item.custNo === rowKey ? originalData : item
                    ) as TbDprgtTobeLimtServ[]
                  );
                }
              }
            } catch (error) {
              message.error("保存失败");
              if (originalData) {
                setDataSource((prev) =>
                  prev.map((item) =>
                    item.custNo === rowKey ? originalData : item
                  ) as TbDprgtTobeLimtServ[]
                );
              }
            } finally {
              setEditableRowKeys([]);
              setIsEditing(false);
              setEditingRecord(null);
              await refreshData();
            }
          },
          onCancel: async (rowKey) => {
            if (editingRecord) {
              setDataSource((prevDataSource) =>
                prevDataSource.map((item) =>
                  item.custNo === editingRecord.custNo
                    ? { ...editingRecord }
                    : item
                )
              );
            }
            setEditableRowKeys([]);
            setIsEditing(false);
            setEditingRecord(null);
            return true; // 返回成功状态
          }
        }}
      />
    </Modal>
  );
};

export default DprgtTobeLimtServFormEdit;


