import React, { useEffect, useState } from "react";
import { Modal, message, Button } from "antd";
import {
  dprgtChgCardUpdate,
  getDprgtChgCard,
  insertDprgtChgCard,
  dprgtChgCardDelete,
} from "../service";
import type { DprgtChgCard, PageResponse } from "../data";
import type { ProColumns } from "@ant-design/pro-components";
import { EditableProTable, ProFormRadio } from "@ant-design/pro-components";

/*
 * @Description: 换卡登记簿编辑表单
 * @Author: 自动生成示例
 * @Date: 2025-01-17 10:00:00
 */

interface PaginationType {
  current: number;
  pageSize: number;
  total: number;
}

export type DprgtChgCardFormProps = {
  onCancel: () => void;
  open: boolean;
  data: PageResponse<DprgtChgCard> | null;
};

const DEFAULT_PAGE_SIZE = 10;
const TABLE_SCROLL = { x: 2000 } as const;
const TABLE_PAGINATION = {
  showSizeChanger: true,
  showQuickJumper: true,
} as const;

const MODAL_BODY_STYLE = {
  maxHeight: "70vh",
  overflow: "auto",
  padding: "24px",
  borderRadius: "10px",
} as const;

const MODAL_STYLE = {
  borderRadius: "10px",
  overflow: "hidden",
} as const;

const INITIAL_PAGINATION: PaginationType = {
  current: 1,
  pageSize: DEFAULT_PAGE_SIZE,
  total: 0,
};

const DprgtChgCardFormEdit: React.FC<DprgtChgCardFormProps> = (props) => {
  const [dataSource, setDataSource] = useState<readonly DprgtChgCard[]>([]);
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);
  const [isEditing, setIsEditing] = useState(false);
  const [editingRecord, setEditingRecord] = useState<DprgtChgCard | null>(null);
  const [pagination, setPagination] = useState<PaginationType>(INITIAL_PAGINATION);
  const [loading, setLoading] = useState(false);
  const [refresh, setRefresh] = useState(0);

  // 刷新数据方法
  const refreshData = async () => {
    if (!props.data?.records?.[0]) {
      return;
    }

    const chgCardRecord = props.data.records[0];
    if (!chgCardRecord?.mainContrNo || !chgCardRecord?.zoneVal) {
      console.log('缺少必要的查询参数');
      return;
    }

    setLoading(true);
    try {
      const res = await getDprgtChgCard(
        chgCardRecord.mainContrNo,
        chgCardRecord.zoneVal,
        "chgcardPageInfo",
        {
          current: pagination.current,
          pageSize: pagination.pageSize,
        }
      );
      
      if (res.code === 200 && res.data) {
        let detailData;
        try {
          detailData = typeof res.data === 'string' ? JSON.parse(res.data) : res.data;
        } catch (parseError) {
          message.error('数据格式错误');
          return;
        }

        // 更新数据源和分页信息
        setDataSource(detailData.records || []);
        setPagination({
          current: detailData.current || INITIAL_PAGINATION.current,
          pageSize: detailData.size || INITIAL_PAGINATION.pageSize,
          total: detailData.total || INITIAL_PAGINATION.total,
        });
        setRefresh(r => r + 1);
      } else {
        message.error(res.msg || "获取换卡信息失败");
      }
    } catch (error) {
      message.error("查询换卡信息失败");
    } finally {
      setLoading(false);
    }
  };

  // 初始化数据
  useEffect(() => {
    let isMounted = true;

    const initData = async () => {
      console.log('初始化数据, props.open:', props.open, 'props.data:', props.data);
      if (props.open && props.data) {
        const { records, current, size, total } = props.data;
        if (isMounted) {
          setDataSource(records || []);
          setPagination({
            current: current || INITIAL_PAGINATION.current,
            pageSize: size || INITIAL_PAGINATION.pageSize,
            total: total || INITIAL_PAGINATION.total,
          });
        }
      } else if (isMounted) {
        setDataSource([]);
        setPagination(INITIAL_PAGINATION);
      }
    };

    initData();
    return () => {
      isMounted = false;
    };
  }, [props.open, props.data]);

  // 记录新增位置选择
  const [position, setPosition] = useState<"top" | "bottom" | "hidden">("bottom");

  // 定义表格列
  const columns: ProColumns<DprgtChgCard>[] = [
    {
      title: "卡号",
      dataIndex: "cardNo",
      width: 180,
      // fixed: true,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "新卡卡号",
      dataIndex: "newCardCardNo",
      width: 180,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "主合约编号",
      dataIndex: "mainContrNo",
      width: 200,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "登记日期",
      dataIndex: "enrollDate",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "date",
      render: (_, entity) => entity.enrollDate || "-",
    },
    {
      title: "全局业务跟踪号",
      dataIndex: "globalBusiTrackNo",
      width: 240,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "子交易序号",
      dataIndex: "subtxNo",
      width: 240,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "变更机构号",
      dataIndex: "chgInstNo",
      width: 180,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "变更柜员号",
      dataIndex: "chgTellerNo",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "授权柜员号",
      dataIndex: "authTellerNo",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "交易时间",
      dataIndex: "txTime",
      width: 160,
      ellipsis: true,
      align: "center",
      editable: () => true,
      render: (_, entity) => entity.txTime || "-",
    },
    {
      title: "关联介质状态标志码",
      dataIndex: "relMedmStaFlagCd",
      width: 180,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "是否有效标志",
      dataIndex: "validFlag",
      width: 120,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "select",
      valueEnum: {
        "0": { text: "0-否" },
        "1": { text: "1-是" },
      },
    },
    {
      title: "最后交易日期",
      dataIndex: "lastTxDate",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "date",
      render: (_, entity) => entity.lastTxDate || "-",
    },
    {
      title: "记录状态代码",
      dataIndex: "recordStaCd",
      width: 120,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "select",
      valueEnum: {
        "0": { text: "0-无效" },
        "1": { text: "1-有效" },
      },
    },
    {
      title: "分片值",
      dataIndex: "zoneVal",
      width: 120,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "创建时间戳",
      dataIndex: "createStamp",
      width: 180,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "最后修改时间戳",
      dataIndex: "lastModStamp",
      width: 180,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "操作",
      valueType: "option",
      width: 150,
      // fixed: "right",
      align: "center",
      render: (text, record: DprgtChgCard, _, action) => {
        const isRecordEditing = record.cardNo
          ? editableKeys.includes(record.cardNo)
          : false;
        return [
          isRecordEditing ? (
            <>
              <a
                key="confirm"
                onClick={async () => {
                  try {
                    if (record.cardNo) {
                      await action?.saveEditable?.(record.cardNo);
                      setIsEditing(false);
                    }
                  } catch (err) {
                    console.error("保存失败:", err);
                  }
                }}
              >
                确定
              </a>
              <a
                key="cancel"
                onClick={() => {
                  if (record.cardNo) {
                    action?.cancelEditable?.(record.cardNo);
                    setEditableRowKeys([]);
                    setIsEditing(false);
                  }
                }}
                style={{ marginLeft: 8 }}
              >
                取消
              </a>
            </>
          ) : (
            <Button
              key="editable"
              type="link"
              disabled={true}
              onClick={async () => {
                if (!record.cardNo) return;
                const originalRecord = { ...record };
                setEditingRecord(originalRecord);
                setIsEditing(true);
                await action?.startEditable?.(record.cardNo);
              }}
              style={{ padding: 0 }}
            >
              编辑
            </Button>
          ),
          <Button
            key="delete"
            type="link"
            // disabled={isEditing}
            disabled={true}
            onClick={async () => {
              if (!record.cardNo) return;
              Modal.confirm({
                title: "确认删除",
                content: "确定要删除这条记录吗？",
                onOk: async () => {
                  try {
                    const res = await dprgtChgCardDelete(record);
                    if (res.code === 200) {
                      message.success("删除成功");
                    } else {
                      message.error(res.msg || "删除失败");
                    }
                  } catch (error) {
                    message.error("删除失败");
                  } finally {
                    await refreshData();
                  }
                },
                okText: "确认",
                cancelText: "取消",
              });
            }}
            style={{ marginLeft: 8 }}
          >
            删除
          </Button>,
        ];
      },
    },
  ];

  return (
    <Modal
      width={1200}
      title="查询换卡登记簿信息"
      open={props.open}
      destroyOnClose
      onCancel={props.onCancel}
      footer={null}
      bodyStyle={MODAL_BODY_STYLE}
      style={MODAL_STYLE}
    >
      <div style={{ marginBottom: 16 }}>
        <p>数据状态: {loading ? '加载中' : `已加载 ${dataSource.length} 条记录`}</p>
      </div>

      <EditableProTable<DprgtChgCard>
        key={refresh}
        rowKey="cardNo"
        headerTitle="换卡登记簿信息"
        scroll={TABLE_SCROLL}
        recordCreatorProps={position === "hidden" ? false : {
          position: position,
          creatorButtonText: "新增一行换卡登记信息",
          onClick: async () => {
            const startIndex = (pagination.current - 1) * pagination.pageSize;
            const endIndex = startIndex + pagination.pageSize;
            const currentPageDataCount = Math.min(
              pagination.pageSize,
              pagination.total - (pagination.current - 1) * pagination.pageSize
            );
            if (pagination.current !== 1 && currentPageDataCount >= pagination.pageSize) {
              Modal.error({
                title: "提示",
                content: "当前页面数据已满，请切换至第一页或者未满的页面进行新增",
              });
              return false;
            }
            // 构建新记录，注意各字段默认值可按需要调整
            const newRecord = {
              cardNo: "NEW", // 新增记录标识，由后端生成唯一ID
              newCardCardNo: "",
              mainContrNo: "",
              enrollDate: new Date().toISOString().split("T")[0],
              globalBusiTrackNo: "",
              subtxNo: "",
              chgInstNo: "",
              chgTellerNo: "",
              authTellerNo: "",
              txTime: new Date().toISOString().substr(0, 14),
              relMedmStaFlagCd: "",
              validFlag: "1",
              hostDt: new Date().toISOString().split("T")[0],
              lastTxDate: new Date().toISOString().split("T")[0],
              recordStaCd: "1",
              zoneVal: props.data?.records?.[0]?.zoneVal ?? "",
              createStamp: "",
              lastModStamp: "",
            };
            const newDataSource = [...dataSource];
            newDataSource.splice(endIndex, 0, newRecord);
            setDataSource(newDataSource);
            setIsEditing(true);
            setEditableRowKeys(["NEW"]);
          },
          record: () => {
            const today = new Date();
            const formattedDate = today.toISOString().split("T")[0];
            return {
              cardNo: "NEW",
              newCardCardNo: "",
              mainContrNo: "",
              enrollDate: formattedDate,
              globalBusiTrackNo: "",
              subtxNo: "",
              chgInstNo: "",
              chgTellerNo: "",
              authTellerNo: "",
              txTime: today.toISOString().substr(0, 14),
              relMedmStaFlagCd: "",
              validFlag: "1",
              hostDt: formattedDate,
              lastTxDate: formattedDate,
              recordStaCd: "1",
              zoneVal: props.data?.records?.[0]?.zoneVal ?? "",
              createStamp: "",
              lastModStamp: "",
            } as DprgtChgCard;
          },
        }}
        loading={loading}
        toolBarRender={() => [
          <ProFormRadio.Group
            key="render"
            fieldProps={{
              value: position,
              onChange: (e: any) => setPosition(e.target.value),
            }}
            options={[
              { label: "添加到顶部", value: "top" },
              { label: "添加到底部", value: "bottom" },
              { label: "隐藏", value: "hidden" },
            ]}
          />,
        ]}
        columns={columns}
        request={async (params, sorter, filter) => {
          console.log('表格request方法被调用, params:', params);
          if (isEditing) {
            return {
              data: dataSource,
              total: pagination.total,
              success: true,
            };
          }

          setLoading(true);
          try {
            if (!props.data || !props.data.records || props.data.records.length === 0) {
              console.log('没有初始数据，返回空数组');
              return { data: [], total: 0, success: true };
            }
            
            const chgCardRecord = props.data.records[0];
            if (!chgCardRecord?.mainContrNo || !chgCardRecord?.zoneVal) {
              console.log('缺少查询参数，返回空数组');
              return { data: [], total: 0, success: true };
            }
            
            const { current, pageSize } = params;
            console.log('发起查询请求，参数:', {
              mainContrNo: chgCardRecord.mainContrNo,
              zoneVal: chgCardRecord.zoneVal,
              current: current || 1,
              pageSize: pageSize || DEFAULT_PAGE_SIZE
            });
            
            const res = await getDprgtChgCard(
              chgCardRecord.mainContrNo,
              chgCardRecord.zoneVal,
              "chgcardPageInfo",
              {
                current: current || 1,
                pageSize: pageSize || DEFAULT_PAGE_SIZE,
              }
            );
            
            console.log('获取到的原始响应:', res);
            
            if (res.code === 200 && res.data) {
              let detailData;
              try {
                detailData = typeof res.data === 'string' ? JSON.parse(res.data) : res.data;
                console.log('解析后的数据:', detailData);
              } catch (parseError) {
                console.error('数据解析错误:', parseError);
                message.error('数据格式错误');
                return { data: [], total: 0, success: false };
              }
              
              const records = detailData.records || [];
              console.log('处理后的记录数组:', records);
              
              setDataSource(records);
              setPagination({
                current: detailData.current || 1,
                pageSize: detailData.size || DEFAULT_PAGE_SIZE,
                total: detailData.total || 0,
              });
              
              return {
                data: records,
                total: detailData.total || 0,
                success: true,
              };
            } else {
              console.error('请求响应异常:', res);
              message.error(res.msg || "获取换卡信息失败");
              return { data: [], total: 0, success: false };
            }
          } catch (error) {
            console.error("数据加载错误:", error);
            message.error("查询换卡信息失败");
            return { data: [], total: 0, success: false };
          } finally {
            setLoading(false);
          }
        }}
        dataSource={dataSource}
        pagination={{
          ...TABLE_PAGINATION,
          current: pagination.current,
          pageSize: pagination.pageSize,
          total: pagination.total,
          showTotal: (total) => `总共 ${total} 条`,
          onChange: (current, pageSize) => {
            console.log('分页变化:', { current, pageSize });
            setPagination((prev) => ({ ...prev, current, pageSize }));
          },
        }}
        editable={{
          type: "multiple",
          editableKeys,
          onChange: setEditableRowKeys,
          onSave: async (rowKey, data) => {
            if (data.cardNo === "NEW") {
              try {
                const newData = { ...data, card_no: "NEW" };
                const res = await insertDprgtChgCard(newData);
                if (res.code === 200) {
                  message.success("新增成功");
                  setEditableRowKeys([]);
                  setIsEditing(false);
                  setEditingRecord(null);
                  // 刷新数据并跳转到最后一页
                  const chgCardRecord = props.data?.records?.[0];
                  if (chgCardRecord?.mainContrNo && chgCardRecord?.zoneVal) {
                    const res = await getDprgtChgCard(
                      chgCardRecord.mainContrNo,
                      chgCardRecord.zoneVal,
                      "chgcardPageInfo",
                      { current: 1, pageSize: 1 }
                    );
                    if (res.code === 200 && res.data) {
                      const detailData = JSON.parse(res.data);
                      const total = detailData.total || 0;
                      const lastPage = Math.ceil(total / pagination.pageSize);
                      setPagination((prev) => ({ ...prev, current: lastPage, total }));
                    }
                  }
                  await refreshData();
                  return true;
                } else {
                  message.error(res.msg || "新增失败");
                  await refreshData();
                  return false;
                }
              } catch (error) {
                message.error("新增失败");
                return false;
              }
            }

            if (!editingRecord || !rowKey) {
              message.error("编辑记录不存在");
              return false;
            }
            let originalData: DprgtChgCard | null = null;
            try {
              // 只上传可编辑字段
              const updateData = {
                cardNo: data.cardNo,
                newCardCardNo: data.newCardCardNo,
                mainContrNo: data.mainContrNo,
                enrollDate: data.enrollDate,
                globalBusiTrackNo: data.globalBusiTrackNo,
                subtxNo: data.subtxNo,
                chgInstNo: data.chgInstNo,
                chgTellerNo: data.chgTellerNo,
                authTellerNo: data.authTellerNo,
                txTime: data.txTime,
                relMedmStaFlagCd: data.relMedmStaFlagCd,
                validFlag: data.validFlag,
                lastTxDate: data.lastTxDate,
                recordStaCd: data.recordStaCd,
                zoneVal: data.zoneVal,
              };
              originalData = dataSource.find((item) => item.cardNo === rowKey) || null;
              const res = await dprgtChgCardUpdate(updateData);
              if (res.code === 200) {
                setDataSource((prev) =>
                  prev.map((item) =>
                    item.cardNo === rowKey ? { ...data } : item
                  )
                );
                message.success("保存成功");
              } else {
                message.error(res.msg || "保存失败");
                if (originalData) {
                  setDataSource((prev) =>
                    prev.map((item) =>
                      item.cardNo === rowKey ? originalData : item
                    ) as DprgtChgCard[]
                  );
                }
              }
            } catch (error) {
              message.error("保存失败");
              if (originalData) {
                setDataSource((prev) =>
                  prev.map((item) =>
                    item.cardNo === rowKey ? originalData : item
                  ) as DprgtChgCard[]
                );
              }
            } finally {
              setEditableRowKeys([]);
              setIsEditing(false);
              setEditingRecord(null);
              await refreshData();
            }
          },
          onCancel: async (rowKey) => {
            if (editingRecord) {
              setDataSource((prevDataSource) =>
                prevDataSource.map((item) =>
                  item.cardNo === editingRecord.cardNo
                    ? { ...editingRecord }
                    : item
                )
              );
            }
            setEditableRowKeys([]);
            setIsEditing(false);
            setEditingRecord(null);
            return true; // 返回成功状态
          }
        }}
      />
    </Modal>
  );
};

export default DprgtChgCardFormEdit;
