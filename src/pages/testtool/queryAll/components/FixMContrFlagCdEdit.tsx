import React, { useState, useEffect } from 'react';
import { Modal, Form, Radio, Space, message } from 'antd';
import type { TbDpmstFixCont } from '../data';
import { dpmstFixContUpdate, queryAll } from '../service';
import { flagMeaningsConfig } from './FlagModal';


interface MContrFlagCdEditProps {
  visible: boolean;
  onCancel: () => void;
  record: any;
  mediumNo: string; // 添加介质号属性
  prodtContractNo: string; // 添加合约号属性
  onSuccess: () => void;
}

// 主合约控制标志码
const FixMContrFlagCdEdit: React.FC<MContrFlagCdEditProps> = ({
  visible,
  onCancel,
  record,
  mediumNo,
  prodtContractNo, // 接收合约号
  onSuccess,
}) => {
  const [loading, setLoading] = useState(false);

  const [editedValues, setEditedValues] = useState<Record<number, string>>({});
  const mContrFlagCdConfig = flagMeaningsConfig.mContrFlagCd;

  useEffect(() => {
    if (visible && record) {
      const initialValues: Record<number, string> = {};

      if (record.mContrFlagCd) {
        const bits = record.mContrFlagCd.split('');
        mContrFlagCdConfig.forEach(item => {
          initialValues[item.position] = bits[item.position - 1] || '0';
        });
      } else {
        mContrFlagCdConfig.forEach(item => {
          initialValues[item.position] = '0';
        });
      }

      setEditedValues(initialValues);
    }
  }, [visible, record]);

  const handleValueChange = (position: number, value: string) => {
    setEditedValues(prev => ({
      ...prev,
      [position]: value
    }));
  };

  const handleOk = async () => {
    setLoading(true);
    try {
      // 生成32位标志码
      const flagArray = new Array(32).fill('0');
      Object.entries(editedValues).forEach(([position, value]) => {
        const index = parseInt(position) - 1;
        // 只对前13位赋值
        if (index >= 0 && index < 13) {
          flagArray[index] = value;
        }
      });

      const mContrFlagCd = flagArray.join('');
      const params: TbDpmstFixCont = {
        prodtContractNo: prodtContractNo, // 使用传入的合约号
        zoneVal: record.zoneVal,
        mContrFlagCd,
      };

      const res = await dpmstFixContUpdate(params);
      if (res.code === 200) {
        message.success('更新成功');
        // 调用查询方法重新获取数据
        await queryAll(mediumNo);
        onCancel();
      } else {
        message.error(res.msg || '更新失败');
      }
    } catch (error) {
      message.error('更新失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title="编辑主合约控制标志码"
      open={visible}
      onOk={handleOk}
      onCancel={onCancel}
      confirmLoading={loading}
      width={550}
    >
      <Space direction="vertical" style={{ width: '100%' }} size="large">
        {mContrFlagCdConfig.map(item => (
          <Form.Item key={item.key} label={item.title}>
            <Radio.Group
              value={editedValues[item.position]}
              onChange={(e) => handleValueChange(item.position, e.target.value)}
            >
              <Space>
                {item.options.map(option => (
                  <Radio key={option.value} value={option.value}>
                    {option.label}
                  </Radio>
                ))}
              </Space>
            </Radio.Group>
          </Form.Item>
        ))}
      </Space>
    </Modal>
  );
};

export default FixMContrFlagCdEdit;
