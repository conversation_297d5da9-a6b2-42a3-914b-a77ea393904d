import React, { useState, useEffect } from 'react';
import { Modal, Radio, Space, Button, Typography } from 'antd';
import type { RadioChangeEvent } from 'antd';

const { Title } = Typography;

interface FlagModalProps {
  visible: boolean;
  flag: string | undefined;
  dataIndex?: string;
  onCancel: () => void;
  onSubmit: (values: Record<string, any>) => void;
}

interface FlagBitInfo {
  key: string;
  position: number;
  title: string;
  options: { label: string; value: string }[];
}

export const flagMeaningsConfig: Record<string, FlagBitInfo[]> = {
  relMediumTypeFlagCd: [
    { key: '1', position: 1, title: '第1位:是否关联折', options: [{ label: '否', value: '0' }, { label: '是', value: '1' }] },
    { key: '2', position: 2, title: '第2位:是否关联卡', options: [{ label: '否', value: '0' }, { label: '是', value: '1' }] },
    { key: '3', position: 3, title: '第3位:是否关联普通活期对账簿', options: [{ label: '否', value: '0' }, { label: '是', value: '1' }] },
    { key: '4', position: 4, title: '第4位:是否关联普通定期对账簿', options: [{ label: '否', value: '0' }, { label: '是', value: '1' }] },
    { key: '5', position: 5, title: '第5位:是否关联行业应用子账户对账簿', options: [{ label: '否', value: '0' }, { label: '是', value: '1' }] },
    { key: '6', position: 6, title: '第6位:是否关联湖南省惠农补贴明白折', options: [{ label: '否', value: '0' }, { label: '是', value: '1' }] },
    { key: '7', position: 7, title: '第7位:是否关联湖南省扶贫补贴明白折', options: [{ label: '否', value: '0' }, { label: '是', value: '1' }] },
  ],
  statusFlagCd: [
    { key: '1', position: 1, title: '第1位:介质状态代码', options: [{ label: '正常', value: '0' }, { label: '注销', value: '1' }, { label: '已移出', value: '2' }] },
    { key: '2', position: 2, title: '第2位:挂失状态代码', options: [{ label: '正常', value: '0' }, { label: '凭证临时挂失', value: '1' }, { label: '凭证正式挂失', value: '2' }, { label: '密码挂失', value: '3' }, { label: '双挂失', value: '4' }] },
    { key: '3', position: 3, title: '第3位:激活状态代码', options: [{ label: '正常', value: '0' }, { label: '未激活', value: '1' }] },
    { key: '4', position: 4, title: '第4位:吞没状态代码', options: [{ label: '正常', value: '0' }, { label: '吞没', value: '1' }] },
    { key: '5', position: 5, title: '第5位:可疑状态代码', options: [{ label: '正常', value: '0' }, { label: '可疑', value: '1' }] },
    { key: '6', position: 6, title: '第6位:密码状态代码', options: [{ label: '正常', value: '0' }, { label: '密码锁定', value: '1' }] },
    { key: '7', position: 7, title: '第7位:需要更换折/单标志', options: [{ label: '否', value: '0' }, { label: '是', value: '1' }] },
    { key: '8', position: 8, title: '第8位:已换卡标志', options: [{ label: '否', value: '0' }, { label: '是', value: '1' }] },
  ],
  attrbuteFlagCd: [
    { key: '1', position: 1, title: '第1位:个人联名账户标志', options: [{ label: '否', value: '0' }, { label: '是', value: '1' }] },
    { key: '2', position: 2, title: '第2位:新旧介质标志代码', options: [{ label: '无关', value: '0' }, { label: '新', value: '1' }, { label: '旧', value: '2' }] },
    { key: '3', position: 3, title: '第3位:虚拟介质标志', options: [{ label: '否', value: '0' }, { label: '是', value: '1' }] },
    { key: '4', position: 4, title: '第4位:特定范围挂失标志', options: [{ label: '否', value: '0' }, { label: '是', value: '1' }] },
    { key: '5', position: 5, title: '第5位:是否不校验迁移介质有效性', options: [{ label: '否', value: '0' }, { label: '是', value: '1' }] },
  ],
  crtModeFlagCd: [
    { key: '1', position: 1, title: '第1位:密码', options: [{ label: '无', value: '0' }, { label: '密码', value: '1' }] },
    { key: '2', position: 2, title: '第2位:印鉴', options: [{ label: '无', value: '0' }, { label: '印鉴', value: '1' }] },
    { key: '3', position: 3, title: '第3位:指纹', options: [{ label: '无', value: '0' }, { label: '指纹', value: '1' }] },
    { key: '4', position: 4, title: '第4位:人脸', options: [{ label: '无', value: '0' }, { label: '人脸', value: '1' }] },
    { key: '5', position: 5, title: '第5位:声纹', options: [{ label: '无', value: '0' }, { label: '声纹', value: '1' }] },
    { key: '6', position: 6, title: '第6位:指静脉', options: [{ label: '无', value: '0' }, { label: '指静脉', value: '1' }] },
  ],
  mContrFlagCd: [
    { key: '1', position: 1, title: '第1位:止付状态', options: [{ label: '正常', value: '0' }, { label: '止付', value: '1' }] },
    { key: '2', position: 2, title: '第2位:冻结状态', options: [{ label: '正常', value: '0' }, { label: '冻结', value: '1' }] },
    { key: '3', position: 3, title: '第3位:暂停非柜面交易状态', options: [{ label: '正常', value: '0' }, { label: '暂停', value: '1' }] },
    { key: '4', position: 4, title: '第4位:停用状态', options: [{ label: '正常', value: '0' }, { label: '停用', value: '1' }] },
    { key: '5', position: 5, title: '第5位:中止状态', options: [{ label: '正常', value: '0' }, { label: '中止', value: '1' }] },
    { key: '6', position: 6, title: '第6位:账户群标志', options: [{ label: '否', value: '0' }, { label: '是', value: '1' }] },
    { key: '7', position: 7, title: '第7位:自贸区标志', options: [{ label: '无关', value: '0' }, { label: 'FTI区内自贸区', value: '1' }, { label: 'FTF区内境外自贸区', value: '2' }] },
    { key: '8', position: 8, title: '第8位:个人账户长期不动户状态', options: [{ label: '正常', value: '0' }, { label: '长期不动户', value: '1' }] },
    { key: '9', position: 9, title: '第9位:客户有主动交易标志', options: [{ label: '否', value: '0' }, { label: '是', value: '1' }] },
    { key: '10', position: 10, title: '第10位:非柜面渠道专项管控标志', options: [{ label: '否', value: '0' }, { label: '是', value: '1' }] },
    { key: '11', position: 11, title: '第11位:养老金专户状态', options: [{ label: '无关', value: '0' }, { label: '正常', value: '1' }, { label: '转移', value: '2' }] },
    { key: '12', position: 12, title: '第12位:压缩标志', options: [{ label: '否', value: '0' }, { label: '是', value: '1' }] },
    { key: '13', position: 13, title: '第13位:介质是否未领用标志', options: [{ label: '已领用', value: '0' }, { label: '未领用', value: '1' }] },
  ],
  contrCtrlFlagCd: [
    { key: '1', position: 1, title: '第1位:余额限制类型代码', options: [{ label: '无', value: '0' }, { label: '余额设置上限', value: '1' }] },
    { key: '2', position: 2, title: '第2位:资金用途管控标志', options: [{ label: '不管控', value: '0' }, { label: '管控', value: '1' }] },
  ],
  contrAttrFgCd: [
    { key: '1', position: 1, title: '第1位:柜面核实标志', options: [{ label: '否', value: '0' }, { label: '是', value: '1' }] },
    { key: '2', position: 2, title: '第2位:非绑定账户入金标志', options: [{ label: '否', value: '0' }, { label: '是', value: '1' }] },
    { key: '3', position: 3, title: '第3位:转存标志代码', options: [{ label: '否', value: '0' }, { label: '自动到期转存', value: '1' }, { label: '到期赎回', value: '2' }, { label: '到期约定转存', value: '3' }] },
    { key: '5', position: 5, title: '第5位:卡贷通标志', options: [{ label: '否', value: '0' }, { label: '是', value: '1' }] },
    { key: '7', position: 7, title: '第7位:约定转账标志', options: [{ label: '否', value: '0' }, { label: '是', value: '1' }] },
    { key: '8', position: 8, title: '第8位:定活互转标志', options: [{ label: '否', value: '0' }, { label: '是', value: '1' }] },
    { key: '9', position: 9, title: '第9位:合约来源代码', options: [{ label: '无关', value: '0' }, { label: '卡内活转定', value: '1' }, { label: '账户移入', value: '2' }, { label: '现金新开', value: '3' }] },
    { key: '10', position: 10, title: '第10位:身份核实标志', options: [{ label: '否', value: '0' }, { label: '是', value: '1' }] },
    { key: '11', position: 11, title: '第11位:是否存在协议国税率', options: [{ label: '否', value: '0' }, { label: '是', value: '1' }] },
    { key: '12', position: 12, title: '第12位:自动赎回标志', options: [{ label: '无关', value: '0' }, { label: '允许', value: '1' }, { label: '不允许', value: '2' }] },
  ],
  contrStaFgCd: [
    { key: '1', position: 1, title: '第1位:合约状态代码', options: [{ label: '签约', value: '0' }, { label: '解约', value: '1' }] },
    { key: '2', position: 2, title: '第2位:副卡停用状态代码', options: [{ label: '正常/无关', value: '0' }, { label: '主卡办理副卡停用', value: '1' },  { label: '副卡办理副卡停用', value: '2' }] },
  ],
  accStatusFlagCd: [
    { key: '1', position: 1, title: '第1位:账户状态代码', options: [{ label: '正常', value: '0' }, { label: '未启用', value: '1' }, { label: '销户', value: '2' }] },
    { key: '2', position: 2, title: '第2位:冻结状态代码', options: [{ label: '正常', value: '0' }, { label: '账户冻结', value: '1' }] },
    { key: '3', position: 3, title: '第3位:止付状态代码', options: [{ label: '正常', value: '0' }, { label: '账户止付', value: '1' }] },
    { key: '8', position: 8, title: '第8位:待结清状态代码', options: [{ label: '正常', value: '0' }, { label: '待结清', value: '1' }] },
  ],
  accFlagCd: [
    // { key: '1', position: 1, title: '居民税率类型', options: [
    //   { label: '本国居民税率类型', value: '0' },
    //   { label: '协议缔约国居民税率类型', value: '1' },
    //   { label: '非协议缔约国居民税率类型', value: '2' },
    // ]},
    { key: '2', position: 2, title: '第2位:免征小额管理费标志', options: [{ label: '不免除', value: '0' }, { label: '免除', value: '1' }] },
    { key: '3', position: 3, title: '第3位:免征年费标志', options: [{ label: '不免除', value: '0' }, { label: '免除', value: '1' }] },
    { key: '4', position: 4, title: '第4位:免征VIP服务费标志', options: [{ label: '不免除', value: '0' }, { label: '免除', value: '1' }] },
    { key: '5', position: 5, title: '第5位:计息方式', options: [
      { label: '正常', value: '0' },
      { label: '结转计息', value: '1' },
      { label: '不计息', value: '2' },
    ]},
    { key: '7', position: 7, title: '第7位:账户星级', options: [
      { label: '无关', value: '0' },
      { label: '准一星', value: '1' },
      { label: '一星', value: '2' },
      { label: '二星', value: '3' },
      { label: '三星', value: '4' },
    ]},
    { key: '8', position: 8, title: '第8位:利率启用方式', options: [
      { label: '利率值', value: '0' },
      { label: '利率号', value: '1' },
      { label: '无关', value: '2' },
    ]},
    { key: '10', position: 10, title: '第10位:利率生效种类代码', options: [
      { label: '协议利率', value: '1' },
      { label: '差异化利率', value: '2' },
      { label: '套餐利率', value: '3' },
      { label: '基础利率', value: '4' },
    ]},
    { key: '11', position: 11, title: '第11位:特殊约转利率标志', options: [{ label: '否', value: '0' }, { label: '是', value: '1' }] },
    { key: '16', position: 16, title: '第16位:账户产品标志', options: [{ label: '无关', value: '0' }, { label: '养老金账户', value: '1' }] },
    { key: '25', position: 25, title: '第25位:定期账户扣划标志', options: [{ label: '正常', value: '0' }, { label: '已司法扣划', value: '1' }, { label: '信用卡扣划', value: '2' }] },
    { key: '26', position: 16, title: '第26位:养老金缴存退回结息标志', options: [{ label: '无关', value: '0' }, { label: '允许', value: '1' }, { label: '不允许', value: '2' }] },
  ],
  personalDepositAccountTypeCode: [
    { key: '1', position: 1, title: '账户类型', options: [
      { label: '无关', value: '0' },
      { label: 'Ⅰ类户', value: '1' },
      { label: 'Ⅱ类户', value: '2' },
      { label: 'Ⅲ类户', value: '3' },
    ]},
  ],
};


const FlagModal: React.FC<FlagModalProps> = ({
  visible,
  flag,
  dataIndex,
  onCancel,
  onSubmit,
}) => {
  const [editedValues, setEditedValues] = useState<Record<number, string>>({});

  useEffect(() => {
    if (!dataIndex || !flagMeaningsConfig[dataIndex]) {
      // console.log('无效的dataIndex:', dataIndex);
      return;
    }

    try {
      // console.log('初始化标志值 - dataIndex:', dataIndex, 'flag:', flag);

      // 初始化所有位置的值为'0'
      const initialValues: Record<number, string> = {};
      flagMeaningsConfig[dataIndex].forEach(item => {
        initialValues[item.position] = '0';
      });

      // 如果有flag值，则解析并设置
      if (typeof flag === 'string' && flag.trim()) {
        const flagValue = flag.trim();
        // console.log('解析标志值:', flagValue);

        // 将字符串转换为数组，从左到右读取
        const bits = flagValue.split('');
        // console.log('分割后的位值:', bits);

        // 遍历配置的每个位置
        flagMeaningsConfig[dataIndex].forEach(item => {
          // 配置中的position是从1开始的，需要减1来匹配数组索引
          const index = item.position - 1;
          // 如果该位置有值，就使用实际的值，否则使用默认值'0'
          initialValues[item.position] = index < bits.length ? bits[index] : '0';
          // console.log(`位置 ${item.position} (${item.title}): 索引=${index}, 值=${initialValues[item.position]}`);
        });
      }

      // console.log('最终解析结果:', initialValues);
      setEditedValues(initialValues);
    } catch (error) {
      console.error('初始化标志值时出错:', error);
      // 如果出错，设置默认值
      const defaultValues: Record<number, string> = {};
      flagMeaningsConfig[dataIndex].forEach(item => {
        defaultValues[item.position] = '0';
      });
      setEditedValues(defaultValues);
    }
  }, [flag, dataIndex]);

  const handleValueChange = (position: number, e: RadioChangeEvent) => {
    setEditedValues(prev => ({
      ...prev,
      [position]: e.target.value
    }));
  };

  const handleSubmit = () => {
    if (!dataIndex || !flagMeaningsConfig[dataIndex]) return;

    // 找到最大位置
    const maxPosition = Math.max(...flagMeaningsConfig[dataIndex].map(item => item.position));

    // 生成新的标志码，确保每个位置都有值
    const flagArray = new Array(32).fill('0');

    // 将编辑后的值填入对应位置
    Object.entries(editedValues).forEach(([position, value]) => {
      const index = parseInt(position) - 1;
      if (index >= 0 && index < maxPosition) {
        flagArray[index] = value;
      }
    });

    // 合并成字符串
    const newFlag = flagArray.join('');
    onSubmit({ flag: newFlag });
  };

  if (!dataIndex || !flagMeaningsConfig[dataIndex]) {
    return null;
  }

  return (
    <Modal
      title="标志位详情信息"
      open={visible}
      onCancel={onCancel}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button key="submit" type="primary" onClick={handleSubmit} disabled={false}>
          确定
        </Button>,
      ]}
      width={650}
      bodyStyle={{ 
        maxHeight: '70vh', 
        overflow: 'auto', 
        padding: '24px',
        borderRadius: '10px',  // 添加圆角
      }}
      style={{
        borderRadius: '10px',  // Modal整体圆角
        overflow: 'hidden'    // 确保内容不超出圆角范围
      }}
    >
      <Space direction="vertical" style={{ width: '100%' }} size="large">
        {flagMeaningsConfig[dataIndex].map(item => (
          <div key={item.key}>
            <Title level={5} style={{ marginBottom: 12 }}>{item.title}</Title>
            <Radio.Group
              value={editedValues[item.position]}
              onChange={(e) => handleValueChange(item.position, e)}
              style={{ marginBottom: 16 }}
            >
              <Space>
                {item.options.map(option => (
                  <Radio key={option.value} value={option.value}>
                    {option.label}
                  </Radio>
                ))}
              </Space>
            </Radio.Group>
          </div>
        ))}
      </Space>
    </Modal>
  );
};

export default FlagModal;
