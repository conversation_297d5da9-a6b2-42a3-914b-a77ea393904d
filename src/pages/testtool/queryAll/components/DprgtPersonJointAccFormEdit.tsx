import React, { useEffect, useState } from "react";
import { Modal, message, Button } from "antd";
import {
  dprgtDprgtPersonJointAccUpdate,
  getDprgtPersonJointAcc,
  insertDprgtPersonJointAcc,
  dprgtPersonJointAccDelete,
} from "../service";
import type { DprgtPersonJointAcc, PageResponse } from "../data";
import type { ProColumns } from "@ant-design/pro-components";
import { EditableProTable, ProFormRadio } from "@ant-design/pro-components";

/*
 * @Description: 个人联名账户信息记录表编辑表单
 * @Author: zhujinwei
 * @Date: 2025-01-17 10:00:00
 */

interface PaginationType {
  current: number;
  pageSize: number;
  total: number;
}

export type DprgtPersonJointAccFormProps = {
  onCancel: () => void;
  open: boolean;
  data: PageResponse<DprgtPersonJointAcc> | null;
//   refresh: number;
};

// 表格相关常量
const DEFAULT_PAGE_SIZE = 10;
const TABLE_SCROLL = { x: 1300 } as const;
const TABLE_OPTIONS = {
  density: true,
  fullScreen: true,
  setting: true,
} as const;
const TABLE_PAGINATION = {
  showSizeChanger: true,
  showQuickJumper: true,
} as const;

// Modal相关常量
const MODAL_BODY_STYLE = {
  maxHeight: "70vh",
  overflow: "auto",
  padding: "24px",
  borderRadius: "10px",
} as const;

const MODAL_STYLE = {
  borderRadius: "10px",
  overflow: "hidden",
} as const;

// 工具函数
const formatAmount = (value: string | number | null | undefined): string => {
  if (!value) return "0.00";
  const num = typeof value === "string" ? parseFloat(value) : value;
  return isNaN(num) ? "0.00" : num.toFixed(2);
};

// 分页相关常量
const INITIAL_PAGINATION: PaginationType = {
  current: 1,
  pageSize: DEFAULT_PAGE_SIZE,
  total: 0,
};

const DprgtPersonJointAccFormEdit: React.FC<DprgtPersonJointAccFormProps> = (
  props
) => {
  const [dataSource, setDataSource] = useState<readonly DprgtPersonJointAcc[]>(
    []
  );
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);
  const [isEditing, setIsEditing] = useState(false);
  const [editingRecord, setEditingRecord] =
    useState<DprgtPersonJointAcc | null>(null);
  const [pagination, setPagination] =
    useState<PaginationType>(INITIAL_PAGINATION);
  const [refresh, setRefresh] = useState(0);
  const [loading, setLoading] = useState(false);

  // 刷新数据方法
  const refreshData = async () => {
    if (!props.data?.records?.[0]) return;

    const dprgtPersonJointAcc = props.data.records[0];
    if (!dprgtPersonJointAcc?.mediumNo || !dprgtPersonJointAcc?.zoneVal) return;

    setLoading(true);
    try {
      const res = await getDprgtPersonJointAcc(
        dprgtPersonJointAcc.mediumNo,
        dprgtPersonJointAcc.zoneVal,
        "dprgtPersonJointAccPageInfo",
        {
          current: pagination.current,
          pageSize: pagination.pageSize,
        }
      );

      if (res.code === 200 && res.data) {
        const detailData = JSON.parse(res.data);
        setDataSource(detailData.records || []);
        setPagination({
          current: detailData.current || INITIAL_PAGINATION.current,
          pageSize: detailData.size || INITIAL_PAGINATION.pageSize,
          total: detailData.total || INITIAL_PAGINATION.total,
        });
        setRefresh(refresh + 1);
      } else {
        message.error(res.msg || "获取明细失败");
      }
    } catch (error) {
      message.error("查询明细失败");
    } finally {
      setLoading(false);
    }
  };

  // 初始化数据
  useEffect(() => {
    let isMounted = true;

    const initData = async () => {
      if (props.open && props.data) {
        const { records, current, size, total } = props.data;
        if (isMounted) {
          setDataSource(records || []);
          setPagination({
            current: current || INITIAL_PAGINATION.current,
            pageSize: size || INITIAL_PAGINATION.pageSize,
            total: total || INITIAL_PAGINATION.total,
          });
        }
      } else if (isMounted) {
        setDataSource([]);
        setPagination(INITIAL_PAGINATION);
      }
    };

    initData();

    return () => {
      isMounted = false;
    };
  }, [props.open, props.data]);

  // 定义表格列
  const [position, setPosition] = useState<"top" | "bottom" | "hidden">(
    "bottom"
  );

  const columns: ProColumns<DprgtPersonJointAcc>[] = [
    {
      title: "客户编号",
      dataIndex: "custNo",
      width: 180,
      fixed: true,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "介质编号",
      dataIndex: "mediumNo",
      width: 180,
      // fixed: true,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
        title: "个人证件类型代码",
        dataIndex: "perCertTpCd",
        width: 200,
        ellipsis: true,
        align: 'center',
        editable: () => true,
        valueType: "select",
        valueEnum: {
        '1010': { text: '1010-居民身份证' },
        '1011': { text: '1011-临时居民身份证' },
        '1020': { text: '1020-军人身份证件' },
        '1021': { text: '1021-士兵证' },
        '1022': { text: '1022-军官证' },
        '1023': { text: '1023-文职干部证' },
        '1024': { text: '1024-军官退休证' },
        '1025': { text: '1025-文职干部退休证' },
        '1030': { text: '1030-武警身份证件' },
        '1031': { text: '1031-武警士兵证' },
        '1032': { text: '1032-警官证' },
        '1033': { text: '1033-武警文职干部证' },
        '1034': { text: '1034-武警军官退休证' },
        '1035': { text: '1035-武警文职干部退休证' },
        '1040': { text: '1040-户口簿' },
        '1050': { text: '1050-中国护照' },
        '1051': { text: '1051-外国护照' },
        '1060': { text: '1060-学生证' },
        '1070': { text: '1070-港澳居民来往内地通行证' },
        '1071': { text: '1071-往来港澳通行证' },
        '1080': { text: '1080-台湾居民来往大陆通行证' },
        '1090': { text: '1090-执行公务证' },
        '1100': { text: '1100-机动车驾驶证' },
        '1110': { text: '1110-社会保障卡' },
        '1120': { text: '1120-外国人居留证' },
        '1121': { text: '1121-外国人永久居留证' },
        '1130': { text: '1130-旅行证件' },
        '1140': { text: '1140-香港居民身份证' },
        '1150': { text: '1150-澳门居民身份证' },
        '1160': { text: '1160-台湾居民身份证' },
        '1170': { text: '1170-边民证' },
        '1180': { text: '1180-港澳台居民居住证' },
        '1181': { text: '1181-港澳居民居住证' },
        '1182': { text: '1182-台湾居民居住证' },
        '1190': { text: '1190-外国身份证' },
        '1998': { text: '1998-其他（原98类）' },
        '1999': { text: '1999-其他证件（个人）' },
        },
      },
    {
      title: "个人证件号码",
      dataIndex: "personalCertNo",
      width: 200,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "个人联名账户关系人类别代码",
      dataIndex: "jointAcrlVatgCd",
      width: 200,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "select",
      valueEnum: {
        "01": { text: "01-牵头人" },
        "02": { text: "02-共管人" },
      },
    },
    {
      title: "客户名称",
      dataIndex: "custNm",
      width: 200,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
        title: "加密密码",
        dataIndex: "encPwd",
        width: 200,
        ellipsis: true,
        align: "center",
        editable: () => false,
      },
    {
      title: "最后处理日期",
      dataIndex: "lastDealDate",
      width: 200,
      ellipsis: true,
      align: "center",
      editable: () => true,
      render: (_, entity) => entity.lastDealDate || "-",
      valueType: "date",
    },
    {
      title: "日累计密码错误次数",
      dataIndex: "dsumPsErrTms",
      width: 200,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "密码错误次数",
      dataIndex: "psErrTms",
      width: 200,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "联名人密码状态标志码",
      dataIndex: "jointNaPersPsStaFlagCd",
      width: 200,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "关系开始日期",
      dataIndex: "relatBgnDate",
      width: 200,
      ellipsis: true,
      align: "center",
      editable: () => true,
      render: (_, entity) => entity.relatBgnDate || "-",
      valueType: "date",
    },
    {
      title: "关系结束日期",
      dataIndex: "relatEndDate",
      width: 200,
      ellipsis: true,
      align: "center",
      editable: () => true,
      render: (_, entity) => entity.relatBgnDate || "-",
      valueType: "date",
    },
    {
      title: "关系状态代码",
      dataIndex: "relatStaCd",
      width: 200,
      ellipsis: true,
      align: "center",
      editable: () => false,
      valueType: "select",
      valueEnum: {
        "1": { text: "1-已生效" },
        "2": { text: "2-已失效" },
      },
    },
    {
      title: "最后交易日期",
      dataIndex: "lastTxDate",
      width: 120,
      ellipsis: true,
      align: "center",
      render: (_, entity) => entity.lastTxDate || "-",
      valueType: "date",
    },
    {
        title: "防篡改字段信息",
        dataIndex: "tamProoFieldInfo",
        width: 120,
        ellipsis: true,
        align: "center",
        editable: () => true,
    },
    {
      title: "分片值",
      dataIndex: "zoneVal",
      width: 120,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "操作",
      valueType: "option",
      width: 150,
      // fixed: "right",
      align: "center",
      render: (text, record: DprgtPersonJointAcc, _, action) => {
        const isEditing = record.custNo
          ? editableKeys.includes(record.custNo)
          : false;
        return [
          isEditing ? (
            <>
              <a
                key="confirm"
                onClick={async () => {
                  try {
                    if (record.custNo) {
                      await action?.saveEditable?.(record.custNo);
                      setIsEditing(false);
                    }
                  } catch (err) {
                    console.error("保存失败:", err);
                  }
                }}
              >
                确定
              </a>
              <a
                key="cancel"
                onClick={() => {
                  if (record.custNo) {
                    action?.cancelEditable?.(record.custNo);
                    setEditableRowKeys([]);
                    setIsEditing(false);
                  }
                }}
                style={{ marginLeft: 8 }}
              >
                取消
              </a>
            </>
          ) : (
            <Button
                key="editable"
                type="link"
                disabled={true}
                onClick={async () => {
                if (!record.custNo) return;
                // 先保存原始数据，再进入编辑状态
                const originalRecord = { ...record };
                setEditingRecord(originalRecord);
                setIsEditing(true);
                await action?.startEditable?.(record.custNo);
              }}
              style={{ padding: 0 }}
            >
              编辑
            </Button>
          ),
          <Button
            key="delete"
            type="link"
            disabled={true}
            onClick={async () => {
              if (!record.custNo) return;
              Modal.confirm({
                title: "确认删除",
                content: "确定要删除这条记录吗？",
                onOk: async () => {
                  try {
                    const res = await dprgtPersonJointAccDelete(record);
                    if (res.code === 200) {
                      message.success("删除成功");
                    } else {
                      message.error(res.msg || "删除失败");
                    }
                  } catch (error) {
                    message.error("删除失败");
                  } finally {
                    await refreshData();
                  }
                },
                okText: "确认",
                cancelText: "取消",
              });
            }}
            style={{ marginLeft: 8 }}
          >
            删除
          </Button>,
        ];
      },
    },
  ];

  return (
    <Modal
      width={1200}
      title="查询个人联名账户信息记录表信息"
      open={props.open}
      destroyOnClose
      onCancel={props.onCancel}
      footer={null}
      bodyStyle={MODAL_BODY_STYLE}
      style={MODAL_STYLE}
    >
      <EditableProTable<DprgtPersonJointAcc>
        key={refresh}
        rowKey={(record) => record.custNo || ""}
        headerTitle="个人联名账户信息"
        scroll={TABLE_SCROLL}
        recordCreatorProps={
          position === "hidden"
            ? false
            : {
                position: position,
                creatorButtonText: "新增一行个人联名账户信息",
                onClick: async () => {
                  // 获取当前页数据
                  const startIndex =
                    (pagination.current - 1) * pagination.pageSize;
                  const endIndex = startIndex + pagination.pageSize;

                  const currentPage = pagination.current; // 当前页码
                  const pageSize = pagination.pageSize; // 每页显示的数据量
                  // 计算当前页面中数据量
                  const currentPageDataCount = Math.min(
                    pageSize, // 每页最大显示数
                    pagination.total - (currentPage - 1) * pageSize // 剩余数据量
                  );

                  // 检查当前页是否已满
                  if (
                    currentPage !== 1 &&
                    currentPageDataCount >= pagination.pageSize
                  ) {
                    Modal.error({
                      title: "提示",
                      content:
                        "当前页面数据已满，请切换至第一页或者是未满的页面进行新增",
                    });
                    return false;
                  }

                  // 创建新记录
                  const newRecord = {
                    mediumNo: props.data?.records?.[0]?.mediumNo ?? "",
                    custNo: "",
                    perCertTpCd: props.data?.records?.[0]?.perCertTpCd ?? "",
                    personalCertNo: "",
                    jointAcrlVatgCd: props.data?.records?.[0]?.jointAcrlVatgCd ?? "",
                    custNm: "",
                    encPwd: "",
                    lastDealDate: new Date().toISOString().split("T")[0],
                    dsumPsErrTms: "0",
                    psErrTms: "0",
                    jointNaPersPsStaFlagCd: "00000000000000000000000000000000",
                    relatBgnDate: new Date().toISOString().split("T")[0],
                    relatEndDate: new Date().toISOString().split("T")[0],
                    relatStaCd: "1",
                    lastTxDate: new Date().toISOString().split("T")[0],
                    zoneVal: props.data?.records?.[0]?.zoneVal ?? "",
                    tamProoFieldInfo: "temp",
                  };

                  // 在当前页末尾添加新记录
                  const newDataSource = [...dataSource];
                  newDataSource.splice(endIndex, 0, newRecord);

                  setDataSource(newDataSource);
                  setIsEditing(true);
                  setEditableRowKeys(["00000000000000"]);
                },
                record: () => {
                  const today = new Date();
                  const year = today.getFullYear();
                  const month = String(today.getMonth() + 1).padStart(2, "0");
                  const day = String(today.getDate()).padStart(2, "0");
                  const formattedDate = `${year}-${month}-${day}`;

                  return {
                    mediumNo: props.data?.records?.[0]?.mediumNo ?? "",
                    custNo: props.data?.records?.[0]?.custNo ?? "",
                    perCertTpCd: props.data?.records?.[0]?.perCertTpCd ?? "",
                    personalCertNo: props.data?.records?.[0]?.personalCertNo ?? "",
                    jointAcrlVatgCd: props.data?.records?.[0]?.jointAcrlVatgCd ?? "",
                    custNm: props.data?.records?.[0]?.custNm ?? "",
                    encPwd: props.data?.records?.[0]?.encPwd ?? "",
                    lastDealDate: formattedDate,
                    dsumPsErrTms: props.data?.records?.[0]?.dsumPsErrTms ?? "",
                    psErrTms: props.data?.records?.[0]?.psErrTms ?? "",
                    jointNaPersPsStaFlagCd: props.data?.records?.[0]?.jointNaPersPsStaFlagCd ?? "",
                    relatBgnDate: formattedDate,
                    relatEndDate: formattedDate,
                    relatStaCd: props.data?.records?.[0]?.relatStaCd ?? "",
                    lastTxDate: formattedDate,
                    zoneVal: props.data?.records?.[0]?.zoneVal ?? "",
                    tamProoFieldInfo: props.data?.records?.[0]?.tamProoFieldInfo ?? "",
                  } as DprgtPersonJointAcc;
                },
              }
        }
        loading={loading}
        toolBarRender={() => [
          <ProFormRadio.Group
            key="render"
            fieldProps={{
              value: position,
              onChange: (e: any) => setPosition(e.target.value),
            }}
            options={[
              {
                label: "添加到顶部",
                value: "top",
              },
              {
                label: "添加到底部",
                value: "bottom",
              },
              {
                label: "隐藏",
                value: "hidden",
              },
            ]}
          />,
        ]}
        columns={columns}
        request={async (params, sorter, filter) => {
          // 如果正在编辑，直接返回当前数据
          if (isEditing) {
            return {
              data: dataSource,
              total: pagination.total,
              success: true,
            };
          }

          setLoading(true);
          try {
            if (
              !props.data ||
              !props.data.records ||
              props.data.records.length === 0
            ) {
              return {
                data: [],
                total: 0,
                success: true,
              };
            }
            const dprgtPersonJointAcc = props.data.records[0];
            if (!dprgtPersonJointAcc?.mediumNo || !dprgtPersonJointAcc?.zoneVal) {
              return {
                data: [],
                total: 0,
                success: true,
              };
            }
            const { current, pageSize } = params;
            const res = await getDprgtPersonJointAcc(
                dprgtPersonJointAcc.mediumNo,
                dprgtPersonJointAcc.zoneVal,
              "dprgtPersonJointAccPageInfo",
              {
                current: current || 1,
                pageSize: pageSize || DEFAULT_PAGE_SIZE,
              }
            );
            if (res.code === 200 && res.data) {
              const detailData = JSON.parse(res.data);
              setDataSource(detailData.records || []);
              setPagination({
                current: detailData.current || 1,
                pageSize: detailData.size || DEFAULT_PAGE_SIZE,
                total: detailData.total || 0,
              });
              return {
                data: detailData.records || [],
                total: detailData.total || 0,
                success: true,
              };
            } else {
              message.error(res.msg || "获取个人联名账户信息记录表失败");
              return {
                data: [],
                total: 0,
                success: false,
              };
            }
          } catch (error) {
            message.error("查询个人联名账户信息记录表失败");
            return {
              data: [],
              total: 0,
              success: false,
            };
          } finally {
            setLoading(false);
          }
        }}
        dataSource={dataSource}
        pagination={{
          ...TABLE_PAGINATION,
          current: pagination.current,
          pageSize: pagination.pageSize,
          total: pagination.total,
          showTotal: (total) => `总共 ${total} 条`,
          onChange: (current, pageSize) => {
            setPagination((prev) => ({ ...prev, current, pageSize }));
          },
        }}
        editable={{
          type: "multiple",
          editableKeys,
          onSave: async (rowKey, data) => {
            console.log("保存数据:", rowKey, data);
            if (data.custNo === "00000000000000") {
              // 新增数据
              try {
                const newData = {
                  ...data,
                  custNo: "00000000000000", // 生成唯一ID
                };
                console.log("保存新增数据:", newData);
                const res = await insertDprgtPersonJointAcc(newData);
                if (res.code === 200) {
                  message.success("新增成功");
                  // 新增成功后刷新数据并跳转到最后一页
                  setEditableRowKeys([]);
                  setIsEditing(false);
                  setEditingRecord(null);

                  // 先获取最新的数据总数
                  const dprgtPersonJointAcc = props.data?.records?.[0];
                  if (
                    dprgtPersonJointAcc?.mediumNo &&
                    dprgtPersonJointAcc?.zoneVal
                  ) {
                    const res = await getDprgtPersonJointAcc(
                        dprgtPersonJointAcc.mediumNo,
                        dprgtPersonJointAcc.zoneVal,
                      "dprgtPersonJointAccPageInfo",
                      {
                        current: 1,
                        pageSize: 1,
                      }
                    );

                    if (res.code === 200 && res.data) {
                      const detailData = JSON.parse(res.data);
                      const total = detailData.total || 0;
                      const lastPage = Math.ceil(total / pagination.pageSize);

                      setPagination((prev) => ({
                        ...prev,
                        current: lastPage,
                        total: total,
                      }));
                    }
                  }

                  await refreshData();
                  return true;
                } else {
                  message.error(res.msg || "新增失败");
                  await refreshData();
                  return false;
                }
              } catch (error) {
                message.error("新增失败");
                return false;
              }
            }

            if (!editingRecord || !rowKey) {
              message.error("编辑记录不存在");
              return false;
            }

            let originalData: DprgtPersonJointAcc | null = null;
            try {
              // 只上送可编辑字段
              const updateData = {
                mediumNo: data.mediumNo,
                custNo: data.custNo,
                perCertTpCd: data.perCertTpCd,
                personalCertNo: data.personalCertNo,
                jointAcrlVatgCd: data.jointAcrlVatgCd,
                custNm: data.custNm,
                lastDealDate: data.lastDealDate,
                dsumPsErrTms: data.dsumPsErrTms,
                psErrTms: data.psErrTms,
                jointNaPersPsStaFlagCd: data.jointNaPersPsStaFlagCd,
                relatBgnDate: data.relatBgnDate,
                relatEndDate: data.relatEndDate,
                relatStaCd: data.relatStaCd,
                lastTxDate: data.lastTxDate,
                zoneVal: data.zoneVal,
                tamProoFieldInfo: data.tamProoFieldInfo,
              };

              // 保存当前数据
              originalData =
                dataSource.find((item) => item.custNo === rowKey) || null;

              const res = await dprgtDprgtPersonJointAccUpdate(updateData);

              if (res.code === 200) {
                // 成功时更新数据
                setDataSource((prevDataSource) =>
                  prevDataSource.map((item) =>
                    item.custNo === rowKey ? { ...data } : item
                  )
                );
                message.success("保存成功");
              } else {
                // API调用失败，回滚数据
                message.error(res.msg || "保存失败");
                if (originalData) {
                  setDataSource((prevDataSource) => {
                    return prevDataSource.map((item) =>
                      item.custNo === rowKey ? originalData : item
                    ) as DprgtPersonJointAcc[];
                  });
                }
              }
            } catch (error) {
              // 发生错误，回滚数据
              message.error("保存失败");
              if (originalData) {
                setDataSource((prevDataSource) => {
                  return prevDataSource.map((item) =>
                    item.custNo === rowKey ? originalData : item
                  ) as DprgtPersonJointAcc[];
                });
              }
            } finally {
              // 退出编辑状态
              setEditableRowKeys([]);
              setIsEditing(false);
              setEditingRecord(null);
              if (props.data) {
                try {
                  const dprgtPersonJointAcc =
                    props.data.records && props.data.records[0];
                  if (
                    dprgtPersonJointAcc?.mediumNo &&
                    dprgtPersonJointAcc?.zoneVal
                  ) {
                    setLoading(true);
                    try {
                      const res = await getDprgtPersonJointAcc(
                        dprgtPersonJointAcc.mediumNo,
                        dprgtPersonJointAcc.zoneVal,
                        "dprgtPersonJointAccPageInfo",
                        {
                          current: pagination.current,
                          pageSize: pagination.pageSize,
                        }
                      );
                      if (res.code === 200 && res.data) {
                        const detailData = JSON.parse(res.data);
                        setDataSource(detailData.records || []);
                        setPagination({
                          current:
                            detailData.current || INITIAL_PAGINATION.current,
                          pageSize:
                            detailData.size || INITIAL_PAGINATION.pageSize,
                          total: detailData.total || INITIAL_PAGINATION.total,
                        });
                        setRefresh(refresh + 1);
                      } else {
                        message.error(res.msg || "获取个人联名账户信息记录表失败");
                      }
                    } catch (error) {
                      message.error("查询个人联名账户信息记录表失败");
                    } finally {
                      setLoading(false);
                    }
                  }
                } catch (error) {
                  message.error("查询个人联名账户信息记录表失败");
                }
              }
            }
          },
          onChange: setEditableRowKeys,
          onCancel: async (rowKey) => {
            if (editingRecord) {
              setDataSource((prevDataSource) =>
                prevDataSource.map((item) =>
                  item.custNo === editingRecord.custNo
                    ? { ...editingRecord }
                    : item
                )
              );
            }
            // 退出编辑状态
            setEditableRowKeys([]);
            setIsEditing(false);
            setEditingRecord(null);
            return true;
          },
        }}
      />
    </Modal>
  );
};

export default DprgtPersonJointAccFormEdit;
