import React, { useEffect } from "react";
import moment from "moment";
import {
  ProFormText,
  ProFormSelect,
  ProFormDatePicker,
} from "@ant-design/pro-form";
import { Form, Modal, Row, Col, Spin } from "antd";
import type { TbDpmstFixCont } from "../data";

/*
 * @Description: 定期合约主档表编辑表单
 * @Author: zhujinwei
 * @Date: 2025-01-17 10:00:00
 */

export type DpmstFixContFormValueType = Record<string, unknown> &
  Partial<TbDpmstFixCont>;

export type DpmstFixContFormProps = {
  onCancel: (flag?: boolean, formVals?: DpmstFixContFormValueType) => void;
  onSubmit: (values: DpmstFixContFormValueType) => Promise<void>;
  visible: boolean;
  values: Partial<TbDpmstFixCont>;
  loading?: boolean; // 添加loading属性
  submitting?: boolean; // 添加提交状态属性
};

const DpmstFixContForm: React.FC<DpmstFixContFormProps> = (props) => {
  const [form] = Form.useForm();

  useEffect(() => {
    if (props.visible && props.values) {
      console.log("表单设置值:", props.values);
      form.resetFields();
      form.setFieldsValue({
        prodtContractNo: props.values.prodtContractNo, // 产品合约编号
        baseProdtNo: props.values.baseProdtNo, // 基础产品编码
        vendibiProdtNo: props.values.vendibiProdtNo, // 可售产品编码
        vendibiProdtVerNo: props.values.vendibiProdtVerNo, // 可售产品版本号
        savTypeMclassCode: props.values.savTypeMclassCode, // 储种中类代码
        currCode: props.values.currCode, // 币种代码
        cashExgVatgCd: props.values.cashExgVatgCd, // 钞汇类别代码
        contrSignDt: props.values.contrSignDt, // 合约签约日期
        contrSignTime: props.values.contrSignTime, // 合约签约时间
        contrDueDate: props.values.contrDueDate, // 合约到期日期
        termUnitCode: props.values.termUnitCode, // 期限单位代码
        contractTerm: props.values.contractTerm, // 合约期限
        signContInstNo: props.values.signContInstNo, // 签约机构号
        signChnKindCd: props.values.signChnKindCd, // 签约渠道种类代码
        contrCacContDt: props.values.contrCacContDt, // 合约解约日期
        contrCacContTime: props.values.contrCacContTime, // 合约解约时间
        cacContInstNo: props.values.cacContInstNo, // 解约机构号
        lastMobilityTxDate: props.values.lastMobilityTxDate, // 最后动户交易日期
        dpContrTpCd: props.values.dpContrTpCd, // 个人存款合约类型代码
        prodtContractName: props.values.prodtContractName, // 产品合约名称
        mainContrFlag: props.values.mainContrFlag, // 主合约标志
        mContrFlagCd: props.values.mContrFlagCd, // 主合约控制标志码
        maxSaccnoSeqNo: props.values.maxSaccnoSeqNo, // 最大子账号序号
        contrCtrlFlagCd: props.values.contrCtrlFlagCd, // 合约控制标志码
        contrAttrFgCd: props.values.contrAttrFgCd, // 合约属性标志码
        contrStaFgCd: props.values.contrStaFgCd, // 合约状态标志码
        lastTxDate: props.values.lastTxDate, // 最后交易日期
        lastModStamp: props.values.lastModStamp, // 最后修改时间戳
        zoneVal: props.values.zoneVal, // 客户编号
      });
    }
  }, [form, props.visible, props.values]);

  const handleOk = () => {
    form.submit();
  };

  const handleCancel = () => {
    props.onCancel();
    form.resetFields();
  };

  const handleFinish = async (values: Record<string, any>) => {
    // 转换所有日期字段为yyyyMMdd格式
    const dateFields = [
      "contrSignDt",
      "contrDueDate",
      "contrCacContDt",
      "lastMobilityTxDate",
      "lastTxDate",
    ];
    dateFields.forEach((field) => {
      if (values[field]) {
        values[field] = moment(values[field]).format("YYYYMMDD");
      }
    });
    props.onSubmit(values as DpmstFixContFormValueType);
    return true;
  };

  return (
    <Modal
      width={800}
      title="编辑定期合约信息"
      visible={props.visible}
      destroyOnClose
      onOk={handleOk}
      onCancel={handleCancel}
      bodyStyle={{
        maxHeight: "70vh",
        overflow: "auto",
        padding: "24px",
        borderRadius: "10px", // 添加圆角
      }}
      style={{
        borderRadius: "10px", // Modal整体圆角
        overflow: "hidden", // 确保内容不超出圆角范围
      }}
    >
      <Spin 
        spinning={(props.loading || props.submitting) || false} 
        tip={props.submitting ? "正在提交数据，请稍后..." : "一大波数据正在路上..."}
      >
        <Form
          form={form}
          onFinish={handleFinish}
          initialValues={props.values}
          layout="vertical"
          className="custom-form" // 添加自定义类名
        >
          <style>
            {`
          .custom-form .ant-form-item-label > label {
            font-weight: 600;
          }
        `}
          </style>
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <ProFormText
                name="prodtContractNo"
                label="产品合约编号"
                width="xl"
                placeholder="请输入产品合约编号"
                disabled
                rules={[
                  {
                    required: true,
                    message: "请输入个人内部账号！",
                  },
                ]}
              />
            </Col>
            <Col span={12}>
              <ProFormText
                name="baseProdtNo"
                label="基础产品编码"
                width="xl"
                placeholder="请输入基础产品编码"
              />
            </Col>
          </Row>
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <ProFormText
                name="vendibiProdtNo"
                label="可售产品编码"
                width="xl"
                placeholder="请输入可售产品编码"
              />
            </Col>
            <Col span={12}>
              <ProFormText
                name="vendibiProdtVerNo"
                label="可售产品版本号"
                width="xl"
                placeholder="请输入可售产品版本号"
              />
            </Col>
          </Row>
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <ProFormSelect
                name="savTypeMclassCode"
                label="储种中类代码"
                width="xl"
                placeholder="请输入储种中类代码"
                options={[
                  { value: "010100", label: "010100-储蓄活期" },
                  { value: "010200", label: "010200-结算活期" },
                  { value: "020100", label: "020100-保值储蓄" },
                  { value: "020200", label: "020200-整存整取" },
                  { value: "020300", label: "020300-整存零取" },
                  { value: "020400", label: "020400-零存整取" },
                  { value: "020500", label: "020500-存本取息" },
                  { value: "020600", label: "020600-定额定期" },
                  { value: "020800", label: "020800-大额存单" },
                  { value: "020700", label: "020700-定期协议利率存款" },
                  { value: "030100", label: "030100-不固定定活两便" },
                  { value: "030200", label: "030200-固定定活两便" },
                  { value: "040100", label: "040100-老通知" },
                  { value: "040200", label: "040200-新通知" },
                  { value: "050100", label: "050100-一本通" },
                  { value: "070100", label: "070100-结构性存款" },
                  { value: "990100", label: "990100-小额支付账户" },
                  { value: "990200", label: "990200-行业应用子账户（计息）" },
                  { value: "990300", label: "990300-行业应用子账户（不计息）" },
                  { value: "990400", label: "990400-行业应用子账户（分段）" },
                  { value: "999900", label: "999900-其它" },
                ]}
                fieldProps={{
                  optionItemRender: (item: {
                    value: string;
                    label: string;
                  }) => {
                    const mediumTypeMap: { [key: string]: string } = {
                      "010100": "储蓄活期",
                      "010200": "结算活期",
                      "020100": "保值储蓄",
                      "020200": "整存整取",
                      "020300": "整存零取",
                      "020400": "零存整取",
                      "020500": "存本取息",
                      "020600": "定额定期",
                      "020800": "大额存单",
                      "020700": "定期协议利率存款",
                      "030100": "不固定定活两便",
                      "030200": "固定定活两便",
                      "040100": "老通知",
                      "040200": "新通知",
                      "050100": "一本通",
                      "070100": "结构性存款",
                      "990100": "小额支付账户",
                      "990200": "行业应用子账户（计息）",
                      "990300": "行业应用子账户（不计息）",
                      "990400": "行业应用子账户（分段）",
                      "999900": "其它",
                    };
                    return `${item.value}-${mediumTypeMap[item.value] || item.label}`;
                  },
                }}
              />
            </Col>
            <Col span={12}>
              <ProFormSelect
                name="currCode"
                label="币种代码"
                width="xl"
                placeholder="请输入币种代码"
                options={[
                  { value: "036", label: "036-澳大利亚元" },
                  { value: "124", label: "124-加元" },
                  { value: "344", label: "344-香港元" },
                  { value: "392", label: "392-日元" },
                  { value: "826", label: "826-英镑" },
                  { value: "840", label: "840-美元" },
                  { value: "978", label: "978-欧元（EUR）" },
                  { value: "156", label: "156-人民币元" },
                ]}
                fieldProps={{
                  optionItemRender: (item: {
                    value: string;
                    label: string;
                  }) => {
                    const mediumTypeMap: { [key: string]: string } = {
                      "036": "澳大利亚元",
                      "124": "加元",
                      "344": "香港元",
                      "392": "日元",
                      "826": "英镑",
                      "840": "美元",
                      "978": "欧元（EUR）",
                      "156": "人民币元",
                    };
                    return `${item.value}-${mediumTypeMap[item.value] || item.label}`;
                  },
                }}
              />
            </Col>
          </Row>
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <ProFormSelect
                name="cashExgVatgCd"
                label="钞汇类别代码"
                width="xl"
                placeholder="请输入钞汇类别代码"
                options={[
                  { value: "2", label: "2-钞" },
                  { value: "3", label: "3-汇" },
                ]}
                fieldProps={{
                  optionItemRender: (item: {
                    value: string;
                    label: string;
                  }) => {
                    const mediumTypeMap: { [key: string]: string } = {
                      "2": "钞",
                      "3": "汇",
                    };
                    return `${item.value}-${mediumTypeMap[item.value] || item.label}`;
                  },
                }}
              />
            </Col>
            <Col span={12}>
              <ProFormDatePicker
                name="contrSignDt"
                label="合约签约日期"
                width="xl"
                placeholder="请输入合约签约日期"
              />
            </Col>
          </Row>
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <ProFormDatePicker
                name="contrDueDate"
                label="合约到期日期"
                width="xl"
                placeholder="请输入合约到期日期"
              />
            </Col>
            <Col span={12}>
              <ProFormDatePicker
                name="contrCacContDt"
                label="合约解约日期"
                width="xl"
                placeholder="请输入合约解约日期"
              />
            </Col>
          </Row>
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <ProFormSelect
                name="termUnitCode"
                label="期限单位代码"
                width="xl"
                placeholder="请输入期限单位代码"
                options={[
                  { value: "1", label: "1-日" },
                  { value: "2", label: "2-周" },
                  { value: "3", label: "3-月" },
                  { value: "4", label: "4-季" },
                  { value: "5", label: "5-年" },
                  { value: "6", label: "6-单次" },
                ]}
                fieldProps={{
                  optionItemRender: (item: {
                    value: string;
                    label: string;
                  }) => {
                    const mediumTypeMap: { [key: string]: string } = {
                      "1": "日",
                      "2": "周",
                      "3": "月",
                      "4": "季",
                      "5": "年",
                      "6": "单次",
                    };
                    return `${item.value}-${mediumTypeMap[item.value] || item.label}`;
                  },
                }}
              />
            </Col>
            <Col span={12}>
              <ProFormText
                name="contractTerm"
                label="合约期限"
                width="xl"
                placeholder="请输入合约期限"
              />
            </Col>
          </Row>
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <ProFormText
                name="signContInstNo"
                label="签约机构号"
                width="xl"
                placeholder="请输入签约机构号"
              />
            </Col>
            <Col span={12}>
              <ProFormSelect
                name="signChnKindCd"
                label="签约渠道种类代码"
                width="xl"
                placeholder="请输入签约渠道种类代码"
                options={[
                  { value: "01", label: "01-网点柜面" },
                  { value: "10", label: "10-网上银行" },
                  { value: "12", label: "12-个人网银" },
                  { value: "13", label: "13-电视银行" },
                  { value: "14", label: "14-电话银行" },
                  { value: "15", label: "15-手机银行" },
                  { value: "16", label: "16-企业网银" },
                  { value: "17", label: "17-自助设备" },
                  { value: "18", label: "18-POS" },
                  { value: "20", label: "20-超级网银" },
                  { value: "21", label: "21-大小额支付" },
                  { value: "22", label: "22-银联前置" },
                  { value: "24", label: "24-管理端" },
                  { value: "25", label: "25-交易端" },
                  { value: "26", label: "26-商易通" },
                  { value: "27", label: "27-助农通" },
                  { value: "29", label: "29-外部系统" },
                  { value: "30", label: "30-系统自动" },
                  { value: "31", label: "31-电子汇兑系统" },
                  { value: "32", label: "32-理财规划终端" },
                  { value: "34", label: "34-网汇通" },
                  { value: "35", label: "35-同城支付" },
                  { value: "36", label: "36-移动终端-TSM（可信服务管理）" },
                  { value: "37", label: "37-移动终端-移动展业" },
                  { value: "38", label: "38-直销银行" },
                  { value: "39", label: "39-短信" },
                  { value: "40", label: "40-专属APP" },
                  { value: "41", label: "41-第三方线上渠道" },
                  { value: "43", label: "43-智能柜员机（ITM）" },
                  { value: "42", label: "42-国际支付前置" },
                  { value: "44", label: "44-邮储经营" },
                  { value: "45", label: "45-银银前置系统" },
                  { value: "46", label: "46-U链供应链" },
                  { value: "47", label: "47-油料保障结算系统" },
                  { value: "48", label: "48-银企直联" },
                  { value: "91", label: "91-微信银行" },
                  { value: "99", label: "99-其他" },
                ]}
                fieldProps={{
                  optionItemRender: (item: {
                    value: string;
                    label: string;
                  }) => {
                    const mediumTypeMap: { [key: string]: string } = {
                      "01": "网点柜面",
                      "10": "网上银行",
                      "12": "个人网银",
                      "13": "电视银行",
                      "14": "电话银行",
                      "15": "手机银行",
                      "16": "企业网银",
                      "17": "自助设备",
                      "18": "POS",
                      "20": "超级网银",
                      "21": "大小额支付",
                      "22": "银联前置",
                      "24": "管理端",
                      "25": "交易端",
                      "26": "商易通",
                      "27": "助农通",
                      "29": "外部系统",
                      "30": "系统自动",
                      "31": "电子汇兑系统",
                      "32": "理财规划终端",
                      "34": "网汇通",
                      "35": "同城支付",
                      "36": "移动终端-TSM（可信服务管理）",
                      "37": "移动终端-移动展业",
                      "38": "直销银行",
                      "39": "短信",
                      "40": "专属APP",
                      "41": "第三方线上渠道",
                      "43": "智能柜员机（ITM）",
                      "42": "国际支付前置",
                      "44": "邮储经营",
                      "45": "银银前置系统",
                      "46": "U链供应链",
                      "47": "油料保障结算系统",
                      "48": "银企直联",
                      "91": "微信银行",
                      "99": "其他",
                    };
                    return `${item.value}-${mediumTypeMap[item.value] || item.label}`;
                  },
                }}
              />
            </Col>
          </Row>
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <ProFormSelect
                name="dpContrTpCd"
                label="个人存款合约类型代码"
                width="xl"
                placeholder="请输入个人存款合约类型代码"
                options={[
                  { value: "1001", label: "1001-人民币活期储蓄合约" },
                  { value: "1002", label: "1002-人民币活期结算合约" },
                  { value: "1003", label: "1003-外币活期储蓄合约" },
                  { value: "1004", label: "1004-外币活期结算合约" },
                  { value: "1005", label: "1005-本外币合一结算合约" },
                  { value: "2001", label: "2001-整存整取储蓄存款合约" },
                  { value: "2002", label: "2002-整存整取协议存款合约" },
                  { value: "2003", label: "2003-提前付息定期存款合约" },
                  { value: "2004", label: "2004-定活两便储蓄存款合约" },
                  { value: "2005", label: "2005-整存零取储蓄存款合约" },
                  { value: "2006", label: "2006-存本取息储蓄存款合约" },
                  { value: "2007", label: "2007-零存整取储蓄存款合约" },
                  { value: "2008", label: "2008-通知存款合约" },
                  { value: "2009", label: "2009-结构性存款合约" },
                  { value: "2010", label: "2010-递增计息合约" },
                  { value: "2011", label: "2011-梦想加邮站合约" },
                  { value: "2012", label: "2012-大额存单合约" },
                  { value: "2013", label: "2013-礼仪存单合约" },
                  { value: "2014", label: "2014-邮智存合约" },
                  { value: "3001", label: "3001-行业应用子账户合约" },
                  { value: "3002", label: "3002-电子现金账户合约" },
                  { value: "4001", label: "4001-副卡合约" },
                  { value: "4002", label: "4002-映射卡合约" },
                  { value: "4003", label: "4003-本外币定期一本通合约" },
                ]}
                fieldProps={{
                  optionItemRender: (item: {
                    value: string;
                    label: string;
                  }) => {
                    const mediumTypeMap: { [key: string]: string } = {
                      "1001": "人民币活期储蓄合约",
                      "1002": "人民币活期结算合约",
                      "1003": "外币活期储蓄合约",
                      "1004": "外币活期结算合约",
                      "1005": "本外币合一结算合约",
                      "2001": "整存整取储蓄存款合约",
                      "2002": "整存整取协议存款合约",
                      "2003": "提前付息定期存款合约",
                      "2004": "定活两便储蓄存款合约",
                      "2005": "整存零取储蓄存款合约",
                      "2006": "存本取息储蓄存款合约",
                      "2007": "零存整取储蓄存款合约",
                      "2008": "通知存款合约",
                      "2009": "结构性存款合约",
                      "2010": "递增计息合约",
                      "2011": "梦想加邮站合约",
                      "2012": "大额存单合约",
                      "2013": "礼仪存单合约",
                      "2014": "邮智存合约",
                      "3001": "行业应用子账户合约",
                      "3002": "电子现金账户合约",
                      "4001": "副卡合约",
                      "4002": "映射卡合约",
                      "4003": "本外币定期一本通合约",
                    };
                    return `${item.value}-${mediumTypeMap[item.value] || item.label}`;
                  },
                }}
              />
            </Col>
            <Col span={12}>
              <ProFormText
                name="cacContInstNo"
                label="解约机构号"
                width="xl"
                placeholder="请输入解约机构号"
              />
            </Col>
          </Row>
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <ProFormDatePicker
                name="lastMobilityTxDate"
                label="最后动户交易日期"
                width="xl"
                placeholder="请输入最后动户交易日期"
              />
            </Col>
            <Col span={12}>
              <ProFormText
                name="prodtContractName"
                label="产品合约名称"
                width="xl"
                placeholder="请输入产品合约名称"
              />
            </Col>
          </Row>
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <ProFormSelect
                name="mainContrFlag"
                label="主合约标志"
                width="xl"
                placeholder="请输入主合约标志"
                options={[
                  { value: "0", label: "0-否" },
                  { value: "1", label: "1-是" },
                ]}
                fieldProps={{
                  optionItemRender: (item: {
                    value: string;
                    label: string;
                  }) => {
                    const mediumTypeMap: { [key: string]: string } = {
                      "0": "否",
                      "1": "是",
                    };
                    return `${item.value}-${mediumTypeMap[item.value] || item.label}`;
                  },
                }}
              />
            </Col>
            <Col span={12}>
              <ProFormText
                name="zoneVal"
                label="分片值"
                width="xl"
                placeholder="请输入分片值"
                disabled
                rules={[
                  {
                    required: true,
                    message: "请输入分片值！",
                  },
                ]}
              />
            </Col>
          </Row>
        </Form>
      </Spin>
    </Modal>
  );
};

export default DpmstFixContForm;
