import React, { useEffect, useState, useCallback } from "react";
import { Modal, message, <PERSON><PERSON>, Spin } from "antd";
import { 
  ProFormRadio, 
  EditableProTable 
} from "@ant-design/pro-components";
import type { ProColumns } from "@ant-design/pro-components";
import {
  tbDprgtAccFreeUpdate,
  getTbDprgtAccFree,
  insertTbDprgtAccFree,
  tbDprgtAccFreeDelete,
} from "../service";
import type { DprgtAccFree, PageResponse } from "../data";

/*
 * @Description: 账户免收登记簿编辑表单
 * @Author: taylor zhu
 * @Date: 2024-08-03 10:00:00
 */

interface PaginationType {
  current: number;
  pageSize: number;
  total: number;
}

export type DprgtCarryBalCalIntFormProps = {
  onCancel: () => void;
  open: boolean;
  data: PageResponse<DprgtAccFree> | null;
};

const DEFAULT_PAGE_SIZE = 10;
const TABLE_SCROLL = { x: 2000 } as const;
const MODAL_BODY_STYLE = {
  maxHeight: "70vh",
  overflow: "auto",
  padding: "24px",
  borderRadius: "10px",
} as const;

const MODAL_STYLE = {
  borderRadius: "10px",
  overflow: "hidden",
} as const;

const INITIAL_PAGINATION: PaginationType = {
  current: 1,
  pageSize: DEFAULT_PAGE_SIZE,
  total: 0,
};

const DprgtAccFreeFormEdit: React.FC<DprgtCarryBalCalIntFormProps> = (props) => {
  const [dataSource, setDataSource] = useState<readonly DprgtAccFree[]>([]);
  const [editableKeys, setEditableKeys] = useState<React.Key[]>([]);
  const [isEditing, setIsEditing] = useState(false);
  const [editingRecord, setEditingRecord] = useState<DprgtAccFree | null>(null);
  const [pagination, setPagination] = useState<PaginationType>(INITIAL_PAGINATION);
  const [refresh, setRefresh] = useState(0);
  const [loading, setLoading] = useState(false);
  const [dprgtAccFreeInfo, setDprgtAccFreeInfo] = useState<{persInnerAccno?: string, zoneVal?: string} | null>(null);
  const [position, setPosition] = useState<'top' | 'bottom' | 'hidden'>('bottom');
  const [persInnerAccno, setPersInnerAccno] = useState<string | undefined>(undefined);
  const [zoneVal, setZoneVal] = useState<string | undefined>(undefined);

  useEffect(() => {
    if (props.open) {
      // Check if data has a loading property and set the loading state
      if (props.data?.loading !== undefined) {
        setLoading(props.data.loading);
      }
      
      if (props.data?.records && props.data.records.length > 0) {
        const records = props.data.records;
        setDataSource(records);
        setPagination({
          ...pagination,
          total: props.data?.total || records.length,
        });
        
        // 找到第一条有效记录，获取persInnerAccno和zoneVal
        const firstValidRecord = records.find(record => record.persInnerAccno && record.zoneVal);
        
        if (firstValidRecord?.persInnerAccno && firstValidRecord?.zoneVal) {
          // 只更新状态，不再重复请求数据
          setDprgtAccFreeInfo({
            persInnerAccno: firstValidRecord.persInnerAccno,
            zoneVal: firstValidRecord.zoneVal
          });
          setPersInnerAccno(firstValidRecord.persInnerAccno);
          setZoneVal(firstValidRecord.zoneVal);
        }
      } else if (!props.data?.loading) {
        // Only clear data if not in loading state
        setDataSource([]);
        setPagination(INITIAL_PAGINATION);
        setDprgtAccFreeInfo(null);
        setPersInnerAccno(undefined);
        setZoneVal(undefined);
      }
    } else {
      setDataSource([]);
      setPagination(INITIAL_PAGINATION);
      setDprgtAccFreeInfo(null);
      setPersInnerAccno(undefined);
      setZoneVal(undefined);
    }
  }, [props.open, props.data]);

  const refreshData = useCallback(
    async (current: number = pagination.current, pageSize: number = pagination.pageSize) => {
      // 使用当前状态或accountInfo作为后备
      const accountNo = persInnerAccno || dprgtAccFreeInfo?.persInnerAccno;
      const zoneValue = zoneVal || dprgtAccFreeInfo?.zoneVal;
      
      if (!accountNo || !zoneValue) {
        // 向用户显示错误消息，便于调试
        console.error('查询参数不完整:', { accountNo, zoneValue });
        message.error('分页查询失败：账号信息不完整，请重新打开弹窗');
        return;
      }
      
      setLoading(true);
      
      try {
        const result = await getTbDprgtAccFree(
          accountNo,
          zoneValue,
          'DP_RGT_ACC_FREE_ALL_QUERYPAGES',
          {
            current,
            pageSize,
          },
        );
        
        if (result && result.data) {
          const detailData = JSON.parse(result.data);
          const newPagination = {
            current: current,
            pageSize: pageSize,
            total: detailData.total || detailData.records.length,
          };
          
          setPagination(newPagination);
          setDataSource(detailData.records || []);
          console.log('查询成功，数据条数:', detailData.records?.length);
        }
      } catch (error) {
        console.error('获取数据失败:', error);
        message.error('获取账户星级登记簿数据失败');
      } finally {
        // Ensure loading state is always cleared
        setLoading(false);
      }
    },
    [persInnerAccno, zoneVal, dprgtAccFreeInfo, pagination.current, pagination.pageSize],
  );

  // 定义表格列
  // const [position, setPosition] = useState<"top" | "bottom" | "hidden">("bottom");

  const columns: ProColumns<DprgtAccFree>[] = [
    {
      title: '交易日期',
      dataIndex: 'txDate',
      width: 160,
      ellipsis: true,
      align: "center",
      valueType: "date",
    },
    {
      title: '全局业务跟踪号',
      dataIndex: 'globalBusiTrackNo',
      width: 260,
      ellipsis: true,
      align: "center",
    },
    {
      title: '子交易序号',
      dataIndex: 'subtxNo',
      width: 260,
      ellipsis: true,
      align: "center",
    },
    {
      title: '客户编号',
      dataIndex: 'custNo',
      width: 160,
      ellipsis: true,
      align: "center",
    },
    {
      title: '客户名称',
      dataIndex: 'custNm',
      width: 160,
      ellipsis: true,
      align: "center",
    },
    {
      title: '介质编号',
      dataIndex: 'mediumNo',
      width: 220,
      ellipsis: true,
      align: "center",
    },
    {
      title: '个人内部账号',
      dataIndex: 'persInnerAccno',
      width: 220,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: '免收类型代码',
      dataIndex: 'freeTpCd',
      width: 160,
      ellipsis: true,
      align: "center",
      valueType: "select",
      valueEnum: {
        "06": { text: "06-卡年费" },
      },
    },
    {
      title: '免收开始日期',
      dataIndex: 'freeBgnDt',
      width: 160,
      ellipsis: true,
      align: "center",
      valueType: "date",
    },
    {
      title: '免收到期日期',
      dataIndex: 'freeDueDate',
      width: 160,
      ellipsis: true,
      align: "center",
      valueType: "date",
    },
    {
      title: '免收原因代码',
      dataIndex: 'freeReasnCd',
      width: 160,
      ellipsis: true,
      align: "center",
      valueType: "select",
      valueEnum: {
        "01": { text: "01-唯一账户免" },
      },
    },
    {
      title: '是否变更标志',
      dataIndex: 'chgFlag',
      width: 160,
      ellipsis: true,
      align: "center",
      valueType: "select",
      valueEnum: {
        "0": { text: "0-否" },
        "1": { text: "1-是" },
      },
    },
    {
      title: '账户免收状态代码',
      dataIndex: 'accFreeStaCd',
      width: 160,
      ellipsis: true,
      align: "center",
      valueType: "select",
      valueEnum: {
        "0": { text: "0-无效" },
        "1": { text: "1-有效" },
      },
    },
    {
      title: '占用免收额度标志',
      dataIndex: 'ocupFreeLimitFlag',
      width: 160,
      ellipsis: true,
      align: "center",
      valueType: "select",
      valueEnum: {
        "0": { text: "0-否" },
        "1": { text: "1-是" },
      },
    },
    {
      title: '免收操作类型代码',
      dataIndex: 'freeOprTpCd',
      width: 160,
      ellipsis: true,
      align: "center",
      valueType: "select",
      valueEnum: {
        "1": { text: "1-单笔" },
      },
    },
    {
      title: '开户机构号',
      dataIndex: 'openAccInstNo',
      width: 160,
      ellipsis: true,
      align: "center",
    },
    {
      title: '交易机构号',
      dataIndex: 'txInstNo',
      width: 160,
      ellipsis: true,
      align: "center",
    },
    {
      title: '交易柜员号',
      dataIndex: 'txTellerNo',
      width: 160,
      ellipsis: true,
      align: "center",
    },
    {
      title: '授权柜员号',
      dataIndex: 'authTellerNo',
      width: 160,
      ellipsis: true,
      align: "center",
    },
    {
      title: '交易时间',
      dataIndex: 'txTime',
      width: 160,
      ellipsis: true,
      align: "center",
    },
    {
      title: '交易渠道种类代码',
      dataIndex: 'txChnlKindCd',
      width: 160,
      ellipsis: true,
      align: "center",
      valueType: "select",
      valueEnum: {
        "01": { text: "01-网点柜面" },
        "10": { text: "10-网上银行" },
        "12": { text: "12-个人网银" },
        "13": { text: "13-电视银行" },
        "14": { text: "14-电话银行" },
        "15": { text: "15-手机银行" },
        "16": { text: "16-企业网银" },
        "17": { text: "17-自助设备" },
        "18": { text: "18-POS" },
        "20": { text: "20-超级网银" },
        "21": { text: "21-大小额支付" },
        "22": { text: "22-银联前置" },
        "24": { text: "24-管理端" },
        "25": { text: "25-交易端" },
        "26": { text: "26-商易通" },
        "27": { text: "27-助农通" },
        "29": { text: "29-外部系统" },
        "30": { text: "30-系统自动" },
        "31": { text: "31-电子汇兑系统" },
        "32": { text: "32-理财规划终端" },
        "34": { text: "34-网汇通" },
        "35": { text: "35-同城支付" },
        "36": { text: "36-移动终端-TSM（可信服务管理）" },
        "37": { text: "37-移动终端-移动展业" },
        "38": { text: "38-直销银行" },
        "39": { text: "39-短信" },
        "40": { text: "40-专属APP" },
        "41": { text: "41-第三方线上渠道" },
        "43": { text: "43-智能柜员机（ITM）" },
        "42": { text: "42-国际支付前置" },
        "44": { text: "44-邮储经营" },
        "45": { text: "45-银银前置系统" },
        "46": { text: "46-U链供应链" },
        "47": { text: "47-油料保障结算系统" },
        "48": { text: "48-银企直联" },
        "91": { text: "91-微信银行" },
        "99": { text: "99-其他" },
      },
    },
    {
      title: '变更日期',
      dataIndex: 'chgDate',
      width: 160,
      ellipsis: true,
      align: "center",
      valueType: "date",
    },
    {
      title: '变更全局业务跟踪号',
      dataIndex: 'chgGloTracNo',
      width: 260,
      ellipsis: true,
      align: "center",
    },
    {
      title: '变更子交易序号',
      dataIndex: 'chgSubtxNo',
      width: 260,
      ellipsis: true,
      align: "center",
    },
    {
      title: '交易变更操作类型',
      dataIndex: 'txChgOprTpCd',
      width: 160,
      ellipsis: true,
      align: "center",
      valueType: "select",
      valueEnum: {
        "1": { text: "1-单笔" },
      },
    },
    {
      title: '变更机构号',
      dataIndex: 'chgInstNo',
      width: 160,
      ellipsis: true,
      align: "center",
    },
    {
      title: '变更柜员号',
      dataIndex: 'chgTellerNo',
      width: 160,
      ellipsis: true,
      align: "center",
    },
    {
      title: '变更授权柜员号',
      dataIndex: 'chgAuthTellerNo',
      width: 160,
      ellipsis: true,
      align: "center",
    },
    {
      title: '变更渠道种类代码',
      dataIndex: 'chgChnlKindCode',
      width: 160,
      ellipsis: true,
      align: "center",
      valueType: "select",
      valueEnum: {
        "01": { text: "01-网点柜面" },
      },
    },
    {
      title: '最后交易日期',
      dataIndex: 'lastTxDate',
      width: 160,
      ellipsis: true,
      align: "center",
      valueType: "date",
    },
    {
      title: '分片值',
      dataIndex: 'zoneVal',
      width: 160,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "操作",
      valueType: "option",
      width: 150,
      fixed: "right",
      align: "center",
      render: (text, record: DprgtAccFree, _, action) => {
        const isRowEditing = record.persInnerAccno
          ? editableKeys.includes(record.persInnerAccno)
          : false;
        return [
          isRowEditing ? (
            <>
              <Button
                key="confirm"
                type="link"
                onClick={async () => {
                  try {
                    if (record.persInnerAccno) {
                      await action?.saveEditable?.(record.persInnerAccno);
                      setIsEditing(false);
                    }
                  } catch (err) {
                    console.error("保存失败:", err);
                  }
                }}
              >
                确定
              </Button>
              <Button
                key="cancel"
                type="link"
                onClick={() => {
                  if (record.persInnerAccno) {
                    action?.cancelEditable?.(record.persInnerAccno);
                    setEditableKeys([]);
                    setIsEditing(false);
                  }
                }}
                style={{ marginLeft: 8 }}
              >
                取消
              </Button>
            </>
          ) : (
            <Button
              key="editable"
              type="link"
              disabled={true}
              onClick={async () => {
                if (!record.persInnerAccno) return;
                const originalRecord = { ...record };
                setEditingRecord(originalRecord);
                setIsEditing(true);
                await action?.startEditable?.(record.persInnerAccno);
              }}
              style={{ padding: 0 }}
            >
              编辑
            </Button>
          ),
          <Button
            key="delete"
            type="link"
            // disabled={isEditing}
            disabled={true}
            onClick={async () => {
              if (!record.persInnerAccno) return;
              Modal.confirm({
                title: "确认删除",
                content: "确定要删除这条记录吗？",
                onOk: async () => {
                  try {
                    const res = await tbDprgtAccFreeDelete(record);
                    if (res.code === 200) {
                      message.success("删除成功");
                    } else {
                      message.error(res.msg || "删除失败");
                    }
                  } catch (error) {
                    message.error("删除失败");
                  } finally {
                    await refreshData();
                  }
                },
                okText: "确认",
                cancelText: "取消",
              });
            }}
            style={{ marginLeft: 8 }}
          >
            删除
          </Button>,
        ];
      },
    },
  ];

  const handleAdd = async (record: DprgtAccFree) => {
    try {
      // 设置必要的默认字段
      const newRecord = {
        ...record,
        persInnerAccno: persInnerAccno,
        zoneVal: zoneVal,
        recordStaCd: "1", // 默认有效
        effStaCd: "1", // 默认有效
      };
      
      setLoading(true);
      const res = await insertTbDprgtAccFree(newRecord);
      if (res.code === 200) {
        message.success('添加成功');
        refreshData();
      } else {
        message.error(res.msg || '添加失败');
      }
    } catch (error) {
      console.error('添加失败:', error);
      message.error('添加失败');
    } finally {
      // 添加完成后重置状态
      setLoading(false);
      setRefresh(prev => prev + 1);
      setEditableKeys([]);
      setIsEditing(false);
    }
  };

  return (
    <Modal
      width={1200}
      title="账户免收登记簿"
      open={props.open}
      destroyOnClose
      onCancel={props.onCancel}
      footer={null}
      bodyStyle={MODAL_BODY_STYLE}
      style={MODAL_STYLE}
    >
      <Spin spinning={loading} tip="加载数据中..." size="large">
      <div style={{ marginBottom: 16 }}>
          <p>
            数据状态: {loading ? (
              <span>
                <Spin size="small" style={{ marginRight: 8 }} />
                加载中...
              </span>
            ) : (
              `已加载 ${dataSource.length} 条记录, 总共 ${pagination.total} 条`
            )}
            {dprgtAccFreeInfo && (
              <span style={{ marginLeft: 8 }}>
                | 个人内部账号: {dprgtAccFreeInfo.persInnerAccno}
              </span>
            )}
          </p>
        </div>
        <EditableProTable<DprgtAccFree>
          key={refresh}
          rowKey={(record) => record.persInnerAccno || ""}
          headerTitle="账户免收登记簿信息"
          scroll={TABLE_SCROLL}
          loading={loading}
          toolBarRender={() => [
            <ProFormRadio.Group
              key="positionRender"
              fieldProps={{
                value: position,
                onChange: (e: any) => setPosition(e.target.value),
              }}
              options={[
                { label: "添加到顶部", value: "top" },
                { label: "添加到底部", value: "bottom" },
                { label: "隐藏", value: "hidden" },
              ]}
            />,
          ]}
          recordCreatorProps={{
            position: position === 'hidden' ? 'bottom' : position,
            record: (index, dataSource) => {
              // 直接创建新记录，不检查是否有正在编辑的行
              return {
                persInnerAccno: persInnerAccno,
                txDate: new Date().toISOString().split('T')[0], // 交易日期
                globalBusiTrackNo: "XXXXXXXXXXX",
                subtxNo: "XXXXXXXXXXX",
                custNo: zoneVal,
                effStaCd: "1", // 默认有效
                lastTmTxDt: new Date().toISOString().split('T')[0], // 上次交易日期
                lastTxDate: new Date().toISOString().split('T')[0], // 最后交易日期
                recordStaCd: "1", // 默认有效
                zoneVal: zoneVal,
              } as DprgtAccFree;
            },
            creatorButtonText: '添加一行数据',
            newRecordType: 'dataSource',
          }}
          columns={columns}
          request={async (params, sorter, filter) => {
            // 直接返回当前数据源，不进行额外的API调用
            return {
              data: dataSource as DprgtAccFree[],
              total: pagination.total,
              success: true,
            };
          }}
          value={dataSource}
          onChange={setDataSource}
          editable={{
            type: 'multiple', // 支持同时编辑多行
            editableKeys,
            onSave: async (rowKey, data, row) => {
              try {
                // 如果是新添加的记录则调用新增接口
                if (String(rowKey).startsWith('NEW_')) {
                  await handleAdd(data);
                } else {
                  // 否则调用更新接口
                  setLoading(true);
                  const res = await tbDprgtAccFreeUpdate(data);
                  if (res.code === 200) {
                    message.success('更新成功');
                    refreshData();
                  } else {
                    message.error(res.msg || '更新失败');
                  }
                }
              } catch (error) {
                console.error('保存失败:', error);
                message.error('保存失败');
              } finally {
                // 保存后从编辑状态中移除当前行
                setEditableKeys(keys => keys.filter(key => key !== rowKey));
                if (editableKeys.length <= 1) {
                  setIsEditing(false);
                }
                setLoading(false);
              }
            },
            onChange: setEditableKeys,
            onCancel: async (key) => {
              // 取消编辑时只清除当前行的编辑状态
              setEditableKeys(keys => keys.filter(k => k !== key));
              if (editableKeys.length <= 1) {
                setIsEditing(false);
              }
              return true;
            },
            actionRender: (row, config, dom) => [dom.save, dom.cancel],
          }}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条`,
            onChange: async (page, pageSize) => {
              console.log('分页改变:', { page, pageSize, currentPage: pagination.current, isEditing });
              
              if (isEditing) {
                message.warn('请先完成编辑操作再切换页面');
                return;
              }

              // 仅当页码或页大小变化时才刷新数据
              if (page !== pagination.current || pageSize !== pagination.pageSize) {
                // 更新分页状态
                setPagination({
                  ...pagination,
                  current: page,
                  pageSize: pageSize,
                });

                // 直接调用刷新数据，不做额外判断
                await refreshData(page, pageSize);
              }
            },
          }}
        />
      </Spin>
    </Modal>
  );
};

export default DprgtAccFreeFormEdit;