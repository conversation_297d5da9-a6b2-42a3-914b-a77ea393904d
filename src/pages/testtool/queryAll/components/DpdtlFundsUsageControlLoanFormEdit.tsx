import React, { useEffect, useState, useCallback } from "react";
import { <PERSON><PERSON>, message, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Spin } from "antd";
import { 
  ProFormRadio, 
  EditableProTable 
} from "@ant-design/pro-components";
import { InfoCircleOutlined } from "@ant-design/icons";
import type { ProColumns } from "@ant-design/pro-components";
import {
    getDpdtlFundsUsageControlLoan,
    insertDpdtlFundsUsageControlLoan,
    updateDpdtlFundsUsageControlLoan,
    deleteDpdtlFundsUsageControlLoan,
} from "../service";
import type { TbDpdtlFundsUsageControlLoan, PageResponse } from "../data";

/*
 * @Description: 资金用途管控借据明细表编辑表单
 * @Author: taylor zhu
 * @Date: 2023-04-15 10:00:00
 */

interface PaginationType {
  current: number;
  pageSize: number;
  total: number;
}

export type DpdtlFundsUsageControlLoanFormProps = {
  onCancel: () => void;
  open: boolean;
  data: PageResponse<TbDpdtlFundsUsageControlLoan> | null;
};

const DEFAULT_PAGE_SIZE = 10;
const TABLE_SCROLL = { x: 2000 } as const;
const MODAL_BODY_STYLE = {
  maxHeight: "70vh",
  overflow: "auto",
  padding: "24px",
  borderRadius: "10px",
} as const;

const MODAL_STYLE = {
  borderRadius: "10px",
  overflow: "hidden",
} as const;

const INITIAL_PAGINATION: PaginationType = {
  current: 1,
  pageSize: DEFAULT_PAGE_SIZE,
  total: 0,
};

const DpdtlFundsUsageControlLoanFormEdit: React.FC<DpdtlFundsUsageControlLoanFormProps> = (props) => {
  const [dataSource, setDataSource] = useState<readonly TbDpdtlFundsUsageControlLoan[]>([]);
  const [editableKeys, setEditableKeys] = useState<React.Key[]>([]);
  const [isEditing, setIsEditing] = useState(false);
  const [editingRecord, setEditingRecord] = useState<TbDpdtlFundsUsageControlLoan | null>(null);
  const [pagination, setPagination] = useState<PaginationType>(INITIAL_PAGINATION);
  const [refresh, setRefresh] = useState(0);
  const [loading, setLoading] = useState(false);
  const [accountInfo, setAccountInfo] = useState<{persInnerAccno?: string, zoneVal?: string} | null>(null);
  const [persInnerAccno, setPersInnerAccno] = useState<string | undefined>(undefined);
  const [zoneVal, setZoneVal] = useState<string | undefined>(undefined);

  useEffect(() => {
    if (props.open) {
      // Check if data has a loading property and set the loading state
      if (props.data?.loading !== undefined) {
        setLoading(props.data.loading);
      }
      
      if (props.data?.records && props.data.records.length > 0) {
        const records = props.data.records;
        setDataSource(records);
        setPagination({
          ...pagination,
          total: props.data?.total || records.length,
        });
        
        // 找到第一条有效记录，获取persInnerAccno和zoneVal
        const firstValidRecord = records.find(record => record.persInnerAccno && record.zoneVal);
        
        if (firstValidRecord?.persInnerAccno && firstValidRecord?.zoneVal) {
          // 只更新状态，不再重复请求数据
          setAccountInfo({
            persInnerAccno: firstValidRecord.persInnerAccno,
            zoneVal: firstValidRecord.zoneVal
          });
          setPersInnerAccno(firstValidRecord.persInnerAccno);
          setZoneVal(firstValidRecord.zoneVal);
        }
      } else if (!props.data?.loading) {
        // Only clear data if not in loading state
        setDataSource([]);
        setPagination(INITIAL_PAGINATION);
        setAccountInfo(null);
        setPersInnerAccno(undefined);
        setZoneVal(undefined);
      }
    } else {
      setDataSource([]);
      setPagination(INITIAL_PAGINATION);
      setAccountInfo(null);
      setPersInnerAccno(undefined);
      setZoneVal(undefined);
    }
  }, [props.open, props.data]);

  const refreshData = useCallback(
    async (current: number = pagination.current, pageSize: number = pagination.pageSize) => {
      // 使用当前状态或accountInfo作为后备
      const accountNo = persInnerAccno || accountInfo?.persInnerAccno;
      const zoneValue = zoneVal || accountInfo?.zoneVal;
      
      if (!accountNo || !zoneValue) {
        // 只在控制台记录错误，不向用户显示消息
        console.warn('查询参数不完整');
        return;
      }
      
      setLoading(true);
      
      try {
        const result = await getDpdtlFundsUsageControlLoan(
          accountNo,
          zoneValue,
          'DPDTL_FUNDS_USAGE_CONTROL_ALL_QUERYPAGES',
          {
            current,
            pageSize,
          },
        );
        
        if (result && result.data) {
          const detailData = JSON.parse(result.data);
          const newPagination = {
            current: current,
            pageSize: pageSize,
            total: detailData.total || detailData.records.length,
          };
          
          setPagination(newPagination);
          setDataSource(detailData.records || []);
        }
      } catch (error) {
        console.error('获取数据失败:', error);
        message.error('获取资金用途管控明细数据失败');
      } finally {
        setLoading(false);
      }
    },
    [persInnerAccno, zoneVal, accountInfo],
  );

  // 定义表格列
  const [position, setPosition] = useState<"top" | "bottom" | "hidden">("bottom");

  const columns: ProColumns<TbDpdtlFundsUsageControlLoan>[] = [
    {
      title: "资金用途管控借据编号",
      dataIndex: "fundsUsageCtrllgLnrctNo",
      width: 260,
      fixed: false,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "交易日期",
      dataIndex: "txDate",
      width: 120,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "date",
    },
    {
      title: "全局业务跟踪号",
      dataIndex: "globalBusiTrackNo",
      width: 260,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "子交易序号",
      dataIndex: "subtxNo",
      width: 260,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "明细序号",
      dataIndex: "dtlSeqNo",
      width: 120,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "个人内部账号",
      dataIndex: "persInnerAccno",
      width: 220,
      ellipsis: true,
      align: "center", 
      editable: () => false,
    },
    {
      title: "资金用途管控类型代码",
      dataIndex: "fundsUsageControllTpCd",
      width: 220,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "select",
      valueEnum: {
        "1": { text: "1-个人额度类消费" },
        "2": { text: "2-个人小额贷款" },
        "3": { text: "3-信用卡现金分期" },
        "4": { text: "4-个人信贷水滴还款收单管控" },
      },
    },
    {
      title: "资金使用顺序代码",
      dataIndex: "fundsUseSeqCd",
      width: 220,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "select", 
      valueEnum: {
        "1": { text: "1-先活期结算主账户，再贷款专用账户" },
        "2": { text: "2-先贷款专用账户，再活期结算主账户" },
      },
    },
    {
      title: "顺序号",
      dataIndex: "seqNo",
      width: 100,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "放款机构号列表",
      dataIndex: "disburseInstNoList",
      width: 200,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "交易金额",
      dataIndex: "txAmt",
      width: 120,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "余额",
      dataIndex: "bal",
      width: 120,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "交易时间",
      dataIndex: "txTime",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
        title: "还款金额",
        dataIndex: "repayAmt",
        width: 120,
        ellipsis: true,
        align: "center",
        editable: () => true,
      },
      {
        title: "借据编号",
        dataIndex: "loanRctNo",
        width: 120,
        ellipsis: true,
        align: "center",
        editable: () => true,
      },
      {
        title: "关联借据编号",
        dataIndex: "relLoanRctNo",
        width: 120,
        ellipsis: true,
        align: "center",
        editable: () => true,
      },
      {
        title: "资金使用顺序号",
        dataIndex: "foudsUseSeqNo",
        width: 120,
        ellipsis: true,
        align: "center",
        editable: () => true,
      },
      {
        title: () => (
            <Tooltip
              title={
                <div style={{ width: "100%" }}>
                  <p>管控资金交易标识</p>
                  <ul>
                    <li>
                      <strong>第1位：是否使用卡贷通额度：</strong>
                    </li>
                    <li>0-否</li>
                    <li>1-是</li>
                    <li>
                      <strong>第2位：是否放款：</strong>
                    </li>
                    <li>0-否</li>
                    <li>1-是</li>
                    <li>
                      <strong>第3位：释放标志：</strong>
                    </li>
                    <li>0-无关</li>
                    <li>1-主动释放</li>
                    <li>2-被动释放</li>
                  </ul>
                </div>
              }
              placement="top"
              overlayClassName="custom-tooltip"
              color="#fff"
              overlayStyle={{ minWidth: "350px" }}
            >
              <span>
                管控资金交易标识{" "}
                <InfoCircleOutlined
                  style={{ marginLeft: 4, color: "#1890ff" }}
                />
              </span>
            </Tooltip>
          ),
        // title: "管控资金交易标识",
        dataIndex: "ctrllgFundsTxFlagCd",
        width: 260,
        ellipsis: true,
        align: "center",
        editable: () => true,
      },
    {
      title: "最后交易日期",
      dataIndex: "lastTxDate",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "date",
    },
    {
      title: "记录状态代码",
      dataIndex: "recordStaCd",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => false,
      valueType: "select",
      valueEnum: {
        "0": { text: "0-无效" },
        "1": { text: "1-有效" },
      },
    },
    {
      title: "分片值",
      dataIndex: "zoneVal",
      width: 160,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "创建时间戳",
      dataIndex: "createStamp",
      width: 220,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "最后修改时间戳",
      dataIndex: "lastModStamp",
      width: 220,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "操作",
      valueType: "option",
      width: 150,
      // fixed: "right",
      align: "center",
      render: (text, record: TbDpdtlFundsUsageControlLoan, _, action) => {
        const isRowEditing = record.fundsUsageCtrllgLnrctNo
          ? editableKeys.includes(record.fundsUsageCtrllgLnrctNo)
          : false;
        return [
          isRowEditing ? (
            <>
              <Button
                key="confirm"
                type="link"
                onClick={async () => {
                  try {
                    if (record.fundsUsageCtrllgLnrctNo) {
                      await action?.saveEditable?.(record.fundsUsageCtrllgLnrctNo);
                      setIsEditing(false);
                    }
                  } catch (err) {
                    console.error("保存失败:", err);
                  }
                }}
              >
                确定
              </Button>
              <Button
                key="cancel"
                type="link"
                onClick={() => {
                  if (record.fundsUsageCtrllgLnrctNo) {
                    action?.cancelEditable?.(record.fundsUsageCtrllgLnrctNo);
                    setEditableKeys([]);
                    setIsEditing(false);
                  }
                }}
                style={{ marginLeft: 8 }}
              >
                取消
              </Button>
            </>
          ) : (
            <Button
              key="editable"
              type="link"
              disabled={true}
              onClick={async () => {
                if (!record.fundsUsageCtrllgLnrctNo) return;
                const originalRecord = { ...record };
                setEditingRecord(originalRecord);
                setIsEditing(true);
                await action?.startEditable?.(record.fundsUsageCtrllgLnrctNo);
              }}
              style={{ padding: 0 }}
            >
              编辑
            </Button>
          ),
          <Button
            key="delete"
            type="link"
            disabled={true}
            onClick={async () => {
              if (!record.fundsUsageCtrllgLnrctNo) return;
              Modal.confirm({
                title: "确认删除",
                content: "确定要删除这条记录吗？",
                onOk: async () => {
                  try {
                    const res = await deleteDpdtlFundsUsageControlLoan(record);
                    if (res.code === 200) {
                      message.success("删除成功");
                    } else {
                      message.error(res.msg || "删除失败");
                    }
                  } catch (error) {
                    message.error("删除失败");
                  } finally {
                    await refreshData();
                  }
                },
                okText: "确认",
                cancelText: "取消",
              });
            }}
            style={{ marginLeft: 8 }}
          >
            删除
          </Button>,
        ];
      },
    },
  ];

  const handleAdd = async (record: TbDpdtlFundsUsageControlLoan) => {
    try {
      // 设置必要的默认字段
      const newRecord = {
        ...record,
        persInnerAccno: persInnerAccno || accountInfo?.persInnerAccno,
        zoneVal: zoneVal || accountInfo?.zoneVal,
        recordStaCode: "1", // 默认有效
      };
      
      setLoading(true);
      const res = await insertDpdtlFundsUsageControlLoan(newRecord);
      if (res.code === 200) {
        message.success('添加成功');
        refreshData();
      } else {
        message.error(res.msg || '添加失败');
      }
    } catch (error) {
      console.error('添加失败:', error);
      message.error('添加失败');
    } finally {
      // 添加完成后重置状态
      setLoading(false);
      setRefresh(prev => prev + 1);
      setEditableKeys([]);
      setIsEditing(false);
    }
  };

  return (
    <Modal
      width={1200}
      title="查询资金用途管控明细信息"
      open={props.open}
      destroyOnClose
      onCancel={props.onCancel}
      footer={null}
      bodyStyle={MODAL_BODY_STYLE}
      style={MODAL_STYLE}
    >
      <Spin spinning={loading} tip="加载数据中..." size="large">
        <div style={{ marginBottom: 16 }}>
          <p>
            数据状态: {loading ? (
              <span>
                <Spin size="small" style={{ marginRight: 8 }} />
                加载中...
              </span>
            ) : (
              `已加载 ${dataSource.length} 条记录, 总共 ${pagination.total} 条`
            )}
            {accountInfo && (
              <span style={{ marginLeft: 8 }}>
                | 个人内部账号: {accountInfo.persInnerAccno}
              </span>
            )}
          </p>
        </div>
        
        <EditableProTable<TbDpdtlFundsUsageControlLoan>
          key={refresh}
          rowKey={(record) => record.fundsUsageCtrllgLnrctNo || ""}
          headerTitle="资金用途管控明细信息"
          scroll={TABLE_SCROLL}
          loading={loading}
          toolBarRender={() => [
            <ProFormRadio.Group
              key="positionRender"
              fieldProps={{
                value: position,
                onChange: (e: any) => setPosition(e.target.value),
              }}
              options={[
                { label: "添加到顶部", value: "top" },
                { label: "添加到底部", value: "bottom" },
                { label: "隐藏", value: "hidden" },
              ]}
            />,
          ]}
          recordCreatorProps={{
            position: position === 'hidden' ? 'bottom' : position,
            record: (index, dataSource) => {
              // 直接创建新记录，不检查是否有正在编辑的行
              return {
                persInnerAccno: persInnerAccno || accountInfo?.persInnerAccno,
                fundsUsageCtrlType: "1", // 默认为个人额度类消费
                fundsUseSeqCode: "1", // 默认为先活期后结算主账户
                zoneVal: zoneVal || accountInfo?.zoneVal,
                recordStaCode: "1", // 默认有效
                txDate: new Date().toISOString().split('T')[0], // 当前日期
              } as TbDpdtlFundsUsageControlLoan;
            },
            creatorButtonText: '添加一行数据',
            newRecordType: 'dataSource',
          }}
          columns={columns}
          request={async (params, sorter, filter) => {
            // 直接返回当前数据源，不进行额外的API调用
            return {
              data: dataSource as TbDpdtlFundsUsageControlLoan[],
              total: pagination.total,
              success: true,
            };
          }}
          value={dataSource}
          onChange={setDataSource}
          editable={{
            type: 'multiple', // 支持同时编辑多行
            editableKeys,
            onSave: async (rowKey, data, row) => {
              try {
                // 如果是新添加的记录则调用新增接口
                if (String(rowKey).startsWith('NEW_')) {
                  await handleAdd(data);
                } else {
                  // 否则调用更新接口
                  setLoading(true);
                  const res = await updateDpdtlFundsUsageControlLoan(data);
                  if (res.code === 200) {
                    message.success('更新成功');
                    refreshData();
                  } else {
                    message.error(res.msg || '更新失败');
                  }
                }
              } catch (error) {
                console.error('保存失败:', error);
                message.error('保存失败');
              } finally {
                // 保存后从编辑状态中移除当前行
                setEditableKeys(keys => keys.filter(key => key !== rowKey));
                if (editableKeys.length <= 1) {
                  setIsEditing(false);
                }
                setLoading(false);
              }
            },
            onChange: setEditableKeys,
            onCancel: async (key) => {
              // 取消编辑时只清除当前行的编辑状态
              setEditableKeys(keys => keys.filter(k => k !== key));
              if (editableKeys.length <= 1) {
                setIsEditing(false);
              }
              return true;
            },
            actionRender: (row, config, dom) => [dom.save, dom.cancel],
          }}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条`,
            onChange: async (page, pageSize) => {
              if (isEditing) {
                return;
              }
              
              // 仅当页码或页大小变化时才刷新数据
              if (page !== pagination.current || pageSize !== pagination.pageSize) {
                // 更新分页状态
                setPagination({
                  ...pagination,
                  current: page,
                  pageSize: pageSize,
                });
                
                // 不再做额外条件判断，直接刷新数据
                await refreshData(page, pageSize);
              }
            },
          }}
        />
      </Spin>
    </Modal>
  );
};

export default DpdtlFundsUsageControlLoanFormEdit;