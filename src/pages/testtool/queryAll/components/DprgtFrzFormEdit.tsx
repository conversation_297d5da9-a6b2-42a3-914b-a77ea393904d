import React, { useEffect, useState, useCallback } from "react";
import { <PERSON><PERSON>, message, <PERSON><PERSON>, Spin, Popconfirm } from "antd";
import { ProFormRadio, EditableProTable } from "@ant-design/pro-components";
import type { ProColumns } from "@ant-design/pro-components";
import {
  dprgtFrzUpdate,
  getDprgtFrz,
  insertDprgtFrz,
  dprgtFrzDelete,
  queryDprgtFrzDetail,
} from "../service";
import type { DprgtFrz, PageResponse } from "../data";
import DprgtFrzDetailEdit from "../subComponents/DprgtFrzDetailEdit";
import DprgtFrzAdd from "../subComponents/DprgtFrzAdd";
import moment from "moment";

/*
 * @Description: 冻结登记簿编辑表单
 * @Author: taylor zhu
 * @Date: 2024-08-03 10:00:00
 */

interface PaginationType {
  current: number;
  pageSize: number;
  total: number;
}

export type DprgtFrzFormProps = {
  onCancel: () => void;
  open: boolean;
  data: PageResponse<DprgtFrz> | null;
};

const DEFAULT_PAGE_SIZE = 10;
const TABLE_SCROLL = { x: 2000 } as const;
const MODAL_BODY_STYLE = {
  maxHeight: "70vh",
  overflow: "auto",
  padding: "24px",
  borderRadius: "10px",
} as const;

const MODAL_STYLE = {
  borderRadius: "10px",
  overflow: "hidden",
} as const;

const INITIAL_PAGINATION: PaginationType = {
  current: 1,
  pageSize: DEFAULT_PAGE_SIZE,
  total: 0,
};

const DprgtFrzFormEdit: React.FC<DprgtFrzFormProps> = (props) => {
  const [dataSource, setDataSource] = useState<readonly DprgtFrz[]>([]);
  const [editableKeys, setEditableKeys] = useState<React.Key[]>([]);
  const [isEditing, setIsEditing] = useState(false);
  const [editingRecord, setEditingRecord] = useState<DprgtFrz | null>(null);
  const [pagination, setPagination] =
    useState<PaginationType>(INITIAL_PAGINATION);
  const [refresh, setRefresh] = useState(0);
  const [loading, setLoading] = useState(false);
  const [dprgtFrzInfo, setDprgtFrzInfo] = useState<{
    mainContrNo?: string;
    zoneVal?: string;
  } | null>(null);
  const [mainContrNo, setMainContrNo] = useState<string | undefined>(undefined);
  const [zoneVal, setZoneVal] = useState<string | undefined>(undefined);
  const [detailVisible, setDetailVisible] = useState<boolean>(false);
  const [currentRecord, setCurrentRecord] = useState<DprgtFrz>();
  const [detailLoading, setDetailLoading] = useState<boolean>(false);
  const [addVisible, setAddVisible] = useState<boolean>(false);

  useEffect(() => {
    if (props.open) {
      if (props.data?.loading !== undefined) {
        setLoading(props.data.loading);
      } else if (props.data) {
        // If we have data but no loading property, ensure loading is turned off
        setLoading(false);
      }

      // 当有data数据时，清空分页并设置数据
      if (props.data?.records) {
        setPagination({
          current: 1,
          pageSize: 10,
          total: props.data.total || props.data.records.length,
        });
        setDataSource(props.data.records);

        // 重要：设置账号信息，确保分页功能正常工作
        if (props.data.records.length > 0) {
          const firstRecord = props.data.records[0];

          // 从记录中提取账号信息
          setMainContrNo(firstRecord.mainContrNo);
          setZoneVal(firstRecord.zoneVal);

          // 同时更新accountInfo，作为备份
          setDprgtFrzInfo({
            mainContrNo: firstRecord.mainContrNo,
            zoneVal: firstRecord.zoneVal,
          });
        }
      }
    } else {
      // 当弹窗关闭时，清空数据和分页
      setDataSource([]);
      setPagination({
        current: 1,
        pageSize: 10,
        total: 0,
      });
      // Also ensure loading is reset when modal closes
      setLoading(false);
    }
  }, [props.open, props.data]);

  const refreshData = useCallback(
    async (
      current: number = pagination.current,
      pageSize: number = pagination.pageSize
    ) => {
      // 使用当前状态或dprgtFrzInfo作为后备
      const contrNo = mainContrNo || dprgtFrzInfo?.mainContrNo;
      const zoneValue = zoneVal || dprgtFrzInfo?.zoneVal;

      if (!contrNo || !zoneValue) {
        // 向用户显示错误消息，便于调试
        console.error("查询参数不完整:", { contrNo, zoneValue });
        message.error("分页查询失败：账号信息不完整，请重新打开弹窗");
        return;
      }

      setLoading(true);
      console.log("正在查询数据:", { contrNo, zoneValue, current, pageSize });

      try {
        const result = await getDprgtFrz(
          contrNo,
          zoneValue,
          "DP_RGT_ACC_STAR_ALL_QUERYPAGES",
          {
            current,
            pageSize,
          }
        );

        if (result && result.data) {
          const detailData = JSON.parse(result.data);
          const newPagination = {
            current: current,
            pageSize: pageSize,
            total: detailData.total || detailData.records.length,
          };

          setPagination(newPagination);
          setDataSource(detailData.records || []);
          console.log("查询成功，数据条数:", detailData.records?.length);
        }
      } catch (error) {
        console.error("获取数据失败:", error);
        message.error("获取账户星级登记簿数据失败");
      } finally {
        // Ensure loading state is always cleared
        setLoading(false);
      }
    },
    [
      mainContrNo,
      zoneVal,
      dprgtFrzInfo,
      pagination.current,
      pagination.pageSize,
    ]
  );

  // 定义表格列
  const [position, setPosition] = useState<"top" | "bottom" | "hidden">(
    "bottom"
  );

  const columns: ProColumns<DprgtFrz>[] = [
    {
      title: "冻结号",
      dataIndex: "frzNo",
      width: 180,
      fixed: true,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "冻结日期",
      dataIndex: "frzDate",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "date",
      render: (_, entity) => entity.frzDate || "-",
    },
    {
      title: "交易时间",
      dataIndex: "txTime",
      width: 220,
      ellipsis: true,
      align: "center",
      editable: () => true,
      render: (_, entity) => entity.txTime || "-",
    },
    {
      title: "冻结全局业务跟踪号",
      dataIndex: "frzGloTracNo",
      width: 260,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "冻结子交易序号",
      dataIndex: "frzSubtxNo",
      width: 260,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "冻结业务类型代码",
      dataIndex: "frzBusiTpCd",
      width: 160,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "select",
      valueEnum: {
        "11": { text: "11-金额冻结" },
        "21": { text: "21-账户冻结" },
        "22": { text: "22-合约冻结" },
      },
    },
    {
      title: "执行类型代码",
      dataIndex: "execTpCd",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "select",
      valueEnum: {
        "05": { text: "05-司法冻结" },
        "09": { text: "09-银联跨行延迟转账冻结" },
        "13": { text: "13-司法止付" },
      },
    },
    {
      title: "介质编号",
      dataIndex: "mediumNo",
      width: 200,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "主合约编号",
      dataIndex: "mainContrNo",
      width: 200,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "子账号序号",
      dataIndex: "saccnoSeqNo",
      width: 120,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "个人内部账号",
      dataIndex: "persInnerAccno",
      width: 180,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "客户编号",
      dataIndex: "custNo",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "客户名称",
      dataIndex: "custNm",
      width: 160,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "个人证件类型代码",
      dataIndex: "perCertTpCd",
      width: 160,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "select",
      valueEnum: {
        "1010": { text: "1010-居民身份证" },
        "1011": { text: "1011-临时居民身份证" },
        "1020": { text: "1020-军人身份证件" },
        "1021": { text: "1021-士兵证" },
        "1022": { text: "1022-军官证" },
        "1023": { text: "1023-文职干部证" },
        "1024": { text: "1024-军官退休证" },
        "1025": { text: "1025-文职干部退休证" },
        "1030": { text: "1030-武警身份证件" },
        "1031": { text: "1031-武警士兵证" },
        "1032": { text: "1032-警官证" },
        "1033": { text: "1033-武警文职干部证" },
        "1034": { text: "1034-武警军官退休证" },
        "1035": { text: "1035-武警文职干部退休证" },
        "1040": { text: "1040-户口簿" },
        "1050": { text: "1050-中国护照" },
        "1051": { text: "1051-外国护照" },
        "1060": { text: "1060-学生证" },
        "1070": { text: "1070-港澳居民来往内地通行证" },
        "1071": { text: "1071-往来港澳通行证" },
        "1080": { text: "1080-台湾居民来往大陆通行证" },
        "1090": { text: "1090-执行公务证" },
        "1100": { text: "1100-机动车驾驶证" },
        "1110": { text: "1110-社会保障卡" },
        "1120": { text: "1120-外国人居留证" },
        "1121": { text: "1121-外国人永久居留证" },
        "1130": { text: "1130-旅行证件" },
        "1140": { text: "1140-香港居民身份证" },
        "1150": { text: "1150-澳门居民身份证" },
        "1160": { text: "1160-台湾居民身份证" },
        "1170": { text: "1170-边民证" },
        "1180": { text: "1180-港澳台居民居住证" },
        "1181": { text: "1181-港澳居民居住证" },
        "1182": { text: "1182-台湾居民居住证" },
        "1190": { text: "1190-外国身份证" },
        "1998": { text: "1998-其他（原98类）" },
        "1999": { text: "1999-其他证件（个人）" },
      },
    },
    {
      title: "个人证件号码",
      dataIndex: "personalCertNo",
      width: 180,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "类别标识代码",
      dataIndex: "categFlagCd",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "select",
      valueEnum: {
        "01": { text: "01-活期" },
        "02": { text: "02-定期" },
      },
    },
    {
      title: "轮候序号",
      dataIndex: "waitSeqNo",
      width: 120,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "币种代码",
      dataIndex: "currCode",
      width: 120,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "select",
      valueEnum: {
        "036": { text: "036-澳大利亚元" },
        "124": { text: "124-加元" },
        "344": { text: "344-香港元" },
        "392": { text: "392-日元" },
        "826": { text: "826-英镑" },
        "840": { text: "840-美元" },
        "978": { text: "978-欧元（EUR）" },
        "156": { text: "156-人民币元" },
      },
    },
    {
      title: "钞汇类别代码",
      dataIndex: "cashExgVatgCd",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "select",
      valueEnum: {
        "2": { text: "2-钞" },
        "3": { text: "3-汇" },
      },
    },
    {
      title: "约定执行金额",
      dataIndex: "appointExecAmt",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "需冻结金额",
      dataIndex: "needFrzAmt",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "已扣划金额",
      dataIndex: "dededAmt",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "冻结起始日期",
      dataIndex: "frzBgnDt",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "date",
      render: (_, entity) => entity.frzBgnDt || "-",
    },
    {
      title: "冻结起始日期时间",
      dataIndex: "frzBgnDtTime",
      width: 220,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "冻结终止日期",
      dataIndex: "frzEndDt",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "date",
      render: (_, entity) => entity.frzEndDt || "-",
    },
    {
      title: "冻结终止日期时间",
      dataIndex: "ofrzEndDtTime",
      width: 220,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "失效日期(会计)",
      dataIndex: "invalDtAccti",
      width: 160,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "date",
      render: (_, entity) => entity.invalDtAccti || "-",
    },
    {
      title: "冻结执行文号",
      dataIndex: "frzExecFileNo",
      width: 160,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "有权机关类型代码",
      dataIndex: "powInstTpCd",
      width: 160,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "select",
      valueEnum: {
        ATH01: { text: "ATH01-公安机关" },
        ATH02: { text: "ATH02-人民法院" },
        ATH03: { text: "ATH03-人民检查院子" },
        ATH04: { text: "ATH04-税务机关" },
        ATH05: { text: "ATH05-海关" },
        ATH06: { text: "ATH06-国家安全机关" },
        ATH07: { text: "ATH07-军队保卫部门" },
        ATH08: { text: "ATH08-监狱" },
        ATH09: { text: "ATH09-走私犯罪侦查机关" },
        ATH10: { text: "ATH10-证券监督管理机关" },
        ATH11: { text: "ATH11-监察机关" },
        ATH12: { text: "ATH12-审计机关" },
        ATH13: { text: "ATH13-工商行政管理机关" },
        ATH14: { text: "ATH14-律师" },
        ATH15: { text: "ATH15-银行保险监督管理机构" },
        ATH16: { text: "ATH16-人力资源社会保障行政部门" },
        ATH17: { text: "ATH17-破产管理人" },
        ATH18: { text: "ATH18-价格主管部门" },
        ATH19: { text: "ATH19-国务院财政部门及其派出机构" },
        ATH20: { text: "ATH20-外汇管理机构" },
        ATH21: { text: "ATH21-中国人民银行及其省一级分支机构" },
      },
    },
    {
      title: "有权机关名称",
      dataIndex: "powInstName",
      width: 200,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "联系电话",
      dataIndex: "contTel",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "冻结原因",
      dataIndex: "frzReason",
      width: 200,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "续冻序号",
      dataIndex: "conFrzSeqNo",
      width: 120,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "原冻结起始日期",
      dataIndex: "oldFrzBgnDt",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "date",
      render: (_, entity) => entity.oldFrzBgnDt || "-",
    },
    {
      title: "原冻结终止日期",
      dataIndex: "oldFrzEndDt",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "date",
      render: (_, entity) => entity.oldFrzEndDt || "-",
    },
    {
      title: "未生效续冻结存",
      dataIndex: "notEffConFrzBalFlag",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "select",
      valueEnum: {
        "0": { text: "0-否" },
        "1": { text: "1-是" },
      },
    },
    {
      title: "冻结渠道种类代码",
      dataIndex: "frzChnKindCd",
      width: 160,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "select",
      valueEnum: {
        "01": { text: "01-网点柜面" },
        "10": { text: "10-网上银行" },
        "12": { text: "12-个人网银" },
        "13": { text: "13-电视银行" },
        "14": { text: "14-电话银行" },
        "15": { text: "15-手机银行" },
        "16": { text: "16-企业网银" },
        "17": { text: "17-自助设备" },
        "18": { text: "18-POS" },
        "20": { text: "20-超级网银" },
        "21": { text: "21-大小额支付" },
        "22": { text: "22-银联前置" },
        "24": { text: "24-管理端" },
        "25": { text: "25-交易端" },
        "26": { text: "26-商易通" },
        "27": { text: "27-助农通" },
        "29": { text: "29-外部系统" },
        "30": { text: "30-系统自动" },
        "31": { text: "31-电子汇兑系统" },
        "32": { text: "32-理财规划终端" },
        "34": { text: "34-网汇通" },
        "35": { text: "35-同城支付" },
        "36": { text: "36-移动终端-TSM（可信服务管理）" },
        "37": { text: "37-移动终端-移动展业" },
        "38": { text: "38-直销银行" },
        "39": { text: "39-短信" },
        "40": { text: "40-专属APP" },
        "41": { text: "41-第三方线上渠道" },
        "42": { text: "42-国际支付前置" },
        "43": { text: "43-智能柜员机（ITM）" },
        "44": { text: "44-邮储经营" },
        "45": { text: "45-银银前置系统" },
        "46": { text: "46-U链供应链" },
        "47": { text: "47-油料保障结算系统" },
        "48": { text: "48-银企直联" },
        "91": { text: "91-微信银行" },
        "99": { text: "99-其他" },
      },
    },
    {
      title: "发起系统或组件",
      dataIndex: "startSysOrCmptNo",
      width: 160,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "开户机构号",
      dataIndex: "openAccInstNo",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "冻结机构号",
      dataIndex: "frzInstNo",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "冻结柜员号",
      dataIndex: "frzTellerNo",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "冻结授权柜员号",
      dataIndex: "frzAuthTellerNo",
      width: 160,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "生效状态代码",
      dataIndex: "effStaCd",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "select",
      valueEnum: {
        "1": { text: "1-已生效" },
        "2": { text: "2-已失效" },
      },
    },
    {
      title: "最后交易日期",
      dataIndex: "lastTxDate",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "date",
      render: (_, entity) => entity.lastTxDate || "-",
    },
    {
      title: "分片值",
      dataIndex: "zoneVal",
      width: 160,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "操作",
      valueType: "option",
      width: 200,
      // fixed: "right",
      align: "center",
      render: (text, record: DprgtFrz, _, action) => {
        const isRowEditing = record.mainContrNo
          ? editableKeys.includes(record.mainContrNo)
          : false;
        return [
          isRowEditing ? (
            <>
              <Button
                key="confirm"
                type="link"
                size="small"
                onClick={() => {
                  if (record.mainContrNo) {
                    action?.saveEditable?.(record.mainContrNo);
                    setEditableKeys([]);
                  }
                }}
              >
                保存
              </Button>
              <Button
                key="cancel"
                type="link"
                size="small"
                onClick={() => {
                  if (record.mainContrNo) {
                    action?.cancelEditable?.(record.mainContrNo);
                    setEditableKeys([]);
                  }
                }}
              >
                取消
              </Button>
            </>
          ) : (
            <>
              <Button
                key="edit"
                type="link"
                size="small"
                // disabled={editableKeys.length > 0}
                disabled={true}
                onClick={() => {
                  if (record.mainContrNo) {
                    action?.startEditable?.(record.mainContrNo);
                    setEditableKeys([record.mainContrNo]);
                  }
                }}
              >
                行编辑
              </Button>
              <Button
                key="editDetail"
                type="link"
                size="small"
                onClick={() => handleEditDetail(record)}
                loading={detailLoading && currentRecord?.frzNo === record.frzNo}
              >
                详情编辑
              </Button>
              <Popconfirm
                title="确定要删除该条记录吗?"
                onConfirm={() => handleDelete(record)}
                okText="确定"
                cancelText="取消"
              >
                <Button danger type="link" size="small" disabled={true}>
                  删除
                </Button>
              </Popconfirm>
            </>
          ),
        ];
      },
    },
  ];

  const handleAdd = () => {
    setAddVisible(true);
  };

  const handleAddCancel = () => {
    setAddVisible(false);
  };

  const handleAddSuccess = () => {
    setAddVisible(false);
    refreshData();
  };

  const handleEditDetail = async (record: DprgtFrz) => {
    if (!record.frzNo || !record.zoneVal) {
      message.error("缺少冻结编号或分片值");
      return;
    }

    // 立即打开模态框并设置加载状态
    setDetailVisible(true);
    setDetailLoading(true);
    // 先置空当前记录，避免显示上一次的数据
    setCurrentRecord(undefined);

    try {
      const result = await queryDprgtFrzDetail(record.frzNo, record.zoneVal);
      if (result.code === 200 && result.data) {
        // 检查data是否是字符串，如果是则解析JSON
        let recordData;
        if (typeof result.data === "string") {
          try {
            recordData = JSON.parse(result.data);
          } catch (e) {
            console.error("解析数据失败:", e);
            message.error("解析数据失败");
            return;
          }
        } else {
          recordData = result.data;
        }

        // 设置当前记录
        setCurrentRecord(recordData);
      } else {
        message.error(result.message || "获取冻结记录详情失败");
        // 如果获取失败，关闭模态框
        setDetailVisible(false);
      }
    } catch (error) {
      console.error("获取冻结记录详情时出错:", error);
      message.error("获取冻结记录详情失败");
      // 如果发生错误，关闭模态框
      setDetailVisible(false);
    } finally {
      setDetailLoading(false);
    }
  };

  const handleDetailCancel = () => {
    setDetailVisible(false);
    setCurrentRecord(undefined);
  };

  const handleDetailSuccess = () => {
    setDetailVisible(false);
    setCurrentRecord(undefined);
    // 刷新表格数据
    refreshData();
  };

  const handleDelete = async (record: DprgtFrz) => {
    if (!record.frzNo) return;

    if (record.frzBgnDtTime) {
      // 检查是否为Moment对象
      if (moment.isMoment(record.frzBgnDtTime)) {
        record.frzBgnDtTime = record.frzBgnDtTime.toISOString();
      } else if (typeof record.frzBgnDtTime === "string") {
        // 如果是字符串，尝试转换为ISO格式
        record.frzBgnDtTime = moment(record.frzBgnDtTime).toISOString();
      }
    }
    if (record.ofrzEndDtTime) {
      if (moment.isMoment(record.ofrzEndDtTime)) {
        record.ofrzEndDtTime = record.ofrzEndDtTime.toISOString();
      } else if (typeof record.ofrzEndDtTime === "string") {
        record.ofrzEndDtTime = moment(record.ofrzEndDtTime).toISOString();
      }
    }
    if (record.txTime) {
      if (moment.isMoment(record.txTime)) {
        record.txTime = record.txTime.toISOString();
      } else if (typeof record.txTime === "string") {
        record.txTime = moment(record.txTime).toISOString();
      }
    }

    try {
      setLoading(true);
      const res = await dprgtFrzDelete(record);
      if (res.code === 200) {
        message.success("删除成功");
        // 刷新数据
        if (record.mainContrNo && record.zoneVal) {
          refreshData();
        }
      } else {
        message.error(res.msg || "删除失败");
      }
    } catch (error) {
      console.error("删除失败:", error);
      message.error("删除失败");
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Modal
        width={1200}
        title="查询冻结登记簿信息"
        visible={props.open}
        destroyOnClose
        onCancel={props.onCancel}
        footer={null}
        bodyStyle={MODAL_BODY_STYLE}
        style={MODAL_STYLE}
        zIndex={1000}
      >
        <Spin spinning={loading} tip="加载数据中..." size="large">
          <div style={{ marginBottom: 16 }}>
            <p>
              数据状态:{" "}
              {loading ? (
                <span>
                  <Spin size="small" style={{ marginRight: 8 }} />
                  加载中...
                </span>
              ) : (
                `已加载 ${dataSource.length} 条记录, 总共 ${pagination.total} 条`
              )}
              {dprgtFrzInfo && (
                <span style={{ marginLeft: 8 }}>
                  | 主合约编号: {dprgtFrzInfo.mainContrNo}
                </span>
              )}
            </p>
          </div>

          <EditableProTable<DprgtFrz>
            key={refresh}
            rowKey={(record) => record.mainContrNo || ""}
            headerTitle="冻结登记簿信息"
            scroll={TABLE_SCROLL}
            loading={loading}
            toolBarRender={() => [
              <ProFormRadio.Group
                key="positionRender"
                fieldProps={{
                  value: position,
                  onChange: (e: any) => setPosition(e.target.value),
                }}
                options={[
                  { label: "添加到顶部", value: "top" },
                  { label: "添加到底部", value: "bottom" },
                  { label: "隐藏", value: "hidden" },
                ]}
              />,
            ]}
            recordCreatorProps={{
              position: position === "hidden" ? "bottom" : position,
              record: () => ({}) as DprgtFrz,
              creatorButtonText: "添加一行数据",
              onClick: () => {
                handleAdd();
                return false;
              },
              newRecordType: "dataSource",
            }}
            columns={columns}
            request={async (params, sorter, filter) => {
              // 直接返回当前数据源，不进行额外的API调用
              return {
                data: dataSource as DprgtFrz[],
                total: pagination.total,
                success: true,
              };
            }}
            value={dataSource}
            onChange={setDataSource}
            editable={{
              type: "multiple", // 支持同时编辑多行
              editableKeys,
              onSave: async (rowKey, data, row) => {
                try {
                  // 处理日期和时间字段，转换为后台需要的格式
                  const processedData = { ...data };

                  // 处理纯日期字段，转换为YYYYMMDD格式
                  if (processedData.frzDate) {
                    processedData.frzDate = moment(
                      processedData.frzDate
                    ).format("YYYYMMDD");
                  }
                  if (processedData.frzBgnDt) {
                    processedData.frzBgnDt = moment(
                      processedData.frzBgnDt
                    ).format("YYYYMMDD");
                  }
                  if (processedData.frzEndDt) {
                    processedData.frzEndDt = moment(
                      processedData.frzEndDt
                    ).format("YYYYMMDD");
                  }
                  if (processedData.lastTxDate) {
                    processedData.lastTxDate = moment(
                      processedData.lastTxDate
                    ).format("YYYYMMDD");
                  }
                  if (processedData.oldFrzBgnDt) {
                    processedData.oldFrzBgnDt = moment(
                      processedData.oldFrzBgnDt
                    ).format("YYYYMMDD");
                  }
                  if (processedData.oldFrzEndDt) {
                    processedData.oldFrzEndDt = moment(
                      processedData.oldFrzEndDt
                    ).format("YYYYMMDD");
                  }

                  // 处理时间字段，转换为ISO 8601格式
                  if (processedData.txTime) {
                    processedData.txTime = moment(
                      processedData.txTime
                    ).toISOString();
                  }
                  if (processedData.frzBgnDtTime) {
                    processedData.frzBgnDtTime = moment(
                      processedData.frzBgnDtTime
                    ).toISOString();
                  }
                  if (processedData.ofrzEndDtTime) {
                    processedData.ofrzEndDtTime = moment(
                      processedData.ofrzEndDtTime
                    ).toISOString();
                  }

                  // 处理其他时间戳字段和可能的日期时间字段
                  const anyProcessedData = processedData as any;
                  if (anyProcessedData.createStamp) {
                    anyProcessedData.createStamp = moment(
                      anyProcessedData.createStamp
                    ).toISOString();
                  }
                  if (anyProcessedData.lastModStamp) {
                    anyProcessedData.lastModStamp = moment(
                      anyProcessedData.lastModStamp
                    ).toISOString();
                  }

                  // 处理其他可能的日期时间字段
                  const otherTimeFields = [
                    "relPayEnrollTime",
                    "stopPayEnrollTime",
                    "enrollTime",
                    "relsTime",
                    "revkTime",
                    "signTime",
                    "cacContTime",
                  ];

                  otherTimeFields.forEach((field) => {
                    if (anyProcessedData[field]) {
                      try {
                        anyProcessedData[field] = moment(
                          anyProcessedData[field]
                        ).toISOString();
                      } catch (error) {
                        console.error(`格式化字段 ${field} 失败:`, error);
                      }
                    }
                  });

                  // 如果是新添加的记录则调用新增接口
                  if (String(rowKey).startsWith("NEW_")) {
                    // 修改这里，原来的handleAdd替换为直接调用API
                    // 添加必要的默认字段
                    const newRecord = {
                      ...processedData,
                      persInnerAccno: mainContrNo || dprgtFrzInfo?.mainContrNo,
                      zoneVal: zoneVal || dprgtFrzInfo?.zoneVal,
                      recordStaCd: "1", // 默认有效
                    };

                    setLoading(true);
                    const res = await insertDprgtFrz(newRecord);
                    if (res.code === 200) {
                      message.success("添加成功");
                      refreshData();
                    } else {
                      message.error(res.msg || "添加失败");
                    }
                  } else {
                    // 否则调用更新接口
                    setLoading(true);
                    const res = await dprgtFrzUpdate(processedData);
                    if (res.code === 200) {
                      message.success("更新成功");
                      refreshData();
                    } else {
                      message.error(res.msg || "更新失败");
                    }
                  }
                } catch (error) {
                  console.error("保存失败:", error);
                  message.error("保存失败");
                } finally {
                  // 保存后从编辑状态中移除当前行
                  setEditableKeys((keys) =>
                    keys.filter((key) => key !== rowKey)
                  );
                  if (editableKeys.length <= 1) {
                    setIsEditing(false);
                  }
                  setLoading(false);
                }
              },
              onChange: setEditableKeys,
              onCancel: async (key) => {
                // 取消编辑时只清除当前行的编辑状态
                setEditableKeys((keys) => keys.filter((k) => k !== key));
                if (editableKeys.length <= 1) {
                  setIsEditing(false);
                }
                return true;
              },
              actionRender: (row, config, dom) => [dom.save, dom.cancel],
            }}
            pagination={{
              current: pagination.current,
              pageSize: pagination.pageSize,
              total: pagination.total,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total) => `共 ${total} 条`,
              onChange: async (page, pageSize) => {
                console.log("分页改变:", {
                  page,
                  pageSize,
                  currentPage: pagination.current,
                  isEditing,
                });

                if (isEditing) {
                  message.warn("请先完成编辑操作再切换页面");
                  return;
                }

                // 仅当页码或页大小变化时才刷新数据
                if (
                  page !== pagination.current ||
                  pageSize !== pagination.pageSize
                ) {
                  // 更新分页状态
                  setPagination({
                    ...pagination,
                    current: page,
                    pageSize: pageSize,
                  });

                  // 直接调用刷新数据，不做额外判断
                  await refreshData(page, pageSize);
                }
              },
            }}
          />
        </Spin>
      </Modal>

      {/* 添加冻结记录详情编辑模态框 */}
      <DprgtFrzDetailEdit
        visible={detailVisible}
        onCancel={handleDetailCancel}
        onSuccess={handleDetailSuccess}
        record={currentRecord}
        loading={detailLoading}
      />

      {/* 添加冻结记录添加模态框 */}
      <DprgtFrzAdd
        visible={addVisible}
        onCancel={handleAddCancel}
        onSuccess={handleAddSuccess}
        mainContractInfo={{
          mainContrNo: mainContrNo || dprgtFrzInfo?.mainContrNo,
          zoneVal: zoneVal || dprgtFrzInfo?.zoneVal,
          mediumNo: dataSource?.[0]?.mediumNo,
          persInnerAccno: mainContrNo || dprgtFrzInfo?.mainContrNo,
          custNo: dataSource?.[0]?.custNo,
          custNm: dataSource?.[0]?.custNm,
          perCertTpCd: dataSource?.[0]?.perCertTpCd,
          personalCertNo: dataSource?.[0]?.personalCertNo,
          saccnoSeqNo: dataSource?.[0]?.saccnoSeqNo,
        }}
      />
    </>
  );
};

export default DprgtFrzFormEdit;
