import React, { useEffect, useState, useCallback } from "react";
import { Modal, message, Button } from "antd";
import { 
  ProFormRadio, 
  EditableProTable 
} from "@ant-design/pro-components";
import type { ProColumns } from "@ant-design/pro-components";
import {
  getDprgtCflAddoffInfo,
  insertDprgtCflAddoffInfo,
  updateDprgtCflAddoffInfo,
  deleteDprgtCflAddoffInfo,
} from "../service";
import type { DprgtCflAddoffInfo, PageResponse } from "../data";

/*
 * @Description: 圈存加办信息编辑表单
 * @Author: taylor zhu
 * @Date: 2025-04-15 10:00:00
 */

interface PaginationType {
  current: number;
  pageSize: number;
  total: number;
}

export type DprgtCflAddoffInfoFormProps = {
  onCancel: () => void;
  open: boolean;
  data: PageResponse<DprgtCflAddoffInfo> | null;
};

const DEFAULT_PAGE_SIZE = 10;
const TABLE_SCROLL = { x: 2000 } as const;
const MODAL_BODY_STYLE = {
  maxHeight: "70vh",
  overflow: "auto",
  padding: "24px",
  borderRadius: "10px",
} as const;

const MODAL_STYLE = {
  borderRadius: "10px",
  overflow: "hidden",
} as const;

const INITIAL_PAGINATION: PaginationType = {
  current: 1,
  pageSize: DEFAULT_PAGE_SIZE,
  total: 0,
};

const DprgtCflAddoffInfoFormEdit: React.FC<DprgtCflAddoffInfoFormProps> = (props) => {
  const [dataSource, setDataSource] = useState<readonly DprgtCflAddoffInfo[]>([]);
  const [editableKeys, setEditableKeys] = useState<React.Key[]>([]);
  const [isEditing, setIsEditing] = useState(false);
  const [editingRecord, setEditingRecord] = useState<DprgtCflAddoffInfo | null>(null);
  const [pagination, setPagination] = useState<PaginationType>(INITIAL_PAGINATION);
  const [refresh, setRefresh] = useState(0);
  const [loading, setLoading] = useState(false);
  const [currentContract, setCurrentContract] = useState<{prodtContractNo?: string, zoneVal?: string} | null>(null);
  const [prodtContractNo, setProdtContractNo] = useState<string | undefined>(undefined);
  const [zoneVal, setZoneVal] = useState<string | undefined>(undefined);

  useEffect(() => {
    if (props.open && props.data?.records && props.data.records.length > 0) {
      const records = props.data.records;
      setDataSource(records);
      setPagination({
        ...pagination,
        total: props.data?.total || records.length,
      });
      
      // 找到第一条有效记录，获取prodtContractNo和zoneVal
      const firstValidRecord = records.find(record => record.prodtContractNo && record.zoneVal);
      
      if (firstValidRecord?.prodtContractNo && firstValidRecord?.zoneVal) {
        // 只更新状态，不再重复请求数据
        setCurrentContract({
          prodtContractNo: firstValidRecord.prodtContractNo,
          zoneVal: firstValidRecord.zoneVal
        });
        setProdtContractNo(firstValidRecord.prodtContractNo);
        setZoneVal(firstValidRecord.zoneVal);
      }
    } else {
      setDataSource([]);
      setPagination(INITIAL_PAGINATION);
      setCurrentContract(null);
      setProdtContractNo(undefined);
      setZoneVal(undefined);
    }
  }, [props.open, props.data]);

  const refreshData = useCallback(
    async (current: number = pagination.current, pageSize: number = pagination.pageSize) => {
      // 使用当前状态或currentContract作为后备
      const contractNo = prodtContractNo || currentContract?.prodtContractNo;
      const zoneValue = zoneVal || currentContract?.zoneVal;
      
      if (!contractNo || !zoneValue) {
        // 只在控制台记录错误，不向用户显示消息
        console.warn('查询参数不完整');
        return;
      }
      
      setLoading(true);
      
      try {
        const result = await getDprgtCflAddoffInfo(
          contractNo,
          zoneValue,
          'DPRGT_CFL_ADDOFF_INFO_ALL_QUERYPAGES',
          {
            current,
            pageSize,
          },
        );
        
        if (result && result.data) {
          const detailData = JSON.parse(result.data);
          const newPagination = {
            current: current,
            pageSize: pageSize,
            total: detailData.total || detailData.records.length,
          };
          
          setPagination(newPagination);
          setDataSource(detailData.records || []);
        }
      } catch (error) {
        console.error('获取数据失败:', error);
        message.error('获取圈存加办信息数据失败');
      } finally {
        setLoading(false);
      }
    },
    [prodtContractNo, zoneVal, currentContract],
  );

  // 定义表格列
  const [position, setPosition] = useState<"top" | "bottom" | "hidden">("bottom");

  const columns: ProColumns<DprgtCflAddoffInfo>[] = [
    {
      title: "产品合约编号",
      dataIndex: "prodtContractNo",
      width: 220,
      fixed: false,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "关联产品合约编号",
      dataIndex: "relContrNo",
      width: 220,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "圈存类型代码",
      dataIndex: "cflTpCd",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "select",
      valueEnum: {
        "02": { text: "02-指定账户圈" },
      },
    },
    {
      title: "生效状态代码",
      dataIndex: "effStaCd",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "select",
      valueEnum: {
        "1": { text: "1-已生效" },
        "0": { text: "2-已失效" },
      },
    },
    {
      title: "最后交易日期",
      dataIndex: "lastTxDate",
      width: 120,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "date",
      render: (_, entity) => entity.lastTxDate || "-",
    },
    {
      title: "记录状态代码",
      dataIndex: "recordStaCd",
      width: 100,
      ellipsis: true,
      align: "center",
      editable: () => false,
      valueType: "select",
      valueEnum: {
        "0": { text: "0-无效" },
        "1": { text: "1-有效" },
      },
    },
    {
      title: "分片值",
      dataIndex: "zoneVal",
      width: 160,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "创建时间戳",
      dataIndex: "createStamp",
      width: 180,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "最后修改时间戳",
      dataIndex: "lastModStamp",
      width: 180,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "操作",
      valueType: "option",
      width: 150,
      fixed: "right",
      align: "center",
      render: (text, record: DprgtCflAddoffInfo, _, action) => {
        const isRowEditing = record.prodtContractNo
          ? editableKeys.includes(record.prodtContractNo)
          : false;
        return [
          isRowEditing ? (
            <>
              <Button
                key="confirm"
                type="link"
                onClick={async () => {
                  try {
                    if (record.prodtContractNo) {
                      await action?.saveEditable?.(record.prodtContractNo);
                      setIsEditing(false);
                    }
                  } catch (err) {
                    console.error("保存失败:", err);
                  }
                }}
              >
                确定
              </Button>
              <Button
                key="cancel"
                type="link"
                onClick={() => {
                  if (record.prodtContractNo) {
                    action?.cancelEditable?.(record.prodtContractNo);
                    setEditableKeys([]);
                    setIsEditing(false);
                  }
                }}
                style={{ marginLeft: 8 }}
              >
                取消
              </Button>
            </>
          ) : (
            <Button
              key="editable"
              type="link"
              disabled={true}
              onClick={async () => {
                if (!record.prodtContractNo) return;
                const originalRecord = { ...record };
                setEditingRecord(originalRecord);
                setIsEditing(true);
                await action?.startEditable?.(record.prodtContractNo);
              }}
              style={{ padding: 0 }}
            >
              编辑
            </Button>
          ),
          <Button
            key="delete"
            type="link"
            disabled={true}
            onClick={async () => {
              if (!record.prodtContractNo) return;
              Modal.confirm({
                title: "确认删除",
                content: "确定要删除这条记录吗？",
                onOk: async () => {
                  try {
                    const res = await deleteDprgtCflAddoffInfo(record);
                    if (res.code === 200) {
                      message.success("删除成功");
                    } else {
                      message.error(res.msg || "删除失败");
                    }
                  } catch (error) {
                    message.error("删除失败");
                  } finally {
                    await refreshData();
                  }
                },
                okText: "确认",
                cancelText: "取消",
              });
            }}
            style={{ marginLeft: 8 }}
          >
            删除
          </Button>,
        ];
      },
    },
  ];

  const handleAdd = async (record: DprgtCflAddoffInfo) => {
    try {
      // 设置必要的默认字段
      const newRecord = {
        ...record,
        prodtContractNo: prodtContractNo || currentContract?.prodtContractNo,
        zoneVal: zoneVal || currentContract?.zoneVal,
        recordStaCd: "1", // 默认有效
      };
      
      setLoading(true);
      const res = await insertDprgtCflAddoffInfo(newRecord);
      if (res.code === 200) {
        message.success('添加成功');
        refreshData();
      } else {
        message.error(res.msg || '添加失败');
      }
    } catch (error) {
      console.error('添加失败:', error);
      message.error('添加失败');
    } finally {
      // 添加完成后重置状态
      setLoading(false);
      setRefresh(prev => prev + 1);
      setEditableKeys([]);
      setIsEditing(false);
    }
  };

  return (
    <Modal
      width={1200}
      title="查询圈存加办信息"
      open={props.open}
      destroyOnClose
      onCancel={props.onCancel}
      footer={null}
      bodyStyle={MODAL_BODY_STYLE}
      style={MODAL_STYLE}
    >
      <div style={{ marginBottom: 16 }}>
        <p>
          数据状态: {loading ? '加载中' : `已加载 ${dataSource.length} 条记录, 总共 ${pagination.total} 条`}
          {currentContract && (
            <span style={{ marginLeft: 8 }}>
              | 合约编号: {currentContract.prodtContractNo}
            </span>
          )}
        </p>
      </div>
      
      <EditableProTable<DprgtCflAddoffInfo>
        key={refresh}
        rowKey={(record) => record.prodtContractNo || ""}
        headerTitle="圈存加办信息"
        scroll={TABLE_SCROLL}
        loading={loading}
        toolBarRender={() => [
          <ProFormRadio.Group
            key="positionRender"
            fieldProps={{
              value: position,
              onChange: (e: any) => setPosition(e.target.value),
            }}
            options={[
              { label: "添加到顶部", value: "top" },
              { label: "添加到底部", value: "bottom" },
              { label: "隐藏", value: "hidden" },
            ]}
          />,
        ]}
        recordCreatorProps={{
          position: position === 'hidden' ? 'bottom' : position,
          record: (index, dataSource) => {
            // 直接创建新记录，不检查是否有正在编辑的行
            return {
              prodtContractNo: prodtContractNo || currentContract?.prodtContractNo,
              cflTpCd: "02", // 默认为指定账户圈
              effStaCd: "1", // 默认已生效
              zoneVal: zoneVal || currentContract?.zoneVal,
              recordStaCd: "1", // 默认有效
            } as DprgtCflAddoffInfo;
          },
          creatorButtonText: '添加一行数据',
          newRecordType: 'dataSource',
        }}
        columns={columns}
        request={async (params, sorter, filter) => {
          // 直接返回当前数据源，不进行额外的API调用
          return {
            data: dataSource as DprgtCflAddoffInfo[],
            total: pagination.total,
            success: true,
          };
        }}
        value={dataSource}
        onChange={setDataSource}
        editable={{
          type: 'multiple', // 支持同时编辑多行
          editableKeys,
          onSave: async (rowKey, data, row) => {
            try {
              // 如果是新添加的记录则调用新增接口
              if (String(rowKey).startsWith('NEW_')) {
                await handleAdd(data);
              } else {
                // 否则调用更新接口
                setLoading(true);
                const res = await updateDprgtCflAddoffInfo(data);
                if (res.code === 200) {
                  message.success('更新成功');
                  refreshData();
                } else {
                  message.error(res.msg || '更新失败');
                }
              }
            } catch (error) {
              console.error('保存失败:', error);
              message.error('保存失败');
            } finally {
              // 保存后从编辑状态中移除当前行
              setEditableKeys(keys => keys.filter(key => key !== rowKey));
              if (editableKeys.length <= 1) {
                setIsEditing(false);
              }
              setLoading(false);
            }
          },
          onChange: setEditableKeys,
          onCancel: async (key) => {
            // 取消编辑时只清除当前行的编辑状态
            setEditableKeys(keys => keys.filter(k => k !== key));
            if (editableKeys.length <= 1) {
              setIsEditing(false);
            }
            return true;
          },
          actionRender: (row, config, dom) => [dom.save, dom.cancel],
        }}
        pagination={{
          current: pagination.current,
          pageSize: pagination.pageSize,
          total: pagination.total,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 条`,
          onChange: async (page, pageSize) => {
            if (isEditing) {
              return;
            }
            
            // 仅当页码或页大小变化时才刷新数据
            if (page !== pagination.current || pageSize !== pagination.pageSize) {
              // 更新分页状态
              setPagination({
                ...pagination,
                current: page,
                pageSize: pageSize,
              });
              
              // 不再做额外条件判断，直接刷新数据
              await refreshData(page, pageSize);
            }
          },
        }}
      />
    </Modal>
  );
};

export default DprgtCflAddoffInfoFormEdit; 