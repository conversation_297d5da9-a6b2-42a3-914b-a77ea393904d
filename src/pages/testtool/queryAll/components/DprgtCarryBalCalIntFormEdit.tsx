import React, { useEffect, useState, useCallback } from "react";
import { Mo<PERSON>, message, <PERSON><PERSON>, Spin } from "antd";
import { 
  ProFormRadio, 
  EditableProTable 
} from "@ant-design/pro-components";
import type { ProColumns } from "@ant-design/pro-components";
import {
  getDprgtCarryBalCalInt,
  insertDprgtCarryBalCalInt,
  updateDprgtCarryBalCalInt,
  deleteDprgtCarryBalCalInt,
} from "../service";
import type { DprgtCarryBalCalInt, PageResponse } from "../data";

/*
 * @Description: 活期产品合约账户结转余额表编辑表单
 * @Author: taylor zhu
 * @Date: 2024-08-03 10:00:00
 */

interface PaginationType {
  current: number;
  pageSize: number;
  total: number;
}

export type DprgtCarryBalCalIntFormProps = {
  onCancel: () => void;
  open: boolean;
  data: PageResponse<DprgtCarryBalCalInt> | null;
};

const DEFAULT_PAGE_SIZE = 10;
const TABLE_SCROLL = { x: 2000 } as const;
const MODAL_BODY_STYLE = {
  maxHeight: "70vh",
  overflow: "auto",
  padding: "24px",
  borderRadius: "10px",
} as const;

const MODAL_STYLE = {
  borderRadius: "10px",
  overflow: "hidden",
} as const;

const INITIAL_PAGINATION: PaginationType = {
  current: 1,
  pageSize: DEFAULT_PAGE_SIZE,
  total: 0,
};

const DprgtCarryBalCalIntFormEdit: React.FC<DprgtCarryBalCalIntFormProps> = (props) => {
  const [dataSource, setDataSource] = useState<readonly DprgtCarryBalCalInt[]>([]);
  const [editableKeys, setEditableKeys] = useState<React.Key[]>([]);
  const [isEditing, setIsEditing] = useState(false);
  const [editingRecord, setEditingRecord] = useState<DprgtCarryBalCalInt | null>(null);
  const [pagination, setPagination] = useState<PaginationType>(INITIAL_PAGINATION);
  const [refresh, setRefresh] = useState(0);
  const [loading, setLoading] = useState(false);
  const [dprgtCarryBalCalIntInfo, setDprgtCarryBalCalIntInfo] = useState<{persInnerAccno?: string, zoneVal?: string} | null>(null);
  const [position, setPosition] = useState<'top' | 'bottom' | 'hidden'>('bottom');
  const [persInnerAccno, setPersInnerAccno] = useState<string | undefined>(undefined);
  const [zoneVal, setZoneVal] = useState<string | undefined>(undefined);

  useEffect(() => {
    if (props.open) {
      // Check if data has a loading property and set the loading state
      if (props.data?.loading !== undefined) {
        setLoading(props.data.loading);
      }
      
      if (props.data?.records && props.data.records.length > 0) {
        const records = props.data.records;
        setDataSource(records);
        setPagination({
          ...pagination,
          total: props.data?.total || records.length,
        });
        
        // 找到第一条有效记录，获取persInnerAccno和zoneVal
        const firstValidRecord = records.find(record => record.persInnerAccno && record.zoneVal);
        
        if (firstValidRecord?.persInnerAccno && firstValidRecord?.zoneVal) {
          // 只更新状态，不再重复请求数据
          setDprgtCarryBalCalIntInfo({
            persInnerAccno: firstValidRecord.persInnerAccno,
            zoneVal: firstValidRecord.zoneVal
          });
          setPersInnerAccno(firstValidRecord.persInnerAccno);
          setZoneVal(firstValidRecord.zoneVal);
        }
      } else if (!props.data?.loading) {
        // Only clear data if not in loading state
        setDataSource([]);
        setPagination(INITIAL_PAGINATION);
        setDprgtCarryBalCalIntInfo(null);
        setPersInnerAccno(undefined);
        setZoneVal(undefined);
      }
    } else {
      setDataSource([]);
      setPagination(INITIAL_PAGINATION);
      setDprgtCarryBalCalIntInfo(null);
      setPersInnerAccno(undefined);
      setZoneVal(undefined);
    }
  }, [props.open, props.data]);

  const refreshData = useCallback(
    async (current: number = pagination.current, pageSize: number = pagination.pageSize) => {
      // 使用当前状态或accountInfo作为后备
      const accountNo = persInnerAccno || dprgtCarryBalCalIntInfo?.persInnerAccno;
      const zoneValue = zoneVal || dprgtCarryBalCalIntInfo?.zoneVal;
      
      if (!accountNo || !zoneValue) {
        // 向用户显示错误消息，便于调试
        console.error('查询参数不完整:', { accountNo, zoneValue });
        message.error('分页查询失败：账号信息不完整，请重新打开弹窗');
        return;
      }
      
      setLoading(true);
      
      try {
        const result = await getDprgtCarryBalCalInt(
          accountNo,
          zoneValue,
          'DP_RGT_CARRY_BAL_CAL_INT_ALL_QUERYPAGES',
          {
            current,
            pageSize,
          },
        );
        
        if (result && result.data) {
          const detailData = JSON.parse(result.data);
          const newPagination = {
            current: current,
            pageSize: pageSize,
            total: detailData.total || detailData.records.length,
          };
          
          setPagination(newPagination);
          setDataSource(detailData.records || []);
          console.log('查询成功，数据条数:', detailData.records?.length);
        }
      } catch (error) {
        console.error('获取数据失败:', error);
        message.error('获取账户星级登记簿数据失败');
      } finally {
        // Ensure loading state is always cleared
        setLoading(false);
      }
    },
    [persInnerAccno, zoneVal, dprgtCarryBalCalIntInfo, pagination.current, pagination.pageSize],
  );

  // 定义表格列
  // const [position, setPosition] = useState<"top" | "bottom" | "hidden">("bottom");

  const columns: ProColumns<DprgtCarryBalCalInt>[] = [
    {
      title: '个人内部账号',
      dataIndex: 'persInnerAccno',
      width: 260,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: '结转类型代码',
      dataIndex: 'carryTpCd',
      width: 160,
      ellipsis: true,
      align: "center",
      valueType: "select",
      valueEnum: {
        "01": { text: "01-年度结转" },
      },
    },
    {
      title: '结转余额',
      dataIndex: 'carryBal',
      width: 160,
      ellipsis: true,
      align: "center",
      valueType: "money",
    },
    {
      title: '计息积数',
      dataIndex: 'calIntAccu',
      width: 160,
      ellipsis: true,
      align: "center",
      valueType: "digit",
    },
    {
      title: '结转日期',
      dataIndex: 'carryDate',
      width: 160,
      ellipsis: true,
      align: "center",
      valueType: "date",
    },
    {
      title: '生效状态代码',
      dataIndex: 'effStaCd',
      width: 160,
      ellipsis: true,
      align: "center",
      valueType: "select",
      valueEnum: {
        "1": { text: "1-已生效" },
        "2": { text: "2-已失效" },
      },
    },
    {
      title: '上次交易日期',
      dataIndex: 'lastTmTxDt',
      width: 160,
      ellipsis: true,
      align: "center",
      valueType: "date",
    },
    {
      title: '最后交易日期',
      dataIndex: 'lastTxDate',
      width: 160,
      ellipsis: true,
      align: "center",
      valueType: "date",
    },
    {
      title: '记录状态代码',
      dataIndex: 'recordStaCd',
      width: 160,
      ellipsis: true,
      align: "center",
      valueType: "select",
      valueEnum: {
        "0": { text: "0-无效" },
        "1": { text: "1-有效" },
      },
    },
    {
      title: '分片值',
      dataIndex: 'zoneVal',
      width: 160,
      ellipsis: true,
      align: "center",
    },
    {
      title: '作业分片号',
      dataIndex: 'jobZoneNo',
      width: 160,
      ellipsis: true,
      align: "center",
    },
    {
      title: '乐观锁版本号',
      dataIndex: 'optimistLockVerNo',
      width: 160,
      ellipsis: true,
      align: "center",
    },
    {
      title: '创建时间戳',
      dataIndex: 'createStamp',
      width: 160,
      ellipsis: true,
      align: "center",
    },
    {
      title: '最后修改时间戳',
      dataIndex: 'lastModStamp',
      width: 160,
      ellipsis: true,
      align: "center",
    },
    {
      title: "操作",
      valueType: "option",
      width: 150,
      fixed: "right",
      align: "center",
      render: (text, record: DprgtCarryBalCalInt, _, action) => {
        const isRowEditing = record.persInnerAccno
          ? editableKeys.includes(record.persInnerAccno)
          : false;
        return [
          isRowEditing ? (
            <>
              <Button
                key="confirm"
                type="link"
                onClick={async () => {
                  try {
                    if (record.persInnerAccno) {
                      await action?.saveEditable?.(record.persInnerAccno);
                      setIsEditing(false);
                    }
                  } catch (err) {
                    console.error("保存失败:", err);
                  }
                }}
              >
                确定
              </Button>
              <Button
                key="cancel"
                type="link"
                onClick={() => {
                  if (record.persInnerAccno) {
                    action?.cancelEditable?.(record.persInnerAccno);
                    setEditableKeys([]);
                    setIsEditing(false);
                  }
                }}
                style={{ marginLeft: 8 }}
              >
                取消
              </Button>
            </>
          ) : (
            <Button
              key="editable"
              type="link"
              // disabled={isEditing}
              disabled={true}
              onClick={async () => {
                if (!record.persInnerAccno) return;
                const originalRecord = { ...record };
                setEditingRecord(originalRecord);
                setIsEditing(true);
                await action?.startEditable?.(record.persInnerAccno);
              }}
              style={{ padding: 0 }}
            >
              编辑
            </Button>
          ),
          <Button
            key="delete"
            type="link"
            // disabled={isEditing}
            disabled={true}
            onClick={async () => {
              if (!record.persInnerAccno) return;
              Modal.confirm({
                title: "确认删除",
                content: "确定要删除这条记录吗？",
                onOk: async () => {
                  try {
                    const res = await deleteDprgtCarryBalCalInt(record);
                    if (res.code === 200) {
                      message.success("删除成功");
                    } else {
                      message.error(res.msg || "删除失败");
                    }
                  } catch (error) {
                    message.error("删除失败");
                  } finally {
                    await refreshData();
                  }
                },
                okText: "确认",
                cancelText: "取消",
              });
            }}
            style={{ marginLeft: 8 }}
          >
            删除
          </Button>,
        ];
      },
    },
  ];

  const handleAdd = async (record: DprgtCarryBalCalInt) => {
    try {
      // 设置必要的默认字段
      const newRecord = {
        ...record,
        persInnerAccno: persInnerAccno,
        zoneVal: zoneVal,
        recordStaCd: "1", // 默认有效
        effStaCd: "1", // 默认有效
      };
      
      setLoading(true);
      const res = await insertDprgtCarryBalCalInt(newRecord);
      if (res.code === 200) {
        message.success('添加成功');
        refreshData();
      } else {
        message.error(res.msg || '添加失败');
      }
    } catch (error) {
      console.error('添加失败:', error);
      message.error('添加失败');
    } finally {
      // 添加完成后重置状态
      setLoading(false);
      setRefresh(prev => prev + 1);
      setEditableKeys([]);
      setIsEditing(false);
    }
  };

  return (
    <Modal
      width={1200}
      title="活期产品合约账户结转余额表"
      open={props.open}
      destroyOnClose
      onCancel={props.onCancel}
      footer={null}
      bodyStyle={MODAL_BODY_STYLE}
      style={MODAL_STYLE}
    >
      <Spin spinning={loading} tip="加载数据中..." size="large">
      <div style={{ marginBottom: 16 }}>
          <p>
            数据状态: {loading ? (
              <span>
                <Spin size="small" style={{ marginRight: 8 }} />
                加载中...
              </span>
            ) : (
              `已加载 ${dataSource.length} 条记录, 总共 ${pagination.total} 条`
            )}
            {dprgtCarryBalCalIntInfo && (
              <span style={{ marginLeft: 8 }}>
                | 个人内部账号: {dprgtCarryBalCalIntInfo.persInnerAccno}
              </span>
            )}
          </p>
        </div>
        <EditableProTable<DprgtCarryBalCalInt>
          key={refresh}
          rowKey={(record) => record.persInnerAccno || ""}
          headerTitle="活期产品合约账户结转余额表信息"
          scroll={TABLE_SCROLL}
          loading={loading}
          toolBarRender={() => [
            <ProFormRadio.Group
              key="positionRender"
              fieldProps={{
                value: position,
                onChange: (e: any) => setPosition(e.target.value),
              }}
              options={[
                { label: "添加到顶部", value: "top" },
                { label: "添加到底部", value: "bottom" },
                { label: "隐藏", value: "hidden" },
              ]}
            />,
          ]}
          recordCreatorProps={{
            position: position === 'hidden' ? 'bottom' : position,
            record: (index, dataSource) => {
              // 直接创建新记录，不检查是否有正在编辑的行
              return {
                persInnerAccno: persInnerAccno,
                carryTpCd: "01", // 默认为年度结转
                carryBal: 0.00, // 默认结转余额
                calIntAccu: 0, // 默认计息积数
                carryDate: new Date().toISOString().split('T')[0], // 结转日期为当前日期
                effStaCd: "1", // 默认有效
                lastTmTxDt: new Date().toISOString().split('T')[0], // 上次交易日期
                lastTxDate: new Date().toISOString().split('T')[0], // 最后交易日期
                recordStaCd: "1", // 默认有效
                zoneVal: zoneVal,
              } as DprgtCarryBalCalInt;
            },
            creatorButtonText: '添加一行数据',
            newRecordType: 'dataSource',
          }}
          columns={columns}
          request={async (params, sorter, filter) => {
            // 直接返回当前数据源，不进行额外的API调用
            return {
              data: dataSource as DprgtCarryBalCalInt[],
              total: pagination.total,
              success: true,
            };
          }}
          value={dataSource}
          onChange={setDataSource}
          editable={{
            type: 'multiple', // 支持同时编辑多行
            editableKeys,
            onSave: async (rowKey, data, row) => {
              try {
                // 如果是新添加的记录则调用新增接口
                if (String(rowKey).startsWith('NEW_')) {
                  await handleAdd(data);
                } else {
                  // 否则调用更新接口
                  setLoading(true);
                  const res = await updateDprgtCarryBalCalInt(data);
                  if (res.code === 200) {
                    message.success('更新成功');
                    refreshData();
                  } else {
                    message.error(res.msg || '更新失败');
                  }
                }
              } catch (error) {
                console.error('保存失败:', error);
                message.error('保存失败');
              } finally {
                // 保存后从编辑状态中移除当前行
                setEditableKeys(keys => keys.filter(key => key !== rowKey));
                if (editableKeys.length <= 1) {
                  setIsEditing(false);
                }
                setLoading(false);
              }
            },
            onChange: setEditableKeys,
            onCancel: async (key) => {
              // 取消编辑时只清除当前行的编辑状态
              setEditableKeys(keys => keys.filter(k => k !== key));
              if (editableKeys.length <= 1) {
                setIsEditing(false);
              }
              return true;
            },
            actionRender: (row, config, dom) => [dom.save, dom.cancel],
          }}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条`,
            onChange: async (page, pageSize) => {
              console.log('分页改变:', { page, pageSize, currentPage: pagination.current, isEditing });
              
              if (isEditing) {
                message.warn('请先完成编辑操作再切换页面');
                return;
              }

              // 仅当页码或页大小变化时才刷新数据
              if (page !== pagination.current || pageSize !== pagination.pageSize) {
                // 更新分页状态
                setPagination({
                  ...pagination,
                  current: page,
                  pageSize: pageSize,
                });

                // 直接调用刷新数据，不做额外判断
                await refreshData(page, pageSize);
              }
            },
          }}
        />
      </Spin>
    </Modal>
  );
};

export default DprgtCarryBalCalIntFormEdit; 