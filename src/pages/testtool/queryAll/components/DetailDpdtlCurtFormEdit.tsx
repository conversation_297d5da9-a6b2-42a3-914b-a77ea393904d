import React, { useEffect, useState } from "react";
import { Modal, message, Button } from "antd";
import { useAccess } from 'umi';
import {
  dpdtlCurtAccUpdate,
  getDpdtlCurtAcc,
  insertDpdtlCurtAcc,
  dpdtlCurtAccDelete,
} from "../service";
import type { TbDpdtlCurtAcc, PageResponse } from "../data";
import type { ProColumns } from "@ant-design/pro-components";
import { EditableProTable, ProFormRadio } from "@ant-design/pro-components";


/*
 * @Description: 活期明细表编辑表单
 * @Author: zhujinwei
 * @Date: 2025-01-17 10:00:00
 */

interface PaginationType {
  current: number;
  pageSize: number;
  total: number;
}

export type DpdtlCurtAccFormProps = {
  onCancel: () => void;
  open: boolean;
  data: PageResponse<TbDpdtlCurtAcc> | null;
  refresh: number;
};

// 表格相关常量
const DEFAULT_PAGE_SIZE = 10;
const TABLE_SCROLL = { x: 1300 } as const;
const TABLE_OPTIONS = {
  density: true,
  fullScreen: true,
  setting: true,
} as const;
const TABLE_PAGINATION = {
  showSizeChanger: true,
  showQuickJumper: true,
} as const;

// Modal相关常量
const MODAL_BODY_STYLE = {
  maxHeight: "70vh",
  overflow: "auto",
  padding: "24px",
  borderRadius: "10px",
} as const;

const MODAL_STYLE = {
  borderRadius: "10px",
  overflow: "hidden",
} as const;


// 工具函数
const formatAmount = (value: string | number | null | undefined): string => {
  if (!value) return "0.00";
  const num = typeof value === "string" ? parseFloat(value) : value;
  return isNaN(num) ? "0.00" : num.toFixed(2);
};

// 分页相关常量
const INITIAL_PAGINATION: PaginationType = {
  current: 1,
  pageSize: DEFAULT_PAGE_SIZE,
  total: 0,
};

const DetailDpdtlCurtFormEdit: React.FC<DpdtlCurtAccFormProps> = (props) => {
  const [dataSource, setDataSource] = useState<readonly TbDpdtlCurtAcc[]>([]);
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);
  const [isEditing, setIsEditing] = useState(false);
  const [editingRecord, setEditingRecord] = useState<TbDpdtlCurtAcc | null>(
    null
  );
  const [pagination, setPagination] =
    useState<PaginationType>(INITIAL_PAGINATION);
  const [refresh, setRefresh] = useState(0);
  const [loading, setLoading] = useState(false);

  // 添加 access 声明
  const access = useAccess();

  // 刷新数据方法
  const refreshData = async () => {
    if (!props.data?.records?.[0]) return;

    const TbDpmstCurtAcc = props.data.records[0];
    if (!TbDpmstCurtAcc?.persInnerAccno || !TbDpmstCurtAcc?.zoneVal) return;

    setLoading(true);
    try {
      const res = await getDpdtlCurtAcc(
        TbDpmstCurtAcc.persInnerAccno,
        TbDpmstCurtAcc.zoneVal,
        "dpdtlCurtAccPageInfo",
        {
          current: pagination.current,
          pageSize: pagination.pageSize,
        }
      );

      if (res.code === 200 && res.data) {
        const detailData = JSON.parse(res.data);
        setDataSource(detailData.records || []);
        setPagination({
          current: detailData.current || INITIAL_PAGINATION.current,
          pageSize: detailData.size || INITIAL_PAGINATION.pageSize,
          total: detailData.total || INITIAL_PAGINATION.total,
        });
        setRefresh(refresh + 1);
      } else {
        message.error(res.msg || "获取明细失败");
      }
    } catch (error) {
      message.error("查询明细失败");
    } finally {
      setLoading(false);
    }
  };

  // 初始化数据
  useEffect(() => {
    let isMounted = true;

    const initData = async () => {
      if (props.open && props.data) {
        const { records, current, size, total } = props.data;
        if (isMounted) {
          setDataSource(records || []);
          setPagination({
            current: current || INITIAL_PAGINATION.current,
            pageSize: size || INITIAL_PAGINATION.pageSize,
            total: total || INITIAL_PAGINATION.total,
          });
        }
      } else if (isMounted) {
        setDataSource([]);
        setPagination(INITIAL_PAGINATION);
      }
    };

    initData();

    return () => {
      isMounted = false;
    };
  }, [props.open, props.data]);

  // 定义表格列
  const [position, setPosition] = useState<"top" | "bottom" | "hidden">(
    "bottom"
  );

  const columns: ProColumns<TbDpdtlCurtAcc>[] = [
    {
      title: "个人内部账号",
      dataIndex: "persInnerAccno",
      width: 180,
      fixed: true,
      ellipsis: true,
      align: 'center',
      editable: () => false,
    },
    {
      title: "明细序号",
      dataIndex: "dtlSeqNo",
      width: 100,
      fixed: true,
      ellipsis: true,
      align: 'center',
      editable: () => false,
    },
    {
      title: "交易日期",
      dataIndex: "txDate",
      width: 100,
      ellipsis: true,
      align: 'center',
      render: (_, entity) => entity.txDate || "-",
      editable: () => true,
      valueType: 'date',
    },
    {
      title: "全局业务跟踪号",
      dataIndex: "globalBusiTrackNo",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => true,
    },
    {
      title: "子交易序号",
      dataIndex: "subtxNo",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => true,
    },
    {
      title: "介质编号",
      dataIndex: "mediumNo",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => false,
    },
    {
      title: "产品合约编号",
      dataIndex: "prodtContractNo",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => false,
    },
    {
      title: "币种代码",
      dataIndex: "currCode",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => true,
      valueType: 'select',
      valueEnum: {
        '036': { text: '036-澳大利亚元' },
        '124': { text: '124-加元' },
        '344': { text: '344-香港元' },
        '392': { text: '392-日元' },
        '826': { text: '826-英镑' },
        '840': { text: '840-美元' },
        '978': { text: '978-欧元（EUR）' },
        '156': { text: '156-人民币元' },
      },
    },
    {
      title: "钞汇类别代码",
      dataIndex: "cashExgVatgCd",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => true,
      valueType: 'select',
      valueEnum: {
        '2': { text: '2-钞' },
        '3': { text: '3-汇' },
      },
    },
    {
      title: "服务编码",
      dataIndex: "servNo",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => true,
    },
    {
      title: "渠道种类代码",
      dataIndex: "chnlKindCode",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => true,
      valueType: 'select',
      valueEnum: {
        '01': { text: '01-网点柜面' },
        '10': { text: '10-网上银行' },
        '12': { text: '12-个人网银' },
        '13': { text: '13-电视银行' },
        '14': { text: '14-电话银行' },
        '15': { text: '15-手机银行' },
        '16': { text: '16-企业网银' },
        '17': { text: '17-自助设备' },
        '18': { text: '18-POS' },
        '20': { text: '20-超级网银' },
        '21': { text: '21-大小额支付' },
        '22': { text: '22-银联前置' },
        '24': { text: '24-管理端' },
        '25': { text: '25-交易端' },
        '26': { text: '26-商易通' },
        '27': { text: '27-助农通' },
        '29': { text: '29-外部系统' },
        '30': { text: '30-系统自动' },
        '31': { text: '31-电子汇兑系统' },
        '32': { text: '32-理财规划终端' },
        '34': { text: '34-网汇通' },
        '35': { text: '35-同城支付' },
        '36': { text: '36-移动终端-TSM（可信服务管理）' },
        '37': { text: '37-移动终端-移动展业' },
        '38': { text: '38-直销银行' },
        '39': { text: '39-短信' },
        '40': { text: '40-专属APP' },
        '41': { text: '41-第三方线上渠道' },
        '43': { text: '43-智能柜员机（ITM）' },
        '42': { text: '42-国际支付前置' },
        '44': { text: '44-邮储经营' },
        '45': { text: '45-银银前置系统' },
        '46': { text: '46-U链供应链' },
        '47': { text: '47-油料保障结算系统' },
        '48': { text: '48-银企直联' },
        '91': { text: '91-微信银行' },
        '99': { text: '99-其他' },
      },
    },
    {
      title: "发起系统或组件编码",
      dataIndex: "startSysOrCmptNo",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => true,
    },
    {
      title: "存取标志代码",
      dataIndex: "dwFlagCode",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => true,
      valueType: 'select',
      valueEnum: {
        '1': { text: '1-存' },
        '2': { text: '2-取' },
      },
    },
    {
      title: "收付款方式代码",
      dataIndex: "cashTranFlagCode",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => true,
      valueType: 'select',
      valueEnum: {
        '0': { text: '0-现金' },
        '1': { text: '1-转账（仅指活转活）' },
        '2': { text: '2-转存' },
        '3': { text: '3-支票' },
        '4': { text: '4-活转定' },
        '5': { text: '5-定转活' },
      },
    },
    {
      title: "可售产品编码",
      dataIndex: "vendibiProdtNo",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => false,
    },
    {
      title: "交易金额",
      dataIndex: "txAmt",
      width: 120,
      ellipsis: true,
      align: 'center',
      render: (_, entity) => formatAmount(entity.txAmt),
      editable: () => true,
    },
    {
      title: "账户余额",
      dataIndex: "accBal",
      width: 120,
      ellipsis: true,
      align: 'center',
      render: (_, entity) => formatAmount(entity.accBal),
      editable: () => true,
    },
    {
      title: "副卡可用额度",
      dataIndex: "subCardAvalLimit",
      width: 120,
      ellipsis: true,
      align: 'center',
      render: (_, entity) => formatAmount(entity.subCardAvalLimit),
      editable: () => true,
    },
    {
      title: "交易金额类型代码",
      dataIndex: "txamtTpCd",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => true,
      valueType: 'select',
      valueEnum: {
        '0001': { text: '0001-余额(本金)' },
        '0002': { text: '0002-利息' },
        '0003': { text: '0003-费用' },
        '0004': { text: '0004-利息税' },
        '0005': { text: '0005-剩余本金' },
        '0010': { text: '0010-个人所得税（养老金）' },
      },
    },
    {
      title: "存款交易类型代码",
      dataIndex: "deptTxTpCd",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => true,
      valueType: 'select',
      valueEnum: {
        '1': { text: '1-存' },
        '2': { text: '2-取' },
      },
    },
    {
      title: "是否跨行标志",
      dataIndex: "ibankFlag",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => true,
      valueType: 'select',
      valueEnum: {
        '0': { text: '0-否' },
        '1': { text: '1-是' },
      },
    },
    {
      title: "代理人代办标志",
      dataIndex: "agenterAgentFlag",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => true,
      valueType: 'select',
      valueEnum: {
        '0': { text: '0-否' },
        '1': { text: '1-是' },
      },
    },
    {
      title: "交易对手行号",
      dataIndex: "txOpsBankNo",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => true,
    },
    {
      title: "交易对手行名",
      dataIndex: "txOpsBankNm",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => true,
    },
    {
      title: "交易对手账号",
      dataIndex: "txOpsAccno",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => true,
    },
    {
      title: "交易对手名称",
      dataIndex: "txOpsName",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => true,
    },
    {
      title: "转入手机号码",
      dataIndex: "transInMobileNo",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => true,
    },
    {
      title: "对方机构号",
      dataIndex: "opInstNo",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => true,
    },
    {
      title: "交易柜员号",
      dataIndex: "txTellerNo",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => true,
    },
    {
      title: "复核柜员号",
      dataIndex: "reviewTellerNo",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => true,
    },
    {
      title: "授权柜员号",
      dataIndex: "authTellerNo",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => true,
    },
    {
      title: "商户类型号",
      dataIndex: "merTypeNo",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => true,
    },
    {
      title: "商户编码",
      dataIndex: "merNo",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => true,
    },
    {
      title: "商户类型号",
      dataIndex: "merTypeNo",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => true,
    },
    {
      title: "商户描述",
      dataIndex: "merDesc",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => true,
    },
    {
      title: "终端标识代码",
      dataIndex: "terminalFlagCd",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => true,
      valueType: 'select',
      valueEnum: {
        '01': { text: '01-无关' },
        '02': { text: '02-ATM' },
        '03': { text: '03-POS' },
      },
    },
    {
      title: "终端号",
      dataIndex: "terminalNo",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => true,
    },
    {
      title: "个人摘要代码",
      dataIndex: "summNo",
      width: 200,
      ellipsis: true,
      align: 'center',
      editable: () => true,
    },
    {
      title: "摘要名称",
      dataIndex: "summName",
      width: 120,
      ellipsis: true,
      align: 'center',
      editable: () => true,
    },
    {
      title: "账户交易标志码",
      dataIndex: "accTxFlagCd",
      width: 120,
      ellipsis: true,
      align: 'center',
      editable: () => true,
    },
    {
      title: "执行步骤序号",
      dataIndex: "execStepSeqNo",
      width: 120,
      ellipsis: true,
      align: 'center',
      editable: () => true,
    },
    {
      title: "交易备注",
      dataIndex: "txRemark",
      width: 120,
      ellipsis: true,
      align: 'center',
      editable: () => true,
    },
    
    {
      title: "分片值",
      dataIndex: "zoneVal",
      width: 120,
      ellipsis: true,
      align: 'center',
      editable: () => false,
    },
    {
      title: "操作",
      valueType: "option",
      width: 150,
      // fixed: "right",
      align: 'center',
      render: (text, record: TbDpdtlCurtAcc, _, action) => {
        const isEditing = record.dtlSeqNo
          ? editableKeys.includes(record.dtlSeqNo)
          : false;
        const isDisabled = !access.hasEnvPerms(
          localStorage.getItem("currentEnv") as string
        );
        return [
          isEditing ? (
            <>
              <a
                key="confirm"
                onClick={async () => {
                  try {
                    if (record.dtlSeqNo) {
                      await action?.saveEditable?.(record.dtlSeqNo);
                      setIsEditing(false);
                    }
                  } catch (err) {
                    console.error("保存失败:", err);
                  }
                }}
              >
                确定
              </a>
              <a
                key="cancel"
                onClick={() => {
                  if (record.dtlSeqNo) {
                    action?.cancelEditable?.(record.dtlSeqNo);
                    setEditableRowKeys([]);
                    setIsEditing(false);
                  }
                }}
                style={{ marginLeft: 8 }}
              >
                取消
              </a>
            </>
          ) : (
            <Button
              key="editable"
              type="link"
              disabled={true}
              onClick={async (e) => {
                e.preventDefault(); // 阻止默认事件
                if (isDisabled) {
                  message.warning('您暂无编辑权限');
                  return;
                }
                if (!record.dtlSeqNo) return;
                // 先保存原始数据，再进入编辑状态
                const originalRecord = { ...record };
                setEditingRecord(originalRecord);
                setIsEditing(true);
                await action?.startEditable?.(record.dtlSeqNo);
              }}
              style={{ 
                cursor: isDisabled ? 'not-allowed' : 'pointer',
                color: isDisabled ? 'rgba(0, 0, 0, 0.25)' : undefined
              }}
            >
              编辑
            </Button>
          ),
          <Button
            key="delete"
            type="link"
            disabled={true}
            onClick={async (e) => {
              e.preventDefault(); // 阻止默认事件
              if (isDisabled) {
                message.warning('您暂无删除权限');
                return;
              }
              if (!record.dtlSeqNo) return;
              Modal.confirm({
                title: "确认删除",
                content: "确定要删除这条记录吗？",
                onOk: async () => {
                  try {
                    const res = await dpdtlCurtAccDelete(record);
                    if (res.code === 200) {
                      message.success("删除成功");
                    } else {
                      message.error(res.msg || "删除失败");
                    }
                  } catch (error) {
                    message.error("删除失败");
                  } finally {
                    await refreshData();
                  }
                },
                okText: "确认",
                cancelText: "取消",
              });
            }}
            style={{ 
              marginLeft: 8,
              cursor: isDisabled ? 'not-allowed' : 'pointer',
              color: isDisabled ? 'rgba(0, 0, 0, 0.25)' : undefined
            }}
          >
            删除
          </Button>,
        ];
      },
    },
  ];

  return (
    <Modal
      width={1200}
      title="查询活期账户明细信息"
      open={props.open}
      destroyOnClose
      onCancel={props.onCancel}
      footer={null}
      bodyStyle={MODAL_BODY_STYLE}
      style={MODAL_STYLE}
    >
      <EditableProTable<TbDpdtlCurtAcc>
        key={refresh}
        rowKey={(record) => record.dtlSeqNo || ""}
        headerTitle="交易明细"
        scroll={TABLE_SCROLL}
        recordCreatorProps={
          position === "hidden"
            ? false
            : {
                position: position,
                creatorButtonText: "新增一行交易明细",
                // 添加按钮样式和禁用状态
                style: !access.hasEnvPerms(
                  localStorage.getItem("currentEnv") as string
                )
                  ? {
                      cursor: 'not-allowed',
                      color: 'rgba(0, 0, 0, 0.25)',
                      backgroundColor: '#f5f5f5',
                      borderColor: '#d9d9d9',
                      pointerEvents: 'none'  // 禁用点击事件
                    }
                  : undefined,
                onClick: async (e) => {
                  e.preventDefault(); // 阻止默认事件
                if (!access.hasEnvPerms(localStorage.getItem("currentEnv") as string)) {
                  message.warning('您暂无编辑权限');
                  return;
                }
                  // 获取当前页数据
                  const startIndex = (pagination.current - 1) * pagination.pageSize;
                  const endIndex = startIndex + pagination.pageSize;

                  const currentPage = pagination.current; // 当前页码
                  const pageSize = pagination.pageSize; // 每页显示的数据量
                  // 计算当前页面中数据量
                  const currentPageDataCount = Math.min(
                    pageSize, // 每页最大显示数
                    pagination.total - (currentPage - 1) * pageSize // 剩余数据量
                  );

                  // 检查当前页是否已满
                  if (currentPage !== 1 && currentPageDataCount >= pagination.pageSize) {
                    Modal.error({
                      title: '提示',
                      content: '当前页面数据已满，请切换至第一页或者是未满的页面进行新增',
                    });
                    return false;
                  }

                  // 创建新记录
                  const newRecord = {
                    dtlSeqNo: "9999",
                    persInnerAccno: props.data?.records?.[0]?.persInnerAccno ?? "",
                    txDate: new Date().toISOString().split('T')[0],
                    globalBusiTrackNo: "",
                    subtxNo: "",
                    prodtContractNo: props.data?.records?.[0]?.prodtContractNo ?? "",
                    mediumNo: props.data?.records?.[0]?.mediumNo ?? "",
                    currCode: "156",
                    cashExgVatgCd: "2",
                    servNo: "",
                    chnlKindCode: "01",
                    startSysOrCmptNo: "1022199",
                    dwFlagCode: "1",
                    cashTranFlagCode: "0",
                    vendibiProdtNo: props.data?.records?.[0]?.vendibiProdtNo ?? "",
                    txAmt: "0.00",
                    accBal: "0.00",
                    subCardAvalLimit: "0.00",
                    txamtTpCd: "0001",
                    deptTxTpCd: "1",
                    ibankFlag: "0",
                    agenterAgentFlag: "0",
                    txOpsBankNo: "",
                    txOpsBankNm: "",
                    txOpsAccno: "",
                    txOpsName: "",
                    transInMobileNo: "",
                    opInstNo: "",
                    txInstNo: "",
                    txTellerNo: "",
                    reviewTellerNo: "",
                    authTellerNo: "",
                    merTypeNo: "",
                    merNo: "",
                    merDesc: "",
                    terminalFlagCd: "01",
                    terminalNo: "",
                    summNo: "",
                    summName: "",
                    accTxFlagCd: "",
                    execStepSeqNo: "",
                    txRemark: "",
                    recordStaCd: "0",
                    zoneVal: props.data?.records?.[0]?.zoneVal ?? "",
                  };

                  // 在当前页末尾添加新记录
                  const newDataSource = [...dataSource];
                  newDataSource.splice(endIndex, 0, newRecord);

                  setDataSource(newDataSource);
                  setIsEditing(true);
                  setEditableRowKeys(["9999"]);
                },
                record: () => {
                  const today = new Date();
                  const year = today.getFullYear();
                  const month = String(today.getMonth() + 1).padStart(2, '0');
                  const day = String(today.getDate()).padStart(2, '0');
                  const formattedDate = `${year}-${month}-${day}`;
                
                  return {
                    dtlSeqNo: "9999",
                    persInnerAccno: props.data?.records?.[0]?.persInnerAccno ?? "",
                    txDate: formattedDate,
                    globalBusiTrackNo: "",
                    subtxNo: "",
                    prodtContractNo: props.data?.records?.[0]?.prodtContractNo ?? "",
                    mediumNo: props.data?.records?.[0]?.mediumNo ?? "",
                    currCode: "156",
                    cashExgVatgCd: "2",
                    servNo: "",
                    chnlKindCode: "01",
                    startSysOrCmptNo: "1022199",
                    dwFlagCode: "1",
                    cashTranFlagCode: "0",
                    vendibiProdtNo: props.data?.records?.[0]?.vendibiProdtNo ?? "",
                    txAmt: "0.00",
                    accBal: "0.00",
                    subCardAvalLimit: "0.00",
                    txamtTpCd: "0001",
                    deptTxTpCd: "1",
                    ibankFlag: "0",
                    agenterAgentFlag: "0",
                    txOpsBankNo: "",
                    txOpsBankNm: "",
                    txOpsAccno: "",
                    txOpsName: "",
                    transInMobileNo: "",
                    opInstNo: "",
                    txInstNo: "",
                    txTellerNo: "",
                    reviewTellerNo: "",
                    authTellerNo: "",
                    merTypeNo: "",
                    merNo: "",
                    merDesc: "",
                    terminalFlagCd: "01",
                    terminalNo: "",
                    summNo: "",
                    summName: "",
                    accTxFlagCd: "",
                    execStepSeqNo: "",
                    txRemark: "",
                    recordStaCd: "0",
                    zoneVal: props.data?.records?.[0]?.zoneVal ?? "",
                  } as TbDpdtlCurtAcc;
                }
              }
        }
        
        loading={loading}
        toolBarRender={() => [
          <ProFormRadio.Group
            key="render"
            fieldProps={{
              value: position,
              onChange: (e: any) => setPosition(e.target.value),
            }}
            options={[
              {
                label: "添加到顶部",
                value: "top",
              },
              {
                label: "添加到底部",
                value: "bottom",
              },
              {
                label: "隐藏",
                value: "hidden",
              },
            ]}
          />,
        ]}
        columns={columns}
        request={async (params, sorter, filter) => {
          // 如果正在编辑，直接返回当前数据
          if (isEditing) {
            return {
              data: dataSource,
              total: pagination.total,
              success: true
            };
          }

          setLoading(true);
          try {
            if (
              !props.data ||
              !props.data.records ||
              props.data.records.length === 0
            ) {
              return {
                data: [],
                total: 0,
                success: true,
              };
            }
            const TbDpmstCurtAcc = props.data.records[0];
            if (!TbDpmstCurtAcc?.persInnerAccno || !TbDpmstCurtAcc?.zoneVal) {
              return {
                data: [],
                total: 0,
                success: true,
              };
            }
            const { current, pageSize } = params;
            const res = await getDpdtlCurtAcc(
              TbDpmstCurtAcc.persInnerAccno,
              TbDpmstCurtAcc.zoneVal,
              "dpdtlCurtAccPageInfo",
              {
                current: current || 1,
                pageSize: pageSize || DEFAULT_PAGE_SIZE,
              }
            );
            if (res.code === 200 && res.data) {
              const detailData = JSON.parse(res.data);
              setDataSource(detailData.records || []);
              setPagination({
                current: detailData.current || 1,
                pageSize: detailData.size || DEFAULT_PAGE_SIZE,
                total: detailData.total || 0,
              });
              return {
                data: detailData.records || [],
                total: detailData.total || 0,
                success: true,
              };
            } else {
              message.error(res.msg || "获取明细失败");
              return {
                data: [],
                total: 0,
                success: false,
              };
            }
          } catch (error) {
            message.error("查询明细失败");
            return {
              data: [],
              total: 0,
              success: false,
            };
          } finally {
            setLoading(false);
          }
        }}
        dataSource={dataSource}
        pagination={{
          ...TABLE_PAGINATION,
          current: pagination.current,
          pageSize: pagination.pageSize,
          total: pagination.total,
          showTotal: (total) => `总共 ${total} 条`,
          onChange: (current, pageSize) => {
            setPagination((prev) => ({ ...prev, current, pageSize }));
          },
        }}
        editable={{
          type: "multiple",
          editableKeys,
          onSave: async (rowKey, data) => {
            console.log("保存数据:", rowKey, data);
            if (data.dtlSeqNo === "9999") {
              // 新增数据
              try {
                const newData = {
                  ...data,
                  dtlSeqNo: "9999", // 生成唯一ID
                };
                console.log("保存新增数据:", newData);
                const res = await insertDpdtlCurtAcc(newData);
                if (res.code === 200) {
                  message.success("新增成功");
                  // 新增成功后刷新数据并跳转到最后一页
                  setEditableRowKeys([]);
                  setIsEditing(false);
                  setEditingRecord(null);
                  
                  // 先获取最新的数据总数
                  const TbDpmstCurtAcc = props.data?.records?.[0];
                  if (TbDpmstCurtAcc?.persInnerAccno && TbDpmstCurtAcc?.zoneVal) {
                    const res = await getDpdtlCurtAcc(
                      TbDpmstCurtAcc.persInnerAccno,
                      TbDpmstCurtAcc.zoneVal,
                      "dpdtlCurtAccPageInfo",
                      {
                        current: 1,
                        pageSize: 1
                      }
                    );
                    
                    if (res.code === 200 && res.data) {
                      const detailData = JSON.parse(res.data);
                      const total = detailData.total || 0;
                      const lastPage = Math.ceil(total / pagination.pageSize);
                      
                      setPagination(prev => ({
                        ...prev,
                        current: lastPage,
                        total: total
                      }));
                    }
                  }
                  
                  await refreshData();
                  return true;
                } else {
                  message.error(res.msg || "新增失败");
                  await refreshData();
                  return false;
                }
              } catch (error) {
                message.error("新增失败");
                return false;
              }
            }

            if (!editingRecord || !rowKey) {
              message.error("编辑记录不存在");
              return false;
            }

            let originalData: TbDpdtlCurtAcc | null = null;
            try {
              // 只上送可编辑字段
              const updateData = {
                dtlSeqNo: data.dtlSeqNo,
                txDate: data.txDate,
                globalBusiTrackNo: data.globalBusiTrackNo,
                subtxNo: data.subtxNo,
                prodtContractNo: data.prodtContractNo,
                mediumNo: data.mediumNo,
                currCode: data.currCode,
                cashExgVatgCd: data.cashExgVatgCd,
                servNo: data.servNo,
                chnlKindCode: data.chnlKindCode,
                startSysOrCmptNo: data.startSysOrCmptNo,
                dwFlagCode: data.dwFlagCode,
                cashTranFlagCode: data.cashTranFlagCode,
                vendibiProdtNo: data.vendibiProdtNo,
                txAmt: data.txAmt,
                accBal: data.accBal,
                subCardAvalLimit: data.subCardAvalLimit,
                txamtTpCd: data.txamtTpCd,
                deptTxTpCd: data.deptTxTpCd,
                ibankFlag: data.ibankFlag,
                agenterAgentFlag: data.agenterAgentFlag,
                txOpsBankNo: data.txOpsBankNo,
                txOpsBankNm: data.txOpsBankNm,
                txOpsAccno: data.txOpsAccno,
                txOpsName: data.txOpsName,
                transInMobileNo: data.transInMobileNo,
                opInstNo: data.opInstNo,
                txInstNo: data.txInstNo,
                txTellerNo: data.txTellerNo,
                reviewTellerNo: data.reviewTellerNo,
                authTellerNo: data.authTellerNo,
                merTypeNo: data.merTypeNo,
                merNo: data.merNo,
                merDesc: data.merDesc,
                terminalFlagCd: data.terminalFlagCd,
                terminalNo: data.terminalNo,
                summNo: data.summNo,
                summName: data.summName,
                accTxFlagCd: data.accTxFlagCd,
                execStepSeqNo: data.execStepSeqNo,
                txRemark: data.txRemark,
                lastTxDate: data.lastTxDate,
                recordStaCd: data.recordStaCd,
              };

              // 保存当前数据
              originalData =
                dataSource.find((item) => item.dtlSeqNo === rowKey) || null;

              const res = await dpdtlCurtAccUpdate(updateData);

              if (res.code === 200) {
                // 成功时更新数据
                setDataSource((prevDataSource) =>
                  prevDataSource.map((item) =>
                    item.dtlSeqNo === rowKey ? { ...data } : item
                  )
                );
                message.success("保存成功");
              } else {
                // API调用失败，回滚数据
                message.error(res.msg || "保存失败");
                if (originalData) {
                  setDataSource((prevDataSource) => {
                    return prevDataSource.map((item) =>
                      item.dtlSeqNo === rowKey ? originalData : item
                    ) as TbDpdtlCurtAcc[];
                  });
                }
              }
            } catch (error) {
              // 发生错误，回滚数据
              message.error("保存失败");
              if (originalData) {
                setDataSource((prevDataSource) => {
                  return prevDataSource.map((item) =>
                    item.dtlSeqNo === rowKey ? originalData : item
                  ) as TbDpdtlCurtAcc[];
                });
              }
            } finally {
              // 退出编辑状态
              setEditableRowKeys([]);
              setIsEditing(false);
              setEditingRecord(null);
              if (props.data) {
                try {
                  const TbDpmstCurtAcc =
                    props.data.records && props.data.records[0];
                  if (
                    TbDpmstCurtAcc?.persInnerAccno &&
                    TbDpmstCurtAcc?.zoneVal
                  ) {
                    setLoading(true);
                    try {
                      const res = await getDpdtlCurtAcc(
                        TbDpmstCurtAcc.persInnerAccno,
                        TbDpmstCurtAcc.zoneVal,
                        "dpdtlCurtAccPageInfo",
                        {
                          current: pagination.current,
                          pageSize: pagination.pageSize,
                        }
                      );
                      if (res.code === 200 && res.data) {
                        const detailData = JSON.parse(res.data);
                        console.log("解析后的明细数据:", detailData);
                        setDataSource(detailData.records || []);
                        setPagination({
                          current:
                            detailData.current || INITIAL_PAGINATION.current,
                          pageSize:
                            detailData.size || INITIAL_PAGINATION.pageSize,
                          total: detailData.total || INITIAL_PAGINATION.total,
                        });
                        setRefresh(refresh + 1);
                      } else {
                        message.error(res.msg || "获取明细失败");
                      }
                    } catch (error) {
                      message.error("查询明细失败");
                    } finally {
                      setLoading(false);
                    }
                  }
                } catch (error) {
                  message.error("查询明细失败");
                }
              }
            }
          },
          onChange: setEditableRowKeys,
          onCancel: async (rowKey) => {
            if (editingRecord) {
              setDataSource((prevDataSource) =>
                prevDataSource.map((item) =>
                  item.dtlSeqNo === editingRecord.dtlSeqNo ? { ...editingRecord } : item
                )
              );
            }
            // 退出编辑状态
            setEditableRowKeys([]);
            setIsEditing(false);
            setEditingRecord(null);
            return true;
          },
        }}
      />
    </Modal>
  );
};

export default DetailDpdtlCurtFormEdit;
