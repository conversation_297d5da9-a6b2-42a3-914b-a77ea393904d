import React, { useEffect, useState, useCallback } from "react";
import { Mo<PERSON>, message, <PERSON><PERSON>, Spin } from "antd";
import { 
  ProFormRadio, 
  EditableProTable 
} from "@ant-design/pro-components";
import type { ProColumns } from "@ant-design/pro-components";
import {
  tbDprgtStopPayUpdate,
  getTbDprgtStopPay,
  insertTbDprgtStopPay,
  tbDprgtStopPayDelete,
} from "../service";
import type { DprgtStopPay, PageResponse } from "../data";

/*
 * @Description: 止付登记簿编辑表单
 * @Author: taylor zhu
 * @Date: 2024-08-03 10:00:00
 */

interface PaginationType {
  current: number;
  pageSize: number;
  total: number;
}

export type DprgtStopPayFormProps = {
  onCancel: () => void;
  open: boolean;
  data: PageResponse<DprgtStopPay> | null;
};

const DEFAULT_PAGE_SIZE = 10;
const TABLE_SCROLL = { x: 2000 } as const;
const MODAL_BODY_STYLE = {
  maxHeight: "70vh",
  overflow: "auto",
  padding: "24px",
  borderRadius: "10px",
} as const;

const MODAL_STYLE = {
  borderRadius: "10px",
  overflow: "hidden",
} as const;

const INITIAL_PAGINATION: PaginationType = {
  current: 1,
  pageSize: DEFAULT_PAGE_SIZE,
  total: 0,
};

const DprgtStopPayFormEdit: React.FC<DprgtStopPayFormProps> = (props) => {
  const [dataSource, setDataSource] = useState<readonly DprgtStopPay[]>([]);
  const [editableKeys, setEditableKeys] = useState<React.Key[]>([]);
  const [isEditing, setIsEditing] = useState(false);
  const [editingRecord, setEditingRecord] = useState<DprgtStopPay | null>(null);
  const [pagination, setPagination] = useState<PaginationType>(INITIAL_PAGINATION);
  const [refresh, setRefresh] = useState(0);
  const [loading, setLoading] = useState(false);
  const [dprgtFrzInfo, setDprgtFrzInfo] = useState<{mainContrNo?: string, zoneVal?: string} | null>(null);
  const [mainContrNo, setMainContrNo] = useState<string | undefined>(undefined);
  const [zoneVal, setZoneVal] = useState<string | undefined>(undefined);

  useEffect(() => {
    if (props.open) {
      if (props.data?.loading !== undefined) {
        setLoading(props.data.loading);
      } else if (props.data) {
        // If we have data but no loading property, ensure loading is turned off
        setLoading(false);
      }
      
      // 当有data数据时，清空分页并设置数据
      if (props.data?.records) {
        setPagination({
          current: 1,
          pageSize: 10,
          total: props.data.total || props.data.records.length,
        });
        setDataSource(props.data.records);
        
        // 重要：设置账号信息，确保分页功能正常工作
        if (props.data.records.length > 0) {
          const firstRecord = props.data.records[0];
          
          // 从记录中提取账号信息
          setMainContrNo(firstRecord.mainContrNo);
          setZoneVal(firstRecord.zoneVal);
          
          // 同时更新accountInfo，作为备份
          setDprgtFrzInfo({
            mainContrNo: firstRecord.mainContrNo,
            zoneVal: firstRecord.zoneVal
          });
        }
      }
    } else {
      // 当弹窗关闭时，清空数据和分页
      setDataSource([]);
      setPagination({
        current: 1,
        pageSize: 10,
        total: 0,
      });
      // Also ensure loading is reset when modal closes
      setLoading(false);
    }
  }, [props.open, props.data]);

  const refreshData = useCallback(
    async (current: number = pagination.current, pageSize: number = pagination.pageSize) => {
      // 使用当前状态或dprgtFrzInfo作为后备
      const contrNo = mainContrNo || dprgtFrzInfo?.mainContrNo;
      const zoneValue = zoneVal || dprgtFrzInfo?.zoneVal;
      
      if (!contrNo || !zoneValue) {
        // 向用户显示错误消息，便于调试
        console.error('查询参数不完整:', { contrNo, zoneValue });
        message.error('分页查询失败：账号信息不完整，请重新打开弹窗');
        return;
      }
      
      setLoading(true);
      console.log('正在查询数据:', { contrNo, zoneValue, current, pageSize });
      
      try {
        const result = await getTbDprgtStopPay(
          contrNo,
          zoneValue,
          'DP_RGT_STOP_PAY_ALL_QUERYPAGES',
          {
            current,
            pageSize,
          },
        );
        
        if (result && result.data) {
          const detailData = JSON.parse(result.data);
          const newPagination = {
            current: current,
            pageSize: pageSize,
            total: detailData.total || detailData.records.length,
          };
          
          setPagination(newPagination);
          setDataSource(detailData.records || []);
          console.log('查询成功，数据条数:', detailData.records?.length);
        }
      } catch (error) {
        console.error('获取数据失败:', error);
        message.error('获取止付登记簿数据失败');
      } finally {
        // Ensure loading state is always cleared
        setLoading(false);
      }
    },
    [mainContrNo, zoneVal, dprgtFrzInfo, pagination.current, pagination.pageSize],
  );

  // 定义表格列
  const [position, setPosition] = useState<"top" | "bottom" | "hidden">("bottom");

  const columns: ProColumns<DprgtStopPay>[] = [
    {
      title: "止付号",
      dataIndex: "stopPayNo",
      width: 140,
      fixed: true,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "止付日期",
      dataIndex: "stopPayDate",
      width: 120,
      ellipsis: true,
      align: "center",
      editable: () => true,
      render: (_, entity) => entity.stopPayDate || "-",
      valueType: "date",
    },
    {
      title: "止付全局业务跟踪号",
      dataIndex: "stopPayGloTracNo",
      width: 260,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "止付子交易序号",
      dataIndex: "stopPaySubtxNo",
      width: 260,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "止付业务类型代码",
      dataIndex: "stopPayBusiTpCd",
      width: 200,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "select",
      valueEnum: {
        "01": { text: "01-账户止付" },
        "02": { text: "02-限额止付" },
        "03": { text: "03-合约止付" },
      },
    },
    {
      title: "执行类型代码",
      dataIndex: "execTpCd",
      width: 180,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "select",
      valueEnum: {
        "01": { text: "01-普通止付" },
        "02": { text: "02-存款证明止付" },
        "03": { text: "03-延迟转账止付" },
        "04": { text: "04-预授权止付" },
        "06": { text: "06-质押止付" },
        "07": { text: "07-急付款止付" },
        "08": { text: "08-入账汇款限额止付" },
        "10": { text: "10-结构性存款认购止付" },
        "11": { text: "11-差错止付" },
        "12": { text: "12-卡贷通消费退款止付" },
        "13": { text: "13-司法止付" },
        "14": { text: "14-反欺诈止付" },
        "15": { text: "15-信贷资金管控止付" },
        "16": {
          text: "16-不活跃账户止付（指客户账户三年无交易且账户余额小于等于10元时，判断为不活跃账户）",
        },
        "17": { text: "17-存单出让止付" },
        "99": { text: "99-其他" },
      },
    },
    {
      title: "止付关联信息",
      dataIndex: "stopPayRelInfo",
      width: 200,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "介质编号",
      dataIndex: "mediumNo",
      width: 200,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "主合约编号",
      dataIndex: "mainContrNo",
      width: 200,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "子账号序号",
      dataIndex: "saccnoSeqNo",
      width: 120,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "个人内部账号",
      dataIndex: "persInnerAccno",
      width: 200,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "客户编号",
      dataIndex: "custNo",
      width: 180,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "客户名称",
      dataIndex: "custNm",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "个人证件类型代码",
      dataIndex: "perCertTpCd",
      width: 180,
      ellipsis: true,
      align: "center",
      editable: () => false,
      valueType: "select",
      valueEnum: {
        "1010": { text: "1010-居民身份证" },
        "1011": { text: "1011-临时居民身份证" },
        "1020": { text: "1020-军人身份证件" },
        "1021": { text: "1021-士兵证" },
        "1022": { text: "1022-军官证" },
        "1023": { text: "1023-文职干部证" },
        "1024": { text: "1024-军官退休证" },
        "1025": { text: "1025-文职干部退休证" },
        "1030": { text: "1030-武警身份证件" },
        "1031": { text: "1031-武警士兵证" },
        "1032": { text: "1032-警官证" },
        "1033": { text: "1033-武警文职干部证" },
        "1034": { text: "1034-武警军官退休证" },
        "1035": { text: "1035-武警文职干部退休证" },
        "1040": { text: "1040-户口簿" },
        "1050": { text: "1050-中国护照" },
        "1051": { text: "1051-外国护照" },
        "1060": { text: "1060-学生证" },
        "1070": { text: "1070-港澳居民来往内地通行证" },
        "1071": { text: "1071-往来港澳通行证" },
        "1080": { text: "1080-台湾居民来往大陆通行证" },
        "1090": { text: "1090-执行公务证" },
        "1100": { text: "1100-机动车驾驶证" },
        "1110": { text: "1110-社会保障卡" },
        "1120": { text: "1120-外国人居留证" },
        "1121": { text: "1121-外国人永久居留证" },
        "1130": { text: "1130-旅行证件" },
        "1140": { text: "1140-香港居民身份证" },
        "1150": { text: "1150-澳门居民身份证" },
        "1160": { text: "1160-台湾居民身份证" },
        "1170": { text: "1170-边民证" },
        "1180": { text: "1180-港澳台居民居住证" },
        "1181": { text: "1181-港澳居民居住证" },
        "1182": { text: "1182-台湾居民居住证" },
        "1190": { text: "1190-外国身份证" },
        "1998": { text: "1998-其他（原98类）" },
        "1999": { text: "1999-其他证件（个人）" },
      },
    },
    {
      title: "个人证件号码",
      dataIndex: "personalCertNo",
      width: 200,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "类别标识代码",
      dataIndex: "categFlagCd",
      width: 180,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "select",
      valueEnum: {
        "01": { text: "01-活期" },
        "02": { text: "02-定期" },
      },
    },
    {
      title: "副卡卡号",
      dataIndex: "subCardCardNo",
      width: 220,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "副卡产品合约编号",
      dataIndex: "subCardContrNo",
      width: 220,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "约定执行金额",
      dataIndex: "appointExecAmt",
      width: 180,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "需止付金额",
      dataIndex: "needStpPayAmt",
      width: 180,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "币种代码",
      dataIndex: "currCode",
      width: 180,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "select",
      valueEnum: {
        "036": { text: "036-澳大利亚元" },
        "124": { text: "124-加元" },
        "344": { text: "344-香港元" },
        "392": { text: "392-日元" },
        "826": { text: "826-英镑" },
        "840": { text: "840-美元" },
        "978": { text: "978-欧元（EUR）" },
        "156": { text: "156-人民币元" },
      },
    },
    {
      title: "钞汇类别代码",
      dataIndex: "cashExgVatgCd",
      width: 200,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "select",
      valueEnum: {
        "2": { text: "2-钞" },
        "3": { text: "3-汇" },
      },
    },
    {
      title: "止付起始日期",
      dataIndex: "stopPayBgnDate",
      width: 160,
      ellipsis: true,
      align: "center",
      editable: () => true,
      render: (_, entity) => entity.stopPayBgnDate || "-",
      valueType: "date",
    },
    {
      title: "止付到期日期",
      dataIndex: "stopPayDueDate",
      width: 160,
      ellipsis: true,
      align: "center",
      editable: () => true,
      render: (_, entity) => entity.stopPayDueDate || "-",
      valueType: "date",
    },
    {
      title: "止付登记时间",
      dataIndex: "stopPayEnrollTime",
      width: 200,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "解止付登记时间",
      dataIndex: "relPayEnrollTime",
      width: 200,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "止付执行文号",
      dataIndex: "stopPayFileNo",
      width: 200,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "有权机关名称",
      dataIndex: "powInstName",
      width: 200,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "联系电话",
      dataIndex: "contTel",
      width: 200,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "续止付序号",
      dataIndex: "stpPaySeqNo",
      width: 120,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "生效状态代码",
      dataIndex: "effStaCd",
      width: 200,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "select",
      valueEnum: {
        "1": { text: "1-已生效" },
        "2": { text: "2-已失效" },
      },
    },
    {
      title: "止付原因",
      dataIndex: "stopPayReason",
      width: 200,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "止付渠道种类代码",
      dataIndex: "stopPayChnKindCd",
      width: 200,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "select",
      valueEnum: {
        "01": { text: "01-网点柜面" },
        "10": { text: "10-网上银行" },
        "12": { text: "12-个人网银" },
        "13": { text: "13-电视银行" },
        "14": { text: "14-电话银行" },
        "15": { text: "15-手机银行" },
        "16": { text: "16-企业网银" },
        "17": { text: "17-自助设备" },
        "18": { text: "18-POS" },
        "20": { text: "20-超级网银" },
        "21": { text: "21-大小额支付" },
        "22": { text: "22-银联前置" },
        "24": { text: "24-管理端" },
        "25": { text: "25-交易端" },
        "26": { text: "26-商易通" },
        "27": { text: "27-助农通" },
        "29": { text: "29-外部系统" },
        "30": { text: "30-系统自动" },
        "31": { text: "31-电子汇兑系统" },
        "32": { text: "32-理财规划终端" },
        "34": { text: "34-网汇通" },
        "35": { text: "35-同城支付" },
        "36": { text: "36-移动终端-TSM（可信服务管理）" },
        "37": { text: "37-移动终端-移动展业" },
        "38": { text: "38-直销银行" },
        "39": { text: "39-短信" },
        "40": { text: "40-专属APP" },
        "41": { text: "41-第三方线上渠道" },
        "42": { text: "42-国际支付前置" },
        "43": { text: "43-智能柜员机（ITM）" },
        "44": { text: "44-邮储经营" },
        "45": { text: "45-银银前置系统" },
        "46": { text: "46-U链供应链" },
        "47": { text: "47-油料保障结算系统" },
        "48": { text: "48-银企直联" },
        "91": { text: "91-微信银行" },
        "99": { text: "99-其他" },
      },
    },
    {
      title: "发起系统或组件编码",
      dataIndex: "startSysOrCmptNo",
      width: 160,
      ellipsis: true,
      align: "center",
    },
    {
      title: "开户机构号",
      dataIndex: "openAccInstNo",
      width: 120,
      ellipsis: true,
      align: "center",
    },
    {
      title: "止付机构号",
      dataIndex: "stopPayInstNo",
      width: 120,
      ellipsis: true,
      align: "center",
    },
    {
      title: "止付柜员号",
      dataIndex: "stopPayTellerNo",
      width: 120,
      ellipsis: true,
      align: "center",
    },
    {
      title: "止付授权柜员号",
      dataIndex: "stpPayTellerNo",
      width: 120,
      ellipsis: true,
      align: "center",
    },
    {
      title: "交易时间",
      dataIndex: "txTime",
      width: 140,
      ellipsis: false,
      align: "center",
    },
    {
      title: "最后交易日期",
      dataIndex: "lastTxDate",
      width: 120,
      ellipsis: true,
      align: "center",
      editable: () => true,
      render: (_, entity) => entity.lastTxDate || "-",
      valueType: "date",
    },
    {
      title: "数据处理状态代码",
      dataIndex: "dataDealStaCd",
      width: 180,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "select",
      valueEnum: {
        "0": { text: "0-待获取路由" },
        "1": { text: "1-待处理" },
        "2": { text: "2-处理中" },
        "3": { text: "3-待重试" },
        "4": { text: "4-成功" },
        "5": { text: "5-已取消" },
        "6": { text: "6-处理失败" },
        "7": { text: "7-冲正中" },
        "8": { text: "8-冲正成功" },
        "9": { text: "9-取消中" },
      },
    },
    {
      title: "记录状态代码",
      dataIndex: "recordStaCd",
      width: 180,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "select",
      valueEnum: {
        "0": { text: "0-无效" },
        "1": { text: "1-有效" },
      },
    },
    {
      title: "分片值",
      dataIndex: "zoneVal",
      width: 160,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "操作",
      valueType: "option",
      width: 150,
      // fixed: "right",
      align: "center",
      render: (text, record: DprgtStopPay, _, action) => {
        const isRowEditing = record.mainContrNo
          ? editableKeys.includes(record.mainContrNo)
          : false;
        return [
          isRowEditing ? (
            <>
              <Button
                key="confirm"
                type="link"
                onClick={async () => {
                  try {
                    if (record.mainContrNo) {
                      await action?.saveEditable?.(record.mainContrNo);
                      setIsEditing(false);
                    }
                  } catch (err) {
                    console.error("保存失败:", err);
                  }
                }}
              >
                确定
              </Button>
              <Button
                key="cancel"
                type="link"
                onClick={() => {
                  if (record.mainContrNo) {
                    action?.cancelEditable?.(record.mainContrNo);
                    setEditableKeys([]);
                    setIsEditing(false);
                  }
                }}
                style={{ marginLeft: 8 }}
              >
                取消
              </Button>
            </>
          ) : (
            <Button
              key="editable"
              type="link"
            //   disabled={isEditing}
              disabled={true}
              onClick={async () => {
                if (!record.mainContrNo) return;
                const originalRecord = { ...record };
                setEditingRecord(originalRecord);
                setIsEditing(true);
                await action?.startEditable?.(record.mainContrNo);
              }}
              style={{ padding: 0 }}
            >
              编辑
            </Button>
          ),
          <Button
            key="delete"
            type="link"
            // disabled={isEditing}
            disabled={true}
            onClick={async () => {
              if (!record.mainContrNo) return;
              Modal.confirm({
                title: "确认删除",
                content: "确定要删除这条记录吗？",
                onOk: async () => {
                  try {
                    const res = await tbDprgtStopPayDelete(record);
                    if (res.code === 200) {
                      message.success("删除成功");
                    } else {
                      message.error(res.msg || "删除失败");
                    }
                  } catch (error) {
                    message.error("删除失败");
                  } finally {
                    await refreshData();
                  }
                },
                okText: "确认",
                cancelText: "取消",
              });
            }}
            style={{ marginLeft: 8 }}
          >
            删除
          </Button>,
        ];
      },
    },
  ];

  const handleAdd = async (record: DprgtStopPay) => {
    try {
      // 设置必要的默认字段
      const newRecord = {
        ...record,
        mainContrNo: mainContrNo || dprgtFrzInfo?.mainContrNo,
        zoneVal: zoneVal || dprgtFrzInfo?.zoneVal,
        recordStaCd: "1", // 默认有效
      };
      
      setLoading(true);
      const res = await insertTbDprgtStopPay(newRecord);
      if (res.code === 200) {
        message.success('添加成功');
        refreshData();
      } else {
        message.error(res.msg || '添加失败');
      }
    } catch (error) {
      console.error('添加失败:', error);
      message.error('添加失败');
    } finally {
      // 添加完成后重置状态
      setLoading(false);
      setRefresh(prev => prev + 1);
      setEditableKeys([]);
      setIsEditing(false);
    }
  };

  return (
    <Modal
      width={1200}
      title="查询止付登记簿信息"
      open={props.open}
      destroyOnClose
      onCancel={props.onCancel}
      footer={null}
      bodyStyle={MODAL_BODY_STYLE}
      style={MODAL_STYLE}
    >
      <Spin spinning={loading} tip="加载数据中..." size="large">
        <div style={{ marginBottom: 16 }}>
          <p>
            数据状态: {loading ? (
              <span>
                <Spin size="small" style={{ marginRight: 8 }} />
                加载中...
              </span>
            ) : (
              `已加载 ${dataSource.length} 条记录, 总共 ${pagination.total} 条`
            )}
            {dprgtFrzInfo && (
              <span style={{ marginLeft: 8 }}>
                | 主合约编号: {dprgtFrzInfo.mainContrNo}
              </span>
            )}
          </p>
        </div>
        
        <EditableProTable<DprgtStopPay>
          key={refresh}
          rowKey={(record) => record.mainContrNo || ""}
          headerTitle="止付登记簿信息"
          scroll={TABLE_SCROLL}
          loading={loading}
          toolBarRender={() => [
            <ProFormRadio.Group
              key="positionRender"
              fieldProps={{
                value: position,
                onChange: (e: any) => setPosition(e.target.value),
              }}
              options={[
                { label: "添加到顶部", value: "top" },
                { label: "添加到底部", value: "bottom" },
                { label: "隐藏", value: "hidden" },
              ]}
            />,
          ]}
          recordCreatorProps={{
            position: position === 'hidden' ? 'bottom' : position,
            record: (index, dataSource) => {
              // 直接创建新记录，不检查是否有正在编辑的行
              return {
                mainContrNo: mainContrNo || dprgtFrzInfo?.mainContrNo,
                servSysTpCd: "01", // 默认为星级服务体系
                addoffTpCd: "01", // 默认为单笔人工加办
                zoneVal: zoneVal || dprgtFrzInfo?.zoneVal,
                effStaCd: "1", // 默认有效
                recordStaCd: "1", // 默认有效
                lastTxDate: new Date().toISOString().split('T')[0], // 最后交易日期
              } as DprgtStopPay;
            },
            creatorButtonText: '添加一行数据',
            newRecordType: 'dataSource',
          }}
          columns={columns}
          request={async (params, sorter, filter) => {
            // 直接返回当前数据源，不进行额外的API调用
            return {
              data: dataSource as DprgtStopPay[],
              total: pagination.total,
              success: true,
            };
          }}
          value={dataSource}
          onChange={setDataSource}
          editable={{
            type: 'multiple', // 支持同时编辑多行
            editableKeys,
            onSave: async (rowKey, data, row) => {
              try {
                // 如果是新添加的记录则调用新增接口
                if (String(rowKey).startsWith('NEW_')) {
                  await handleAdd(data);
                } else {
                  // 否则调用更新接口
                  setLoading(true);
                  const res = await tbDprgtStopPayUpdate(data);
                  if (res.code === 200) {
                    message.success('更新成功');
                    refreshData();
                  } else {
                    message.error(res.msg || '更新失败');
                  }
                }
              } catch (error) {
                console.error('保存失败:', error);
                message.error('保存失败');
              } finally {
                // 保存后从编辑状态中移除当前行
                setEditableKeys(keys => keys.filter(key => key !== rowKey));
                if (editableKeys.length <= 1) {
                  setIsEditing(false);
                }
                setLoading(false);
              }
            },
            onChange: setEditableKeys,
            onCancel: async (key) => {
              // 取消编辑时只清除当前行的编辑状态
              setEditableKeys(keys => keys.filter(k => k !== key));
              if (editableKeys.length <= 1) {
                setIsEditing(false);
              }
              return true;
            },
            actionRender: (row, config, dom) => [dom.save, dom.cancel],
          }}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条`,
            onChange: async (page, pageSize) => {
              console.log('分页改变:', { page, pageSize, currentPage: pagination.current, isEditing });
              
              if (isEditing) {
                message.warn('请先完成编辑操作再切换页面');
                return;
              }
              
              // 仅当页码或页大小变化时才刷新数据
              if (page !== pagination.current || pageSize !== pagination.pageSize) {
                // 更新分页状态
                setPagination({
                  ...pagination,
                  current: page,
                  pageSize: pageSize,
                });
                
                // 直接调用刷新数据，不做额外判断
                await refreshData(page, pageSize);
              }
            },
          }}
        />
      </Spin>
    </Modal>
  );
};

export default DprgtStopPayFormEdit;
