import React, { useEffect, useState, useCallback } from "react";
import { Modal, message, Button } from "antd";
import { 
  ProFormRadio, 
  EditableProTable 
} from "@ant-design/pro-components";
import type { ProColumns } from "@ant-design/pro-components";
import {
  getDprgtContrAsst,
  insertDprgtContrAsst,
  updateDprgtContrAsst,
  deleteDprgtContrAsst,
} from "../service";
import type { DprgtContrAsst, PageResponse } from "../data";

/*
 * @Description: 合约辅助表编辑表单
 * @Author: taylor zhu
 * @Date: 2025-04-12 10:00:00
 */

interface PaginationType {
  current: number;
  pageSize: number;
  total: number;
}

export type DprgtContrAsstFormProps = {
  onCancel: () => void;
  open: boolean;
  data: PageResponse<DprgtContrAsst> | null;
};

const DEFAULT_PAGE_SIZE = 10;
const TABLE_SCROLL = { x: 3000 } as const;
const MODAL_BODY_STYLE = {
  maxHeight: "70vh",
  overflow: "auto",
  padding: "24px",
  borderRadius: "10px",
} as const;

const MODAL_STYLE = {
  borderRadius: "10px",
  overflow: "hidden",
} as const;

const INITIAL_PAGINATION: PaginationType = {
  current: 1,
  pageSize: DEFAULT_PAGE_SIZE,
  total: 0,
};

const DprgtContrAsstFormEdit: React.FC<DprgtContrAsstFormProps> = (props) => {
  const [dataSource, setDataSource] = useState<readonly DprgtContrAsst[]>([]);
  const [editableKeys, setEditableKeys] = useState<React.Key[]>([]);
  const [isEditing, setIsEditing] = useState(false);
  const [editingRecord, setEditingRecord] = useState<DprgtContrAsst | null>(null);
  const [pagination, setPagination] = useState<PaginationType>(INITIAL_PAGINATION);
  const [refresh, setRefresh] = useState(0);
  const [loading, setLoading] = useState(false);
  const [currentContract, setCurrentContract] = useState<{prodtContractNo?: string, zoneVal?: string} | null>(null);
  const [prodtContractNo, setProdtContractNo] = useState<string | undefined>(undefined);
  const [zoneVal, setZoneVal] = useState<string | undefined>(undefined);

  useEffect(() => {
    if (props.open && props.data?.records && props.data.records.length > 0) {
      const records = props.data.records;
      setDataSource(records);
      setPagination({
        ...pagination,
        total: props.data?.total || records.length,
      });
      
      // 找到第一条有效记录，获取prodtContractNo和zoneVal
      const firstValidRecord = records.find(record => record.prodtContractNo && record.zoneVal);
      
      if (firstValidRecord?.prodtContractNo && firstValidRecord?.zoneVal) {
        // 只更新状态，不再重复请求数据
        setCurrentContract({
          prodtContractNo: firstValidRecord.prodtContractNo,
          zoneVal: firstValidRecord.zoneVal
        });
        setProdtContractNo(firstValidRecord.prodtContractNo);
        setZoneVal(firstValidRecord.zoneVal);
      }
    } else {
      setDataSource([]);
      setPagination(INITIAL_PAGINATION);
      setCurrentContract(null);
      setProdtContractNo(undefined);
      setZoneVal(undefined);
    }
  }, [props.open, props.data]);

  const refreshData = useCallback(
    async (current: number = pagination.current, pageSize: number = pagination.pageSize) => {
      // 使用当前状态或currentContract作为后备
      const contractNo = prodtContractNo || currentContract?.prodtContractNo;
      const zoneValue = zoneVal || currentContract?.zoneVal;
      
      if (!contractNo || !zoneValue) {
        // 只在控制台记录错误，不向用户显示消息
        console.warn('查询参数不完整');
        return;
      }
      
      setLoading(true);
      
      try {
        const result = await getDprgtContrAsst(
          contractNo,
          zoneValue,
          'DPRGT_CONTR_ASST_ALL_QUERYPAGES',
          {
            current,
            pageSize,
          },
        );
        
        if (result && result.data) {
          const detailData = JSON.parse(result.data);
          const newPagination = {
            current: current,
            pageSize: pageSize,
            total: detailData.total || detailData.records.length,
          };
          
          setPagination(newPagination);
          setDataSource(detailData.records || []);
        }
      } catch (error) {
        console.error('获取数据失败:', error);
        message.error('获取合约辅助表数据失败');
      } finally {
        setLoading(false);
      }
    },
    [prodtContractNo, zoneVal, currentContract],
  );

  // 定义表格列
  const [position, setPosition] = useState<"top" | "bottom" | "hidden">("bottom");

  const columns: ProColumns<DprgtContrAsst>[] = [
    {
      title: "产品合约编号",
      dataIndex: "prodtContractNo",
      width: 220,
      fixed: true,
      // ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "合约特殊签约类",
      dataIndex: "contrSpecSignTpCd",
      width: 220,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "select",
      valueEnum: {
        "01": { text: "01-商易惠签约" },
        "03": { text: "03-存单转让签约" },
        "04": { text: "04-互金主题合约签约" },
        "05": { text: "05-邮惠商家签约" },
        "07": { text: "07-成长计划签约" },
      },
    },
    {
      title: "可售产品编码",
      dataIndex: "vendibiProdtNo",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "可售产品版本号",
      dataIndex: "vendibiProdtVerNo",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "主题编码",
      dataIndex: "topicNo",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "请求来源",
      dataIndex: "reqSource",
      width: 120,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "首季活期积数",
      dataIndex: "fqtrCurtAccu",
      width: 120,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "digit",
    },
    {
      title: "签约日期",
      dataIndex: "signContDate",
      width: 120,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "date",
      render: (_, entity) => entity.signContDate || "-",
    },
    {
      title: "签约机构号",
      dataIndex: "signContInstNo",
      width: 120,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "签约柜员号",
      dataIndex: "signTellerNo",
      width: 120,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "授权柜员号",
      dataIndex: "authTellerNo",
      width: 120,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "渠道种类代码",
      dataIndex: "chnlKindCode",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "select",
      valueEnum: {
        "01": { text: "01-网点柜面" },
        "10": { text: "10-网上银行" },
        "12": { text: "12-个人网银" },
        "13": { text: "13-电视银行" },
        "14": { text: "14-电话银行" },
        "15": { text: "15-手机银行" },
        "16": { text: "16-企业网银" },
        "17": { text: "17-自助设备" },
        "18": { text: "18-POS" },
        "20": { text: "20-超级网银" },
        "21": { text: "21-大小额支付" },
        "22": { text: "22-银联前置" },
        "24": { text: "24-管理端" },
        "25": { text: "25-交易端" },
        "26": { text: "26-商易通" },
        "27": { text: "27-助农通" },
        "29": { text: "29-外部系统" },
        "30": { text: "30-系统自动" },
        "31": { text: "31-电子汇兑系统" },
        "32": { text: "32-理财规划终端" },
        "34": { text: "34-网汇通" },
        "35": { text: "35-同城支付" },
        "36": { text: "36-移动终端-TSM（可信服务管理）" },
        "37": { text: "37-移动终端-移动展业" },
        "38": { text: "38-直销银行" },
        "39": { text: "39-短信" },
        "40": { text: "40-专属APP" },
        "41": { text: "41-第三方线上渠道" },
        "43": { text: "43-智能柜员机（ITM）" },
        "42": { text: "42-国际支付前置" },
        "44": { text: "44-邮储经营" },
        "45": { text: "45-银银前置系统" },
        "46": { text: "46-U链供应链" },
        "47": { text: "47-油料保障结算系统" },
        "48": { text: "48-银企直联" },
        "91": { text: "91-微信银行" },
        "99": { text: "99-其他" },
      },
    },
    {
      title: "发起系统或组件",
      dataIndex: "startSysOrCmptNo",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "到期日期",
      dataIndex: "dueDate",
      width: 120,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "date",
      render: (_, entity) => entity.dueDate || "-",
    },
    {
      title: "新产品生效日期",
      dataIndex: "newProdtEffDtAccti",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "date",
      render: (_, entity) => entity.newProdtEffDtAccti || "-",
    },
    {
      title: "新可售产品编码",
      dataIndex: "newVendibiProdtNo",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "新可售产品版本",
      dataIndex: "newVendibiProdtVerNo",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "合约特殊签约状态代码",
      dataIndex: "contrSpecSignStaCd",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "select",
      valueEnum: {
        "1001": { "text": "1001-商易惠生效" },
        "1002": { "text": "1002-商易惠失效" },
        "2001": { "text": "2001-礼仪存单未转让" },
        "2002": { "text": "2002-礼仪存单已转让待受让" },
        "2003": { "text": "2003-礼仪存单已转让成功" },
        "2004": { "text": "2004-礼仪存单已转让撤销" },
        "2005": { "text": "2005-礼仪存单超时自动退回" },
        "2006": { "text": "2006-礼仪存单已拒收" },
        "2007": { "text": "2007-礼仪存单已扣划" },
        "2008": { "text": "2008-礼仪存单未转让已兑付" },
        "2009": { "text": "2009-礼仪存单已受让未兑付" },
        "2010": { "text": "2010-礼仪存单已受让已兑付" },
        "3001": { "text": "3001-大额存单未转让" },
        "3002": { "text": "3002-大额存单已转让待受让" },
        "3003": { "text": "3003-大额存单已转让成功" },
        "3004": { "text": "3004-大额存单已扣划" },
        "3005": { "text": "3005-大额存单未转让已兑付" },
        "3006": { "text": "3006-大额存单已受让未兑付" },
        "3007": { "text": "3007-大额存单已受让已兑付" },
        "5001": { "text": "5001-主题卡生效" },
        "5002": { "text": "5002-主题卡失效" },
        "6001": { "text": "6001-邮惠商家签约生效" },
        "6002": { "text": "6002-邮惠商家签约失效" },
        "7001": { "text": "7001-成长计划手动新开" },
        "7002": { "text": "7002-成长计划手动销户" },
        "7003": { "text": "7003-成长计划手动部提销户" },
        "7004": { "text": "7004-成长计划手动部提新开" },
        "7005": { "text": "7005-成长计划日终新开" },
        "7006": { "text": "7006-成长计划日终销户" },
        "7007": { "text": "7007-成长计划手动移出" },
        "7008": { "text": "7008-成长计划手动移入" },

      },
    },
    {
      title: "最后交易日期",
      dataIndex: "lastTxDate",
      width: 120,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "date",
      render: (_, entity) => entity.lastTxDate || "-",
    },
    {
      title: "记录状态代码",
      dataIndex: "recordStaCd",
      width: 100,
      ellipsis: true,
      align: "center",
      editable: () => false,
      valueType: "select",
      valueEnum: {
        "0": { text: "0-无效" },
        "1": { text: "1-有效" },
      },
    },
    {
      title: "分片值",
      dataIndex: "zoneVal",
      width: 160,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "创建时间戳",
      dataIndex: "createStamp",
      width: 180,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "最后修改时间戳",
      dataIndex: "lastModStamp",
      width: 180,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "操作",
      valueType: "option",
      width: 150,
      // fixed: "right",
      align: "center",
      render: (text, record: DprgtContrAsst, _, action) => {
        const isRowEditing = record.prodtContractNo
          ? editableKeys.includes(record.prodtContractNo)
          : false;
        return [
          isRowEditing ? (
            <>
              <Button
                key="confirm"
                type="link"
                onClick={async () => {
                  try {
                    if (record.prodtContractNo) {
                      await action?.saveEditable?.(record.prodtContractNo);
                      setIsEditing(false);
                    }
                  } catch (err) {
                    console.error("保存失败:", err);
                  }
                }}
              >
                确定
              </Button>
              <Button
                key="cancel"
                type="link"
                onClick={() => {
                  if (record.prodtContractNo) {
                    action?.cancelEditable?.(record.prodtContractNo);
                    setEditableKeys([]);
                    setIsEditing(false);
                  }
                }}
                style={{ marginLeft: 8 }}
              >
                取消
              </Button>
            </>
          ) : (
            <Button
              key="editable"
              type="link"
              disabled={true}
              onClick={async () => {
                if (!record.prodtContractNo) return;
                const originalRecord = { ...record };
                setEditingRecord(originalRecord);
                setIsEditing(true);
                await action?.startEditable?.(record.prodtContractNo);
              }}
              style={{ padding: 0 }}
            >
              编辑
            </Button>
          ),
          <Button
            key="delete"
            type="link"
            disabled={true}
            onClick={async () => {
              if (!record.prodtContractNo) return;
              Modal.confirm({
                title: "确认删除",
                content: "确定要删除这条记录吗？",
                onOk: async () => {
                  try {
                    const res = await deleteDprgtContrAsst(record);
                    if (res.code === 200) {
                      message.success("删除成功");
                    } else {
                      message.error(res.msg || "删除失败");
                    }
                  } catch (error) {
                    message.error("删除失败");
                  } finally {
                    await refreshData();
                  }
                },
                okText: "确认",
                cancelText: "取消",
              });
            }}
            style={{ marginLeft: 8 }}
          >
            删除
          </Button>,
        ];
      },
    },
  ];

  const handleAdd = async (record: DprgtContrAsst) => {
    try {
      // 设置必要的默认字段
      const newRecord = {
        ...record,
        prodtContractNo: prodtContractNo || currentContract?.prodtContractNo,
        zoneVal: zoneVal || currentContract?.zoneVal,
        recordStaCd: "1", // 默认有效
      };
      
      setLoading(true);
      const res = await insertDprgtContrAsst(newRecord);
      if (res.code === 200) {
        message.success('添加成功');
        refreshData();
      } else {
        message.error(res.msg || '添加失败');
      }
    } catch (error) {
      console.error('添加失败:', error);
      message.error('添加失败');
    } finally {
      // 添加完成后重置状态
      setLoading(false);
      setRefresh(prev => prev + 1);
      setEditableKeys([]);
      setIsEditing(false);
    }
  };

  return (
    <Modal
      width={1200}
      title="查询合约辅助表信息"
      open={props.open}
      destroyOnClose
      onCancel={props.onCancel}
      footer={null}
      bodyStyle={MODAL_BODY_STYLE}
      style={MODAL_STYLE}
    >
      <div style={{ marginBottom: 16 }}>
        <p>
          数据状态: {loading ? '加载中' : `已加载 ${dataSource.length} 条记录, 总共 ${pagination.total} 条`}
          {currentContract && (
            <span style={{ marginLeft: 8 }}>
              | 合约编号: {currentContract.prodtContractNo}
            </span>
          )}
        </p>
      </div>
      
      <EditableProTable<DprgtContrAsst>
        key={refresh}
        rowKey={(record) => record.prodtContractNo || ""}
        headerTitle="合约辅助表信息"
        scroll={TABLE_SCROLL}
        loading={loading}
        toolBarRender={() => [
          <ProFormRadio.Group
            key="positionRender"
            fieldProps={{
              value: position,
              onChange: (e: any) => setPosition(e.target.value),
            }}
            options={[
              { label: "添加到顶部", value: "top" },
              { label: "添加到底部", value: "bottom" },
              { label: "隐藏", value: "hidden" },
            ]}
          />,
        ]}
        recordCreatorProps={{
          position: position === 'hidden' ? 'bottom' : position,
          record: (index, dataSource) => {
            // 直接创建新记录，不检查是否有正在编辑的行
            return {
              prodtContractNo: prodtContractNo || currentContract?.prodtContractNo,
              contrSpecSignTpCd: "01", // 默认商易惠签约
              zoneVal: zoneVal || currentContract?.zoneVal,
              signContDate: new Date().toISOString().split('T')[0], // 当前日期
              recordStaCd: "1", // 默认有效
            } as DprgtContrAsst;
          },
          creatorButtonText: '添加一行数据',
          newRecordType: 'dataSource',
        }}
        columns={columns}
        request={async (params, sorter, filter) => {
          // 直接返回当前数据源，不进行额外的API调用
          return {
            data: dataSource as DprgtContrAsst[],
            total: pagination.total,
            success: true,
          };
        }}
        value={dataSource}
        onChange={setDataSource}
        editable={{
          type: 'multiple', // 支持同时编辑多行
          editableKeys,
          onSave: async (rowKey, data, row) => {
            try {
              // 如果是新添加的记录则调用新增接口
              if (String(rowKey).startsWith('NEW_')) {
                await handleAdd(data);
              } else {
                // 否则调用更新接口
                setLoading(true);
                const res = await updateDprgtContrAsst(data);
                if (res.code === 200) {
                  message.success('更新成功');
                  refreshData();
                } else {
                  message.error(res.msg || '更新失败');
                }
              }
            } catch (error) {
              console.error('保存失败:', error);
              message.error('保存失败');
            } finally {
              // 保存后从编辑状态中移除当前行
              setEditableKeys(keys => keys.filter(key => key !== rowKey));
              if (editableKeys.length <= 1) {
                setIsEditing(false);
              }
              setLoading(false);
            }
          },
          onChange: setEditableKeys,
          onCancel: async (key) => {
            // 取消编辑时只清除当前行的编辑状态
            setEditableKeys(keys => keys.filter(k => k !== key));
            if (editableKeys.length <= 1) {
              setIsEditing(false);
            }
            return true;
          },
          actionRender: (row, config, dom) => [dom.save, dom.cancel],
        }}
        pagination={{
          current: pagination.current,
          pageSize: pagination.pageSize,
          total: pagination.total,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 条`,
          onChange: async (page, pageSize) => {
            if (isEditing) {
              return;
            }
            
            // 仅当页码或页大小变化时才刷新数据
            if (page !== pagination.current || pageSize !== pagination.pageSize) {
              // 更新分页状态
              setPagination({
                ...pagination,
                current: page,
                pageSize: pageSize,
              });
              
              // 不再做额外条件判断，直接刷新数据
              await refreshData(page, pageSize);
            }
          },
        }}
      />
    </Modal>
  );
};

export default DprgtContrAsstFormEdit; 