import React, { useEffect, useState, useCallback } from "react";
import { Modal, message, Button } from "antd";
import type { ProColumns } from "@ant-design/pro-components";
import { EditableProTable, ProFormRadio } from "@ant-design/pro-components";
import {
  getConsum,
  insertConsum,
  updateConsum,
  deleteConsum,
} from "../service";
import type { Consum, PageResponse } from "../data";

/*
 * @Description: 消费登记簿编辑表单
 * @Author: 你的名字
 * @Date: 2025-01-17 10:00:00
 */

interface PaginationType {
  current: number;
  pageSize: number;
  total: number;
}

export type ConsumFormProps = {
  onCancel: () => void;
  open: boolean;
  data: PageResponse<Consum> | null;
};

const DEFAULT_PAGE_SIZE = 10;
const TABLE_SCROLL = { x: 3000 } as const;

const MODAL_BODY_STYLE = {
  maxHeight: "70vh",
  overflow: "auto",
  padding: "24px",
  borderRadius: "10px",
} as const;

const MODAL_STYLE = {
  borderRadius: "10px",
  overflow: "hidden",
} as const;

const INITIAL_PAGINATION: PaginationType = {
  current: 1,
  pageSize: DEFAULT_PAGE_SIZE,
  total: 0,
};

const ConsumFormEdit: React.FC<ConsumFormProps> = (props) => {
  const [dataSource, setDataSource] = useState<readonly Consum[]>([]);
  const [editableKeys, setEditableKeys] = useState<React.Key[]>([]);
  const [isEditing, setIsEditing] = useState(false);
  const [editingRecord, setEditingRecord] = useState<Consum | null>(null);
  const [pagination, setPagination] = useState<PaginationType>(INITIAL_PAGINATION);
  const [refresh, setRefresh] = useState(0);
  const [loading, setLoading] = useState(false);
  const [currentContract, setCurrentContract] = useState<{mainContrNo?: string, zoneVal?: string} | null>(null);
  const [mainContrNo, setMainContrNo] = useState<string | undefined>(undefined);
  const [zoneVal, setZoneVal] = useState<string | undefined>(undefined);

  useEffect(() => {
    if (props.open && props.data?.records && props.data.records.length > 0) {
      const records = props.data.records;
      setDataSource(records);
      setPagination({
        ...pagination,
        total: props.data?.total || records.length,
      });
      
      // 找到第一条有效记录，获取mainContrNo和zoneVal
      const firstValidRecord = records.find(record => record.mainContrNo && record.zoneVal);
      
      if (firstValidRecord?.mainContrNo && firstValidRecord?.zoneVal) {
        // 直接更新状态，不使用setTimeout
        setCurrentContract({
          mainContrNo: firstValidRecord.mainContrNo,
          zoneVal: firstValidRecord.zoneVal
        });
        setMainContrNo(firstValidRecord.mainContrNo);
        setZoneVal(firstValidRecord.zoneVal);
        
        // 使用更新后的值直接调用刷新
        getConsum(
          firstValidRecord.mainContrNo,
          firstValidRecord.zoneVal,
          'CONSUM_ALL_QUERYPAGES',
          {
            current: 1,
            pageSize: pagination.pageSize,
          },
        ).then(result => {
          if (result && result.records) {
            const newPagination = {
              current: 1,
              pageSize: pagination.pageSize,
              total: result.total || result.records.length,
            };
            
            setPagination(newPagination);
            setDataSource(result.records);
          }
        }).catch(error => {
          console.error('初始化数据失败:', error);
          // 这里不显示错误消息，因为数据已经显示出来了
        }).finally(() => {
          setLoading(false);
        });
      }
    } else {
      setDataSource([]);
      setPagination(INITIAL_PAGINATION);
      setCurrentContract(null);
      setMainContrNo(undefined);
      setZoneVal(undefined);
    }
  }, [props.open, props.data]);

  const refreshData = useCallback(
    async (current: number = pagination.current, pageSize: number = pagination.pageSize) => {
      // 使用当前状态或currentContract作为后备
      const contractNo = mainContrNo || currentContract?.mainContrNo;
      const zoneValue = zoneVal || currentContract?.zoneVal;
      
      if (!contractNo || !zoneValue) {
        // 只在控制台记录错误，不向用户显示消息
        console.warn('查询参数不完整');
        return;
      }
      
      setLoading(true);
      
      try {
        const result = await getConsum(
          contractNo,
          zoneValue,
          'CONSUM_ALL_QUERYPAGES',
          {
            current,
            pageSize,
          },
        );
        
        if (result && result.records) {
          const newPagination = {
            current: current,
            pageSize: pageSize,
            total: result.total || result.records.length,
          };
          
          setPagination(newPagination);
          setDataSource(result.records);
        }
      } catch (error) {
        console.error('获取数据失败:', error);
        message.error('获取数据失败');
      } finally {
        setLoading(false);
      }
    },
    [mainContrNo, zoneVal, currentContract],
  );

  const [position, setPosition] = useState<"top" | "bottom" | "hidden">("bottom");

  const columns: ProColumns<Consum>[] = [
    {
      title: "交易日期",
      dataIndex: "txDate",
      width: 120,
      fixed: true,
      ellipsis: true,
      align: "center",
      editable: () => false,
      valueType: "date",
      render: (_, entity) => entity.txDate || "-",
    },
    {
      title: "全局业务跟踪号",
      dataIndex: "globalBusiTrackNo",
      width: 260,
      // fixed: true,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "子交易序号",
      dataIndex: "subtxNo",
      width: 260,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "主合约编号",
      dataIndex: "mainContrNo",
      width: 220,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "子账号序号",
      dataIndex: "saccnoSeqNo",
      width: 100,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "个人内部账号",
      dataIndex: "persInnerAccno",
      width: 220,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "交易金额",
      dataIndex: "txAmt",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => false,
      render: (_, entity) => Number(entity.txAmt).toFixed(2),
    },
    {
      title: "账户变动金额",
      dataIndex: "mainAccChgAmt",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => false,
      render: (_, entity) => Number(entity.mainAccChgAmt).toFixed(2),
    },
    {
      title: "信贷类合约账户变动金额",
      dataIndex: "creditClsContrAcChgAmt",
      width: 220,
      ellipsis: true,
      align: "center",
      editable: () => false,
      render: (_, entity) => Number(entity.creditClsContrAcChgAmt).toFixed(2),
    },
    {
      title: "已退货金额",
      dataIndex: "canSaleRetAmt",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => false,
      render: (_, entity) => Number(entity.canSaleRetAmt).toFixed(2),
    },
    {
      title: "账户已退货金额",
      dataIndex: "accReturnedAmt",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => false,
      render: (_, entity) => Number(entity.accReturnedAmt).toFixed(2),
    },
    {
      title: "信贷类合约账户已退货金额",
      dataIndex: "creditClsContrReturnedAmt",
      width: 220,
      ellipsis: true,
      align: "center",
      editable: () => false,
      render: (_, entity) => Number(entity.creditClsContrReturnedAmt).toFixed(2),
    },
    {
      title: "发起系统/组件编码",
      dataIndex: "startSysOrCmptNo",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "交易渠道种类代码",
      dataIndex: "txChnlKindCd",
      width: 160,
      ellipsis: true,
      align: "center",
      editable: () => false,
      valueType: "select",
      valueEnum: {
        "01": { text: "01-网点柜面" },
        "10": { text: "10-网上银行" },
        "12": { text: "12-个人网银" },
        "13": { text: "13-电视银行" },
        "14": { text: "14-电话银行" },
        "15": { text: "15-手机银行" },
        "16": { text: "16-企业网银" },
        "17": { text: "17-自助设备" },
        "18": { text: "18-POS" },
        "20": { text: "20-超级网银" },
        "21": { text: "21-大小额支付" },
        "22": { text: "22-银联前置" },
        "24": { text: "24-管理端" },
        "25": { text: "25-交易端" },
        "26": { text: "26-商易通" },
        "27": { text: "27-助农通" },
        "29": { text: "29-外部系统" },
        "30": { text: "30-系统自动" },
        "31": { text: "31-电子汇兑系统" },
        "32": { text: "32-理财规划终端" },
        "34": { text: "34-网汇通" },
        "35": { text: "35-同城支付" },
        "36": { text: "36-移动终端-TSM（可信服务管理）" },
        "37": { text: "37-移动终端-移动展业" },
        "38": { text: "38-直销银行" },
        "39": { text: "39-短信" },
        "40": { text: "40-专属APP" },
        "41": { text: "41-第三方线上渠道" },
        "43": { text: "43-智能柜员机（ITM）" },
        "42": { text: "42-国际支付前置" },
        "44": { text: "44-邮储经营" },
        "45": { text: "45-银银前置系统" },
        "46": { text: "46-U链供应链" },
        "47": { text: "47-油料保障结算系统" },
        "48": { text: "48-银企直联" },
        "91": { text: "91-微信银行" },
        "99": { text: "99-其他" },
      },
    },
    {
      title: "交易机构号",
      dataIndex: "txInstNo",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "交易柜员号",
      dataIndex: "txTellerNo",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "授权柜员号",
      dataIndex: "authTellerNo",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "交易时间",
      dataIndex: "txTime",
      width: 160,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "摘要名称",
      dataIndex: "summName",
      width: 100,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "商户编码",
      dataIndex: "merNo",
      width: 220,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "商户描述",
      dataIndex: "merDesc",
      width: 220,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "交易变更状态代码",
      dataIndex: "txChgStaCd",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => false,
      valueType: "select",
      valueEnum: {
        "0": { text: "0-正常" },
        "6": { text: "6-被恢复" },
        "7": { text: "7-被冲正" },
        "9": { text: "9-被撤销" },
      },
    },
    {
      title: "变更日期",
      dataIndex: "chgDate",
      width: 120,
      ellipsis: true,
      align: "center",
      editable: () => false,
      valueType: "date",
      render: (_, entity) => entity.chgDate || "-",
    },
    {
      title: "变更全局业务跟踪号",
      dataIndex: "chgGloTracNo",
      width: 220,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "变更子交易序号",
      dataIndex: "chgSubtxNo",
      width: 220,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "主机日期",
      dataIndex: "hostDt",
      width: 120,
      ellipsis: true,
      align: "center",
      editable: () => false,
      valueType: "date",
      render: (_, entity) => entity.hostDt || "-",
    },
    {
      title: "最后交易日期",
      dataIndex: "lastTxDate",
      width: 120,
      ellipsis: true,
      align: "center",
      editable: () => false,
      valueType: "date",
      render: (_, entity) => entity.lastTxDate || "-",
    },
    {
      title: "记录状态代码",
      dataIndex: "recordStaCd",
      width: 100,
      ellipsis: true,
      align: "center",
      editable: () => false,
      valueType: "select",
      valueEnum: {
        "0": { text: "0-无效" },
        "1": { text: "1-有效" },
      },
    },
    {
      title: "分片值",
      dataIndex: "zoneVal",
      width: 160,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "创建时间戳",
      dataIndex: "createStamp",
      width: 180,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "最后修改时间戳",
      dataIndex: "lastModStamp",
      width: 180,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "操作",
      valueType: "option",
      width: 150,
      // fixed: "right",
      align: "center",
      render: (text, record: Consum, _, action) => {
        const isRowEditing = record.globalBusiTrackNo
          ? editableKeys.includes(record.globalBusiTrackNo)
          : false;
        return [
          isRowEditing ? (
            <>
              <Button
                key="confirm"
                type="link"
                onClick={async () => {
                  try {
                    if (record.globalBusiTrackNo) {
                      await action?.saveEditable?.(record.globalBusiTrackNo);
                      setIsEditing(false);
                    }
                  } catch (err) {
                    console.error("保存失败:", err);
                  }
                }}
              >
                确定
              </Button>
              <Button
                key="cancel"
                type="link"
                onClick={() => {
                  if (record.globalBusiTrackNo) {
                    action?.cancelEditable?.(record.globalBusiTrackNo);
                    setEditableKeys([]);
                    setIsEditing(false);
                  }
                }}
                style={{ marginLeft: 8 }}
              >
                取消
              </Button>
            </>
          ) : (
            <Button
              key="editable"
              type="link"
              disabled={true}
              onClick={async () => {
                if (!record.globalBusiTrackNo) return;
                const originalRecord = { ...record };
                setEditingRecord(originalRecord);
                setIsEditing(true);
                await action?.startEditable?.(record.globalBusiTrackNo);
              }}
              style={{ padding: 0 }}
            >
              编辑
            </Button>
          ),
          <Button
            key="delete"
            type="link"
            disabled={true}
            onClick={async () => {
              if (!record.globalBusiTrackNo) return;
              Modal.confirm({
                title: "确认删除",
                content: "确定要删除这条记录吗？",
                onOk: async () => {
                  try {
                    const res = await deleteConsum(record);
                    if (res.code === 200) {
                      message.success("删除成功");
                    } else {
                      message.error(res.msg || "删除失败");
                    }
                  } catch (error) {
                    message.error("删除失败");
                  } finally {
                    await refreshData();
                  }
                },
                okText: "确认",
                cancelText: "取消",
              });
            }}
            style={{ marginLeft: 8 }}
          >
            删除
          </Button>,
        ];
      },
    },
  ];

  const handleAdd = async (record: Consum) => {
    try {
      // 设置必要的默认字段
      const newRecord = {
        ...record,
        mainContrNo: mainContrNo || currentContract?.mainContrNo,
        zoneVal: zoneVal || currentContract?.zoneVal,
        recordStaCd: "1", // 默认有效
      };
      
      setLoading(true);
      const res = await insertConsum(newRecord);
      if (res.code === 200) {
        message.success('添加成功');
        refreshData();
      } else {
        message.error(res.msg || '添加失败');
      }
    } catch (error) {
      console.error('添加失败:', error);
      message.error('添加失败');
    } finally {
      // 添加完成后重置状态
      setLoading(false);
      setRefresh(prev => prev + 1);
      setEditableKeys([]);
      setIsEditing(false);
    }
  };

  return (
    <Modal
      width={1200}
      title="查询消费登记簿信息"
      open={props.open}
      destroyOnClose
      onCancel={props.onCancel}
      footer={null}
      bodyStyle={MODAL_BODY_STYLE}
      style={MODAL_STYLE}
    >
      <div style={{ marginBottom: 16 }}>
        <p>
          数据状态: {loading ? '加载中' : `已加载 ${dataSource.length} 条记录, 总共 ${pagination.total} 条`}
          {currentContract && (
            <span style={{ marginLeft: 8 }}>
              | 合约编号: {currentContract.mainContrNo}
            </span>
          )}
        </p>
      </div>
      
      <EditableProTable<Consum>
        key={refresh}
        rowKey={(record) => record.globalBusiTrackNo || ""}
        headerTitle="消费登记簿信息"
        scroll={TABLE_SCROLL}
        loading={loading}
        toolBarRender={() => [
          <ProFormRadio.Group
            key="positionRender"
            fieldProps={{
              value: position,
              onChange: (e: any) => setPosition(e.target.value),
            }}
            options={[
              { label: "添加到顶部", value: "top" },
              { label: "添加到底部", value: "bottom" },
              { label: "隐藏", value: "hidden" },
            ]}
          />,
        ]}
        recordCreatorProps={{
          position: position === 'hidden' ? 'bottom' : position,
          record: (index, dataSource) => {
            // 直接创建新记录，不检查是否有正在编辑的行
            return {
              globalBusiTrackNo: `NEW_${Date.now()}`,
              txDate: new Date().toISOString().split('T')[0], // 当前日期
              mainContrNo: mainContrNo || currentContract?.mainContrNo,
              zoneVal: zoneVal || currentContract?.zoneVal,
            } as Consum;
          },
          creatorButtonText: '添加一行数据',
          newRecordType: 'dataSource',
        }}
        columns={columns}
        request={async (params, sorter, filter) => {
          if (isEditing) {
            return {
              data: dataSource as Consum[],
              total: pagination.total,
              success: true,
            };
          }
          
          // 这里不要调用refreshData，因为表格刷新时会自动调用，在onChange中处理分页
          return {
            data: dataSource as Consum[],
            total: pagination.total,
            success: true,
          };
        }}
        value={dataSource}
        onChange={setDataSource}
        editable={{
          type: 'multiple', // 改为multiple支持同时编辑多行
          editableKeys,
          onSave: async (rowKey, data, row) => {
            try {
              // 如果是新添加的记录则调用新增接口
              if (String(rowKey).startsWith('NEW_')) {
                await handleAdd(data);
              } else {
                // 否则调用更新接口
                setLoading(true);
                const res = await updateConsum(data);
                if (res.code === 200) {
                  message.success('更新成功');
                  refreshData();
                } else {
                  message.error(res.msg || '更新失败');
                }
              }
            } catch (error) {
              console.error('保存失败:', error);
              message.error('保存失败');
            } finally {
              // 保存后从编辑状态中移除当前行
              setEditableKeys(keys => keys.filter(key => key !== rowKey));
              if (editableKeys.length <= 1) {
                setIsEditing(false);
              }
              setLoading(false);
            }
          },
          onChange: setEditableKeys,
          onCancel: async (key) => {
            // 取消编辑时只清除当前行的编辑状态
            setEditableKeys(keys => keys.filter(k => k !== key));
            if (editableKeys.length <= 1) {
              setIsEditing(false);
            }
            return true;
          },
          actionRender: (row, config, dom) => [dom.save, dom.cancel],
        }}
        pagination={{
          current: pagination.current,
          pageSize: pagination.pageSize,
          total: pagination.total,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 条`,
          onChange: async (page, pageSize) => {
            if (isEditing) {
              return;
            }
            
            // 更新分页状态
            setPagination({
              ...pagination,
              current: page,
              pageSize: pageSize,
            });
            
            // 手动触发数据刷新
            await refreshData(page, pageSize);
          },
        }}
      />
    </Modal>
  );
};

export default ConsumFormEdit;
