import React, { useState, useEffect } from 'react';
import { Modal, Form, Radio, Space, message } from 'antd';
import type { TbDpmstCurtCont } from '../data';
import { dpmstCurtContUpdate, queryAll } from '../service';
import FlagModal, { flagMeaningsConfig } from './FlagModal';


interface MContrFlagCdEditProps {
  visible: boolean;
  onCancel: () => void;
  record: any;
  mediumNo: string; // 添加介质号属性
  prodtContractNo: string; // 添加合约号属性
  onSuccess: () => void;
}
// 主合约控制标志码
const CurtMContrFlagCdEdit: React.FC<MContrFlagCdEditProps> = ({
  visible,
  onCancel,
  record,
  mediumNo,
  prodtContractNo, // 接收合约号
  onSuccess,
}) => {
  const [loading, setLoading] = useState(false);

  const [editedValues, setEditedValues] = useState<Record<number, string>>({});
  const mContrFlagCdConfig = flagMeaningsConfig.mContrFlagCd;

  useEffect(() => {
    if (visible && record) {
      const initialValues: Record<number, string> = {};

      if (record.mContrFlagCd) {
        const bits = record.mContrFlagCd.split('');
        mContrFlagCdConfig.forEach(item => {
          initialValues[item.position] = bits[item.position - 1] || '0';
        });
      } else {
        mContrFlagCdConfig.forEach(item => {
          initialValues[item.position] = '0';
        });
      }

      setEditedValues(initialValues);
    }
  }, [visible, record]);

  const handleValueChange = (position: number, value: string) => {
    setEditedValues(prev => ({
      ...prev,
      [position]: value
    }));
  };

  const handleOk = async () => {
    setLoading(true);
    try {
      // 生成32位标志码
      const flagArray = new Array(32).fill('0');
      Object.entries(editedValues).forEach(([position, value]) => {
        const index = parseInt(position) - 1;
        flagArray[index] = value;
      });

      const mContrFlagCd = flagArray.join('');
      const params: TbDpmstCurtCont = {
        prodtContractNo: prodtContractNo, // 使用传入的合约号
        zoneVal: record.zoneVal,
        mContrFlagCd,
      };

      const res = await dpmstCurtContUpdate(params);
      if (res.code === 200) {
        message.success('更新成功');
        // 调用查询方法重新获取数据
        await queryAll(mediumNo);
        onCancel();
      } else {
        message.error(res.msg || '更新失败');
      }
    } catch (error) {
      message.error('更新失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  return (
    <FlagModal
      visible={visible}
      flag={record && typeof record.mContrFlagCd === 'string' ? record.mContrFlagCd : undefined}
      dataIndex="mContrFlagCd"
      onCancel={onCancel}
      onSubmit={async (values) => {
        console.log('CurtMContrFlagCdEdit - 准备提交的数据:', {
          prodtContractNo,
          zoneVal: record?.zoneVal,
          mContrFlagCd: values.flag
        });
        setLoading(true);
        try {
          const params: TbDpmstCurtCont = {
            prodtContractNo: prodtContractNo,
            zoneVal: record?.zoneVal,
            mContrFlagCd: values.flag,
          };

          const res = await dpmstCurtContUpdate(params);
          if (res.code === 200) {
            message.success('更新成功');
            await queryAll(mediumNo);
            onSuccess();
          } else {
            message.error(res.msg || '更新失败');
          }
        } catch (error) {
          message.error('更新失败，请重试');
        } finally {
          setLoading(false);
        }
      }}
    />
  );
};

export default CurtMContrFlagCdEdit;
