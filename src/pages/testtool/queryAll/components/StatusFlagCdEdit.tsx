import React, { useState, useEffect } from 'react';
import { Modal, Form, Radio, message } from 'antd';
import type { TbDpmstMedium } from '../data';
import { dpmstMediumUpdate, queryAll } from '../service';
import { flagMeaningsConfig } from './FlagModal';

interface StatusFlagCdEditProps {
  visible: boolean;
  onCancel: () => void;
  record: any;
  mediumNo: string;
  onSuccess: () => void;
}

const StatusFlagCdEdit: React.FC<StatusFlagCdEditProps> = ({
  visible,
  onCancel,
  record,
  mediumNo,
  onSuccess,
}) => {
  const [loading, setLoading] = useState(false);
  const [editedValues, setEditedValues] = useState<Record<number, string>>({});
  const statusFlagConfig = flagMeaningsConfig.statusFlagCd;

  useEffect(() => {
    if (visible && record) {
      const initialValues: Record<number, string> = {};
      if (record.statusFlagCd) {
        const bits = record.statusFlagCd.split('');
        statusFlagConfig.forEach(item => {
          initialValues[item.position] = bits[item.position - 1] || '0';
        });
      } else {
        statusFlagConfig.forEach(item => {
          initialValues[item.position] = '0';
        });
      }
      setEditedValues(initialValues);
    }
  }, [visible, record]);

  const handleValueChange = (position: number, value: string) => {
    setEditedValues(prev => ({
      ...prev,
      [position]: value
    }));
  };

  const handleOk = async () => {
    setLoading(true);
    try {
      const flagArray = new Array(32).fill('0');
      Object.entries(editedValues).forEach(([position, value]) => {
        const index = parseInt(position) - 1;
        if (index >= 0 && index < 8) {
          flagArray[index] = value;
        }
      });

      const statusFlagCd = flagArray.join('');
      const params: TbDpmstMedium = {
        mediumNo: mediumNo,
        zoneVal: record.zoneVal,
        statusFlagCd,
      };

      const res = await dpmstMediumUpdate(params);
      if (res.code === 200) {
        message.success('更新成功');
        await queryAll(mediumNo);
        onCancel();
      } else {
        message.error(res.msg || '更新失败');
      }
    } catch (error) {
      message.error('更新失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title="编辑介质状态标志码"
      open={visible}
      onOk={handleOk}
      onCancel={onCancel}
      confirmLoading={loading}
      width={2500}
      bodyStyle={{ 
        padding: '24px 32px',
        maxHeight: '80vh',
        overflowY: 'auto'
      }}
    >
      <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
        {statusFlagConfig.map(item => (
          <div 
            key={item.key} 
            style={{
              display: 'flex',
              alignItems: 'center',
              width: '100%',
            }}
          >
            <div style={{ 
              width: '200px', 
              flexShrink: 0,
              paddingRight: '24px',
              fontWeight: 500
            }}>
              {item.title}
            </div>
            <Radio.Group
              value={editedValues[item.position]}
              onChange={(e) => handleValueChange(item.position, e.target.value)}
              style={{ 
                flex: 1,
                display: 'flex',
              }}
            >
              <div style={{ 
                display: 'flex', 
                gap: '48px',
                flex: 1,
              }}>
                {item.options.map(option => (
                  <Radio 
                    key={option.value} 
                    value={option.value}
                    style={{ 
                      marginRight: 0,
                      minWidth: 'fit-content',
                      whiteSpace: 'nowrap',
                      padding: '4px 0'
                    }}
                  >
                    {option.label}
                  </Radio>
                ))}
              </div>
            </Radio.Group>
          </div>
        ))}
      </div>
    </Modal>
  );
};

export default StatusFlagCdEdit;
