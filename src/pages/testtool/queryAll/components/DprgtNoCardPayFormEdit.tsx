import React, { useEffect, useState } from "react";
import { Modal, message, But<PERSON>, Toolt<PERSON> } from "antd";
import {
  dprgtNoCardPayUpdate,
  getDprgtNoCardPay,
  insertDprgtNoCardPay,
  dprgtNoCardPayDelete,
} from "../service";
import type { DprgtNoCardPay, PageResponse } from "../data";
import type { ProColumns } from "@ant-design/pro-components";
import { EditableProTable, ProFormRadio } from "@ant-design/pro-components";

/*
 * @Description: 无卡支付协议登记编辑表单
 * @Author: 自动生成示例
 * @Date: 2025-01-17 10:00:00
 */

interface PaginationType {
  current: number;
  pageSize: number;
  total: number;
}

export type DprgtNoCardPayFormProps = {
  onCancel: () => void;
  open: boolean;
  data: PageResponse<DprgtNoCardPay> | null;
};

const DEFAULT_PAGE_SIZE = 10;
const TABLE_SCROLL = { x: 1300 } as const;
const TABLE_OPTIONS = {
  density: true,
  fullScreen: true,
  setting: true,
} as const;
const TABLE_PAGINATION = {
  showSizeChanger: true,
  showQuickJumper: true,
} as const;

const MODAL_BODY_STYLE = {
  maxHeight: "70vh",
  overflow: "auto",
  padding: "24px",
  borderRadius: "10px",
} as const;

const MODAL_STYLE = {
  borderRadius: "10px",
  overflow: "hidden",
} as const;

const INITIAL_PAGINATION: PaginationType = {
  current: 1,
  pageSize: DEFAULT_PAGE_SIZE,
  total: 0,
};

const DprgtNoCardPayFormEdit: React.FC<DprgtNoCardPayFormProps> = (props) => {
  const [dataSource, setDataSource] = useState<readonly DprgtNoCardPay[]>([]);
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);
  const [isEditing, setIsEditing] = useState(false);
  const [editingRecord, setEditingRecord] = useState<DprgtNoCardPay | null>(null);
  const [pagination, setPagination] = useState<PaginationType>(INITIAL_PAGINATION);
  const [refresh, setRefresh] = useState(0);
  const [loading, setLoading] = useState(false);

  // 刷新数据方法
  const refreshData = async () => {
    if (!props.data?.records?.[0]) return;

    const noCardPayRecord = props.data.records[0];
    if (!noCardPayRecord?.mediumNo || !noCardPayRecord?.zoneVal) return;

    setLoading(true);
    try {
      const res = await getDprgtNoCardPay(
        noCardPayRecord.mediumNo,
        noCardPayRecord.zoneVal,
        "dprgtNoCardPayPageInfo",
        {
          current: pagination.current,
          pageSize: pagination.pageSize,
        }
      );

      if (res.code === 200 && res.data) {
        const detailData = JSON.parse(res.data);
        setDataSource(detailData.records || []);
        setPagination({
          current: detailData.current || INITIAL_PAGINATION.current,
          pageSize: detailData.size || INITIAL_PAGINATION.pageSize,
          total: detailData.total || INITIAL_PAGINATION.total,
        });
        setRefresh(refresh + 1);
      } else {
        message.error(res.msg || "获取无卡支付协议失败");
      }
    } catch (error) {
      message.error("查询无卡支付协议失败");
    } finally {
      setLoading(false);
    }
  };

  // 初始化数据
  useEffect(() => {
    let isMounted = true;

    const initData = async () => {
      if (props.open && props.data) {
        const { records, current, size, total } = props.data;
        if (isMounted) {
          setDataSource(records || []);
          setPagination({
            current: current || INITIAL_PAGINATION.current,
            pageSize: size || INITIAL_PAGINATION.pageSize,
            total: total || INITIAL_PAGINATION.total,
          });
        }
      } else if (isMounted) {
        setDataSource([]);
        setPagination(INITIAL_PAGINATION);
      }
    };

    initData();
    return () => {
      isMounted = false;
    };
  }, [props.open, props.data]);

  // 表格上新增记录的位置选择
  const [position, setPosition] = useState<"top" | "bottom" | "hidden">("bottom");

  // 定义表格列
  const columns: ProColumns<DprgtNoCardPay>[] = [
    {
      title: "无卡支付协议编号",
      dataIndex: "noCardPayAgrNo",
      width: 220,
      fixed: true,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "介质编号",
      dataIndex: "mediumNo",
      width: 180,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "产品合约编号",
      dataIndex: "prodtContractNo",
      width: 220,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "无卡支付类型代码",
      dataIndex: "noCardPayTpCd",
      width: 160,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "select",
      valueEnum: {
        "01": { text: "01-小额临时支付" },
        "02": { text: "02-无卡自助消费业务" },
      },
    },
    {
      title: "客户名称",
      dataIndex: "custNm",
      width: 200,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "个人证件类型代码",
      dataIndex: "perCertTpCd",
      width: 160,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "select",
      valueEnum: {
        '1010': { text: '1010-居民身份证' },
        '1011': { text: '1011-临时居民身份证' },
        '1020': { text: '1020-军人身份证件' },
        '1021': { text: '1021-士兵证' },
        '1022': { text: '1022-军官证' },
        '1023': { text: '1023-文职干部证' },
        '1024': { text: '1024-军官退休证' },
        '1025': { text: '1025-文职干部退休证' },
        '1030': { text: '1030-武警身份证件' },
        '1031': { text: '1031-武警士兵证' },
        '1032': { text: '1032-警官证' },
        '1033': { text: '1033-武警文职干部证' },
        '1034': { text: '1034-武警军官退休证' },
        '1035': { text: '1035-武警文职干部退休证' },
        '1040': { text: '1040-户口簿' },
        '1050': { text: '1050-中国护照' },
        '1051': { text: '1051-外国护照' },
        '1060': { text: '1060-学生证' },
        '1070': { text: '1070-港澳居民来往内地通行证' },
        '1071': { text: '1071-往来港澳通行证' },
        '1080': { text: '1080-台湾居民来往大陆通行证' },
        '1090': { text: '1090-执行公务证' },
        '1100': { text: '1100-机动车驾驶证' },
        '1110': { text: '1110-社会保障卡' },
        '1120': { text: '1120-外国人居留证' },
        '1121': { text: '1121-外国人永久居留证' },
        '1130': { text: '1130-旅行证件' },
        '1140': { text: '1140-香港居民身份证' },
        '1150': { text: '1150-澳门居民身份证' },
        '1160': { text: '1160-台湾居民身份证' },
        '1170': { text: '1170-边民证' },
        '1180': { text: '1180-港澳台居民居住证' },
        '1181': { text: '1181-港澳居民居住证' },
        '1182': { text: '1182-台湾居民居住证' },
        '1190': { text: '1190-外国身份证' },
        '1998': { text: '1998-其他（原98类）' },
        '1999': { text: '1999-其他证件（个人）' },
      },
    },
    {
      title: "个人证件号码",
      dataIndex: "personalCertNo",
      width: 180,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "支付手机号",
      dataIndex: "payMobileNo",
      width: 160,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "签约日期",
      dataIndex: "signContDate",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "date",
      render: (_, entity) => entity.signContDate || "-",
    },
    {
      title: "到期日期",
      dataIndex: "dueDate",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "date",
      render: (_, entity) => entity.dueDate || "-",
    },
    {
      title: "签约状态代码",
      dataIndex: "signContStatus",
      width: 120,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "select",
      valueEnum: {
        "0": { text: "0-签约" },
        "1": { text: "1-解约" },
      },
    },
    {
      title: "签约渠道种类代码",
      dataIndex: "signChnKindCd",
      width: 180,
      ellipsis: true,
      align: "center",
      editable: () => true,
      // 这里可根据实际需求扩展更多选项
      valueType: "select",
      valueEnum: {
        "01": { text: "01-网点柜面" },
        "10": { text: "10-网上银行" },
        "12": { text: "12-个人网银" },
        "13": { text: "13-电视银行" },
        "14": { text: "14-电话银行" },
        "15": { text: "15-手机银行" },
        "16": { text: "16-企业网银" },
        "17": { text: "17-自助设备" },
        "18": { text: "18-POS" },
        "20": { text: "20-超级网银" },
        "21": { text: "21-大小额支付" },
        "22": { text: "22-银联前置" },
        "24": { text: "24-管理端" },
        "25": { text: "25-交易端" },
        "26": { text: "26-商易通" },
        "27": { text: "27-助农通" },
        "29": { text: "29-外部系统" },
        "30": { text: "30-系统自动" },
        "31": { text: "31-电子汇兑系统" },
        "32": { text: "32-理财规划终端" },
        "34": { text: "34-网汇通" },
        "35": { text: "35-同城支付" },
        "36": { text: "36-移动终端-TSM（可信服务管理）" },
        "37": { text: "37-移动终端-移动展业" },
        "38": { text: "38-直销银行" },
        "39": { text: "39-短信" },
        "40": { text: "40-专属APP" },
        "41": { text: "41-第三方线上渠道" },
        "43": { text: "43-智能柜员机（ITM）" },
        "42": { text: "42-国际支付前置" },
        "44": { text: "44-邮储经营" },
        "45": { text: "45-银银前置系统" },
        "46": { text: "46-U链供应链" },
        "47": { text: "47-油料保障结算系统" },
        "48": { text: "48-银企直联" },
        "91": { text: "91-微信银行" },
        "99": { text: "99-其他" },
      },
    },
    {
      title: "签约机构号",
      dataIndex: "signContInstNo",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "签约柜员号",
      dataIndex: "signTellerNo",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "授权柜员号",
      dataIndex: "authTellerNo",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "解约日期",
      dataIndex: "cacContDt",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "date",
      render: (_, entity) => entity.cacContDt || "-",
    },
    {
      title: "解约渠道种类代码",
      dataIndex: "cacContChnKindCd",
      width: 160,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "select",
      valueEnum: {
        "01": { text: "01-网点柜面" },
        "10": { text: "10-网上银行" },
        "12": { text: "12-个人网银" },
        "13": { text: "13-电视银行" },
        "14": { text: "14-电话银行" },
        "15": { text: "15-手机银行" },
        "16": { text: "16-企业网银" },
        "17": { text: "17-自助设备" },
        "18": { text: "18-POS" },
        "20": { text: "20-超级网银" },
        "21": { text: "21-大小额支付" },
        "22": { text: "22-银联前置" },
        "24": { text: "24-管理端" },
        "25": { text: "25-交易端" },
        "26": { text: "26-商易通" },
        "27": { text: "27-助农通" },
        "29": { text: "29-外部系统" },
        "30": { text: "30-系统自动" },
        "31": { text: "31-电子汇兑系统" },
        "32": { text: "32-理财规划终端" },
        "34": { text: "34-网汇通" },
        "35": { text: "35-同城支付" },
        "36": { text: "36-移动终端-TSM（可信服务管理）" },
        "37": { text: "37-移动终端-移动展业" },
        "38": { text: "38-直销银行" },
        "39": { text: "39-短信" },
        "40": { text: "40-专属APP" },
        "41": { text: "41-第三方线上渠道" },
        "43": { text: "43-智能柜员机（ITM）" },
        "42": { text: "42-国际支付前置" },
        "44": { text: "44-邮储经营" },
        "45": { text: "45-银银前置系统" },
        "46": { text: "46-U链供应链" },
        "47": { text: "47-油料保障结算系统" },
        "48": { text: "48-银企直联" },
        "91": { text: "91-微信银行" },
        "99": { text: "99-其他" },
      },
    },
    {
      title: "解约机构号",
      dataIndex: "cacContInstNo",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "解约柜员号",
      dataIndex: "cacContTellerNo",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "解约授权柜员号",
      dataIndex: "cacContAuthTellerNo",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "最后交易日期",
      dataIndex: "lastTxDate",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "date",
      render: (_, entity) => entity.lastTxDate || "-",
    },
    {
      title: "最后修改时间戳",
      dataIndex: "lastModStamp",
      width: 180,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "分片值",
      dataIndex: "zoneVal",
      width: 160,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "操作",
      valueType: "option",
      width: 150,
      fixed: "right",
      align: "center",
      render: (text, record: DprgtNoCardPay, _, action) => {
        const isRecordEditing = record.noCardPayAgrNo
          ? editableKeys.includes(record.noCardPayAgrNo)
          : false;
        return [
          isRecordEditing ? (
            <>
              <a
                key="confirm"
                onClick={async () => {
                  try {
                    if (record.noCardPayAgrNo) {
                      await action?.saveEditable?.(record.noCardPayAgrNo);
                      setIsEditing(false);
                    }
                  } catch (err) {
                    console.error("保存失败:", err);
                  }
                }}
              >
                确定
              </a>
              <a
                key="cancel"
                onClick={() => {
                  if (record.noCardPayAgrNo) {
                    action?.cancelEditable?.(record.noCardPayAgrNo);
                    setEditableRowKeys([]);
                    setIsEditing(false);
                  }
                }}
                style={{ marginLeft: 8 }}
              >
                取消
              </a>
            </>
          ) : (
            <Button
              key="editable"
              type="link"
              disabled={true}
              onClick={async () => {
                if (!record.noCardPayAgrNo) return;
                // 保存原始数据，再进入编辑状态
                const originalRecord = { ...record };
                setEditingRecord(originalRecord);
                setIsEditing(true);
                await action?.startEditable?.(record.noCardPayAgrNo);
              }}
              style={{ padding: 0 }}
            >
              编辑
            </Button>
          ),
          <Button
            key="delete"
            type="link"
            disabled={true}
            onClick={async () => {
              if (!record.noCardPayAgrNo) return;
              Modal.confirm({
                title: "确认删除",
                content: "确定要删除这条记录吗？",
                onOk: async () => {
                  try {
                    const res = await dprgtNoCardPayDelete(record);
                    if (res.code === 200) {
                      message.success("删除成功");
                    } else {
                      message.error(res.msg || "删除失败");
                    }
                  } catch (error) {
                    message.error("删除失败");
                  } finally {
                    await refreshData();
                  }
                },
                okText: "确认",
                cancelText: "取消",
              });
            }}
            style={{ marginLeft: 8 }}
          >
            删除
          </Button>,
        ];
      },
    },
  ];

  return (
    <Modal
      width={1200}
      title="查询无卡支付协议登记信息"
      open={props.open}
      destroyOnClose
      onCancel={props.onCancel}
      footer={null}
      bodyStyle={MODAL_BODY_STYLE}
      style={MODAL_STYLE}
    >
      <EditableProTable<DprgtNoCardPay>
        key={refresh}
        rowKey={(record) => record.noCardPayAgrNo || ""}
        headerTitle="无卡支付协议登记信息"
        scroll={TABLE_SCROLL}
        recordCreatorProps={
          position === "hidden"
            ? false
            : {
                position: position,
                creatorButtonText: "新增一行无卡支付协议信息",
                onClick: async () => {
                  // 获取当前页数据
                  const startIndex =
                    (pagination.current - 1) * pagination.pageSize;
                  const endIndex = startIndex + pagination.pageSize;

                  const currentPage = pagination.current;
                  const pageSize = pagination.pageSize;
                  const currentPageDataCount = Math.min(
                    pageSize,
                    pagination.total - (currentPage - 1) * pageSize
                  );

                  if (
                    currentPage !== 1 &&
                    currentPageDataCount >= pagination.pageSize
                  ) {
                    Modal.error({
                      title: "提示",
                      content:
                        "当前页面数据已满，请切换至第一页或者是未满的页面进行新增",
                    });
                    return false;
                  }

                  // 创建新记录（注意：各字段默认值可根据需要调整）
                  const newRecord = {
                    noCardPayAgrNo: "0000000000", // 此处应由后台生成唯一ID
                    mediumNo: props.data?.records?.[0]?.mediumNo ?? "",
                    prodtContractNo: "",
                    noCardPayTpCd: "",
                    custNm: "",
                    perCertTpCd: "",
                    personalCertNo: "",
                    payMobileNo: "",
                    signContDate: new Date().toISOString().split("T")[0],
                    dueDate: new Date().toISOString().split("T")[0],
                    signContStatus: "0",
                    signChnKindCd: "",
                    signContInstNo: "",
                    signTellerNo: "",
                    authTellerNo: "",
                    cacContDt: new Date().toISOString().split("T")[0],
                    cacContChnKindCd: "",
                    cacContInstNo: "",
                    cacContTellerNo: "",
                    cacContAuthTellerNo: "",
                    lastTxDate: new Date().toISOString().split("T")[0],
                    zone_val: props.data?.records?.[0]?.zoneVal ?? "",
                    lastModStamp: "",
                  };

                  const newDataSource = [...dataSource];
                  newDataSource.splice(endIndex, 0, newRecord);

                  setDataSource(newDataSource);
                  setIsEditing(true);
                  setEditableRowKeys(["0000000000"]);
                },
                record: () => {
                  const today = new Date();
                  const formattedDate = today.toISOString().split("T")[0];
                  return {
                    noCardPayAgrNo: "NEW",
                    mediumNo: props.data?.records?.[0]?.mediumNo ?? "",
                    prodtContractNo: "",
                    noCardPayTpCd: "",
                    custNm: "",
                    perCertTpCd: "",
                    personalCertNo: "",
                    payMobileNo: "",
                    signContDate: formattedDate,
                    dueDate: formattedDate,
                    signContStatus: "0",
                    signChnKindCd: "",
                    signContInstNo: "",
                    signTellerNo: "",
                    authTellerNo: "",
                    cacContDt: formattedDate,
                    cacContChnKindCd: "",
                    cacContInstNo: "",
                    cacContTellerNo: "",
                    cacContAuthTellerNo: "",
                    lastTxDate: formattedDate,
                    zoneVal: props.data?.records?.[0]?.zoneVal ?? "",
                    lastModStamp: "",
                  } as DprgtNoCardPay;
                },
              }
        }
        loading={loading}
        toolBarRender={() => [
          <ProFormRadio.Group
            key="render"
            fieldProps={{
              value: position,
              onChange: (e: any) => setPosition(e.target.value),
            }}
            options={[
              { label: "添加到顶部", value: "top" },
              { label: "添加到底部", value: "bottom" },
              { label: "隐藏", value: "hidden" },
            ]}
          />,
        ]}
        columns={columns}
        request={async (params, sorter, filter) => {
          if (isEditing) {
            return {
              data: dataSource,
              total: pagination.total,
              success: true,
            };
          }

          setLoading(true);
          try {
            if (!props.data || !props.data.records || props.data.records.length === 0) {
              return { data: [], total: 0, success: true };
            }
            const noCardPayRecord = props.data.records[0];
            if (!noCardPayRecord?.mediumNo || !noCardPayRecord?.zoneVal) {
              return { data: [], total: 0, success: true };
            }
            const { current, pageSize } = params;
            const res = await getDprgtNoCardPay(
              noCardPayRecord.mediumNo,
              noCardPayRecord.zoneVal,
              "dprgtNoCardPayPageInfo",
              {
                current: current || 1,
                pageSize: pageSize || DEFAULT_PAGE_SIZE,
              }
            );
            if (res.code === 200 && res.data) {
              const detailData = JSON.parse(res.data);
              setDataSource(detailData.records || []);
              setPagination({
                current: detailData.current || 1,
                pageSize: detailData.size || DEFAULT_PAGE_SIZE,
                total: detailData.total || 0,
              });
              return {
                data: detailData.records || [],
                total: detailData.total || 0,
                success: true,
              };
            } else {
              message.error(res.msg || "获取无卡支付协议登记信息失败");
              return { data: [], total: 0, success: false };
            }
          } catch (error) {
            message.error("查询无卡支付协议登记信息失败");
            return { data: [], total: 0, success: false };
          } finally {
            setLoading(false);
          }
        }}
        dataSource={dataSource}
        pagination={{
          ...TABLE_PAGINATION,
          current: pagination.current,
          pageSize: pagination.pageSize,
          total: pagination.total,
          showTotal: (total) => `总共 ${total} 条`,
          onChange: (current, pageSize) => {
            setPagination((prev) => ({ ...prev, current, pageSize }));
          },
        }}
        editable={{
          type: "multiple",
          editableKeys,
          onSave: async (rowKey, data) => {
            console.log("保存数据:", rowKey, data);
            if (data.noCardPayAgrNo === "NEW") {
              // 新增数据
              try {
                const newData = {
                  ...data,
                  noCardPayAgrNo: "NEW", // 后台应生成唯一ID
                };
                const res = await insertDprgtNoCardPay(newData);
                if (res.code === 200) {
                  message.success("新增成功");
                  setEditableRowKeys([]);
                  setIsEditing(false);
                  setEditingRecord(null);

                  // 刷新总数及跳转最后一页
                  const noCardPayRecord = props.data?.records?.[0];
                  if (noCardPayRecord?.mediumNo && noCardPayRecord?.zoneVal) {
                    const res = await getDprgtNoCardPay(
                      noCardPayRecord.mediumNo,
                      noCardPayRecord.zoneVal,
                      "dprgtNoCardPayPageInfo",
                      {
                        current: 1,
                        pageSize: 1,
                      }
                    );
                    if (res.code === 200 && res.data) {
                      const detailData = JSON.parse(res.data);
                      const total = detailData.total || 0;
                      const lastPage = Math.ceil(total / pagination.pageSize);
                      setPagination((prev) => ({
                        ...prev,
                        current: lastPage,
                        total: total,
                      }));
                    }
                  }
                  await refreshData();
                  return true;
                } else {
                  message.error(res.msg || "新增失败");
                  await refreshData();
                  return false;
                }
              } catch (error) {
                message.error("新增失败");
                return false;
              }
            }

            if (!editingRecord || !rowKey) {
              message.error("编辑记录不存在");
              return false;
            }

            let originalData: DprgtNoCardPay | null = null;
            try {
              // 只上传可编辑字段
              const updateData = {
                mediumNo: data.mediumNo,
                prodtContractNo: data.prodtContractNo,
                noCardPayTpCd: data.noCardPayTpCd,
                custNm: data.custNm,
                perCertTpCd: data.perCertTpCd,
                personalCertNo: data.personalCertNo,
                payMobileNo: data.payMobileNo,
                signContDate: data.signContDate,
                dueDate: data.dueDate,
                signContStatus: data.signContStatus,
                signChnKindCd: data.signChnKindCd,
                signContInstNo: data.signContInstNo,
                signTellerNo: data.signTellerNo,
                authTellerNo: data.authTellerNo,
                cacContDt: data.cacContDt,
                cacContChnKindCd: data.cacContChnKindCd,
                cacContInstNo: data.cacContInstNo,
                cacContTellerNo: data.cacContTellerNo,
                cacContAuthTellerNo: data.cacContAuthTellerNo,
                lastTxDate: data.lastTxDate,
                zoneVal: data.zoneVal,
                lastModStamp: data.lastModStamp,
              };

              originalData =
                dataSource.find((item) => item.noCardPayAgrNo === rowKey) || null;

              const res = await dprgtNoCardPayUpdate(updateData);
              if (res.code === 200) {
                setDataSource((prevDataSource) =>
                  prevDataSource.map((item) =>
                    item.noCardPayAgrNo === rowKey ? { ...data } : item
                  )
                );
                message.success("保存成功");
              } else {
                message.error(res.msg || "保存失败");
                if (originalData) {
                  setDataSource((prevDataSource) =>
                    prevDataSource.map((item) =>
                      item.noCardPayAgrNo === rowKey ? originalData : item
                    ) as DprgtNoCardPay[]
                  );
                }
              }
            } catch (error) {
              message.error("保存失败");
              if (originalData) {
                setDataSource((prevDataSource) =>
                  prevDataSource.map((item) =>
                    item.noCardPayAgrNo === rowKey ? originalData : item
                  ) as DprgtNoCardPay[]
                );
              }
            } finally {
              setEditableRowKeys([]);
              setIsEditing(false);
              setEditingRecord(null);
              if (props.data) {
                try {
                  const noCardPayRecord = props.data.records && props.data.records[0];
                  if (noCardPayRecord?.mediumNo && noCardPayRecord?.zoneVal) {
                    setLoading(true);
                    try {
                      const res = await getDprgtNoCardPay(
                        noCardPayRecord.mediumNo,
                        noCardPayRecord.zoneVal,
                        "dprgtNoCardPayPageInfo",
                        {
                          current: pagination.current,
                          pageSize: pagination.pageSize,
                        }
                      );
                      if (res.code === 200 && res.data) {
                        const detailData = JSON.parse(res.data);
                        setDataSource(detailData.records || []);
                        setPagination({
                          current: detailData.current || INITIAL_PAGINATION.current,
                          pageSize: detailData.size || INITIAL_PAGINATION.pageSize,
                          total: detailData.total || INITIAL_PAGINATION.total,
                        });
                        setRefresh(refresh + 1);
                      } else {
                        message.error(res.msg || "获取无卡支付协议登记信息失败");
                      }
                    } catch (error) {
                      message.error("查询无卡支付协议登记信息失败");
                    } finally {
                      setLoading(false);
                    }
                  }
                } catch (error) {
                  message.error("查询无卡支付协议登记信息失败");
                }
              }
            }
          },
          onChange: setEditableRowKeys,
          onCancel: async (rowKey) => {
            if (editingRecord) {
              setDataSource((prevDataSource) =>
                prevDataSource.map((item) =>
                  item.noCardPayAgrNo === editingRecord.noCardPayAgrNo
                    ? { ...editingRecord }
                    : item
                )
              );
            }
            setEditableRowKeys([]);
            setIsEditing(false);
            setEditingRecord(null);
            return true;
          },
        }}
      />
    </Modal>
  );
};

export default DprgtNoCardPayFormEdit;
