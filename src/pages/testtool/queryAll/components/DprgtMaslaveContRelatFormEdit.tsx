import React, { useEffect } from "react";
import moment from "moment";
import {
  ProFormText,
  ProFormSelect,
} from "@ant-design/pro-form";
import { Form, Modal, Row, Col, Spin } from "antd";
import type { TbDprgtMaslaveContRelat } from "../data";

/*
 * @Description: 主从合约主档表编辑表单
 * @Author: zhujinwei
 * @Date: 2025-01-17 10:00:00
 */

export type DprgtMaslaveContRelatFormValueType = Record<string, unknown> &
  Partial<TbDprgtMaslaveContRelat>;

export type DprgtMaslaveContRelatFormProps = {
  onCancel: (flag?: boolean, formVals?: DprgtMaslaveContRelatFormValueType) => void;
  onSubmit: (values: DprgtMaslaveContRelatFormValueType) => Promise<void>;
  visible: boolean;
  values: Partial<TbDprgtMaslaveContRelat>;
  loading?: boolean; // 添加loading属性
  submitting?: boolean; // 添加提交状态属性
};

const DprgtMaslaveContRelatForm: React.FC<DprgtMaslaveContRelatFormProps> = (props) => {
  const [form] = Form.useForm();

  useEffect(() => {
    if (props.visible && props.values) {
      console.log("表单设置值:", props.values);
      form.resetFields();
      form.setFieldsValue({
        mainContrNo: props.values.mainContrNo, // 主合约编号
        saccnoSeqNo: props.values.saccnoSeqNo, // 子账户序号
        baseProdtNo: props.values.baseProdtNo, // 基础产品编码
        vendibiProdtNo: props.values.vendibiProdtNo,   // 可售产品编码
        currCode: props.values.currCode,        // 币种代码
        cashExgVatgCd: props.values.cashExgVatgCd, // 钞汇类别代码
        dpContrTpCd: props.values.dpContrTpCd, // 个人存款合约类型代码
        relContrNo: props.values.relContrNo, // 关联产品合约编号
        relCategFgCd: props.values.relCategFgCd, // 关联类别标识代码
        zoneVal: props.values.zoneVal,          // 分片值
      });
    }
  }, [form, props.visible, props.values]);

  const handleOk = () => {
    form.submit();
  };

  const handleCancel = () => {
    props.onCancel();
    form.resetFields();
  };

  const handleFinish = async (values: Record<string, any>) => {
    // 转换所有日期字段为yyyyMMdd格式
    const dateFields = [
      "lastTxDate",
    ];
    dateFields.forEach((field) => {
      if (values[field]) {
        values[field] = moment(values[field]).format("YYYYMMDD");
      }
    });
    props.onSubmit(values as DprgtMaslaveContRelatFormValueType);
    return true;
  };

  return (
    <Modal
      width={800}
      title="编辑主从合约信息"
      visible={props.visible}
      destroyOnClose
      onOk={handleOk}
      onCancel={handleCancel}
      bodyStyle={{ 
        maxHeight: '70vh', 
        overflow: 'auto', 
        padding: '24px',
        borderRadius: '10px',  // 添加圆角
      }}
      style={{
        borderRadius: '10px',  // Modal整体圆角
        overflow: 'hidden'    // 确保内容不超出圆角范围
      }}
    >
      <Spin 
        spinning={(props.loading || props.submitting) || false} 
        tip={props.submitting ? "正在提交数据，请稍后..." : "一大波数据正在路上..."}
      >
      <Form
        form={form}
        onFinish={handleFinish}
        initialValues={props.values}
        layout="vertical"
        className="custom-form" // 添加自定义类名
      >
        <style>
        {`
          .custom-form .ant-form-item-label > label {
            font-weight: 600;
          }
        `}
        </style>
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <ProFormText
              name="mainContrNo"
              label="主合约编号"
              width="xl"
              placeholder="请输入主合约编号"
              disabled
              rules={[
                {
                  required: true,
                  message: "请输入主合约编号！",
                },
              ]}
            />
          </Col>
          <Col span={12}>
            <ProFormText
              name="saccnoSeqNo"
              label="子账户序号"
              width="xl"
              placeholder="请输入子账户序号"
              disabled
              rules={[
                {
                  required: true,
                  message: "请输入子账户序号",
                },
              ]}
            />
          </Col>
        </Row>
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <ProFormText
              name="baseProdtNo"
              label="基础产品编码"
              width="xl"
              placeholder="请输入基础产品编码"
            />
          </Col>
          <Col span={12}>
            <ProFormText
              name="vendibiProdtNo"
              label="可售产品编码"
              width="xl"
              placeholder="请输入可售产品编码"
            />
          </Col>
        </Row>
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <ProFormSelect
              name="currCode"
              label="币种代码"
              width="xl"
              placeholder="请输入币种代码"
              options={
                [
                  {value: '036', label: '036-澳大利亚元'},
                  {value: '124', label: '124-加元'},
                  {value: '344', label: '344-香港元'},
                  {value: '392', label: '392-日元'},
                  {value: '826', label: '826-英镑'},
                  {value: '840', label: '840-美元'},
                  {value: '978', label: '978-欧元（EUR）'},
                  {value: '156', label: '156-人民币元'},
                ]
              }
              fieldProps={{
                optionItemRender: (item: { value: string; label: string }) => {
                  const mediumTypeMap: { [key: string]: string } = {
                    '036': '澳大利亚元',
                    '124': '加元',
                    '344': '香港元',
                    '392': '日元',
                    '826': '英镑',
                    '840': '美元',
                    '978': '欧元（EUR）',
                    '156': '人民币元',
                  };
                  return `${item.value}-${mediumTypeMap[item.value] || item.label}`;
                },
              }}
            />
          </Col>
          <Col span={12}>
            <ProFormSelect
              name="cashExgVatgCd"
              label="钞汇类别代码"
              width="xl"
              placeholder="请输入钞汇类别代码"
              options={
                [
                  {value: '2', label: '2-钞'},
                  {value: '3', label: '3-汇'},
                ]
              }
              fieldProps={{
                optionItemRender: (item: { value: string; label: string }) => {
                  const mediumTypeMap: { [key: string]: string } = {
                    '2': '钞',
                    '3': '汇',
                  };
                  return `${item.value}-${mediumTypeMap[item.value] || item.label}`;
                },
              }}
            />
          </Col>
        </Row>
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <ProFormSelect
              name="dpContrTpCd"
              label="个人存款合约类型代码"
              width="xl"
              placeholder="请输入个人存款合约类型代码"
              options={
                [
                  {value: '1001', label: '1001-人民币活期储蓄合约'},
                  {value: '1002', label: '1002-人民币活期结算合约'},
                  {value: '1003', label: '1003-外币活期储蓄合约'},
                  {value: '1004', label: '1004-外币活期结算合约'},
                  {value: '1005', label: '1005-本外币合一结算合约'},
                  {value: '2001', label: '2001-整存整取储蓄存款合约'},
                  {value: '2002', label: '2002-整存整取协议存款合约'},
                  {value: '2003', label: '2003-提前付息定期存款合约'},
                  {value: '2004', label: '2004-定活两便储蓄存款合约'},
                  {value: '2005', label: '2005-整存零取储蓄存款合约'},
                  {value: '2006', label: '2006-存本取息储蓄存款合约'},
                  {value: '2007', label: '2007-零存整取储蓄存款合约'},
                  {value: '2008', label: '2008-通知存款合约'},
                  {value: '2009', label: '2009-结构性存款合约'},
                  {value: '2010', label: '2010-递增计息合约'},
                  {value: '2011', label: '2011-梦想加邮站合约'},
                  {value: '2012', label: '2012-大额存单合约'},
                  {value: '2013', label: '2013-礼仪存单合约'},
                  {value: '2014', label: '2014-邮智存合约'},
                  {value: '3001', label: '3001-行业应用子账户合约'},
                  {value: '3002', label: '3002-电子现金账户合约'},
                  {value: '4001', label: '4001-副卡合约'},
                  {value: '4002', label: '4002-映射卡合约'},
                  {value: '4003', label: '4003-本外币定期一本通合约'},
                ]
              }
              fieldProps={{
                optionItemRender: (item: { value: string; label: string }) => {
                  const mediumTypeMap: { [key: string]: string } = {
                    '1001': '人民币活期储蓄合约',
                    '1002': '人民币活期结算合约',
                    '1003': '外币活期储蓄合约',
                    '1004': '外币活期结算合约',
                    '1005': '本外币合一结算合约',
                    '2001': '整存整取储蓄存款合约',
                    '2002': '整存整取协议存款合约',
                    '2003': '提前付息定期存款合约',
                    '2004': '定活两便储蓄存款合约',
                    '2005': '整存零取储蓄存款合约',
                    '2006': '存本取息储蓄存款合约',
                    '2007': '零存整取储蓄存款合约',
                    '2008': '通知存款合约',
                    '2009': '结构性存款合约',
                    '2010': '递增计息合约',
                    '2011': '梦想加邮站合约',
                    '2012': '大额存单合约',
                    '2013': '礼仪存单合约',
                    '2014': '邮智存合约',
                    '3001': '行业应用子账户合约',
                    '3002': '电子现金账户合约',
                    '4001': '副卡合约',
                    '4002': '映射卡合约',
                    '4003': '本外币定期一本通合约',
                  };
                  return `${item.value}-${mediumTypeMap[item.value] || item.label}`;
                },
              }}
            />
          </Col>
          <Col span={12}>
            <ProFormText
              name="relContrNo"
              label="关联产品合约编号"
              width="xl"
              placeholder="请输入关联产品合约编号"
            />
          </Col>
        </Row>
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <ProFormSelect
              name="relCategFgCd"
              label="关联类别标识代码"
              width="xl"
              placeholder="请输入关联类别标识代码"
              options={
                [
                  {value: '01', label: '01-活期'},
                  {value: '02', label: '02-定期'},
                ]
              }
              fieldProps={{
                optionItemRender: (item: { value: string; label: string }) => {
                  const mediumTypeMap: { [key: string]: string } = {
                    '01': '活期',
                    '02': '定期',
                  };
                  return `${item.value}-${mediumTypeMap[item.value] || item.label}`;
                },
              }}
            />
          </Col>
          <Col span={12}>
            <ProFormText
              name="zoneVal"
              label="分片值"
              width="xl"
              placeholder="请输入分片值"
              disabled
              rules={[
                {
                  required: true,
                  message: "请输入分片值！",
                },
              ]}
            />
          </Col>
        </Row>
      </Form>
      </Spin>
    </Modal>
  );
};

export default DprgtMaslaveContRelatForm;
