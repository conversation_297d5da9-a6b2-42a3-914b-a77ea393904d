import React, { useState, useEffect } from "react";
import { Modal, Form, Radio, Space, message, Button, Spin } from "antd";
import type { TbDpmstMedium } from "../data";
import { dpmstMediumUpdate, queryAll } from "../service";
import { flagMeaningsConfig } from "./FlagModal";
import { useAccess } from "umi";

interface RelMediumTypeFlagCdEditProps {
  visible: boolean;
  onCancel: () => void;
  record: any;
  mediumNo: string; // 添加介质号属性
  onSuccess: () => void;
  loading?: boolean; // 加载状态
  submitting?: boolean; // 提交状态
}

const RelMediumTypeFlagCdEdit: React.FC<RelMediumTypeFlagCdEditProps> = ({
  visible,
  onCancel,
  record,
  mediumNo, // 接收介质号
  onSuccess,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const access = useAccess();

  const [editedValues, setEditedValues] = useState<Record<number, string>>({});
  const flagConfig = flagMeaningsConfig.relMediumTypeFlagCd;

  const [options, setOptions] = useState([
    { label: "是否关联折", value: 0 },
    { label: "是否关联卡", value: 1 },
    { label: "是否关联普通活期对账簿", value: 2 },
    { label: "是否关联普通定期对账簿", value: 3 },
    { label: "是否关联行业应用子账户对账簿", value: 4 },
    { label: "是否关联湖南省惠农补贴明白折", value: 5 },
    { label: "是否关联湖南省扶贫补贴明白折", value: 6 },
  ]);
  const [checkedValues, setCheckedValues] = useState<boolean[]>([]);
  useEffect(() => {
    if (visible && record) {
      const initialValues: Record<number, string> = {};

      if (record.relMediumTypeFlagCd) {
        const bits = record.relMediumTypeFlagCd.split("");
        flagConfig.forEach((item) => {
          initialValues[item.position] = bits[item.position - 1] || "0";
        });
      } else {
        flagConfig.forEach((item) => {
          initialValues[item.position] = "0";
        });
      }

      setEditedValues(initialValues);
    }
  }, [visible, record]);

  const handleValueChange = (position: number, value: string) => {
    setEditedValues((prev) => ({
      ...prev,
      [position]: value,
    }));
  };

  const handleOk = async () => {
    setLoading(true);
    try {
      // 生成7位标志码
      const flagArray = new Array(7).fill("0");
      Object.entries(editedValues).forEach(([position, value]) => {
        const index = parseInt(position) - 1;
        if (index >= 0 && index < 7) {
          flagArray[index] = value;
        }
      });

      const relMediumTypeFlagCd = flagArray.join("");
      const params: TbDpmstMedium = {
        mediumNo: mediumNo, // 使用传入的介质号
        zoneVal: record.zoneVal,
        relMediumTypeFlagCd,
      };

      const res = await dpmstMediumUpdate(params);
      if (res.code === 200) {
        message.success("更新成功");
        // 调用查询方法重新获取数据
        await queryAll(mediumNo);
        onCancel();
      } else {
        message.error(res.msg || "更新失败");
      }
    } catch (error) {
      message.error("更新失败，请重试");
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    onCancel();
  };

  const handleCheckboxChange = (index: number, checked: boolean) => {
    const newCheckedValues = [...checkedValues];
    newCheckedValues[index] = checked;
    setCheckedValues(newCheckedValues);
  };
  return (
    <Modal
      title="编辑关联介质类型标志码"
      open={visible}
      onOk={handleOk}
      onCancel={onCancel}
      confirmLoading={loading}
      width={550}
    >
        <Space direction="vertical" style={{ width: "100%" }} size="large">
          {flagConfig.map((item) => (
            <Form.Item key={item.key} label={item.title}>
              <Radio.Group
                value={editedValues[item.position]}
                onChange={(e) =>
                  handleValueChange(item.position, e.target.value)
                }
              >
                <Space>
                  {item.options.map((option) => (
                    <Radio key={option.value} value={option.value}>
                      {option.label}
                    </Radio>
                  ))}
                </Space>
              </Radio.Group>
            </Form.Item>
          ))}
        </Space>
    </Modal>
  );
};

export default RelMediumTypeFlagCdEdit;
