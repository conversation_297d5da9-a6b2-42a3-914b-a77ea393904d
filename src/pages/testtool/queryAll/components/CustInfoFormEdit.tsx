import React, { useEffect } from "react";
import moment from "moment";
import {
  ProFormText,
  ProFormSelect,
  ProFormDatePicker,
} from "@ant-design/pro-form";
import { Form, Modal, Row, Col, Spin } from "antd";
import type { CustInfo } from "../data";

/*
 * @Description: 客户账户信息编辑表单
 * @Author: zhujinwei
 * @Date: 2025-01-17 10:00:00
 */

export type CustInfoFormValueType = Record<string, unknown> &
  Partial<CustInfo>;

export type CustInfoFormProps = {
  onCancel: (flag?: boolean, formVals?: CustInfoFormValueType) => void;
  onSubmit: (values: CustInfoFormValueType) => Promise<void>;
  visible: boolean;
  values: Partial<CustInfo>;
  loading?: boolean; // 添加loading属性
  submitting?: boolean; // 添加提交状态属性
};

const CustInfoForm: React.FC<CustInfoFormProps> = (props) => {
  const [form] = Form.useForm();

  useEffect(() => {
    if (props.visible && props.values) {
      console.log("表单设置值:", props.values);
      form.resetFields();
      form.setFieldsValue({
        custNo: props.values.custNo,            // 客户编号
        custNm: props.values.custNm,            // 客户名称
        perCertTpCd: props.values.perCertTpCd,  // 证件类型
        personalCertNo: props.values.personalCertNo,   // 证件号码
        certValidDlineDate: props.values.certValidDlineDate ? moment(props.values.certValidDlineDate, "YYYYMMDD") : undefined,        // 证件有效期
        ordinaryCardNumShee: props.values.ordinaryCardNumShee, // 正常卡数量
        specCardNumShee: props.values.specCardNumShee,          // 特殊卡数量
        curtAccnum: props.values.curtAccnum,    // 活期账户数
        iLaccNum: props.values.iLaccNum,        // I类账户数
        iiLaccNum: props.values.iiLaccNum,      // II类账户数
        iiiLaccNum: props.values.iiiLaccNum,    // III类账户数
        mobileNo: props.values.mobileNo,        // 手机号码
        custIdentityToVerifyFlag: props.values.custIdentityToVerifyFlag,    // 客户身份识别标识
        sameMobilenoMcustFlag: props.values.sameMobilenoMcustFlag,          // 同一手机号多客户标识
        nineItmInfoIncompFlag: props.values.nineItmInfoIncompFlag,        // 九项信息不符标识
        custInfoUnfitFlag: props.values.custInfoUnfitFlag,  // 客户信息不符标识
        shardingId: props.values.shardingId,          // 分片值
      });
    }
  }, [form, props.visible, props.values]);

  const handleOk = () => {
    form.submit();
  };

  const handleCancel = () => {
    props.onCancel();
    form.resetFields();
  };

  const handleFinish = async (values: Record<string, any>) => {
    // 转换所有日期字段为yyyyMMdd格式
    const dateFields = [
      "lastTxDate",
      "certValidDlineDate", // 添加证件有效期字段
    ];
    dateFields.forEach((field) => {
      if (values[field]) {
        values[field] = moment(values[field]).format("YYYYMMDD");
      }
    });
    props.onSubmit(values as CustInfoFormValueType);
    return true;
  };

  return (
    <Modal
      width={800}
      title="编辑客户账户信息"
      visible={props.visible}
      destroyOnClose
      onOk={handleOk}
      onCancel={handleCancel}
      bodyStyle={{ 
        maxHeight: '70vh', 
        overflow: 'auto', 
        padding: '24px',
        borderRadius: '10px',  // 添加圆角
      }}
      style={{
        borderRadius: '10px',  // Modal整体圆角
        overflow: 'hidden'    // 确保内容不超出圆角范围
      }}
    >
      <Spin 
        spinning={(props.loading || props.submitting) || false} 
        tip={props.submitting ? "正在提交数据，请稍后..." : "一大波数据正在路上..."}
      >
      <Form
        form={form}
        onFinish={handleFinish}
        initialValues={props.values}
        layout="vertical"
        className="custom-form" // 添加自定义类名
      >
        <style>
        {`
          .custom-form .ant-form-item-label > label {
            font-weight: 600;
          }
        `}
        </style>
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <ProFormText
              name="custNo"
              label="客户编号"
              width="xl"
              placeholder="请输入客户编号"
              disabled
              rules={[
                {
                  required: true,
                  message: "请输入客户编号！",
                },
              ]}
            />
          </Col>
          <Col span={12}>
            <ProFormText
              name="custNm"
              label="客户名称"
              width="xl"
              placeholder="请输入客户名称"
            />
          </Col>
        </Row>
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <ProFormSelect
              name="perCertTpCd"
              label="证件类型"
              width="xl"
              placeholder="请输入证件类型"
              options={
                [
                  {value: '1010', label: '1010-居民身份证'},
                  {value: '1011', label: '1011-临时居民身份证'},
                  {value: '1020', label: '1020-军人身份证件'},
                  {value: '1021', label: '1021-士兵证'},
                  {value: '1022', label: '1022-军官证'},
                  {value: '1023', label: '1023-文职干部证'},
                  {value: '1024', label: '1024-军官退休证'},
                  {value: '1025', label: '1025-文职干部退休证'},
                  {value: '1030', label: '1030-武警身份证件'},
                  {value: '1031', label: '1031-武警士兵证'},
                  {value: '1032', label: '1032-警官证'},
                  {value: '1033', label: '1033-武警文职干部证'},
                  {value: '1034', label: '1034-武警军官退休证'},
                  {value: '1035', label: '1035-武警文职干部退休证'},
                  {value: '1040', label: '1040-户口簿'},
                  {value: '1050', label: '1050-中国护照'},
                  {value: '1051', label: '1051-外国护照'},
                  {value: '1060', label: '1060-学生证'},
                  {value: '1070', label: '1070-港澳居民来往内地通行证'},
                  {value: '1071', label: '1071-往来港澳通行证'},
                  {value: '1080', label: '1080-台湾居民来往大陆通行证'},
                  {value: '1090', label: '1090-执行公务证'},
                  {value: '1100', label: '1100-机动车驾驶证'},
                  {value: '1110', label: '1110-社会保障卡'},
                  {value: '1120', label: '1120-外国人居留证'},
                  {value: '1121', label: '1121-外国人永久居留证'},
                  {value: '1130', label: '1130-旅行证件'},
                  {value: '1140', label: '1140-香港居民身份证'},
                  {value: '1150', label: '1150-澳门居民身份证'},
                  {value: '1160', label: '1160-台湾居民身份证'},
                  {value: '1170', label: '1170-边民证'},
                  {value: '1180', label: '1180-港澳台居民居住证'},
                  {value: '1181', label: '1181-港澳居民居住证'},
                  {value: '1182', label: '1182-台湾居民居住证'},
                  {value: '1190', label: '1190-外国身份证'},
                  {value: '1998', label: '1998-其他（原98类）'},
                  {value: '1999', label: '1999-其他证件（个人）'},
                ]
              }
              fieldProps={{
                optionItemRender: (item: { value: string; label: string }) => {
                  const mediumTypeMap: { [key: string]: string } = {
                    '1010': '居民身份证',
                    '1011': '临时居民身份证',
                    '1020': '军人身份证件',
                    '1021': '士兵证',
                    '1022': '军官证',
                    '1023': '文职干部证',
                    '1024': '军官退休证',
                    '1025': '文职干部退休证',
                    '1030': '武警身份证件',
                    '1031': '武警士兵证',
                    '1032': '警官证',
                    '1033': '武警文职干部证',
                    '1034': '武警军官退休证',
                    '1035': '武警文职干部退休证',
                    '1040': '户口簿',
                    '1050': '中国护照',
                    '1051': '外国护照',
                    '1060': '学生证',
                    '1070': '港澳居民来往内地通行证',
                    '1071': '往来港澳通行证',
                    '1080': '台湾居民来往大陆通行证',
                    '1090': '执行公务证',
                    '1100': '机动车驾驶证',
                    '1110': '社会保障卡',
                    '1120': '外国人居留证',
                    '1121': '外国人永久居留证',
                    '1130': '旅行证件',
                    '1140': '香港居民身份证',
                    '1150': '澳门居民身份证',
                    '1160': '台湾居民身份证',
                    '1170': '边民证',
                    '1180': '港澳台居民居住证',
                    '1181': '港澳居民居住证',
                    '1182': '台湾居民居住证',
                    '1190': '外国身份证',
                    '1998': '其他（原98类）',
                    '1999': '其他证件（个人）',
                  };
                  return `${item.value}-${mediumTypeMap[item.value] || item.label}`;
                },
              }}
            />
          </Col>
          <Col span={12}>
            <ProFormText
              name="personalCertNo"
              label="证件号码"
              width="xl"
              placeholder="请输入证件号码"
            />
          </Col>
        </Row>
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <ProFormDatePicker
              name="certValidDlineDate"
              label="证件有效期"
              width="xl"
              placeholder="请输入证件有效期"
            />
          </Col>
          <Col span={12}>
            <ProFormText
              name="ordinaryCardNumShee"
              label="正常卡数量"
              width="xl"
              placeholder="请输入正常卡数量"
            />
          </Col>
        </Row>
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <ProFormText
              name="specCardNumShee"
              label="特殊卡数量"
              width="xl"
              placeholder="请输入特殊卡数量"
            />
          </Col>
          <Col span={12}>
            <ProFormText
              name="curtAccnum"
              label="活期账户数"
              width="xl"
              placeholder="请输入活期账户数"
            />
          </Col>
        </Row>
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <ProFormText
              name="iLaccNum"
              label="I类账户数"
              width="xl"
              placeholder="请输入I类账户数"
            />
          </Col>
          <Col span={12}>
            <ProFormText
              name="iiLaccNum"
              label="II类账户数"
              width="xl"
              placeholder="请输入II类账户数"
            />
          </Col>
        </Row>
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <ProFormText
              name="iiiLaccNum"
              label="III类账户数"
              width="xl"
              placeholder="请输入III类账户数"
            />
          </Col>
          <Col span={12}>
            <ProFormText
              name="mobileNo"
              label="手机号码"
              width="xl"
              placeholder="请输入手机号码"
            />
          </Col>
        </Row>
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <ProFormSelect
              name="custIdentityToVerifyFlag"
              label="客户身份待核实标识"
              width="xl"
              placeholder="请输入客户身份待核实标识"
              options={[
                {
                  value: '0',
                  label: '0-否',
                },
                {
                  value: '1',
                  label: '1-是',
                },
              ]}
            />
          </Col>
          <Col span={12}>
            <ProFormSelect
              name="sameMobilenoMcustFlag"
              label="同一手机号多客户标识"
              width="xl"
              placeholder="请输入同一手机号多客户标识"
              options={[
                {
                  value: '0',
                  label: '0-否',
                },
                {
                  value: '1',
                  label: '1-是',
                },
              ]}
            />
          </Col>
        </Row>
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <ProFormSelect
              name="nineItmInfoIncompFlag"
              label="九项信息不符标识"
              width="xl"
              placeholder="请输入九项信息不符标识"
              options={[
                {
                  value: '0',
                  label: '0-不全',
                },
                {
                  value: '1',
                  label: '1-全',
                },
              ]}
            />
          </Col>
          <Col span={12}>
            <ProFormSelect
              name="custInfoUnfitFlag"
              label="客户信息不符标识"
              width="xl"
              placeholder="请输入客户信息不符标识"
              options={[
                {
                  value: '0',
                  label: '0-否',
                },
                {
                  value: '1',
                  label: '1-是',
                },
              ]}
            />
          </Col>
        </Row>
        {/* <Row gutter={[16, 16]}>
          <Col span={12}>
            <ProFormText
              name="shardingId"
              label="分片值"
              width="xl"
              disabled
              rules={[
                {
                  required: true,
                  message: "请输入分片值！",
                },
              ]}
            />
          </Col>
        </Row> */}
      </Form>
      </Spin>
    </Modal>
  );
};

export default CustInfoForm;
