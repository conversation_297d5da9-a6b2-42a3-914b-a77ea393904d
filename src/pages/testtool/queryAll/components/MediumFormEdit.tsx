import React, { useEffect } from "react";
import moment from "moment";
import {
  ProFormText,
  ProFormSelect,
  ProFormDatePicker,
} from "@ant-design/pro-form";
import { Form, Modal, Row, Col, Spin } from "antd";
import type { TbDpmstMedium } from "../data";
/*
 * 介质主档表编辑
 */
export type MediumFormValueType = Record<string, unknown> &
  Partial<TbDpmstMedium>;

export type MediumFormProps = {
  onCancel: (flag?: boolean, formVals?: MediumFormValueType) => void;
  onSubmit: (values: MediumFormValueType) => Promise<void>;
  visible: boolean;
  values: Partial<TbDpmstMedium>;
  loading?: boolean; // 添加loading属性
  submitting?: boolean; // 添加提交状态属性
};

const MediumForm: React.FC<MediumFormProps> = (props) => {
  const [form] = Form.useForm();

  useEffect(() => {
    if (props.visible && props.values) {
      console.log("表单设置值:", props.values);
      form.resetFields();
      form.setFieldsValue({
        mediumNo: props.values.mediumNo,
        mediumTpCd: props.values.mediumTpCd,
        mainContrNo: props.values.mainContrNo,
        categFlagCd: props.values.categFlagCd,
        mediumPrintNo: props.values.mediumPrintNo, // 介质印刷号
        cardSeqNo: props.values.cardSeqNo, // 卡序列号
        cardMediumCode: props.values.cardMediumCode, // 卡介质代码
        cardKindCd: props.values.cardKindCd, // 卡种代码
        dcardCategNo: props.values.dcardCategNo, // 借记卡类别编码
        enrolledLineNum: props.values.enrolledLineNum, // 已登记打印行数
        openDt: props.values.openDt, // 开户日期
        drawInstNo: props.values.drawInstNo, // 开户机构号
        applyMakecardDt: props.values.applyMakecardDt, // 申请制卡日期
        mediumValidDt: props.values.mediumValidDt, // 介质有效日期
        crtModeFlagCd: props.values.crtModeFlagCd, // 介质认证方式标志码
        lastTxDate: props.values.lastTxDate, // 最后交易日期
        zoneVal: props.values.zoneVal, // 客户编号
      });
    }
  }, [form, props.visible, props.values]);

  const handleOk = () => {
    form.submit();
  };

  const handleCancel = () => {
    props.onCancel();
    form.resetFields();
  };

  const handleFinish = async (values: Record<string, any>) => {
    // 转换所有日期字段为yyyyMMdd格式
    const dateFields = [
      "lastTxDate",
      "openDt",
      "applyMakecardDt",
      "mediumValidDt",
    ];
    dateFields.forEach((field) => {
      if (values[field]) {
        values[field] = moment(values[field]).format("YYYYMMDD");
      }
    });
    props.onSubmit(values as MediumFormValueType);
    return true;
  };

  return (
    <Modal
      width={800}
      title="编辑介质信息"
      visible={props.visible}
      destroyOnClose
      onOk={handleOk}
      onCancel={handleCancel}
      bodyStyle={{
        maxHeight: "70vh",
        overflow: "auto",
        padding: "24px",
        borderRadius: "10px", // 添加圆角
      }}
      style={{
        borderRadius: "10px", // Modal整体圆角
        overflow: "hidden", // 确保内容不超出圆角范围
      }}
    >
      <Spin 
        spinning={(props.loading || props.submitting) || false} 
        tip={props.submitting ? "正在提交数据，请稍后..." : "一大波数据正在路上..."}
      >
        <Form
          form={form}
          onFinish={handleFinish}
          initialValues={props.values}
          layout="vertical"
          className="custom-form" // 添加自定义类名
        >
          <style>
            {`
          .custom-form .ant-form-item-label > label {
            font-weight: 600;
          }
        `}
          </style>
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <ProFormText
                name="mediumNo"
                label="介质编号"
                width="xl"
                placeholder="请输入介质编号"
                disabled
                rules={[
                  {
                    required: true,
                    message: "请输入介质编号！",
                  },
                ]}
              />
            </Col>
            <Col span={12}>
              <ProFormText
                name="mainContrNo"
                label="主合约编号"
                width="xl"
                disabled
                placeholder="请输入主合约编号"
                rules={[
                  {
                    required: true,
                    message: "请输入主合约编号！",
                  },
                ]}
              />
            </Col>
          </Row>
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <ProFormSelect
                name="mediumTpCd"
                label="介质类型代码"
                width="xl"
                placeholder="请输入介质类型代码"
                options={[
                  {
                    value: "0201",
                    label: "0201-绿卡",
                  },
                  {
                    value: "0202",
                    label: "0202-绿卡通",
                  },
                  {
                    value: "0203",
                    label: "0203-绿卡通副卡",
                  },
                  {
                    value: "0204",
                    label: "0204-小额支付卡",
                  },
                  {
                    value: "0301",
                    label: "0301-活期存折",
                  },
                  {
                    value: "0302",
                    label: "0302-定期零整存折",
                  },
                  {
                    value: "0303",
                    label: "0303-定期整零存折",
                  },
                  {
                    value: "0304",
                    label: "0304-存本取息存折",
                  },
                  {
                    value: "0305",
                    label: "0305-本外币活期一本通",
                  },
                  {
                    value: "0306",
                    label: "0306-本外币定期一本通",
                  },
                  {
                    value: "0309",
                    label: "0309-本币定期一本通",
                  },
                  {
                    value: "0401",
                    label: "0401-整存整取存单",
                  },
                  {
                    value: "0402",
                    label: "0402-定活两便存单",
                  },
                  {
                    value: "0403",
                    label: "0403-通知存款存单",
                  },
                  {
                    value: "0404",
                    label: "0404-整存整取特种存单",
                  },
                ]}
                fieldProps={{
                  optionItemRender: (item: {
                    value: string;
                    label: string;
                  }) => {
                    const mediumTpCdMap: { [key: string]: string } = {
                      "0201": "绿卡",
                      "0202": "绿卡通",
                      "0203": "绿卡通副卡",
                      "0204": "小额支付卡",
                      "0301": "活期存折",
                      "0302": "定期零整存折",
                      "0303": "定期整零存折",
                      "0304": "存本取息存折",
                      "0305": "本外币活期一本通",
                      "0306": "本外币定期一本通",
                      "0309": "本币定期一本通",
                      "0401": "整存整取存单",
                      "0402": "定活两便存单",
                      "0403": "通知存款存单",
                      "0404": "整存整取特种存单",
                    };
                    return `${item.value}-${mediumTpCdMap[item.value] || item.label}`;
                  },
                }}
              />
            </Col>
            <Col span={12}>
              <ProFormSelect
                name="categFlagCd"
                label="类别标识代码"
                width="xl"
                placeholder="请选择类别标识代码"
                options={[
                  {
                    value: "01",
                    label: "01-活期",
                  },
                  {
                    value: "02",
                    label: "02-定期",
                  },
                ]}
                fieldProps={{
                  optionItemRender: (item: {
                    value: string;
                    label: string;
                  }) => {
                    const cateFlagCdMap: { [key: string]: string } = {
                      "01": "活期",
                      "02": "定期",
                    };
                    return `${item.value}-${cateFlagCdMap[item.value] || item.label}`;
                  },
                }}
              />
            </Col>
          </Row>
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <ProFormText
                name="mediumPrintNo"
                label="介质印刷号"
                width="xl"
                placeholder="请输入介质印刷号"
              />
            </Col>
            <Col span={12}>
              <ProFormText
                name="cardSeqNo"
                label="卡序列号"
                width="xl"
                placeholder="请输入卡序列号"
              />
            </Col>
          </Row>
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <ProFormSelect
                name="cardMediumCode"
                label="卡介质代码"
                width="xl"
                placeholder="请输入卡介质代码"
                options={[
                  {
                    value: "1",
                    label: "1-磁条卡",
                  },
                  {
                    value: "2",
                    label: "2-复合卡",
                  },
                  {
                    value: "3",
                    label: "3-单芯片卡",
                  },
                  {
                    value: "4",
                    label: "4-虚拟卡",
                  },
                ]}
                fieldProps={{
                  optionItemRender: (item: {
                    value: string;
                    label: string;
                  }) => {
                    const cardMediumCodeMap: { [key: string]: string } = {
                      "1": "磁条卡",
                      "2": "复合卡",
                      "3": "单芯片卡",
                      "4": "虚拟卡",
                    };
                    return `${item.value}-${cardMediumCodeMap[item.value] || item.label}`;
                  },
                }}
              />
            </Col>
            <Col span={12}>
              <ProFormSelect
                name="cardKindCd"
                label="卡品种代码"
                width="xl"
                placeholder="请选择卡品种代码"
                options={[
                  {
                    value: "01",
                    label: "01-储蓄卡",
                  },
                  {
                    value: "02",
                    label: "02-联名卡(储蓄卡)",
                  },
                  {
                    value: "03",
                    label: "03-联名卡(绿卡通)",
                  },
                  {
                    value: "04",
                    label: "04-认同卡(绿卡通)",
                  },
                  {
                    value: "05",
                    label: "05-认同卡(储蓄卡)",
                  },
                  {
                    value: "06",
                    label: "06-绿卡通卡",
                  },
                  {
                    value: "07",
                    label: "07-绿卡通副卡",
                  },
                  {
                    value: "08",
                    label: "08-小额支付卡",
                  },
                  {
                    value: "09",
                    label: "09-万事达卡",
                  },
                  {
                    value: "13",
                    label: "13-绿卡通(万事网联)",
                  },
                ]}
                fieldProps={{
                  optionItemRender: (item: {
                    value: string;
                    label: string;
                  }) => {
                    const cardKinCdMap: { [key: string]: string } = {
                      "01": "储蓄卡",
                      "02": "联名卡(储蓄卡)",
                      "03": "联名卡(绿卡通)",
                      "04": "认同卡(绿卡通)",
                      "05": "认同卡(储蓄卡)",
                      "06": "绿卡通卡",
                      "07": "绿卡通副卡",
                      "08": "小额支付卡",
                      "09": "万事达卡",
                      "13": "绿卡通(万事网联)",
                    };
                    return `${item.value}-${cardKinCdMap[item.value] || item.label}`;
                  },
                }}
              />
            </Col>
          </Row>
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <ProFormText
                name="dcardCategNo"
                label="借记卡类别编码"
                width="xl"
                placeholder="请输入借记卡类别编码"
              />
            </Col>
            <Col span={12}>
              <ProFormText
                name="enrolledLineNum"
                label="已登记打印行数"
                width="xl"
                placeholder="请选择已登记打印行数"
              />
            </Col>
          </Row>
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <ProFormDatePicker
                name="openDt"
                label="开户日期"
                width="xl"
                placeholder="请选择开户日期"
              />
            </Col>
            <Col span={12}>
              <ProFormText
                name="drawInstNo"
                label="开户机构号"
                width="xl"
                placeholder="请输入开户机构号"
              />
            </Col>
          </Row>
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <ProFormDatePicker
                name="applyMakecardDt"
                label="申请制卡日期"
                width="xl"
                placeholder="请输入申请制卡日期"
              />
            </Col>
            <Col span={12}>
              <ProFormDatePicker
                name="mediumValidDt"
                label="介质有效日期"
                width="xl"
                placeholder="请选择介质有效日期"
              />
            </Col>
          </Row>
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <ProFormDatePicker
                name="lastTxDate"
                label="最后交易日期"
                width="xl"
                placeholder="请选择最后交易日期"
              />
            </Col>
            <Col span={12}>
              <ProFormText
                name="zoneVal"
                label="分片值"
                width="xl"
                placeholder="请输入分片值"
                disabled
                rules={[
                  {
                    required: true,
                    message: "请输入分片值！",
                  },
                ]}
              />
            </Col>
          </Row>
        </Form>
      </Spin>
    </Modal>
  );
};

export default MediumForm;
