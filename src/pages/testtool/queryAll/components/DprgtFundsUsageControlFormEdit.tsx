import React, { useEffect, useState, useCallback } from "react";
import { <PERSON><PERSON>, message, <PERSON><PERSON>, Spin } from "antd";
import { 
  ProFormRadio, 
  EditableProTable 
} from "@ant-design/pro-components";
import type { ProColumns } from "@ant-design/pro-components";
import {
  getDprgtFundsUsageControl,
  insertDprgtFundsUsageControl,
  updateDprgtFundsUsageControl,
  deleteDprgtFundsUsageControl,
} from "../service";
import type { TbDprgtFundsUsageControl, PageResponse } from "../data";

/*
 * @Description: 资金用途管控表编辑表单
 * @Author: taylor zhu
 * @Date: 2024-08-03 10:00:00
 */

interface PaginationType {
  current: number;
  pageSize: number;
  total: number;
}

export type DprgtFundsUsageControlFormProps = {
  onCancel: () => void;
  open: boolean;
  data: PageResponse<TbDprgtFundsUsageControl> | null;
};

const DEFAULT_PAGE_SIZE = 10;
const TABLE_SCROLL = { x: 2000 } as const;
const MODAL_BODY_STYLE = {
  maxHeight: "70vh",
  overflow: "auto",
  padding: "24px",
  borderRadius: "10px",
} as const;

const MODAL_STYLE = {
  borderRadius: "10px",
  overflow: "hidden",
} as const;

const INITIAL_PAGINATION: PaginationType = {
  current: 1,
  pageSize: DEFAULT_PAGE_SIZE,
  total: 0,
};

const DprgtFundsUsageControlFormEdit: React.FC<DprgtFundsUsageControlFormProps> = (props) => {
  const [dataSource, setDataSource] = useState<readonly TbDprgtFundsUsageControl[]>([]);
  const [editableKeys, setEditableKeys] = useState<React.Key[]>([]);
  const [isEditing, setIsEditing] = useState(false);
  const [editingRecord, setEditingRecord] = useState<TbDprgtFundsUsageControl | null>(null);
  const [pagination, setPagination] = useState<PaginationType>(INITIAL_PAGINATION);
  const [refresh, setRefresh] = useState(0);
  const [loading, setLoading] = useState(false);
  const [accountInfo, setAccountInfo] = useState<{persInnerAccno?: string, zoneVal?: string} | null>(null);
  const [persInnerAccno, setPersInnerAccno] = useState<string | undefined>(undefined);
  const [zoneVal, setZoneVal] = useState<string | undefined>(undefined);

  useEffect(() => {
    if (props.open) {
      if (props.data?.loading !== undefined) {
        setLoading(props.data.loading);
      } else if (props.data) {
        // If we have data but no loading property, ensure loading is turned off
        setLoading(false);
      }
      
      // 当有data数据时，清空分页并设置数据
      if (props.data?.records) {
        setPagination({
          current: 1,
          pageSize: 10,
          total: props.data.total || props.data.records.length,
        });
        setDataSource(props.data.records);
        
        // 重要：设置账号信息，确保分页功能正常工作
        if (props.data.records.length > 0) {
          const firstRecord = props.data.records[0];
          
          // 从记录中提取账号信息
          setPersInnerAccno(firstRecord.persInnerAccno);
          setZoneVal(firstRecord.zoneVal);
          
          // 同时更新accountInfo，作为备份
          setAccountInfo({
            persInnerAccno: firstRecord.persInnerAccno,
            zoneVal: firstRecord.zoneVal
          });
        }
      }
    } else {
      // 当弹窗关闭时，清空数据和分页
      setDataSource([]);
      setPagination({
        current: 1,
        pageSize: 10,
        total: 0,
      });
      // Also ensure loading is reset when modal closes
      setLoading(false);
    }
  }, [props.open, props.data]);

  const refreshData = useCallback(
    async (current: number = pagination.current, pageSize: number = pagination.pageSize) => {
      // 使用当前状态或accountInfo作为后备
      const accountNo = persInnerAccno || accountInfo?.persInnerAccno;
      const zoneValue = zoneVal || accountInfo?.zoneVal;
      
      if (!accountNo || !zoneValue) {
        // 向用户显示错误消息，便于调试
        console.error('查询参数不完整:', { accountNo, zoneValue });
        message.error('分页查询失败：账号信息不完整，请重新打开弹窗');
        return;
      }
      
      setLoading(true);
      console.log('正在查询数据:', { accountNo, zoneValue, current, pageSize });
      
      try {
        const result = await getDprgtFundsUsageControl(
          accountNo,
          zoneValue,
          'DPRGT_FUNDS_USAGE_CONTROL_ALL_QUERYPAGES',
          {
            current,
            pageSize,
          },
        );
        
        if (result && result.data) {
          const detailData = JSON.parse(result.data);
          const newPagination = {
            current: current,
            pageSize: pageSize,
            total: detailData.total || detailData.records.length,
          };
          
          setPagination(newPagination);
          setDataSource(detailData.records || []);
          console.log('查询成功，数据条数:', detailData.records?.length);
        }
      } catch (error) {
        console.error('获取数据失败:', error);
        message.error('获取资金用途管控表数据失败');
      } finally {
        // Ensure loading state is always cleared
        setLoading(false);
      }
    },
    [persInnerAccno, zoneVal, accountInfo, pagination.current, pagination.pageSize],
  );

  // 定义表格列
  const [position, setPosition] = useState<"top" | "bottom" | "hidden">("bottom");

  const columns: ProColumns<TbDprgtFundsUsageControl>[] = [
    {
      title: "资金用途管控编号",
      dataIndex: "fundsUsageCtrllgNo",
      width: 240,
      fixed: false,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "个人内部账号",
      dataIndex: "persInnerAccno",
      width: 220,
      ellipsis: true,
      align: "center", 
      editable: () => false,
    },
    {
      title: "资金用途管控类型代码",
      dataIndex: "fundsUsageControllTpCd",
      width: 180,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "select",
      valueEnum: {
        "1": { text: "1-个人额度类消费" },
        "2": { text: "2-个人小额贷款" },
        "3": { text: "3-信用卡现金分期" },
        "4": { text: "4-个人信贷水滴还款收单管控" },
      },
    },
    {
        title: "放款机构号列表",
        dataIndex: "disburseInstNoList",
        width: 260,
        ellipsis: true,
        align: "center",
        editable: () => true,
      },
      {
        title: "资金使用顺序代码",
        dataIndex: "fundsUseSeqCd",
        width: 260,
        ellipsis: true,
        align: "center",
        editable: () => true,
        valueType: "select", 
        valueEnum: {
          "1": { text: "1-先活期结算主账户，再贷款专用账户" },
          "2": { text: "2-先贷款专用账户，再活期结算主账户" },
        },
      },
    {
      title: "顺序号",
      dataIndex: "seqNo",
      width: 100,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "余额",
      dataIndex: "bal",
      width: 120,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "可用余额",
      dataIndex: "avalBal",
      width: 120,
      ellipsis: true,
      align: "center",
      editable: () => true,
    },
    {
      title: "是否有效标志",
      dataIndex: "validFlag",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "select", 
        valueEnum: {
          "0": { text: "0-否" },
          "1": { text: "1-是" },
        },
    },
    {
      title: "最后交易日期",
      dataIndex: "lastTxDate",
      width: 140,
      ellipsis: true,
      align: "center",
      editable: () => true,
      valueType: "date",
    },
    {
      title: "分片值",
      dataIndex: "zoneVal",
      width: 160,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "创建时间戳",
      dataIndex: "createStamp",
      width: 220,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "最后修改时间戳",
      dataIndex: "lastModStamp",
      width: 220,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "操作",
      valueType: "option",
      width: 150,
      // fixed: "right",
      align: "center",
      render: (text, record: TbDprgtFundsUsageControl, _, action) => {
        const isRowEditing = record.fundsUsageCtrllgNo
          ? editableKeys.includes(record.fundsUsageCtrllgNo)
          : false;
        return [
          isRowEditing ? (
            <>
              <Button
                key="confirm"
                type="link"
                onClick={async () => {
                  try {
                    if (record.fundsUsageCtrllgNo) {
                      await action?.saveEditable?.(record.fundsUsageCtrllgNo);
                      setIsEditing(false);
                    }
                  } catch (err) {
                    console.error("保存失败:", err);
                  }
                }}
              >
                确定
              </Button>
              <Button
                key="cancel"
                type="link"
                onClick={() => {
                  if (record.fundsUsageCtrllgNo) {
                    action?.cancelEditable?.(record.fundsUsageCtrllgNo);
                    setEditableKeys([]);
                    setIsEditing(false);
                  }
                }}
                style={{ marginLeft: 8 }}
              >
                取消
              </Button>
            </>
          ) : (
            <Button
              key="editable"
              type="link"
              disabled={true}
              onClick={async () => {
                if (!record.fundsUsageCtrllgNo) return;
                const originalRecord = { ...record };
                setEditingRecord(originalRecord);
                setIsEditing(true);
                await action?.startEditable?.(record.fundsUsageCtrllgNo);
              }}
              style={{ padding: 0 }}
            >
              编辑
            </Button>
          ),
          <Button
            key="delete"
            type="link"
            disabled={true}
            onClick={async () => {
              if (!record.fundsUsageCtrllgNo) return;
              Modal.confirm({
                title: "确认删除",
                content: "确定要删除这条记录吗？",
                onOk: async () => {
                  try {
                    const res = await deleteDprgtFundsUsageControl(record);
                    if (res.code === 200) {
                      message.success("删除成功");
                    } else {
                      message.error(res.msg || "删除失败");
                    }
                  } catch (error) {
                    message.error("删除失败");
                  } finally {
                    await refreshData();
                  }
                },
                okText: "确认",
                cancelText: "取消",
              });
            }}
            style={{ marginLeft: 8 }}
          >
            删除
          </Button>,
        ];
      },
    },
  ];

  const handleAdd = async (record: TbDprgtFundsUsageControl) => {
    try {
      // 设置必要的默认字段
      const newRecord = {
        ...record,
        persInnerAccno: persInnerAccno || accountInfo?.persInnerAccno,
        zoneVal: zoneVal || accountInfo?.zoneVal,
        recordStaCd: "1", // 默认有效
      };
      
      setLoading(true);
      const res = await insertDprgtFundsUsageControl(newRecord);
      if (res.code === 200) {
        message.success('添加成功');
        refreshData();
      } else {
        message.error(res.msg || '添加失败');
      }
    } catch (error) {
      console.error('添加失败:', error);
      message.error('添加失败');
    } finally {
      // 添加完成后重置状态
      setLoading(false);
      setRefresh(prev => prev + 1);
      setEditableKeys([]);
      setIsEditing(false);
    }
  };

  return (
    <Modal
      width={1200}
      title="查询资金用途管控表信息"
      open={props.open}
      destroyOnClose
      onCancel={props.onCancel}
      footer={null}
      bodyStyle={MODAL_BODY_STYLE}
      style={MODAL_STYLE}
    >
      <Spin spinning={loading} tip="加载数据中..." size="large">
        <div style={{ marginBottom: 16 }}>
          <p>
            数据状态: {loading ? (
              <span>
                <Spin size="small" style={{ marginRight: 8 }} />
                加载中...
              </span>
            ) : (
              `已加载 ${dataSource.length} 条记录, 总共 ${pagination.total} 条`
            )}
            {accountInfo && (
              <span style={{ marginLeft: 8 }}>
                | 个人内部账号: {accountInfo.persInnerAccno}
              </span>
            )}
          </p>
        </div>
        
        <EditableProTable<TbDprgtFundsUsageControl>
          key={refresh}
          rowKey={(record) => record.fundsUsageCtrllgNo || ""}
          headerTitle="资金用途管控表信息"
          scroll={TABLE_SCROLL}
          loading={loading}
          toolBarRender={() => [
            <ProFormRadio.Group
              key="positionRender"
              fieldProps={{
                value: position,
                onChange: (e: any) => setPosition(e.target.value),
              }}
              options={[
                { label: "添加到顶部", value: "top" },
                { label: "添加到底部", value: "bottom" },
                { label: "隐藏", value: "hidden" },
              ]}
            />,
          ]}
          recordCreatorProps={{
            position: position === 'hidden' ? 'bottom' : position,
            record: (index, dataSource) => {
              // 直接创建新记录，不检查是否有正在编辑的行
              return {
                persInnerAccno: persInnerAccno || accountInfo?.persInnerAccno,
                fundsUsageControllTpCd: "1", // 默认为个人额度类消费
                fundsUseSeqCd: "1", // 默认为先活期结算主账户，再贷款专用账户
                zoneVal: zoneVal || accountInfo?.zoneVal,
                recordStaCd: "1", // 默认有效
                lastTxDate: new Date().toISOString().split('T')[0], // 最后交易日期
              } as TbDprgtFundsUsageControl;
            },
            creatorButtonText: '添加一行数据',
            newRecordType: 'dataSource',
          }}
          columns={columns}
          request={async (params, sorter, filter) => {
            // 直接返回当前数据源，不进行额外的API调用
            return {
              data: dataSource as TbDprgtFundsUsageControl[],
              total: pagination.total,
              success: true,
            };
          }}
          value={dataSource}
          onChange={setDataSource}
          editable={{
            type: 'multiple', // 支持同时编辑多行
            editableKeys,
            onSave: async (rowKey, data, row) => {
              try {
                // 如果是新添加的记录则调用新增接口
                if (String(rowKey).startsWith('NEW_')) {
                  await handleAdd(data);
                } else {
                  // 否则调用更新接口
                  setLoading(true);
                  const res = await updateDprgtFundsUsageControl(data);
                  if (res.code === 200) {
                    message.success('更新成功');
                    refreshData();
                  } else {
                    message.error(res.msg || '更新失败');
                  }
                }
              } catch (error) {
                console.error('保存失败:', error);
                message.error('保存失败');
              } finally {
                // 保存后从编辑状态中移除当前行
                setEditableKeys(keys => keys.filter(key => key !== rowKey));
                if (editableKeys.length <= 1) {
                  setIsEditing(false);
                }
                setLoading(false);
              }
            },
            onChange: setEditableKeys,
            onCancel: async (key) => {
              // 取消编辑时只清除当前行的编辑状态
              setEditableKeys(keys => keys.filter(k => k !== key));
              if (editableKeys.length <= 1) {
                setIsEditing(false);
              }
              return true;
            },
            actionRender: (row, config, dom) => [dom.save, dom.cancel],
          }}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条`,
            onChange: async (page, pageSize) => {
              console.log('分页改变:', { page, pageSize, currentPage: pagination.current, isEditing });
              
              if (isEditing) {
                message.warn('请先完成编辑操作再切换页面');
                return;
              }
              
              // 更新分页状态
              setPagination({
                ...pagination,
                current: page,
                pageSize: pageSize,
              });
              
              // 直接调用刷新数据，不做额外判断
              await refreshData(page, pageSize);
            },
          }}
        />
      </Spin>
    </Modal>
  );
};

export default DprgtFundsUsageControlFormEdit; 