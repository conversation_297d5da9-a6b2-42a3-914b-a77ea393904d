import React, { useEffect, useState, useCallback } from "react";
import { Mo<PERSON>, message, <PERSON><PERSON>, Spin, Tooltip } from "antd";
import { InfoCircleOutlined } from "@ant-design/icons";
import { ProFormRadio, EditableProTable } from "@ant-design/pro-components";
import type { ProColumns } from "@ant-design/pro-components";
import {
  getDprgtCustLimtServ,
  insertDprgtCustLimtServ,
  updateDprgtCustLimtServ,
  deleteDprgtCustLimtServ,
} from "../service";
import type { TbDprgtCustLimtServ, PageResponse } from "../data";

/*
 * @Description: 客户限制服务登记簿列表编辑表单
 * @Author: taylor zhu
 * @Date: 2024-08-03 10:00:00
 */

interface PaginationType {
  current: number;
  pageSize: number;
  total: number;
}

export type TbDprgtCustLimtServFormProps = {
  onCancel: () => void;
  open: boolean;
  data: PageResponse<TbDprgtCustLimtServ> | null;
};

const DEFAULT_PAGE_SIZE = 10;
const TABLE_SCROLL = { x: 2000 } as const;
const MODAL_BODY_STYLE = {
  maxHeight: "70vh",
  overflow: "auto",
  padding: "24px",
  borderRadius: "10px",
} as const;

const MODAL_STYLE = {
  borderRadius: "10px",
  overflow: "hidden",
} as const;

const INITIAL_PAGINATION: PaginationType = {
  current: 1,
  pageSize: DEFAULT_PAGE_SIZE,
  total: 0,
};

const DpRgtCustLimtServFormEdit: React.FC<TbDprgtCustLimtServFormProps> = (
  props
) => {
  const [dataSource, setDataSource] = useState<readonly TbDprgtCustLimtServ[]>(
    []
  );
  const [editableKeys, setEditableKeys] = useState<React.Key[]>([]);
  const [isEditing, setIsEditing] = useState(false);
  const [editingRecord, setEditingRecord] =
    useState<TbDprgtCustLimtServ | null>(null);
  const [pagination, setPagination] =
    useState<PaginationType>(INITIAL_PAGINATION);
  const [refresh, setRefresh] = useState(0);
  const [loading, setLoading] = useState(false);
  const [dprgtCustLimtServInfo, setDprgtCustLimtServInfo] = useState<{
    custNo?: string;
    zoneVal?: string;
  } | null>(null);
  const [custNo, setCustNo] = useState<string | undefined>(undefined);
  const [zoneVal, setZoneVal] = useState<string | undefined>(undefined);

  useEffect(() => {
    if (props.open) {
      if (props.data?.loading !== undefined) {
        setLoading(props.data.loading);
      } else if (props.data) {
        // If we have data but no loading property, ensure loading is turned off
        setLoading(false);
      }

      // 当有data数据时，清空分页并设置数据
      if (props.data?.records) {
        setPagination({
          current: 1,
          pageSize: 10,
          total: props.data.total || props.data.records.length,
        });
        setDataSource(props.data.records);

        // 重要：设置账号信息，确保分页功能正常工作
        if (props.data.records.length > 0) {
          const firstRecord = props.data.records[0];

          // 从记录中提取账号信息
          setCustNo(firstRecord.custNo);
          setZoneVal(firstRecord.zoneVal);

          // 同时更新accountInfo，作为备份
          setDprgtCustLimtServInfo({
            custNo: firstRecord.custNo,
            zoneVal: firstRecord.zoneVal,
          });
        }
      }
    } else {
      // 当弹窗关闭时，清空数据和分页
      setDataSource([]);
      setPagination({
        current: 1,
        pageSize: 10,
        total: 0,
      });
      // Also ensure loading is reset when modal closes
      setLoading(false);
    }
  }, [props.open, props.data]);

  const refreshData = useCallback(
    async (
      current: number = pagination.current,
      pageSize: number = pagination.pageSize
    ) => {
      // 使用当前状态或dprgtCustLimtServInfo作为后备
      const custNo = dprgtCustLimtServInfo?.custNo;
      const zoneValue = dprgtCustLimtServInfo?.zoneVal;

      if (!custNo || !zoneValue) {
        // 向用户显示错误消息，便于调试
        console.error("查询参数不完整:", { custNo, zoneValue });
        message.error("分页查询失败：账号信息不完整，请重新打开弹窗");
        return;
      }

      setLoading(true);
      console.log("正在查询数据:", { custNo, zoneValue, current, pageSize });

      try {
        const result = await getDprgtCustLimtServ(
          custNo,
          zoneValue,
          "DP_RGT_CUST_LIMIT_SERV_ALL_QUERYPAGES",
          {
            current,
            pageSize,
          }
        );

        if (result && result.data) {
          const detailData = JSON.parse(result.data);
          const newPagination = {
            current: current,
            pageSize: pageSize,
            total: detailData.total || detailData.records.length,
          };

          setPagination(newPagination);
          setDataSource(detailData.records || []);
          console.log("查询成功，数据条数:", detailData.records?.length);
        }
      } catch (error) {
        console.error("获取数据失败:", error);
        message.error("获取客户限制服务登记簿列表数据失败");
      } finally {
        // Ensure loading state is always cleared
        setLoading(false);
      }
    },
    [custNo, zoneVal, pagination.current, pagination.pageSize]
  );

  // 定义表格列
  const [position, setPosition] = useState<"top" | "bottom" | "hidden">(
    "bottom"
  );

  const columns: ProColumns<TbDprgtCustLimtServ>[] = [
    {
      title: "交易日期",
      dataIndex: "txDate",
      width: 160,
      ellipsis: true,
      align: "center",
      editable: () => false,
      valueType: "date",
      render: (_, entity) => entity.txDate || "-",
    },
    {
      title: "全局业务跟踪号",
      dataIndex: "globalBusiTrackNo",
      width: 260,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "子交易序号",
      dataIndex: "subtxNo",
      width: 260,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "客户编号",
      dataIndex: "custNo",
      width: 160,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "限制类型代码",
      dataIndex: "limtTpCd",
      width: 220,
      ellipsis: true,
      align: "center",
      editable: () => false,
      valueType: "select",
      valueEnum: {
        "60": { text: "60-强化尽职调查管控" },
        "71": { text: "71-惩戒客户" },
      },
    },
    {
      title: "限制终止日期",
      dataIndex: "limtEndDt",
      width: 160,
      ellipsis: true,
      align: "center",
      editable: () => false,
      valueType: "date",
      render: (_, entity) => entity.limtEndDt || "-",
    },
    {
      title: "限制原因代码",
      dataIndex: "limtReasnCd",
      width: 260,
      ellipsis: true,
      align: "center",
      editable: () => false,
      valueType: "select",
      valueEnum: {
        "6001": { text: "6001-存在洗钱风险" },
        "7101": { text: "7101-惩戒客户(联合惩戒)" },
      },
    },
    {
      title: "限制原因补充描述",
      dataIndex: "limtReasonSupplemDesc",
      width: 260,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: () => (
        <Tooltip
          title={
            <div style={{ width: "100%" }}>
              <p>强化尽职调查管控措施标志码</p>
              <ul>
                <li>
                  <strong>第1位 柜员采取客户级非柜面渠道专项管控：</strong>
                </li>
                <li>0-无关</li>
                <li>1-限制非柜面交易</li>
                <li>2-控制非柜面交易规模</li>
                <li>3-控制非柜面交易频率</li>
                <li>4-控制非柜面交易规模和频率</li>
              </ul>
            </div>
          }
          placement="top"
          overlayClassName="custom-tooltip"
          color="#fff"
          overlayStyle={{ minWidth: "350px" }}
        >
          <span>
            强化尽职调查管控措施标志码{" "}
            <InfoCircleOutlined
              style={{ marginLeft: 4, color: "#1890ff" }}
            />
          </span>
        </Tooltip>
      ),
      // title: "强化尽职调查管控措施标志码",
      dataIndex: "stgnFdiCtrllgActnFlagCd",
      width: 260,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "客户名称",
      dataIndex: "custNm",
      width: 160,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "个人证件类型代码",
      dataIndex: "perCertTpCd",
      width: 180,
      ellipsis: true,
      align: "center",
      editable: () => false,
      valueType: "select",
      valueEnum: {
        "1010": { text: "1010-居民身份证" },
        "1011": { text: "1011-临时居民身份证" },
        "1020": { text: "1020-军人身份证件" },
        "1021": { text: "1021-士兵证" },
        "1022": { text: "1022-军官证" },
        "1023": { text: "1023-文职干部证" },
        "1024": { text: "1024-军官退休证" },
        "1025": { text: "1025-文职干部退休证" },
        "1030": { text: "1030-武警身份证件" },
        "1031": { text: "1031-武警士兵证" },
        "1032": { text: "1032-警官证" },
        "1033": { text: "1033-武警文职干部证" },
        "1034": { text: "1034-武警军官退休证" },
        "1035": { text: "1035-武警文职干部退休证" },
        "1040": { text: "1040-户口簿" },
        "1050": { text: "1050-中国护照" },
        "1051": { text: "1051-外国护照" },
        "1060": { text: "1060-学生证" },
        "1070": { text: "1070-港澳居民来往内地通行证" },
        "1071": { text: "1071-往来港澳通行证" },
        "1080": { text: "1080-台湾居民来往大陆通行证" },
        "1090": { text: "1090-执行公务证" },
        "1100": { text: "1100-机动车驾驶证" },
        "1110": { text: "1110-社会保障卡" },
        "1120": { text: "1120-外国人居留证" },
        "1121": { text: "1121-外国人永久居留证" },
        "1130": { text: "1130-旅行证件" },
        "1140": { text: "1140-香港居民身份证" },
        "1150": { text: "1150-澳门居民身份证" },
        "1160": { text: "1160-台湾居民身份证" },
        "1170": { text: "1170-边民证" },
        "1180": { text: "1180-港澳台居民居住证" },
        "1181": { text: "1181-港澳居民居住证" },
        "1182": { text: "1182-台湾居民居住证" },
        "1190": { text: "1190-外国身份证" },
        "1998": { text: "1998-其他（原98类）" },
        "1999": { text: "1999-其他证件（个人）" },
      },
    },
    {
      title: "个人证件号码",
      dataIndex: "personalCertNo",
      width: 260,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "交易时间",
      dataIndex: "txTime",
      width: 160,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "交易机构号",
      dataIndex: "txInstNo",
      width: 160,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "交易柜员号",
      dataIndex: "txTellerNo",
      width: 160,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "授权柜员号",
      dataIndex: "authTellerNo",
      width: 160,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "渠道种类代码",
      dataIndex: "chnlKindCode",
      width: 260,
      ellipsis: true,
      align: "center",
      editable: () => false,
      valueType: "select",
      valueEnum: {
        "01": { text: "01-网点柜面" },
        "10": { text: "10-网上银行" },
        "12": { text: "12-个人网银" },
        "13": { text: "13-电视银行" },
        "14": { text: "14-电话银行" },
        "15": { text: "15-手机银行" },
        "16": { text: "16-企业网银" },
        "17": { text: "17-自助设备" },
        "18": { text: "18-POS" },
        "20": { text: "20-超级网银" },
        "21": { text: "21-大小额支付" },
        "22": { text: "22-银联前置" },
        "24": { text: "24-管理端" },
        "25": { text: "25-交易端" },
        "26": { text: "26-商易通" },
        "27": { text: "27-助农通" },
        "29": { text: "29-外部系统" },
        "30": { text: "30-系统自动" },
        "31": { text: "31-电子汇兑系统" },
        "32": { text: "32-理财规划终端" },
        "34": { text: "34-网汇通" },
        "35": { text: "35-同城支付" },
        "36": { text: "36-移动终端-TSM（可信服务管理）" },
        "37": { text: "37-移动终端-移动展业" },
        "38": { text: "38-直销银行" },
        "39": { text: "39-短信" },
        "40": { text: "40-专属APP" },
        "41": { text: "41-第三方线上渠道" },
        "43": { text: "43-智能柜员机（ITM）" },
        "42": { text: "42-国际支付前置" },
        "44": { text: "44-邮储经营" },
        "45": { text: "45-银银前置系统" },
        "46": { text: "46-U链供应链" },
        "47": { text: "47-油料保障结算系统" },
        "48": { text: "48-银企直联" },
        "91": { text: "91-微信银行" },
        "99": { text: "99-其他" },
      },
    },
    {
      title: "发起系统或组件编码",
      dataIndex: "startSysOrCmptNo",
      width: 160,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "生效状态代码",
      dataIndex: "effStaCd",
      width: 160,
      ellipsis: true,
      align: "center",
      editable: () => false,
      valueType: "select",
      valueEnum: {
        "1": { text: "1-已生效" },
        "2": { text: "2-已失效" },
      },
    },
    {
      title: "解除日期",
      dataIndex: "relsDt",
      width: 160,
      ellipsis: true,
      align: "center",
      editable: () => false,
      valueType: "date",
      render: (_, entity) => entity.relsDt || "-",
    },
    {
      title: "解除时间",
      dataIndex: "relsTime",
      width: 160,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "解除全局业务跟踪号",
      dataIndex: "relsGloTracNo",
      width: 260,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "解除子交易序号",
      dataIndex: "relsSubtxNo",
      width: 260,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "解除执行文号",
      dataIndex: "relsFileNo",
      width: 160,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "解除有权机关名称",
      dataIndex: "relsPowInstName",
      width: 160,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "解除限制原因代码",
      dataIndex: "relsLimtReasnCd",
      width: 160,
      ellipsis: true,
      align: "center",
      editable: () => false,
      valueType: "select",
      valueEnum: {
        "01": { text: "01-客户申请解除" },
        "02": { text: "02-银行申请解除" },
        "03": { text: "03-有权机关申请解除" },
        "04": { text: "04-强制销户默认解除" },
        "05": { text: "05-系统自动解除" },
        "06": { text: "06-已核实后解除" },
        "07": { text: "07-线上预约解除" },
        "08": { text: "08-销户后系统自动解除" },
        "09": { text: "09-非同一人解除" },
      },
    },
    {
      title: "解除限制原因补充描述",
      dataIndex: "relsLimtReasonSupplemDesc",
      width: 160,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "解除机构号",
      dataIndex: "relsInstNo",
      width: 160,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "解除柜员号",
      dataIndex: "relsTellerNo",
      width: 160,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "解除授权柜员号",
      dataIndex: "relsAuthTellerNo",
      width: 160,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "最后交易日期",
      dataIndex: "lastTxDate",
      width: 160,
      ellipsis: true,
      align: "center",
      editable: () => false,
      valueType: "date",
      render: (_, entity) => entity.lastTxDate || "-",
    },
    {
      title: "分片值",
      dataIndex: "zoneVal",
      width: 160,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "创建时间戳",
      dataIndex: "createStamp",
      width: 160,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "最后修改时间戳",
      dataIndex: "lastModStamp",
      width: 160,
      ellipsis: true,
      align: "center",
      editable: () => false,
    },
    {
      title: "操作",
      valueType: "option",
      width: 150,
      align: "center",
      render: (text, record: TbDprgtCustLimtServ, _, action) => {
        const isRowEditing = record.custNo
          ? editableKeys.includes(record.custNo)
          : false;
        return [
          isRowEditing ? (
            <>
              <Button
                key="confirm"
                type="link"
                onClick={async () => {
                  try {
                    if (record.custNo) {
                      await action?.saveEditable?.(record.custNo);
                      setIsEditing(false);
                    }
                  } catch (err) {
                    console.error("保存失败:", err);
                  }
                }}
              >
                确定
              </Button>
              <Button
                key="cancel"
                type="link"
                onClick={() => {
                  if (record.custNo) {
                    action?.cancelEditable?.(record.custNo);
                    setEditableKeys([]);
                    setIsEditing(false);
                  }
                }}
                style={{ marginLeft: 8 }}
              >
                取消
              </Button>
            </>
          ) : (
            <Button
              key="editable"
              type="link"
              //   disabled={isEditing}
              disabled={true}
              onClick={async () => {
                if (!record.custNo) return;
                const originalRecord = { ...record };
                setEditingRecord(originalRecord);
                setIsEditing(true);
                await action?.startEditable?.(record.custNo);
              }}
              style={{ padding: 0 }}
            >
              编辑
            </Button>
          ),
          <Button
            key="delete"
            type="link"
            // disabled={isEditing}
            disabled={true}
            onClick={async () => {
              if (!record.custNo) return;
              Modal.confirm({
                title: "确认删除",
                content: "确定要删除这条记录吗？",
                onOk: async () => {
                  try {
                    const res = await deleteDprgtCustLimtServ(record);
                    if (res.code === 200) {
                      message.success("删除成功");
                    } else {
                      message.error(res.msg || "删除失败");
                    }
                  } catch (error) {
                    message.error("删除失败");
                  } finally {
                    await refreshData();
                  }
                },
                okText: "确认",
                cancelText: "取消",
              });
            }}
            style={{ marginLeft: 8 }}
          >
            删除
          </Button>,
        ];
      },
    },
  ];

  const handleAdd = async (record: TbDprgtCustLimtServ) => {
    try {
      // 设置必要的默认字段
      const newRecord = {
        ...record,
        custNo: custNo || dprgtCustLimtServInfo?.custNo,
        zoneVal: zoneVal || dprgtCustLimtServInfo?.zoneVal,
        recordStaCd: "1", // 默认有效
      };

      setLoading(true);
      const res = await insertDprgtCustLimtServ(newRecord);
      if (res.code === 200) {
        message.success("添加成功");
        refreshData();
      } else {
        message.error(res.msg || "添加失败");
      }
    } catch (error) {
      console.error("添加失败:", error);
      message.error("添加失败");
    } finally {
      // 添加完成后重置状态
      setLoading(false);
      setRefresh((prev) => prev + 1);
      setEditableKeys([]);
      setIsEditing(false);
    }
  };

  return (
    <Modal
      width={1200}
      title="查询客户限制服务登记簿信息"
      open={props.open}
      destroyOnClose
      onCancel={props.onCancel}
      footer={null}
      bodyStyle={MODAL_BODY_STYLE}
      style={MODAL_STYLE}
    >
      <Spin spinning={loading} tip="加载数据中..." size="large">
        <div style={{ marginBottom: 16 }}>
          <p>
            数据状态:{" "}
            {loading ? (
              <span>
                <Spin size="small" style={{ marginRight: 8 }} />
                加载中...
              </span>
            ) : (
              `已加载 ${dataSource.length} 条记录, 总共 ${pagination.total} 条`
            )}
            {dprgtCustLimtServInfo && (
              <span style={{ marginLeft: 8 }}>
                | 客户号: {dprgtCustLimtServInfo.custNo}
              </span>
            )}
          </p>
        </div>

        <EditableProTable<TbDprgtCustLimtServ>
          key={refresh}
          rowKey={(record) => record.custNo || ""}
          headerTitle="客户限制服务登记簿信息"
          scroll={TABLE_SCROLL}
          loading={loading}
          toolBarRender={() => [
            <ProFormRadio.Group
              key="positionRender"
              fieldProps={{
                value: position,
                onChange: (e: any) => setPosition(e.target.value),
              }}
              options={[
                { label: "添加到顶部", value: "top" },
                { label: "添加到底部", value: "bottom" },
                { label: "隐藏", value: "hidden" },
              ]}
            />,
          ]}
          recordCreatorProps={{
            position: position === "hidden" ? "bottom" : position,
            record: (index, dataSource) => {
              // 直接创建新记录，不检查是否有正在编辑的行
              return {
                persInnerAccno: custNo || dprgtCustLimtServInfo?.custNo,
                zoneVal: zoneVal || dprgtCustLimtServInfo?.zoneVal,
                lastTxDate: new Date().toISOString().split("T")[0], // 最后交易日期
              } as TbDprgtCustLimtServ;
            },
            creatorButtonText: "添加一行数据",
            newRecordType: "dataSource",
          }}
          columns={columns}
          request={async (params, sorter, filter) => {
            // 直接返回当前数据源，不进行额外的API调用
            return {
              data: dataSource as TbDprgtCustLimtServ[],
              total: pagination.total,
              success: true,
            };
          }}
          value={dataSource}
          onChange={setDataSource}
          editable={{
            type: "multiple", // 支持同时编辑多行
            editableKeys,
            onSave: async (rowKey, data, row) => {
              try {
                // 如果是新添加的记录则调用新增接口
                if (String(rowKey).startsWith("NEW_")) {
                  await handleAdd(data);
                } else {
                  // 否则调用更新接口
                  setLoading(true);
                  const res = await updateDprgtCustLimtServ(data);
                  if (res.code === 200) {
                    message.success("更新成功");
                    refreshData();
                  } else {
                    message.error(res.msg || "更新失败");
                  }
                }
              } catch (error) {
                console.error("保存失败:", error);
                message.error("保存失败");
              } finally {
                // 保存后从编辑状态中移除当前行
                setEditableKeys((keys) => keys.filter((key) => key !== rowKey));
                if (editableKeys.length <= 1) {
                  setIsEditing(false);
                }
                setLoading(false);
              }
            },
            onChange: setEditableKeys,
            onCancel: async (key) => {
              // 取消编辑时只清除当前行的编辑状态
              setEditableKeys((keys) => keys.filter((k) => k !== key));
              if (editableKeys.length <= 1) {
                setIsEditing(false);
              }
              return true;
            },
            actionRender: (row, config, dom) => [dom.save, dom.cancel],
          }}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条`,
            onChange: async (page, pageSize) => {
              console.log("分页改变:", {
                page,
                pageSize,
                currentPage: pagination.current,
                isEditing,
              });

              if (isEditing) {
                message.warn("请先完成编辑操作再切换页面");
                return;
              }

              // 仅当页码或页大小变化时才刷新数据
              if (
                page !== pagination.current ||
                pageSize !== pagination.pageSize
              ) {
                // 更新分页状态
                setPagination({
                  ...pagination,
                  current: page,
                  pageSize: pageSize,
                });

                // 直接调用刷新数据，不做额外判断
                await refreshData(page, pageSize);
              }
            },
          }}
        />
      </Spin>
    </Modal>
  );
};

export default DpRgtCustLimtServFormEdit;
