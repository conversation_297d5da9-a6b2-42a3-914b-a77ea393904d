export interface DataItem {
  mediumNo?: string;
  // applyMakecardDt?: string;
  // attrbuteFlagCd?: string;
  // cardKinCd?: string;
  // cardMediumCode?: string;
  // cardSeqNo?: string;
  // cateFlagCd?: string;
  // createStamp?: string;
  // crtModeFlagCd?: string;
  // custNo?: string;
  // dcardCateNo?: string;
  // drawInstNo?: string;
  // encPwd?: string;
  // enrolledLineNum?: string;
  // lastModStamp?: string;
  // lastTxDate?: string;
  // mainContrNo?: string;
  // mediumPrintNo?: string;
  // mediumTpCd?: string;
  // mediumValidDt?: string;
  // openDt?: string;
  // relMediumTypeFlagCd?: string;
  // statusFlagCd?: string;
  zoneVal?: string;
  prodtContractNo?: string;
  persInnerAccno?: string;
}

interface Pagination {
  total: number;
  pageSize: number;
  current: number;
  onChange?: (page: number, pageSize: number) => void;
}

export interface TabData {
  key: string;
  tab: string;
  data: any[];
  columns: any[];
  pagination?: Pagination; // 添加可选的分页属性
}

export interface FlagDetail {
  cardStatus: string;
  lossStatus: string;
  transferStatus: string;
  freezeStatus: string;
  activeStatus: string;
}

// 介质主档表
export interface TbDpmstMedium {
  mediumNo?: string;
  applyMakecardDt?: string;
  attrbuteFlagCd?: string;
  cardKindCd?: string;
  cardMediumCode?: string;
  cardSeqNo?: string;
  categFlagCd?: string;
  createStamp?: string;
  crtModeFlagCd?: string;
  custNo?: string;
  dcardCategNo?: string;
  drawInstNo?: string;
  encPwd?: string;
  enrolledLineNum?: string;
  lastModStamp?: string;
  lastTxDate?: string;
  mainContrNo?: string;
  mediumPrintNo?: string;
  mediumTpCd?: string;
  mediumValidDt?: string;
  openDt?: string;
  relMediumTypeFlagCd?: string;
  statusFlagCd?: string;
  zoneVal?: string;
}

// 活期合约主档表
export interface TbDpmstCurtCont {
  prodtContractNo?: string;
  baseProdtNo?: string;
  vendibiProdtNo?: string;
  vendibiProdtVerNo?: string;
  savTypeMclassCode?: string;
  currCode?: string;
  agrNtaxRate?: string;
  contrSignDt?: string;
  contrSignTime?: string;
  signContInstNo?: string;
  signChnKindCd?: string;
  contrCacContDt?: string;
  contrCacContTime?: string;
  cacContInstNo?: string;
  lastMobilityTxDate?: string;
  dpContrTpCd?: string;
  persDepAccKindCd?: string;
  specContrTpCd?: string;
  prodtContractName?: string;
  prodtContractDesc?: string;
  mainContrFlag?: string;
  mContrFlagCd?: string;
  maxSaccnoSeqNo?: string;
  contrCtrlFlagCd?: string;
  contrAttrFgCd?: string;
  contrStaFgCd?: string;
  lastTxDate?: string;
  createStamp?: string;
  lastModStamp?: string;
  zoneVal?: string;
}

// 活期账户主档表
export interface TbDpmstCurtAcc {
  persInnerAccno?: string;
  custNo?: string;
  persDepAccTpCd?: string;
  prodtContractNo?: string;
  currCode?: string;
  cashExgVatgCd?: string;
  termUnitCode?: string;
  openaccDate?: string;
  openAccInstNo?: string;
  clsaccDate?: string;
  clsAccInstNo?: string;
  openaccAmt?: string;
  accBal?: string;
  accAvalBal?: string;
  calIntAccu?: string;
  intRate?: string;
  intRateNo?: string;
  bgnIntDate?: string;
  lastTmTxDt?: string;
  lastdayBal?: string;
  curDtlNo?: string;
  accStatusFlagCd?: string;
  accFlagCd?: string;
  campTellerNo?: string;
  lastTxDate?: string;
  createStamp?: string;
  lastModStamp?: string;
  zoneVal?: string;
}

// 定期合约主档表
export interface TbDpmstFixCont {
  prodtContractNo?: string;
  baseProdtNo?: string;
  vendibiProdtNo?: string;
  vendibiProdtVerNo?: string;
  savTypeMclassCode?: string;
  currCode?: string;
  cashExgVatgCd?: string;
  contrSignDt?: string;
  contrSignTime?: string;
  contrDueDate?: string;
  termUnitCode?: string;
  contractTerm?: string;
  signContInstNo?: string;
  signChnKindCd?: string;
  contrCacContDt?: string;
  contrCacContTime?: string;
  dpContrTpCd?: string;
  cacContInstNo?: string;
  lastMobilityTxDate?: string;
  prodtContractName?: string;
  prodtContractDesc?: string;
  mainContrFlag?: string;
  mContrFlagCd?: string;
  maxSaccnoSeqNo?: string;
  contrCtrlFlagCd?: string;
  contrAttrFgCd?: string;
  contrStaFgCd?: string;
  lastTxDate?: string;
  createStamp?: string;
  lastModStamp?: string;
  zoneVal?: string;
}
// 定期账户主档表
export interface TbDpmstFixAcc {
  persInnerAccno?: string;
  custNo?: string;
  persDepAccTpCd?: string;
  prodtContractNo?: string;
  currCode?: string;
  cashExgVatgCd?: string;
  termUnitCode?: string;
  deptTerm?: string;
  openaccDate?: string;
  openAccInstNo?: string;
  clsaccDate?: string;
  clsAccInstNo?: string;
  openaccAmt?: string;
  accBal?: string;
  accAvalBal?: string;
  calIntAccu?: string;
  intRate?: string;
  intRateNo?: string;
  fixDeptOvdueIntRateNo?: string;
  prodTpNo?: string;
  bgnIntDate?: string;
  dueDate?: string;
  prtExtdTimes?: string;
  lastTmTxDt?: string;
  lastdayBal?: string;
  curDtlNo?: string;
  execCycleCd?: string;
  execCycVal?: string;
  accStatusFlagCd?: string;
  accFlagCd?: string;
  campTellerNo?: string;
  lastTxDate?: string;
  createStamp?: string;
  lastModStamp?: string;
  zoneVal?: string;
}
// 主从合约关系表
export interface TbDprgtMaslaveContRelat {
  mainContrNo?: string;
  saccnoSeqNo?: string;
  baseProdtNo?: string;
  vendibiProdtNo?: string;
  currCode?: string;
  cashExgVatgCd?: string;
  dpContrTpCd?: string;
  relContrNo?: string;
  relCategFgCd?: string;
  zoneVal?: string;
}
// 客户信息表
export interface CustInfo {
  custNo?: string;
  custNm?: string;
  perCertTpCd?: string;
  personalCertNo?: string;
  certValidDlineDate?: string;
  ordinaryCardNumShee?: string;
  specCardNumShee?: string;
  curtAccnum?: string;
  iLaccNum?: string;
  iiLaccNum?: string;
  iiiLaccNum?: string;
  mobileNo?: string;
  custIdentityToVerifyFlag?: string;
  sameMobilenoMcustFlag?: string;
  nineItmInfoIncompFlag?: string;
  custInfoUnfitFlag?: string;
  shardingId?: string;
}

// 活期产品合约账户交易明细表
export interface TbDpdtlCurtAcc {
  persInnerAccno?: string;
  txDate?: string;
  globalBusiTrackNo?: string;
  subtxNo?: string;
  dtlSeqNo?: string;
  prodtContractNo?: string;
  mediumNo?: string;
  currCode?: string;
  cashExgVatgCd?: string;
  servNo?: string;
  chnlKindCode?: string;
  startSysOrCmptNo?: string;
  dwFlagCode?: string;
  cashTranFlagCode?: string;
  vendibiProdtNo?: string;
  txAmt?: string;
  accBal?: string;
  subCardAvalLimit?: string;
  txamtTpCd?: string;
  deptTxTpCd?: string;
  ibankFlag?: string;
  agenterAgentFlag?: string;
  txOpsBankNo?: string;
  txOpsBankNm?: string;
  txOpsAccno?: string;
  txOpsName?: string;
  transInMobileNo?: string;
  opInstNo?: string;
  // txTime?: string;
  txInstNo?: string;
  txTellerNo?: string;
  reviewTellerNo?: string;
  authTellerNo?: string;
  merTypeNo?: string;
  merNo?: string;
  merDesc?: string;
  terminalFlagCd?: string;
  terminalNo?: string;
  summNo?: string;
  summName?: string;
  accTxFlagCd?: string;
  execStepSeqNo?: string;
  txRemark?: string;
  lastTxDate?: string;
  recordStaCd?: string;
  zoneVal?: string;
  // createStamp?: string;
  // lastModStamp?: string;
}

// 定期产品合约账户交易明细表
export interface TbDpdtlFixAcc {
  persInnerAccno?: string;
  txDate?: string;
  globalBusiTrackNo?: string;
  subtxNo?: string;
  dtlSeqNo?: string;
  prodtContractNo?: string;
  mediumNo?: string;
  currCode?: string;
  cashExgVatgCd?: string;
  servNo?: string;
  termUnitCode?: string;
  deptTerm?: string;
  bgnIntDate?: string;
  dueDate?: string;
  chnlKindCode?: string;
  startSysOrCmptNo?: string;
  dwFlagCode?: string;
  cashTranFlagCode?: string;
  txAmt?: string;
  intRate?: string;
  accBal?: string;
  txamtTpCd?: string;
  deptTxTpCd?: string;
  agenterAgentFlag?: string;
  txOpsBankNo?: string;
  txOpsBankNm?: string;
  txOpsAccno?: string;
  txOpsName?: string;
  txTime?: string;
  txInstNo?: string;
  txTellerNo?: string;
  reviewTellerNo?: string;
  authTellerNo?: string;
  terminalFlagCd?: string;
  terminalNo?: string;
  summNo?: string;
  summName?: string;
  tranDepFlag?: string;
  accTxFlagCd?: string;
  oldMediumNo?: string;
  oldPersInnerAccno?: string;
  oldSaccnoSeqNo?: string;
  newMediumNo?: string;
  newOpenPersInnerAccno?: string;
  newSaccnoSeqNo?: string;
  execStepSeqNo?: string;
  txRemark?: string;
  lastTxDate?: string;
  recordStaCd?: string;
  zoneVal?: string;
  jobZoneNo?: string;
  optimistLockVerNo?: string;
  createStamp?: string;
  lastModStamp?: string;
}

// 个人存款生命周期表
export interface TbDprgtPersDepLifeCyc {
  enrollDate?: string;
  globalBusiTrackNo?: string;
  subtxNo?: string;
  mediumNo?: string;
  saccnoSeqNo?: string;
  contrAccTpCd?: string;
  dpContrTpCd?: string;
  execLvlFlagCd?: string;
  statChgReasnCd?: string;
  prodtContractNo?: string;
  persInnerAccno?: string;
  vendibiProdtNo?: string;
  vendibiProdtName?: string;
  baseProdtNo?: string;
  savTypeMclassCode?: string;
  categFlagCd?: string;
  currCode?: string;
  cashExgVatgCd?: string;
  txAmt?: string;
  termUnitCode?: string;
  deptTerm?: string;
  custNo?: string;
  custNm?: string;
  perCertTpCd?: string;
  personalCertNo?: string;
  lifeCycStaCd?: string;
  chnlKindCode?: string;
  startSysOrCmptNo?: string;
  batchNo?: string;
  openAccInstNo?: string;
  clsAccInstNo?: string;
  txInstNo?: string;
  txTellerNo?: string;
  authTellerNo?: string;
  campTellerNo?: string;
  settaccUsageClsCd?: string;
  othUsageDesc?: string;
  hvPsFlag?: string;
  jointNaAccFlag?: string;
  lifeCycFlagCd?: string;
  obankFcurrDrawDocEntryNo?: string;
  batDealFlag?: string;
  dealFlag?: string;
  forCounGoveMemApproveInfo?: string;
  orgCertTypeCode?: string;
  orgCertNo?: string;
  lastTxDate?: string;
  zoneVal?: string;
}

// 个人联名账户信息记录表
export interface DprgtPersonJointAcc {
  mediumNo?: string;
  custNo?: string;
  perCertTpCd?: string;
  personalCertNo?: string;
  jointAcrlVatgCd?: string;
  custNm?: string;
  encPwd?: string;
  lastDealDate?: string;
  dsumPsErrTms?: string;
  psErrTms?: string;
  jointNaPersPsStaFlagCd?: string;
  relatBgnDate?: string;
  relatEndDate?: string;
  relatStaCd?: string;
  lastTxDate?: string;
  zoneVal?: string;
  tamProoFieldInfo?: string;
  createStamp?: string;
  lastModStamp?: string;
}

// 冻结登记簿
export interface DprgtFrz {
  frzNo?: string;
  frzDate?: string;
  txTime?: string;
  frzGloTracNo?: string;
  frzSubtxNo?: string;
  frzBusiTpCd?: string;
  execTpCd?: string;
  mediumNo?: string;
  mainContrNo?: string;
  saccnoSeqNo?: string;
  persInnerAccno?: string;
  custNo?: string;
  custNm?: string;
  perCertTpCd?: string;
  personalCertNo?: string;
  categFlagCd?: string;
  waitSeqNo?: string;
  currCode?: string;
  cashExgVatgCd?: string;
  appointExecAmt?: string;
  needFrzAmt?: string;
  dededAmt?: string;
  frzBgnDt?: string;
  frzBgnDtTime?: string;
  frzEndDt?: string;
  ofrzEndDtTime?: string;
  invalDtAccti?: string;
  frzExecFileNo?: string;
  powInstTpCd?: string;
  powInstName?: string;
  powInstInaccDealOpnnCd?: string;
  jstcCaseAttrCd?: string;
  contTel?: string;
  frzReason?: string;
  conFrzSeqNo?: string;
  oldFrzBgnDt?: string;
  oldFrzEndDt?: string;
  notEffConFrzBalFlag?: string;
  frzChnKindCd?: string;
  startSysOrCmptNo?: string;
  openAccInstNo?: string;
  frzInstNo?: string;
  frzTellerNo?: string;
  frzAuthTellerNo?: string;
  effStaCd?: string;
  lastTxDate?: string;
  zoneVal?: string;
  createStamp?: string;
  lastModStamp?: string;
}

// 止付登记簿
export interface DprgtStopPay {
  stopPayNo?: string;
  stopPayDate?: string;
  stopPayGloTracNo?: string;
  stopPaySubtxNo?: string;
  stopPayBusiTpCd?: string;
  execTpCd?: string;
  stopPayRelInfo?: string;
  mediumNo?: string;
  mainContrNo?: string;
  saccnoSeqNo?: string;
  persInnerAccno?: string;
  custNo?: string;
  custNm?: string;
  perCertTpCd?: string;
  personalCertNo?: string;
  categFlagCd?: string;
  subCardCardNo?: string;
  subCardContrNo?: string;
  appointExecAmt?: string;
  needStpPayAmt?: string;
  currCode?: string;
  cashExgVatgCd?: string;
  stopPayBgnDate?: string;
  stopPayDueDate?: string;
  stopPayEnrollTime?: string;
  relPayEnrollTime?: string;
  stopPayFileNo?: string;
  powInstName?: string;
  contTel?: string;
  stpPaySeqNo?: string;
  effStaCd?: string;
  stopPayReason?: string;
  stopPayChnKindCd?: string;
  startSysOrCmptNo?: string;
  openAccInstNo?: string;
  stopPayInstNo?: string;
  stopPayTellerNo?: string;
  stpPayTellerNo?: string;
  txTime?: string;
  lastTxDate?: string;
  dataDealStaCd?: string;
  zoneVal?: string;
  createStamp?: string;
  lastModStamp?: string;
}

// 解止付登记簿
export interface DprgtRlsStopPay {
  relsStopDate?: string;
  rlPayGloTracNo?: string;
  relPaySubtxNo?: string;
  stopPayNo?: string;
  relsStopAmt?: string;
  relPayFileNo?: string;
  relPayPowInstName?: string;
  contTel?: string;
  relPayModeCd?: string;
  rlPayVatgCd?: string;
  relPayChnlKindCd?: string;
  startSysOrCmptNo?: string;
  relPayInstNo?: string;
  relPayTellerNo?: string;
  rlPayAuthTellerNo?: string;
  rlPayReason?: string;
  txTime?: string;
  lastTxDate?: string;
  zoneVal?: string;
  createStamp?: string;
  lastModStamp?: string;
}

// 挂失/解挂登记簿
export interface DprgtLoss {
  rptLossNo?: string;
  mediumTpCd?: string;
  mediumNo?: string;
  drawInstNo?: string;
  custNo?: string;
  custNm?: string;
  perCertTpCd?: string;
  personalCertNo?: string;
  rptLossModeCode?: string;
  rptLossItemCode?: string;
  hvLossFlag?: string;
  rptLossDate?: string;
  lossGloTracNo?: string;
  lossSubtxNo?: string;
  accBal?: string;
  fcurrBal?: string;
  lossChnlKindCd?: string;
  lossSysNo?: string;
  rptLossInstNo?: string;
  lossDeviceLinkInstNo?: string;
  rptLossTellerNo?: string;
  lossAuthTellerNo?: string;
  rptLossTime?: string;
  jointAcrlVatgCd?: string;
  jointNaPersAccname?: string;
  jointNaPersCertTpCd?: string;
  jointNaPersCertNo?: string;
  lossTxFlagCd?: string;
  losStatDealCd?: string;
  reissueBehindMediumNo?: string;
  relLossModeCd?: string;
  lossDueDate?: string;
  relLossDt?: string;
  relLossGloTracNo?: string;
  relLossSubtxNo?: string;
  relLossChnKindCd?: string;
  relLossSysNo?: string;
  relLossInstNo?: string;
  relLossDeviceLinkInstNo?: string;
  relLossTellerNo?: string;
  relLossAuthTellerNo?: string;
  relLossTime?: string;
  lastTxDate?: string;
  zoneVal?: string;
}

// 签约加办关系登记
export interface TbDprgtSignAdd {
  mediumNo?: string;
  signOpsSysNo?: string;
  signKindCd?: string;
  signSubtypeCd?: string;
  prodtContractNo?: string;
  signDesc?: string;
  signAddoffTimes?: string;
  signContStatus?: string;
  signContEffDate?: string;
  signTime?: string;
  signContDate?: string;
  enrollGloTracNo?: string;
  enrollSubtxNo?: string;
  signMobileNo?: string;
  signTellerNo?: string;
  signContInstNo?: string;
  authTellerNo?: string;
  signInvalDt?: string;
  signInfoFlagCd?: string;
  cacContTime?: string;
  cacContDt?: string;
  cacContGloTracNo?: string;
  cacContSubtxNo?: string;
  cacContInstNo?: string;
  cacContTellerNo?: string;
  cacContAuthTellerNo?: string;
  lastTxDate?: string;
  zoneVal?: string;
}

// 账户免收登记簿
export interface DprgtAccFree {
  txDate?: string;
  globalBusiTrackNo?: string;
  subtxNo?: string;
  custNo?: string;
  custNm?: string;
  mediumNo?: string;
  persInnerAccno?: string;
  freeTpCd?: string;
  freeBgnDt?: string;
  freeDueDate?: string;
  freeReasnCd?: string;
  chgFlag?: string;
  accFreeStaCd?: string;
  ocupFreeLimitFlag?: string;
  freeOprTpCd?: string;
  openAccInstNo?: string;
  txInstNo?: string;
  txTellerNo?: string;
  authTellerNo?: string;
  txTime?: string;
  txChnlKindCd?: string;
  chgDate?: string;
  chgGloTracNo?: string;
  chgSubtxNo?: string;
  txChgOprTpCd?: string;
  chgInstNo?: string;
  chgTellerNo?: string;
  chgAuthTellerNo?: string;
  chgChnlKindCode?: string;
  lastTxDate?: string;
  zoneVal?: string;
}

// 无卡支付协议登记
export interface DprgtNoCardPay {
  noCardPayAgrNo?: string;
  mediumNo?: string;
  prodtContractNo?: string;
  noCardPayTpCd?: string;
  custNm?: string;
  perCertTpCd?: string;
  personalCertNo?: string;
  payMobileNo?: string;
  signContDate?: string;
  dueDate?: string;
  signContStatus?: string;
  signChnKindCd?: string;
  signContInstNo?: string;
  signTellerNo?: string;
  authTellerNo?: string;
  cacContDt?: string;
  cacContChnKindCd?: string;
  cacContInstNo?: string;
  cacContTellerNo?: string;
  cacContAuthTellerNo?: string;
  lastTxDate?: string;
  zoneVal?: string;
  lastModStamp?: string;
}

// 换卡登记簿
export interface DprgtChgCard {
  cardNo?: string;
  newCardCardNo?: string;
  mainContrNo?: string;
  enrollDate?: string;
  globalBusiTrackNo?: string;
  subtxNo?: string;
  chgInstNo?: string;
  chgTellerNo?: string;
  authTellerNo?: string;
  txTime?: string;
  relMedmStaFlagCd?: string;
  validFlag?: string;
  hostDt?: string;
  lastTxDate?: string;
  recordStaCd?: string;
  zoneVal?: string;
  createStamp?: string;
  lastModStamp?: string;
}

// 待限制服务登记簿
export interface TbDprgtTobeLimtServ {
  enrollDate?: string;               // 登记日期
  globalBusiTrackNo?: string;        // 全局业务跟踪号
  subtxNo?: string;                  // 子交易序号
  prodtContractNo?: string;          // 产品合约编号
  enrollTime?: string;               // 登记时间
  limtTpCd?: string;                 // 限制类型代码
  mediumNo?: string;                 // 介质编号
  categFlagCd?: string;              // 类别标识代码
  custNo?: string;                   // 客户编号
  custNm?: string;                   // 客户名称
  perCertTpCd?: string;              // 个人证件类型代码
  personalCertNo?: string;           // 个人证件号码
  openaccDate?: string;              // 开户日期
  openAccInstNo?: string;            // 开户机构号
  txInstNo?: string;                 // 交易机构号
  txTellerNo?: string;               // 交易柜员号
  authTellerNo?: string;             // 授权柜员号
  chnlKindCode?: string;             // 渠道种类代码
  startSysOrCmptNo?: string;         // 发起系统或组件编码
  limtReasnCd?: string;              // 限制原因代码
  limtFileNo?: string;               // 限制执行文号
  limtPowInstName?: string;          // 限制有权机关名称
  expectLimtDt?: string;             // 预计限制日期
  curStatusCode?: string;            // 当前状态代码
  chgChnlKindCode?: string;          // 变更渠道种类代码
  chgSysNo?: string;                 // 变更系统编码
  relsDt?: string;                   // 解除日期
  relsTime?: string;                 // 解除时间
  relsGloTracNo?: string;            // 解除全局业务跟踪号
  relsSubtxNo?: string;              // 解除子交易序号
  relsFileNo?: string;               // 解除执行文号
  relsPowInstName?: string;          // 解除有权机关名称
  relsLimtRe?: string;               // 解除限制原因代码
  relsLimtReasonSupplemDesc?: string; // 解除限制原因补充描述
  relsLimtModeCd?: string;           // 解除限制方式代码
  relsInstNo?: string;               // 解除机构号
  relsTellerNo?: string;             // 解除柜员号
  relsAuthTellerNo?: string;         // 解除授权柜员号
  lastTxDate?: string;               // 最后交易日期
  recordStaCd?: string;              // 记录状态代码
  zoneVal?: string;                  // 分片值
  createStamp?: string;              // 创建时间戳
  lastModStamp?: string;             // 最后修改时间戳
}

// 限制服务登记簿
export interface TbDprgtLimtServ {
  enrollDate?: string;               // 登记日期
  globalBusiTrackNo?: string;        // 全局业务跟踪号
  subtxNo?: string;                  // 子交易序号
  prodtContractNo?: string;          // 产品合约编号
  enrollTime?: string;               // 登记时间
  limtTpCd?: string;                 // 限制类型代码
  mediumNo?: string;                 // 介质编号
  categFlagCd?: string;              // 类别标识代码
  custNo?: string;                   // 客户编号
  custNm?: string;                   // 客户名称
  perCertTpCd?: string;              // 个人证件类型代码
  personalCertNo?: string;           // 个人证件号码
  openAccDate?: string;              // 开户日期
  accBal?: string;                   // 账户余额
  lastMobilityTxDate?: string;       // 最后动户交易日
  txInstNo?: string;                 // 交易机构号
  txTellerNo?: string;               // 交易柜员号
  authTellerNo?: string;             // 授权柜员号
  chnlKindCode?: string;             // 渠道种类代码
  startSysOrCmptNo?: string;         // 发起系统或组件编码
  limtNo?: string;                   // 限制编号
  limtReasnCd?: string;              // 限制原因代码
  limtReasonSupplemDesc?: string;    // 限制原因补充描述
  limtFileNo?: string;               // 限制执行文号
  limtPowInstName?: string;          // 限制有权机关名称
  policeConterName?: string;         // 公安联系人名称
  policeConterTel?: string;          // 公安联系人电话
  effStaCd?: string;                 // 生效状态代码
  limtEndDt?: string;                // 限制终止日期
  chgChnlKindCode?: string;          // 变更渠道种类代码
  chgSysNo?: string;                 // 变更系统编码
  chgHostDt?: string;                // 变更主机日期
  relsDt?: string;                   // 解除日期
  relsTime?: string;                 // 解除时间
  relsGloTracNo?: string;            // 解除全局业务跟踪号
  relsSubtxNo?: string;              // 解除子交易序号
  relsFileNo?: string;               // 解除执行文号
  relsPowInstName?: string;          // 解除有权机关名称
  relsLimtReasnCd?: string;          // 解除限制原因代码
  relsLimtReasonSupplemDesc?: string; // 解除限制原因补充描述
  relsLimtModeCd?: string;           // 解除限制方式代码
  relsInstNo?: string;               // 解除机构号
  relsTellerNo?: string;             // 解除柜员号
  relsAuthTellerNo?: string;         // 解除授权柜员号
  hostDt?: string;                   // 主机日期
  lastTxDate?: string;               // 最后交易日期
  recordStaCd?: string;              // 记录状态代码
  zoneVal?: string;                  // 分片值
  jobZoneNo?: string;                // 作业分片号
  optimistLockVerNo?: string;        // 乐观锁版本号
  createStamp?: string;              // 创建时间戳
  lastModStamp?: string;             // 最后修改时间戳
}

// 分页响应数据接口
export interface PageResponse<T> {
  records: T[];
  total: number;
  size: number;
  current: number;
  loading?: boolean; // Add optional loading property
}

// 分页参数接口
export interface PageParams {
  current: number;
  pageSize: number;
}

// 消费登记簿
export interface Consum {
  txDate?: string;               // 交易日期
  globalBusiTrackNo?: string;    // 全局业务跟踪号
  subtxNo?: string;              // 子交易序号
  mainContrNo?: string;          // 主合约编号
  saccnoSeqNo?: number;          // 子账号序号
  persInnerAccno?: string;       // 个人内部账号
  txAmt?: number;                // 交易金额
  mainAccChgAmt?: number;        // 账户变动金额
  creditClsContrAcChgAmt?: number; // 信贷类合约账户变动金额
  canSaleRetAmt?: number;        // 已退货金额
  accReturnedAmt?: number;       // 账户已退货金额
  creditClsContrReturnedAmt?: number; // 信贷类合约账户已退货金额
  startSysOrCmptNo?: string;     // 发起系统或组件编码
  txChnlKindCd?: string;         // 交易渠道种类代码
  txInstNo?: string;             // 交易机构号
  txTellerNo?: string;           // 交易柜员号
  authTellerNo?: string;         // 授权柜员号
  txTime?: string;               // 交易时间
  summName?: string;             // 摘要名称
  merNo?: string;                // 商户编码
  merDesc?: string;              // 商户描述
  txChgStaCd?: string;           // 交易变更状态代码
  chgDate?: string;              // 变更日期
  chgGloTracNo?: string;         // 变更全局业务跟踪号
  chgSubtxNo?: string;           // 变更子交易序号
  hostDt?: string;               // 主机日期
  lastTxDate?: string;           // 最后交易日期
  recordStaCd?: string;          // 记录状态代码
  zoneVal?: string;              // 分片值
  optimistLockVerNo?: number;    // 乐观锁版本号
  createStamp?: string;          // 创建时间戳
  lastModStamp?: string;         // 最后修改时间戳
}

// 退货登记簿
export interface SaleRet {
  txDate?: string;               // 交易日期
  globalBusiTrackNo?: string;    // 全局业务跟踪号
  subtxNo?: string;              // 子交易序号
  saleRetTpCd?: string;          // 退货类型代码
  mainContrNo?: string;          // 主合约编号
  saccnoSeqNo?: number;          // 子账号序号
  persInnerAccno?: string;       // 个人内部账号
  saleRetAmt?: number;           // 退货金额
  startSysOrCmptNo?: string;     // 发起系统或组件编码
  txChnlKindCd?: string;         // 交易渠道种类代码
  txInstNo?: string;             // 交易机构号
  txTellerNo?: string;           // 交易柜员号
  authTellerNo?: string;         // 授权柜员号
  txTime?: string;               // 交易时间
  oldTxDate?: string;            // 原交易日期
  oldDtGloTracNo?: string;       // 原明细全局业务跟踪号
  oldDtSubtxNo?: string;         // 原明细子交易序号
  saleRetStsCd?: string;         // 退货状态代码
  hostDt?: string;               // 主机日期
  lastTxDate?: string;           // 最后交易日期
  recordStaCd?: string;          // 记录状态代码
  zoneVal?: string;              // 分片值
  optimistLockVerNo?: number;    // 乐观锁版本号
  createStamp?: string;          // 创建时间戳
  lastModStamp?: string;         // 最后修改时间戳
}

// 账户升降级登记簿
export interface AccUpdown {
  txDate?: string;               // 交易日期
  globalBusiTrackNo?: string;    // 全局业务跟踪号
  subtxNo?: string;              // 子交易序号
  prodtContractNo?: string;      // 产品合约编号
  mediumNo?: string;             // 介质编号
  perCertTpCd?: string;          // 个人证件类型代码
  personalCertNo?: string;       // 个人证件号码
  upDownTpCd?: string;           // 升降级类型代码
  accOldLvlCd?: string;          // 账户原等级代码
  accNewLvlCd?: string;          // 账户新等级代码
  chnlKindCode?: string;         // 渠道种类代码
  startSysOrCmptNo?: string;     // 发起系统或组件编码
  txRemark?: string;             // 交易备注
  txTime?: string;               // 交易时间
  txInstNo?: string;             // 交易机构号
  txTellerNo?: string;           // 交易柜员号
  authTellerNo?: string;         // 授权柜员号
  hostDt?: string;               // 主机日期
  lastTxDate?: string;           // 最后交易日期
  recordStaCd?: string;          // 记录状态代码
  zoneVal?: string;              // 分片值
  optimistLockVerNo?: number;    // 乐观锁版本号
  createStamp?: string;          // 创建时间戳
  lastModStamp?: string;         // 最后修改时间戳
}

// 合约辅助表
export interface DprgtContrAsst {
  prodtContractNo?: string;      // 产品合约编号
  contrSpecSignTpCd?: string;    // 合约特殊签约类
  vendibiProdtNo?: string;       // 可售产品编码
  vendibiProdtVerNo?: string;    // 可售产品版本号
  topicNo?: string;              // 主题编码
  reqSource?: string;            // 请求来源
  fqtrCurtAccu?: number;         // 首季活期积数
  signContDate?: string;         // 签约日期
  signContInstNo?: string;       // 签约机构号
  signTellerNo?: string;         // 签约柜员号
  authTellerNo?: string;         // 授权柜员号
  chnlKindCode?: string;         // 渠道种类代码
  startSysOrCmptNo?: string;     // 发起系统或组件
  dueDate?: string;              // 到期日期
  newProdtEffDtAccti?: string;   // 新产品生效日期
  newVendibiProdtNo?: string;    // 新可售产品编码
  newVendibiProdtVerNo?: string; // 新可售产品版本
  contrSpecSignStaCd?: string;   // 合约特殊签约状
  hostDt?: string;               // 主机日期
  lastTxDate?: string;           // 最后交易日期
  recordStaCd?: string;          // 记录状态代码
  zoneVal?: string;              // 分片值
  jobZoneNo?: number;            // 作业分片号
  optimistLockVerNo?: number;    // 乐观锁版本号
  createStamp?: string;          // 创建时间戳
  lastModStamp?: string;         // 最后修改时间戳
}

// 圈存加办信息表
export interface DprgtCflAddoffInfo {
  prodtContractNo?: string;      // 产品合约编号
  relContrNo?: string;           // 关联产品合约编号
  cflTpCd?: string;              // 圈存类型代码
  effStaCd?: string;             // 生效状态代码
  lastTxDate?: string;           // 最后交易日期
  recordStaCd?: string;          // 记录状态代码
  zoneVal?: string;              // 分片值
  optimistLockVerNo?: number;    // 乐观锁版本号
  createStamp?: string;          // 创建时间戳
  lastModStamp?: string;         // 最后修改时间戳
}

// 账户绑定关系登记表
export interface DprgtAccBindRelat {
  prodtContractNo?: string;        // 产品合约编号
  relContrNoOrOtBnkAccno?: string;     // 关联产品合约编号
  mediumNo?: string;               // 介质编号
  accBindTpCd?: string;            // 账户绑定类型代码
  accBindKindCd?: string;          // 账户绑定种类代码
  custNo?: string;                 // 客户编号
  bindAccBnkNo?: string;           // 绑定账户行号
  bindAccBankNm?: string;          // 绑定账户行名
  defauAccFlag?: string;           // 默认账户标志
  accBindStaCd?: string;           // 账户绑定状态代码
  bindDt?: string;                 // 绑定日期
  bindChnKindCd?: string;          // 绑定渠道种类代码
  startSysOrCmptNo?: string;       // 发起系统或组件编号
  bindInstNo?: string;             // 绑定机构号
  bindTellerNo?: string;           // 绑定柜员号
  authTellerNo?: string;           // 授权柜员号
  unboundDt?: string;              // 解绑日期
  unboundChnKindCd?: string;       // 解绑渠道种类代码
  unboundSysNo?: string;           // 解绑系统编码
  unboundTellerNo?: string;        // 解绑柜员号
  unboundInstNo?: string;          // 解绑机构号
  unboundAuthTellerNo?: string;    // 解绑授权柜员号
  hostDt?: string;                 // 主机日期
  chgHostDt?: string;              // 变更主机日期
  lastTxDate?: string;             // 最后交易日期
  recordStaCd?: string;            // 记录状态代码
  zoneVal?: string;                // 分片值
  optimistLockVerNo?: number;      // 乐观锁版本号
  createStamp?: string;            // 创建时间戳
  lastModStamp?: string;           // 最后修改时间戳
}

// 资金用途管控明细表
export interface TbDpdtlFundsUsageControl {
  fundsUsageCtrlCode?: string;     // 资金用途管控编码
  txDate?: string;                  // 交易日期
  globalBusiTrackNo?: string;      // 全局业务跟踪号
  subtxNo?: string;                // 子交易序号
  dtlSeqNo?: string;               // 明细序号
  persInnerAccno?: string;         // 个人内部账号
  fundsUsageControllTpCd?: string;     // 资金用途管控类型代码
  fundsUseSeqCd?: string;        // 资金使用顺序代码
  seqNo?: string;                  // 顺序号
  disburseInstNoList?: string;       // 放款机构号列表
  txAmt?: string;                  // 交易金额
  bal?: string;                    // 余额
  txTime?: string;                 // 交易时间
  ctrllgFundsTxFlagCd?: string;      // 管控资金交易标识
  lastTxDate?: string;             // 最后交易日期
  recordStaCd?: string;          // 记录状态代码
  zoneVal?: string;                // 分片值
  createStamp?: string;            // 创建时间戳
  lastModStamp?: string;           // 最后修改时间戳
}

// 资金用途管控表
export interface TbDprgtFundsUsageControl {
  fundsUsageCtrllgNo?: string;      // 资金用途管控编号
  persInnerAccno?: string;          // 个人内部账号
  fundsUsageControllTpCd?: string;  // 资金用途管控类型代码
  disburseInstNoList?: string;      // 放款机构号列表
  fundsUseSeqCd?: string;           // 资金使用顺序代码
  seqNo?: string;                   // 顺序号
  bal?: string;             // 余额
  avalBal?: string;             // 可用余额
  validFlag?: string;            // 是否有效标志
  lastTxDate?: string;              // 最后交易日期
  zoneVal?: string;                 // 分片值
  createStamp?: string;             // 创建时间戳
  lastModStamp?: string;            // 最后修改时间戳
}

// 资金用途管控借据表
export interface TbDprgtFundsUsageControlLoan {
  fundsUsageCtrllgNo?: string;      // 资金用途管控编号
  persInnerAccno?: string;          // 个人内部账号
  fundsUsageControllTpCd?: string;  // 资金用途管控类型代码
  disburseInstNoList?: string;      // 放款机构号列表
  fundsUseSeqCd?: string;           // 资金使用顺序代码
  bal?: string;                     // 余额
  avalBal?: string;                 // 可用余额
  disburseAmt?: string;             // 放款金额
  repayAmt?: string;                // 还款金额
  validFlag?: string;               // 是否有效标志
  lastTxDate?: string;              // 最后交易日期
  recordStaCd?: string;             // 记录状态代码
  zoneVal?: string;                 // 分片值
  optimistLockVerNo?: string;       // 乐观锁版本号
  createStamp?: string;             // 创建时间戳
  lastModStamp?: string;            // 最后修改时间戳
}

// 资金用途管控借据明细表
export interface TbDpdtlFundsUsageControlLoan {
  fundsUsageCtrllgLnrctNo?: string;     // 资金用途管控借据编号
  txDate?: string;                  // 交易日期
  globalBusiTrackNo?: string;      // 全局业务跟踪号
  subtxNo?: string;                // 子交易序号
  dtlSeqNo?: string;               // 明细序号
  persInnerAccno?: string;         // 个人内部账号
  fundsUsageControllTpCd?: string;     // 资金用途管控类型代码
  fundsUseSeqCd?: string;        // 资金使用顺序代码
  seqNo?: string;                  // 顺序号
  disburseInstNoList?: string;       // 放款机构号列表
  txAmt?: string;                  // 交易金额
  bal?: string;                    // 余额
  txTime?: string;                 // 交易时间
  repayAmt?: string;                // 还款金额
  loanRctNo?: string;               // 借据编号
  relLoanRctNo?: string;            // 关联借据编号
  foudsUseSeqNo?: string;           // 资金使用顺序号
  ctrllgFundsTxFlagCd?: string;      // 管控资金交易标识
  lastTxDate?: string;             // 最后交易日期
  recordStaCd?: string;          // 记录状态代码
  zoneVal?: string;                // 分片值
  createStamp?: string;            // 创建时间戳
  lastModStamp?: string;           // 最后修改时间戳
}

// 账户星级登记簿
export interface DprgtAccStar {
  persInnerAccno?: string;        // 个人内部账号
  servSysTpCd?: string;           // 服务体系类型代码
  accStarCd?: string;             // 账户星级代码
  pricingAgrNo?: string;          // 定价协议编号
  addoffTpCd?: string;            // 加办类型代码
  disDueDt?: string;              // 优惠到期日期
  perAddoffTimes?: number;        // 单笔加办次数
  batchAddoffTimes?: number;      // 批量加办次数
  chgDate?: string;               // 变更日期
  txInstNo?: string;              // 交易机构号
  txTellerNo?: string;            // 交易柜员号
  authTellerNo?: string;          // 授权柜员号
  txTime?: string;                // 交易时间
  effStaCd?: string;              // 生效状态代码
  lastTxDate?: string;            // 最后交易日期
  recordStaCd?: string;           // 记录状态代码
  zoneVal?: string;               // 分片值
  jobZoneNo?: number;             // 作业分片号
  optimistLockVerNo?: number;     // 乐观锁版本号
  createStamp?: string;           // 创建时间戳
  lastModStamp?: string;          // 最后修改时间戳
}

/**活期产品合约账户结转余额表 */
export interface DprgtCarryBalCalInt {
  persInnerAccno?: string;        // 个人内部账号
  carryTpCd?: string;             // 结转类型代码
  carryBal?: number;              // 结转余额
  calIntAccu?: number;            // 计息积数
  carryDate?: string;             // 结转日期
  effStaCd?: string;              // 生效状态代码
  lastTmTxDt?: string;            // 上次交易日期
  lastTxDate?: string;            // 最后交易日期
  recordStaCd?: string;           // 记录状态代码
  zoneVal?: string;               // 分片值
  jobZoneNo?: number;             // 作业分片号
  optimistLockVerNo?: number;     // 乐观锁版本号
  createStamp?: string;           // 创建时间戳
  lastModStamp?: string;          // 最后修改时间戳
}

export interface TbDprgtDepProve {
  deptProveNo?: string;           // 存款证明编号
  custNo?: string;                // 客户编号
  custNm?: string;                // 客户名称
  custPyName?: string;            // 客户拼音姓名
  perCertTpCd?: string;           // 个人证件类型代码
  personalCertNo?: string;        // 个人证件号码
  deptProveVatgCd?: string;       // 存款证明类别代码
  proveTmVatgCd?: string;         // 证明时间类别代码
  openNum?: string;               // 开立份数
  openedTimes?: string;           // 已增开次数
  proveBgnDt?: string;            // 证明起始日期
  globalBusiTrackNo?: string;     // 全局业务跟踪号
  subtxNo?: string;               // 子交易序号
  proveDueDate?: string;          // 证明到期日期
  txChnlKindCd?: string;          // 交易渠道种类代码
  startSysOrCmptNo?: string;      // 发起系统或组件编号
  txInstNo?: string;              // 交易机构号
  txTellerNo?: string;            // 交易柜员号
  authTellerNo?: string;          // 授权柜员号
  txTime?: string;                // 交易时间
  deptProveTxFlagCd?: string;     // 存款证明交易标识代码
  deptProveStaCd?: string;        // 存款证明状态代码
  deptProveRemark?: string;       // 存款证明备注
  cancelDate?: string;            // 撤销日期
  cancelChnKindCd?: string;       // 撤销渠道种类代码
  cancelSysNo?: string;           // 撤销系统编号
  revokeInstNo?: string;          // 撤销机构号
  revokeTellerNo?: string;        // 撤销柜员号
  revkAuthTellerNo?: string;      // 撤销授权柜员号
  revkTime?: string;              // 撤销时间
  lastTxDate?: string;            // 最后交易日期
  recordStaCd?: string;           // 记录状态代码
  zoneVal?: string;               // 分片值
  createStamp?: string;           // 创建时间戳
  lastModStamp?: string;          // 最后修改时间戳
}

// 客户限制服务登记簿列表
export interface TbDprgtCustLimtServ {
  txDate?: string;                // 交易日期
  globalBusiTrackNo?: string;     // 全局业务跟踪号
  subtxNo?: string;               // 子交易序号
  custNo?: string;                // 客户编号
  limtTpCd?: string;              // 限制类型代码
  limtEndDt?: string;             // 限制终止日期
  limtReasnCd?: string;           // 限制原因代码
  limtReasonSupplemDesc?: string; // 限制原因补充描述
  stgnFdiCtrllgActnFlagCd?: string; // 强化尽职调查管控措施标志码
  custNm?: string;                // 客户名称
  perCertTpCd?: string;           // 个人证件类型代码
  personalCertNo?: string;        // 个人证件号码
  txTime?: string;                // 交易时间
  txInstNo?: string;              // 交易机构号
  txTellerNo?: string;            // 交易柜员号
  authTellerNo?: string;          // 授权柜员号
  chnlKindCode?: string;          // 渠道种类代码
  startSysOrCmptNo?: string;      // 发起系统或组件编码
  effStaCd?: string;              // 生效状态代码
  relsDt?: string;                // 解除日期
  relsTime?: string;              // 解除时间
  relsGloTracNo?: string;         // 解除全局业务跟踪号
  relsSubtxNo?: string;           // 解除子交易序号
  relsFileNo?: string;            // 解除执行文号
  relsPowInstName?: string;       // 解除有权机关名称
  relsLimtReasnCd?: string;       // 解除限制原因代码
  relsLimtReasonSupplemDesc?: string; // 解除限制原因补充描述
  relsInstNo?: string;            // 解除机构号
  relsTellerNo?: string;          // 解除柜员号
  relsAuthTellerNo?: string;      // 解除授权柜员号
  lastTxDate?: string;            // 最后交易日期
  recordStaCd?: string;           // 记录状态代码
  zoneVal?: string;               // 分片值
  createStamp?: string;           // 创建时间戳
  lastModStamp?: string;          // 最后修改时间戳
}

export interface TbDprgtDepProveOpenInfo {
  deptProveNo?: string;          // 存款证明编号
  persInnerAccno?: string;       // 个人内部账号
  mediumNo?: string;             // 介质编号
  saccnoSeqNo?: string;          // 子账号序号
  prodtContractNo?: string;      // 产品合约编号
  openaccDate?: string;          // 开户日期
  categFlagCd?: string;          // 类别标识代码
  savTypeMclassCode?: string;    // 储种中类代码
  stopPayNo?: string;            // 止付号
  depProveAmt?: string;          // 存款证明金额
  currCode?: string;             // 币种代码
  cashExgVatgCd?: string;        // 钞汇类别代码
  baseProdtNo?: string;          // 基础产品编码
  relatStaCd?: string;           // 关系状态代码
  lastTxDate?: string;           // 最后交易日期
  recordStaCd?: string;          // 记录状态代码
  zoneVal?: string;              // 分片值
  optimistLockVerNo?: string;    // 乐观锁版本号
  createStamp?: string;          // 创建时间戳
  lastModStamp?: string;         // 最后修改时间戳
}
