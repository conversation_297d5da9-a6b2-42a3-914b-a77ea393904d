.app-container {
  padding: 16px;  // 从 24px 改为 16px
  background-color: #f0f2f5;
  min-height: 100vh;  // 改回100vh
  display: flex;
  flex-direction: column;
  overflow-x: hidden;
  max-width: 100%;
  position: relative;
  margin-bottom: 48px;  // 添加底部边距

  .content-wrapper {
    flex: 1 0 auto;    // 修改 flex 属性
    display: flex;
    flex-direction: column;
    min-height: calc(100vh - 180px);  // 添加最小高度
    overflow: auto;
    width: 100%;
    max-width: 1700px !important;
    margin: 0 auto !important;
    padding: 0 16px;
    position: relative;           // 添加这行
    
    .search-section {
      background-color: #fff;
      border-radius: 10px;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
      margin-bottom: 20px;  // 从 24px 改为 20px
      margin-top: 48px;     // 从 38px 改为 30px
      transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
      width: 100% !important;        // 添加 !important
      flex-shrink: 0;
      max-width: 100% !important; // 添加最大宽度限制
  
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }
    }

    .table-wrapper {
      background-color: #fff;
      padding: 20px;
      border-radius: 10px;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
      margin-bottom: 20px;
      display: flex;
      flex-direction: column;
      width: 100%;
      max-width: 100%;
      overflow-x: auto;
      transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
      height: auto !important;      // 添加这行，确保高度自动
      min-height: 0;               // 添加这行，允许内容收缩
      flex: 0 1 auto;             // 修改这行，允许内容自动伸缩
  
      .data-section-table {
        overflow: auto;
        width: 100%;
        flex: 0 1 auto;           // 修改这行，允许表格内容自动伸缩
        height: auto !important;   // 添加这行，确保高度自动
      }

      :global(.ant-tabs) {
        display: flex;
        flex-direction: column;
        height: auto !important;   // 修改这行，确保高度自动
        width: 100%;
      }

      :global(.ant-tabs-content) {
        flex: 0 1 auto;           // 修改这行，允许内容自动伸缩
        overflow: visible;
        width: 100%;
        height: auto !important;   // 添加这行，确保高度自动
      }
  
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }
  
      .data-section-table {
        overflow: auto; // 修改为 auto
        width: 100%;
        flex: 1; // 添加此行
      }

      .table-section {
        margin-top: 24px;
        width: 100%;
      }

      :global(.ant-tabs) {
        display: flex;
        flex-direction: column;
        height: 100%;
        width: 100%;
      }

      :global(.ant-tabs-content) {
        flex: 1;
        overflow: visible; // 修改为 visible
        width: 100%;
      }

      // 添加分页样式
      :global(.ant-pagination) {
        margin: 16px 0;
        display: flex;
        justify-content: flex-end;
      }
    }
  }
}

// .search-section-wrapper {
//   .ant-card {
//     background-color: #fff;
//     border-radius: 8px;
//     transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
//     margin-bottom: 24px;
    
//     &:hover {
//       transform: translateY(-2px);
//       box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
//     }
//   }
// }

.search-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px; // 减小间距
  width: 100%;
  flex-wrap: wrap;
  padding: 0 16px;
}

.search-label {
  font-size: 16px;
  color: #333;
  font-weight: 450;
  white-space: nowrap;
  min-width: 60px;
  text-align: right;
  margin-right: 0;  // 移除右边距
  display: inline-block;
}

.search-input-wrapper {
  flex: 0 1 260px; // 修改最大宽度，约为原来的2/3
  min-width: 200px;
  margin: 0 auto; // 确保居中

  .ant-input {
    width: 100%;
    border-radius: 8px !important; // 增加圆角
    outline: none !important;
    
    &:hover, &:focus {
      border-color: #1677ff;
      box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.1);
      outline: none !important;
    }
  }

  .ant-input-affix-wrapper {
    border-radius: 8px !important; // 增加圆角
    outline: none !important;
    
    &:hover, &:focus {
      outline: none !important;
    }
  }
}

.search-buttons {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
  margin-left: -4px; // 向左微调，拉近与输入框的距离

  .ant-btn {
    border-radius: 6px;
    padding: 4px 16px;
    height: 32px;
    
    &.ant-btn-primary {
      background: #1677ff;
      font-weight: 500;
      
      &:hover {
        background: #4096ff;
        box-shadow: 0 2px 8px rgba(22, 119, 255, 0.2);
      }
      
      &:active {
        background: #0958d9;
      }
    }
  }
}

@media screen and (max-width: 576px) {
  .search-container {
    gap: 12px;
  }

  .search-input-wrapper {
    flex: 1 1 100%;
    max-width: 100%;
  }

  .search-buttons {
    width: 100%;
    justify-content: center;
  }
}

.flag-text {
  color: #1890ff !important;
}

/* 确保优先级 */
.ant-table-cell .flag-text {
  color: #1890ff !important;
}

.search-section {
  .search-label {
    font-size: 14px;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.85);
  }

  .search-buttons {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
  }

  @media (max-width: 576px) {
    .search-buttons {
      justify-content: flex-start;
      margin-top: 16px;
    }
  }
}

// 添加页脚样式
:global(.ant-pro-footer) {
  position: fixed;        // 改为 fixed
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  text-align: center;
  padding: 16px;
  background-color: #f0f2f5;  // 添加背景色
  z-index: 1000;             // 提高层级
  box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.03);  // 添加上阴影
}

.ant-btn {
  // 重置DAC按钮悬停效果
  &:has(.anticon-redo) {
    &:hover {
      background-color: #ffcdd2 !important;
      border-color: #ef9a9a !important;
      color: #d32f2f !important;
    }
  }
}

// 一键销户按钮悬停效果
.search-buttons {
  .ant-btn {
    &[style*="background-color: rgb(232, 245, 233)"] {
      &:hover {
        background-color: #c8e6c9 !important;
        border-color: #81c784 !important;
        color: #2e7d32 !important;
      }
    }

    // 取消一键销户按钮悬停效果
    &[style*="background-color: rgb(255, 248, 225)"] {
      &:hover {
        background-color: #fff3c4 !important;
        border-color: #ffd54f !important;
        color: #f57f17 !important;
      }
    }
  }
}

.search-section {
  .ant-row {
    margin: 0 -12px;
  }

  .search-buttons {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
    
    // 添加后备方案
    .ant-btn {
      margin-right: 10px;
      
      &:last-child {
        margin-right: 0;
      }
    }
    
    @media (max-width: 576px) {
      margin-top: 16px;
      justify-content: flex-start;
    }
  }

  @media (max-width: 576px) {
    .ant-col {
      padding: 0 12px;
    }
  }
}

.ant-tooltip {
  // 覆盖默认的 Tooltip 样式
  .ant-tooltip-inner {
    min-width: 350px;  // 增加最小宽度
    min-height: 32px;
    padding: 16px 20px;  // 增加内边距
    color: rgba(0, 0, 0, 0.85);
    text-align: left;
    text-decoration: none;
    word-wrap: break-word;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15);
    font-size: 14px;
    line-height: 1.5;
  }
  
  // 移除箭头
  .ant-tooltip-arrow {
    display: none;
  }
  
  // 确保没有多余的边框
  .ant-tooltip-content {
    border: none;
    background: transparent;
    max-width: 600px;  // 设置最大宽度
    width: auto;       // 自动调整宽度
  }
  
  // 自定义列表样式
  ul {
    margin: 8px 0 0 0;
    padding-left: 20px;
    
    li {
      margin-bottom: 8px;  // 增加列表项间距
      line-height: 1.6;    // 增加行高
      font-size: 14px;     // 确保字体大小合适
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
  
  // 标题样式
  p {
    margin: 0 0 12px 0;  // 增加底部间距
    font-weight: 600;
    color: #1890ff;
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 10px;
    font-size: 16px;     // 增加标题字体大小
  }
}

// 覆盖自定义类名的样式
.custom-tooltip {
  .ant-tooltip-inner {
    border-radius: 8px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15);
    width: auto;
    min-width: 300px;  // 确保最小宽度
  }
}

// 移除之前可能冲突的样式
.ant-tooltip-inner {
  min-width: 300px;  // 与上面保持一致
  min-height: auto;
  background-color: #fff;
  color: rgba(0, 0, 0, 0.85);
  border-radius: 8px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15);
}