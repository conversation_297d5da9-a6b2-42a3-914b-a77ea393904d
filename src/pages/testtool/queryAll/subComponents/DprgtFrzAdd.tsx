import React, { useState, useEffect } from "react";
import {
  Modal,
  Form,
  Input,
  Select,
  DatePicker,
  Button,
  message,
  Spin,
  Row,
  Col,
} from "antd";
import moment from "moment";
import { insertDprgtFrz } from "../service";
import type { DprgtFrz } from "../data";
import "./DprgtFrzDetailEdit.css"; // 复用DetailEdit的样式文件

const { TextArea } = Input;
const { Option } = Select;

interface DprgtFrzAddProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: () => void;
  mainContractInfo: {
    mainContrNo?: string;
    zoneVal?: string;
    mediumNo?: string;
    persInnerAccno?: string;
    custNo?: string;
    custNm?: string;
    perCertTpCd?: string;
    personalCertNo?: string;
    saccnoSeqNo?: string;
  };
}

// 添加表单验证错误的类型定义
interface FormValidationError {
  errorFields?: Array<{
    name: string[];
    errors: string[];
  }>;
  message?: string;
  [key: string]: any; // 添加索引签名以允许任何其他属性
}

const DprgtFrzAdd: React.FC<DprgtFrzAddProps> = ({
  visible,
  onCancel,
  onSuccess,
  mainContractInfo,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState<boolean>(false);
  const [frzBusiTpCd, setFrzBusiTpCd] = useState<string>("11");
  const [execTpCd, setExecTpCd] = useState<string>("05");
  const [categFlagCd, setCategFlagCd] = useState<string>("01");
  const [currCode, setCurrCode] = useState<string>("156");
  const [cashExgVatgCd, setCashExgVatgCd] = useState<string>("3");
  const [notEffConFrzBalFlag, setNotEffConFrzBalFlag] = useState<string>("0");
  const [frzChnKindCd, setFrzChnKindCd] = useState<string>();
  const [effStaCd, setEffStaCd] = useState<string>("1");

  // 重置表单
  useEffect(() => {
    if (visible) {
      form.resetFields();

      // 获取当前日期和时间
      const currentDate = moment();
      const date = new Date();
      const year = date.getFullYear().toString();
      const month = (date.getMonth() + 1).toString().padStart(2, "0");
      const day = date.getDate().toString().padStart(2, "0");
      const txStartTime =
        year +
        month +
        day +
        date.getHours().toString().padStart(2, "0") +
        date.getMinutes().toString().padStart(2, "0") +
        date.getSeconds().toString().padStart(2, "0") +
        date.getMilliseconds().toString().padStart(3, "0");

      // 生成全局业务跟踪号
      const globalBusiTrackNo =
        txStartTime + // 时间戳 (17位)
        "1022199" + // 源系统 (7位)
        "CK001" + // 发起交易实例序号 (5位)
        Math.floor(Math.random() * 1000)
          .toString()
          .padStart(3, "0"); // 序列号 (3位)

      // 生成冻结子交易序号
      const randomChar = Math.floor(Math.random() * 10).toString();
      const subtxNo =
        "10221990001111000000000" + randomChar + year + month + day;

      // 设置默认值
      form.setFieldsValue({
        frzDate: currentDate,
        txTime: currentDate,
        frzBusiTpCd: "11",
        execTpCd: "05",
        currCode: "156",
        cashExgVatgCd: "3",
        categFlagCd: "01",
        effStaCd: "1",
        waitSeqNo: "1",
        notEffConFrzBalFlag: "0",
        frzBgnDt: currentDate,
        frzBgnDtTime: currentDate,
        frzEndDt: currentDate.clone().add(3, "months"),
        ofrzEndDtTime: currentDate.clone().add(3, "months"),
        needFrzAmt: "0",
        dededAmt: "0",
        ...mainContractInfo,
        frzGloTracNo: globalBusiTrackNo, // 使用生成的常量而非函数引用
        frzSubtxNo: subtxNo, // 使用生成的常量而非函数引用
        frzChnKindCd: "01",
        frzExecFileNo: "(2011)京丰执字第00013号裁定书",
        powInstTpCd: "ATH02",
        powInstName: "北京市丰台区人民法院",
        frzReason: "司法冻结",
      });

      // 设置Select组件状态
      setFrzBusiTpCd("11");
      setExecTpCd("05");
      setCategFlagCd("01");
      setCurrCode("156");
      setCashExgVatgCd("3");
      setNotEffConFrzBalFlag("0");
      setFrzChnKindCd("01"); // 设置默认值为01-网点柜面
      setEffStaCd("1");
    }
  }, [visible, mainContractInfo, form]);

  // 提交表单
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();

      setLoading(true);

      // 详细日志以帮助排查问题
      console.log("表单验证通过，开始处理表单数据");

      // 转换日期字段前，先检查是否为有效的Moment对象
      const processDateField = (field: any): string | undefined => {
        if (field && moment.isMoment(field)) {
          return field.format("YYYYMMDD");
        } else if (field && typeof field === "string") {
          return field;
        }
        return undefined;
      };

      // 转换时间字段前，先检查是否为有效的Moment对象
      const processTimeField = (field: any): string | undefined => {
        if (field && moment.isMoment(field)) {
          return field.format("YYYYMMDDHHmmss");
        } else if (field && typeof field === "string") {
          try {
            return moment(field).format("YYYYMMDDHHmmss");
          } catch (e) {
            return field;
          }
        }
        return undefined;
      };

      // 格式化日期字段
      const formattedValues = {
        ...values,
        frzDate: processDateField(values.frzDate),
        frzBgnDt: processDateField(values.frzBgnDt),
        frzEndDt: processDateField(values.frzEndDt),
        txTime: processTimeField(values.txTime),
        frzBgnDtTime: processTimeField(values.frzBgnDtTime),
        ofrzEndDtTime: processTimeField(values.ofrzEndDtTime),
        lastTxDate: moment().format("YYYYMMDD"),
        recordStaCd: "1", // 默认有效
        frzBusiTpCd,
        execTpCd,
        categFlagCd,
        currCode,
        cashExgVatgCd,
        notEffConFrzBalFlag,
        frzChnKindCd,
        effStaCd,
      };

      console.log("准备提交数据:", formattedValues);

      const response = await insertDprgtFrz(formattedValues as DprgtFrz);

      if (response.code === 200) {
        message.success("添加成功");
        onSuccess();
      } else {
        message.error(response.msg || "添加失败");
      }
    } catch (error) {
      // 改进错误处理逻辑
      const validationError = error as FormValidationError;
      if (
        validationError.errorFields &&
        validationError.errorFields.length > 0
      ) {
        const field = validationError.errorFields[0];
        message.error(
          `表单验证失败：${field.name.join(".")}字段${field.errors[0]}`
        );
      } else {
        message.error(`提交失败：${validationError.message || "未知错误"}`);
      }
      console.error("表单提交错误:", error);
    } finally {
      setLoading(false);
    }
  };

  // 表单布局
  const formItemLayout = {
    labelCol: { span: 8 },
    wrapperCol: { span: 16 },
  };

  return (
    <Modal
      title="添加冻结登记簿信息"
      visible={visible}
      width={1000}
      onCancel={onCancel}
      footer={[
        <Button key="back" onClick={onCancel}>
          取消
        </Button>,
        <Button
          key="submit"
          type="primary"
          loading={loading}
          onClick={handleSubmit}
        >
          提交
        </Button>,
      ]}
      destroyOnClose
      maskClosable={false}
      className="rounded-modal"
      bodyStyle={{
        maxHeight: "calc(100vh - 300px)",
        overflowY: "auto",
        padding: "20px",
      }}
      zIndex={2000}
    >
      <Spin spinning={loading} tip="提交中...">
        <Form
          form={form}
          {...formItemLayout}
          layout="horizontal"
          className="frz-detail-form"
        >
          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                name="frzGloTracNo"
                label="冻结全局业务跟踪号"
                rules={[
                  { required: true, message: "请输入冻结全局业务跟踪号" },
                ]}
              >
                <Input disabled className="rounded-input" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="frzSubtxNo"
                label="冻结子交易序号"
                rules={[{ required: true, message: "请输入冻结子交易序号" }]}
              >
                <Input disabled className="rounded-input" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                name="frzDate"
                label="冻结日期"
                rules={[{ required: true, message: "请选择冻结日期" }]}
              >
                <DatePicker
                  className="rounded-input"
                  style={{ width: "100%" }}
                  getPopupContainer={(triggerNode) => {
                    const parent = triggerNode.parentNode;
                    return parent instanceof HTMLElement
                      ? parent
                      : document.body;
                  }}
                  popupStyle={{ zIndex: 9999 }}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="txTime"
                label="交易时间"
                rules={[{ required: true, message: "请选择交易时间" }]}
              >
                <DatePicker
                  disabled
                  showTime
                  format="YYYY-MM-DD HH:mm:ss"
                  className="rounded-input"
                  style={{ width: "100%" }}
                  getPopupContainer={(triggerNode) => {
                    const parent = triggerNode.parentNode;
                    return parent instanceof HTMLElement
                      ? parent
                      : document.body;
                  }}
                  popupStyle={{ zIndex: 9999 }}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                name="frzBusiTpCd"
                label="冻结业务类型代码"
                rules={[{ required: true, message: "请选择冻结业务类型代码" }]}
              >
                <Select
                  style={{ width: "100%" }}
                  placeholder="请选择冻结业务类型代码"
                  dropdownStyle={{ zIndex: 9999 }}
                  getPopupContainer={(triggerNode) => triggerNode.parentNode}
                  className="rounded-input"
                  value={frzBusiTpCd}
                  onChange={(value) => setFrzBusiTpCd(value)}
                >
                  <Option value="11">11-金额冻结</Option>
                  <Option value="21">21-账户冻结</Option>
                  <Option value="22">22-合约冻结</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="execTpCd"
                label="执行类型代码"
                rules={[{ required: true, message: "请选择执行类型代码" }]}
              >
                <Select
                  style={{ width: "100%" }}
                  placeholder="请选择执行类型代码"
                  dropdownStyle={{ zIndex: 9999 }}
                  getPopupContainer={(triggerNode) => triggerNode.parentNode}
                  className="rounded-input"
                  value={execTpCd}
                  onChange={(value) => setExecTpCd(value)}
                >
                  <Option value="05">05-司法冻结</Option>
                  <Option value="09">09-银联跨行延迟转账冻结</Option>
                  <Option value="13">13-司法止付</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                name="mainContrNo"
                label="主合约编号"
                rules={[{ required: true, message: "请输入主合约编号" }]}
              >
                <Input disabled className="rounded-input" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="mediumNo"
                label="介质编号"
                rules={[{ required: true, message: "请输入介质编号" }]}
              >
                <Input disabled className="rounded-input" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                name="persInnerAccno"
                label="个人内部账号"
                rules={[{ required: true, message: "请输入个人内部账号" }]}
              >
                <Input disabled className="rounded-input" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="saccnoSeqNo"
                label="子账号序号"
                rules={[{ required: true, message: "请输入子账号序号" }]}
              >
                <Input disabled className="rounded-input" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                name="custNo"
                label="客户编号"
                rules={[{ required: true, message: "请输入客户编号" }]}
              >
                <Input disabled className="rounded-input" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="custNm"
                label="客户名称"
                rules={[{ required: true, message: "请输入客户名称" }]}
              >
                <Input disabled className="rounded-input" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                name="perCertTpCd"
                label="个人证件类型代码"
                rules={[{ required: true, message: "请输入个人证件类型代码" }]}
              >
                <Select
                  disabled
                  style={{ width: "100%" }}
                  placeholder="请选择个人证件类型代码"
                  dropdownStyle={{ zIndex: 9999 }}
                  getPopupContainer={(triggerNode) => triggerNode.parentNode}
                  className="rounded-input"
                >
                  <Option value="1010">1010-居民身份证</Option>
                  <Option value="1011">1011-临时居民身份证</Option>
                  <Option value="1020">1020-军人身份证件</Option>
                  <Option value="1021">1021-士兵证</Option>
                  <Option value="1022">1022-军官证</Option>
                  <Option value="1023">1023-文职干部证</Option>
                  <Option value="1024">1024-军官退休证</Option>
                  <Option value="1025">1025-文职干部退休证</Option>
                  <Option value="1030">1030-武警身份证件</Option>
                  <Option value="1031">1031-武警士兵证</Option>
                  <Option value="1032">1032-警官证</Option>
                  <Option value="1033">1033-武警文职干部证</Option>
                  <Option value="1034">1034-武警军官退休证</Option>
                  <Option value="1035">1035-武警文职干部退休证</Option>
                  <Option value="1040">1040-户口簿</Option>
                  <Option value="1050">1050-中国护照</Option>
                  <Option value="1051">1051-外国护照</Option>
                  <Option value="1060">1060-学生证</Option>
                  <Option value="1070">1070-港澳居民来往内地通行证</Option>
                  <Option value="1071">1071-往来港澳通行证</Option>
                  <Option value="1080">1080-台湾居民来往大陆通行证</Option>
                  <Option value="1090">1090-执行公务证</Option>
                  <Option value="1100">1100-机动车驾驶证</Option>
                  <Option value="1110">1110-社会保障卡</Option>
                  <Option value="1120">1120-外国人居留证</Option>
                  <Option value="1121">1121-外国人永久居留证</Option>
                  <Option value="1130">1130-旅行证件</Option>
                  <Option value="1140">1140-香港居民身份证</Option>
                  <Option value="1150">1150-澳门居民身份证</Option>
                  <Option value="1160">1160-台湾居民身份证</Option>
                  <Option value="1170">1170-边民证</Option>
                  <Option value="1180">1180-港澳台居民居住证</Option>
                  <Option value="1181">1181-港澳居民居住证</Option>
                  <Option value="1182">1182-台湾居民居住证</Option>
                  <Option value="1190">1190-外国身份证</Option>
                  <Option value="1998">1998-其他（原98类）</Option>
                  <Option value="1999">1999-其他证件（个人）</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="personalCertNo"
                label="个人证件号码"
                rules={[{ required: true, message: "请输入个人证件号码" }]}
              >
                <Input disabled className="rounded-input" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                name="categFlagCd"
                label="类别标识代码"
                rules={[{ required: true, message: "请选择类别标识代码" }]}
              >
                <Select
                  style={{ width: "100%" }}
                  placeholder="请选择类别标识代码"
                  dropdownStyle={{ zIndex: 9999 }}
                  getPopupContainer={(triggerNode) => triggerNode.parentNode}
                  className="rounded-input"
                  value={categFlagCd}
                  onChange={(value) => setCategFlagCd(value)}
                >
                  <Option value="01">01-活期</Option>
                  <Option value="02">02-定期</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="waitSeqNo" label="轮候序号">
                <Input className="rounded-input" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                name="currCode"
                label="币种代码"
                rules={[{ required: true, message: "请选择币种代码" }]}
              >
                <Select
                  style={{ width: "100%" }}
                  placeholder="请选择币种代码"
                  dropdownStyle={{ zIndex: 9999 }}
                  getPopupContainer={(triggerNode) => triggerNode.parentNode}
                  className="rounded-input"
                  value={currCode}
                  onChange={(value) => setCurrCode(value)}
                >
                  <Option value="036">036-澳大利亚元</Option>
                  <Option value="124">124-加元</Option>
                  <Option value="344">344-香港元</Option>
                  <Option value="392">392-日元</Option>
                  <Option value="826">826-英镑</Option>
                  <Option value="840">840-美元</Option>
                  <Option value="978">978-欧元（EUR）</Option>
                  <Option value="156">156-人民币元</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="cashExgVatgCd"
                label="钞汇类别代码"
                rules={[{ required: true, message: "请选择钞汇类别代码" }]}
              >
                <Select
                  style={{ width: "100%" }}
                  placeholder="请选择钞汇类别代码"
                  dropdownStyle={{ zIndex: 9999 }}
                  getPopupContainer={(triggerNode) => triggerNode.parentNode}
                  className="rounded-input"
                  value={cashExgVatgCd}
                  onChange={(value) => setCashExgVatgCd(value)}
                >
                  <Option value="2">2-钞</Option>
                  <Option value="3">3-汇</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={12}>
              <Form.Item name="appointExecAmt" label="约定执行金额">
                <Input type="number" className="rounded-input" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="needFrzAmt"
                label="需冻结金额"
                rules={[{ required: true, message: "请输入需冻结金额" }]}
              >
                <Input type="number" className="rounded-input" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={12}>
              <Form.Item name="dededAmt" label="已扣划金额">
                <Input type="number" className="rounded-input" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="frzBgnDt"
                label="冻结起始日期"
                rules={[{ required: true, message: "请选择冻结起始日期" }]}
              >
                <DatePicker
                  className="rounded-input"
                  style={{ width: "100%" }}
                  getPopupContainer={(triggerNode) => {
                    return triggerNode.parentNode instanceof HTMLElement
                      ? triggerNode.parentNode
                      : document.body;
                  }}
                  popupStyle={{ zIndex: 9999 }}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={12}>
              <Form.Item name="frzBgnDtTime" label="冻结起始日期时间">
                <DatePicker
                  showTime
                  format="YYYY-MM-DD HH:mm:ss"
                  className="rounded-input"
                  style={{ width: "100%" }}
                  getPopupContainer={(triggerNode) => {
                    return triggerNode.parentNode instanceof HTMLElement
                      ? triggerNode.parentNode
                      : document.body;
                  }}
                  popupStyle={{ zIndex: 9999 }}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="frzEndDt"
                label="冻结终止日期"
                rules={[{ required: true, message: "请选择冻结终止日期" }]}
              >
                <DatePicker
                  className="rounded-input"
                  style={{ width: "100%" }}
                  getPopupContainer={(triggerNode) => {
                    return triggerNode.parentNode instanceof HTMLElement
                      ? triggerNode.parentNode
                      : document.body;
                  }}
                  popupStyle={{ zIndex: 9999 }}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={12}>
              <Form.Item name="ofrzEndDtTime" label="冻结终止日期时间">
                <DatePicker
                  showTime
                  format="YYYY-MM-DD HH:mm:ss"
                  className="rounded-input"
                  style={{ width: "100%" }}
                  getPopupContainer={(triggerNode) => {
                    return triggerNode.parentNode instanceof HTMLElement
                      ? triggerNode.parentNode
                      : document.body;
                  }}
                  popupStyle={{ zIndex: 9999 }}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="frzExecFileNo" label="冻结执行文号">
                <Input className="rounded-input" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={12}>
              <Form.Item name="powInstTpCd" label="有权机关类型代码">
                <Select
                  style={{ width: "100%" }}
                  placeholder="请选择有权机关类型代码"
                  dropdownStyle={{ zIndex: 9999 }}
                  getPopupContainer={(triggerNode) => triggerNode.parentNode}
                  className="rounded-input"
                >
                  <Option value="ATH01">ATH01-公安机关</Option>
                  <Option value="ATH02">ATH02-人民法院</Option>
                  <Option value="ATH03">ATH03-人民检查院子</Option>
                  <Option value="ATH04">ATH04-税务机关</Option>
                  <Option value="ATH05">ATH05-海关</Option>
                  <Option value="ATH06">ATH06-国家安全机关</Option>
                  <Option value="ATH07">ATH07-军队保卫部门</Option>
                  <Option value="ATH08">ATH08-监狱</Option>
                  <Option value="ATH09">ATH09-走私犯罪侦查机关</Option>
                  <Option value="ATH10">ATH10-证券监督管理机关</Option>
                  <Option value="ATH11">ATH11-监察机关</Option>
                  <Option value="ATH12">ATH12-审计机关</Option>
                  <Option value="ATH13">ATH13-工商行政管理机关</Option>
                  <Option value="ATH14">ATH14-律师</Option>
                  <Option value="ATH15">ATH15-银行保险监督管理机构</Option>
                  <Option value="ATH16">ATH16-人力资源社会保障行政部门</Option>
                  <Option value="ATH17">ATH17-破产管理人</Option>
                  <Option value="ATH18">ATH18-价格主管部门</Option>
                  <Option value="ATH19">
                    ATH19-国务院财政部门及其派出机构
                  </Option>
                  <Option value="ATH20">ATH20-外汇管理机构</Option>
                  <Option value="ATH21">
                    ATH21-中国人民银行及其省一级分支机构
                  </Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="powInstName" label="有权机关名称">
                <Input className="rounded-input" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={12}>
              <Form.Item name="contTel" label="联系电话">
                <Input className="rounded-input" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="conFrzSeqNo" label="续冻序号">
                <Input className="rounded-input" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={24}>
              <Form.Item
                name="frzReason"
                label="冻结原因"
                labelCol={{ span: 4 }}
                wrapperCol={{ span: 20 }}
              >
                <TextArea className="rounded-input" rows={3} />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={12}>
              <Form.Item name="notEffConFrzBalFlag" label="未生效续冻结存">
                <Select
                  style={{ width: "100%" }}
                  placeholder="请选择未生效续冻结存"
                  dropdownStyle={{ zIndex: 9999 }}
                  getPopupContainer={(triggerNode) => triggerNode.parentNode}
                  className="rounded-input"
                  value={notEffConFrzBalFlag}
                  onChange={(value) => setNotEffConFrzBalFlag(value)}
                >
                  <Option value="0">0-否</Option>
                  <Option value="1">1-是</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="frzChnKindCd"
                label="冻结渠道种类代码"
                rules={[{ required: true, message: "请选择冻结渠道种类代码" }]}
              >
                <Select
                  style={{ width: "100%" }}
                  placeholder="请选择冻结渠道种类代码"
                  dropdownStyle={{ zIndex: 9999 }}
                  getPopupContainer={(triggerNode) => triggerNode.parentNode}
                  className="rounded-input"
                  value={frzChnKindCd}
                  onChange={(value) => setFrzChnKindCd(value)}
                >
                  <Option value="01">01-网点柜面</Option>
                  <Option value="10">10-网上银行</Option>
                  <Option value="12">12-个人网银</Option>
                  <Option value="13">13-电视银行</Option>
                  <Option value="14">14-电话银行</Option>
                  <Option value="15">15-手机银行</Option>
                  <Option value="16">16-企业网银</Option>
                  <Option value="17">17-自助设备</Option>
                  <Option value="18">18-POS</Option>
                  <Option value="20">20-超级网银</Option>
                  <Option value="21">21-大小额支付</Option>
                  <Option value="22">22-银联前置</Option>
                  <Option value="24">24-管理端</Option>
                  <Option value="25">25-交易端</Option>
                  <Option value="26">26-商易通</Option>
                  <Option value="27">27-助农通</Option>
                  <Option value="29">29-外部系统</Option>
                  <Option value="30">30-系统自动</Option>
                  <Option value="31">31-电子汇兑系统</Option>
                  <Option value="32">32-理财规划终端</Option>
                  <Option value="34">34-网汇通</Option>
                  <Option value="35">35-同城支付</Option>
                  <Option value="36">36-移动终端-TSM（可信服务管理）</Option>
                  <Option value="37">37-移动终端-移动展业</Option>
                  <Option value="38">38-直销银行</Option>
                  <Option value="39">39-短信</Option>
                  <Option value="40">40-专属APP</Option>
                  <Option value="41">41-第三方线上渠道</Option>
                  <Option value="42">42-国际支付前置</Option>
                  <Option value="43">43-智能柜员机（ITM）</Option>
                  <Option value="44">44-邮储经营</Option>
                  <Option value="45">45-银银前置系统</Option>
                  <Option value="46">46-U链供应链</Option>
                  <Option value="47">47-油料保障结算系统</Option>
                  <Option value="48">48-银企直联</Option>
                  <Option value="91">91-微信银行</Option>
                  <Option value="99">99-其他</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={12}>
              <Form.Item name="frzInstNo" label="冻结机构号">
                <Input className="rounded-input" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="frzTellerNo" label="冻结柜员号">
                <Input className="rounded-input" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                name="effStaCd"
                label="生效状态代码"
                rules={[{ required: true, message: "请选择生效状态代码" }]}
              >
                <Select
                  style={{ width: "100%" }}
                  placeholder="请选择生效状态代码"
                  dropdownStyle={{ zIndex: 9999 }}
                  getPopupContainer={(triggerNode) => triggerNode.parentNode}
                  className="rounded-input"
                  value={effStaCd}
                  onChange={(value) => setEffStaCd(value)}
                >
                  <Option value="1">1-已生效</Option>
                  <Option value="2">2-已失效</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="zoneVal" label="分片值">
                <Input disabled className="rounded-input" />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Spin>
    </Modal>
  );
};

export default DprgtFrzAdd;
