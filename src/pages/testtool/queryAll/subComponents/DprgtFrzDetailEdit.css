/* 圆角模态框样式 */
.rounded-modal .ant-modal-content {
  border-radius: 12px;
  overflow: hidden;
}

.rounded-modal .ant-modal-header {
  border-radius: 12px 12px 0 0;
}

.rounded-modal .ant-modal-footer {
  border-radius: 0 0 12px 12px;
}

/* 圆角输入框样式 */
.rounded-input {
  border-radius: 8px !important;
}

.rounded-input .ant-select-selector {
  border-radius: 8px !important;
}

.rounded-input.ant-input[disabled] {
  border-radius: 8px !important;
  background-color: #f5f5f5;
}

/* 确保下拉菜单正确显示 */
.ant-select-dropdown {
  z-index: 9999 !important;
}

/* 美化滚动条 */
.rounded-modal .ant-modal-body::-webkit-scrollbar {
  width: 8px;
}

.rounded-modal .ant-modal-body::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.rounded-modal .ant-modal-body::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

.rounded-modal .ant-modal-body::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* 表单样式优化 */
.frz-detail-form .ant-form-item {
  margin-bottom: 12px;
}

/* 修复日期选择器内部样式 */
.ant-picker.rounded-input .ant-picker-input > input {
  border-radius: 8px;
} 