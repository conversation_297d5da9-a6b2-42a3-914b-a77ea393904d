import React, { useEffect, useState } from "react";
import {
  Modal,
  Form,
  Input,
  Button,
  DatePicker,
  Select,
  message,
  Row,
  Col,
  Spin,
} from "antd";
import { DprgtFrz } from "../data.d";
import { dprgtFrzUpdate } from "../service";
import moment, { Moment } from "moment";
import "./DprgtFrzDetailEdit.css"; // 引入样式文件

const { TextArea } = Input;
const { Option } = Select;

// 表单布局定义
const formItemLayout = {
  labelCol: { span: 8 },
  wrapperCol: { span: 16 },
};

// 定义一个接口，将所有可能是日期的字段都修改为Moment类型
interface DprgtFrzFormData {
  frzNo?: string;
  frzDate?: Moment;
  txTime?: string;
  frzGloTracNo?: string;
  frzSubtxNo?: string;
  frzBusiTpCd?: string;
  execTpCd?: string;
  mediumNo?: string;
  mainContrNo?: string;
  saccnoSeqNo?: string;
  persInnerAccno?: string;
  custNo?: string;
  custNm?: string;
  perCertTpCd?: string;
  personalCertNo?: string;
  categFlagCd?: string;
  waitSeqNo?: string;
  currCode?: string;
  cashExgVatgCd?: string;
  appointExecAmt?: string;
  needFrzAmt?: string;
  dededAmt?: string;
  frzBgnDt?: Moment; // 日期字段使用Moment类型
  frzBgnDtTime?: string;
  frzEndDt?: Moment; // 日期字段使用Moment类型
  ofrzEndDtTime?: string;
  invalDtAccti?: string;
  frzExecFileNo?: string;
  powInstTpCd?: string;
  powInstName?: string;
  powInstInaccDealOpnnCd?: string;
  jstcCaseAttrCd?: string;
  contTel?: string;
  frzReason?: string;
  conFrzSeqNo?: string;
  oldFrzBgnDt?: Moment; // 日期字段使用Moment类型
  oldFrzEndDt?: Moment; // 日期字段使用Moment类型
  notEffConFrzBalFlag?: string;
  frzChnKindCd?: string;
  startSysOrCmptNo?: string;
  openAccInstNo?: string;
  frzInstNo?: string;
  frzTellerNo?: string;
  frzAuthTellerNo?: string;
  effStaCd?: string;
  lastTxDate?: Moment; // 日期字段使用Moment类型
  zoneVal?: string;
}

interface DprgtFrzDetailEditProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: () => void;
  record: DprgtFrz | undefined;
  loading?: boolean; // 添加loading属性
}

const DprgtFrzDetailEdit: React.FC<DprgtFrzDetailEditProps> = ({
  visible,
  onCancel,
  onSuccess,
  record,
  loading,
}) => {
  const [form] = Form.useForm<DprgtFrzFormData>();
  // 添加修改字段跟踪状态
  const [modifiedFields, setModifiedFields] = useState<Set<string>>(new Set());
  const [initialValues, setInitialValues] = useState<any>({});

  const [effStaCd, setEffStaCd] = useState<string | undefined>(
    record?.effStaCd
  );
  const [frzChnKindCd, setFrzChnKindCd] = useState<string | undefined>(
    record?.frzChnKindCd
  );

  const [frzBusiTpCd, setFrzBusiTpCd] = useState<string | undefined>(
    record?.frzBusiTpCd
  );

  const [execTpCd, setExecTpCd] = useState<string | undefined>(
    record?.execTpCd
  );

  const [perCertTpCd, setPerCertTpCd] = useState<string | undefined>(
    record?.perCertTpCd
  );

  const [categFlagCd, setCategFlagCd] = useState<string | undefined>(
    record?.categFlagCd
  );

  const [currCode, setCurrCode] = useState<string | undefined>(
    record?.currCode
  );

  const [cashExgVatgCd, setCashExgVatgCd] = useState<string | undefined>(
    record?.cashExgVatgCd
  );

  const [powInstTpCd, setPowInstTpCd] = useState<string | undefined>(
    record?.powInstTpCd
  );

  const [powInstInaccDealOpnnCd, setPowInstInaccDealOpnnCd] = useState<
    string | undefined
  >(record?.powInstInaccDealOpnnCd);

  const [jstcCaseAttrCd, setJstcCaseAttrCd] = useState<string | undefined>(
    record?.jstcCaseAttrCd
  );

  const [notEffConFrzBalFlag, setNotEffConFrzBalFlag] = useState<
    string | undefined
  >(record?.notEffConFrzBalFlag);

  useEffect(() => {
    if (record && visible) {
      // 重置修改字段跟踪状态
      setModifiedFields(new Set());

      // 更新Select组件的状态
      setEffStaCd(record.effStaCd);
      setFrzChnKindCd(record.frzChnKindCd);

      // 创建表单数据对象，复制非日期字段
      const formValues: DprgtFrzFormData = {
        ...record,
        // 清除日期字段，避免类型错误
        frzDate: undefined,
        frzBgnDt: undefined,
        frzEndDt: undefined,
        oldFrzBgnDt: undefined,
        oldFrzEndDt: undefined,
        lastTxDate: undefined,
      };

      // 转换日期字段为Moment对象
      if (record.frzDate) {
        formValues.frzDate = moment(record.frzDate);
      }
      if (record.frzBgnDt) {
        formValues.frzBgnDt = moment(record.frzBgnDt);
      }
      if (record.frzEndDt) {
        formValues.frzEndDt = moment(record.frzEndDt);
      }
      if (record.oldFrzBgnDt) {
        formValues.oldFrzBgnDt = moment(record.oldFrzBgnDt);
      }
      if (record.oldFrzEndDt) {
        formValues.oldFrzEndDt = moment(record.oldFrzEndDt);
      }
      if (record.lastTxDate) {
        formValues.lastTxDate = moment(record.lastTxDate);
      }

      // 确保effStaCd和frzChnKindCd正确设置（转为字符串）
      if (record.effStaCd !== undefined && record.effStaCd !== null) {
        formValues.effStaCd = String(record.effStaCd);
      }

      if (record.frzChnKindCd !== undefined && record.frzChnKindCd !== null) {
        formValues.frzChnKindCd = String(record.frzChnKindCd);
      }

      // 保存初始值，用于后续比较
      setInitialValues(formValues);
      form.setFieldsValue(formValues);
    } else {
      form.resetFields();
      setEffStaCd(undefined);
      setFrzChnKindCd(undefined);
      setModifiedFields(new Set());
      setInitialValues({});
    }
  }, [form, record, visible]);

  // 值变化处理函数
  const handleValueChange = (changedValues: any) => {
    // 记录修改的字段
    Object.keys(changedValues).forEach((fieldName) => {
      setModifiedFields((prev) => {
        const newSet = new Set(prev);
        newSet.add(fieldName);
        return newSet;
      });
    });
  };

  // 处理Select值变化
  const handleSelectChange = (fieldName: string, value: any) => {
    // 记录修改的字段
    setModifiedFields((prev) => {
      const newSet = new Set(prev);
      newSet.add(fieldName);
      return newSet;
    });

    // 更新对应的状态
    switch (fieldName) {
      case "effStaCd":
        setEffStaCd(value);
        break;
      case "frzChnKindCd":
        setFrzChnKindCd(value);
        break;
      case "frzBusiTpCd":
        setFrzBusiTpCd(value);
        break;
      case "execTpCd":
        setExecTpCd(value);
        break;
      case "perCertTpCd":
        setPerCertTpCd(value);
        break;
      case "categFlagCd":
        setCategFlagCd(value);
        break;
      case "currCode":
        setCurrCode(value);
        break;
      case "cashExgVatgCd":
        setCashExgVatgCd(value);
        break;
      case "powInstTpCd":
        setPowInstTpCd(value);
        break;
      case "powInstInaccDealOpnnCd":
        setPowInstInaccDealOpnnCd(value);
        break;
      case "jstcCaseAttrCd":
        setJstcCaseAttrCd(value);
        break;
      case "notEffConFrzBalFlag":
        setNotEffConFrzBalFlag(value);
        break;
      default:
        break;
    }
  };

  const handleSubmit = async () => {
    try {
      // 先同步受控组件的值到表单
      form.setFieldsValue({
        effStaCd: effStaCd,
        frzChnKindCd: frzChnKindCd,
        frzBusiTpCd: frzBusiTpCd,
        execTpCd: execTpCd,
        perCertTpCd: perCertTpCd,
        categFlagCd: categFlagCd,
        currCode: currCode,
        cashExgVatgCd: cashExgVatgCd,
        powInstTpCd: powInstTpCd,
        powInstInaccDealOpnnCd: powInstInaccDealOpnnCd,
        jstcCaseAttrCd: jstcCaseAttrCd,
        notEffConFrzBalFlag: notEffConFrzBalFlag,
      });

      const values = await form.validateFields();

      // 只创建包含固定字段的提交数据
      const submitData: Record<string, any> = {
        // 固定字段
        frzNo: record?.frzNo,
        zoneVal: record?.zoneVal,
      };

      // 添加修改过的字段
      modifiedFields.forEach((fieldName) => {
        const fieldValue = values[fieldName as keyof DprgtFrzFormData];

        // 处理日期字段
        if (fieldValue && moment.isMoment(fieldValue)) {
          if (fieldName.includes("Date") || fieldName.includes("Dt")) {
            submitData[fieldName] = fieldValue.format("YYYYMMDD");
          } else {
            submitData[fieldName] = fieldValue.toISOString();
          }
        }
        // 处理时间字段
        else if (fieldName.endsWith("Time") && fieldValue) {
          if (moment.isMoment(fieldValue)) {
            submitData[fieldName] = fieldValue.toISOString();
          } else if (typeof fieldValue === "string") {
            submitData[fieldName] = moment(fieldValue).toISOString();
          } else {
            submitData[fieldName] = fieldValue;
          }
        }
        // 处理其他字段
        else {
          submitData[fieldName] = fieldValue;
        }
      });

      console.log("提交的数据:", submitData);
      console.log("修改的字段:", Array.from(modifiedFields));

      // 将结果转换为DprgtFrz类型
      const result = await dprgtFrzUpdate(submitData as DprgtFrz);

      if (result.code === 200) {
        message.success("更新成功");
        // 调用onSuccess回调，通知父组件刷新数据
        onSuccess();
        // 关闭模态框
        onCancel();
      } else {
        message.error(result.msg || "更新失败");
        onCancel();
      }
    } catch (error) {
      console.error("提交表单时出错:", error);
      message.error("提交表单失败");
    }
  };

  return (
    <Modal
      title="冻结记录详情编辑"
      visible={visible}
      onCancel={onCancel}
      width={1000}
      zIndex={2000}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button
          key="submit"
          type="primary"
          loading={loading}
          onClick={handleSubmit}
        >
          提交
        </Button>,
      ]}
      className="rounded-modal"
      bodyStyle={{
        maxHeight: "calc(100vh - 300px)",
        overflowY: "auto",
        padding: "20px",
      }}
      destroyOnClose
    >
      <Spin spinning={loading} tip="数据加载中...">
        <Form
          form={form}
          layout="horizontal"
          {...formItemLayout}
          className="frz-detail-form"
          onValuesChange={handleValueChange}
        >
          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                name="frzNo"
                label="冻结编号"
                rules={[{ required: true }]}
              >
                <Input disabled className="rounded-input" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="frzDate" label="冻结日期">
                <DatePicker
                  className="rounded-input"
                  style={{ width: "100%" }}
                  getPopupContainer={(triggerNode) => {
                    const parent = triggerNode.parentNode;
                    return parent instanceof HTMLElement
                      ? parent
                      : document.body;
                  }}
                  popupStyle={{ zIndex: 9999 }}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={12}>
              <Form.Item name="frzGloTracNo" label="冻结全局业务跟踪号">
                <Input className="rounded-input" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="frzSubtxNo" label="冻结子交易序号">
                <Input className="rounded-input" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={12}>
              <Form.Item name="frzBusiTpCd" label="冻结业务类型代码">
                <Select
                  style={{ width: "100%" }}
                  placeholder="请选择冻结业务类型代码"
                  dropdownStyle={{ zIndex: 9999 }}
                  getPopupContainer={(triggerNode) => triggerNode.parentNode}
                  className="rounded-input"
                  value={frzBusiTpCd}
                  onChange={(value) => handleSelectChange("frzBusiTpCd", value)}
                >
                  <Option value="11">11-金额冻结</Option>
                  <Option value="21">21-账户冻结</Option>
                  <Option value="22">22-合约冻结</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="execTpCd" label="执行类型代码">
                <Select
                  style={{ width: "100%" }}
                  placeholder="请选执行类型代码"
                  dropdownStyle={{ zIndex: 9999 }}
                  getPopupContainer={(triggerNode) => triggerNode.parentNode}
                  className="rounded-input"
                  value={execTpCd}
                  onChange={(value) => handleSelectChange("execTpCd", value)}
                >
                  <Option value="05">05-司法冻结</Option>
                  <Option value="09">09-银联跨行延迟转账冻结</Option>
                  <Option value="13">13-司法止付</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={12}>
              <Form.Item name="mediumNo" label="介质编号">
                <Input disabled className="rounded-input" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="mainContrNo" label="主合约编号">
                <Input disabled className="rounded-input" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={12}>
              <Form.Item name="saccnoSeqNo" label="子账号序号">
                <Input disabled className="rounded-input" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="persInnerAccno" label="个人内部账号">
                <Input disabled className="rounded-input" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={12}>
              <Form.Item name="custNo" label="客户编号">
                <Input disabled className="rounded-input" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="custNm" label="客户名称">
                <Input disabled className="rounded-input" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={12}>
              <Form.Item name="perCertTpCd" label="个人证件类型代码">
                <Select
                  style={{ width: "100%" }}
                  placeholder="请选择个人证件类型代码"
                  dropdownStyle={{ zIndex: 9999 }}
                  getPopupContainer={(triggerNode) => triggerNode.parentNode}
                  className="rounded-input"
                  value={perCertTpCd}
                  disabled={true}
                  onChange={(value) => handleSelectChange("perCertTpCd", value)}
                >
                  <Option value="1010">1010-居民身份证</Option>
                  <Option value="1011">1011-临时居民身份证</Option>
                  <Option value="1020">1020-军人身份证件</Option>
                  <Option value="1021">1021-士兵证</Option>
                  <Option value="1022">1022-军官证</Option>
                  <Option value="1023">1023-文职干部证</Option>
                  <Option value="1024">1024-军官退休证</Option>
                  <Option value="1025">1025-文职干部退休证</Option>
                  <Option value="1030">1030-武警身份证件</Option>
                  <Option value="1031">1031-武警士兵证</Option>
                  <Option value="1032">1032-警官证</Option>
                  <Option value="1033">1033-武警文职干部证</Option>
                  <Option value="1034">1034-武警军官退休证</Option>
                  <Option value="1035">1035-武警文职干部退休证</Option>
                  <Option value="1040">1040-户口簿</Option>
                  <Option value="1050">1050-中国护照</Option>
                  <Option value="1051">1051-外国护照</Option>
                  <Option value="1060">1060-学生证</Option>
                  <Option value="1070">1070-港澳居民来往内地通行证</Option>
                  <Option value="1071">1071-往来港澳通行证</Option>
                  <Option value="1080">1080-台湾居民来往大陆通行证</Option>
                  <Option value="1090">1090-执行公务证</Option>
                  <Option value="1100">1100-机动车驾驶证</Option>
                  <Option value="1110">1110-社会保障卡</Option>
                  <Option value="1120">1120-外国人居留证</Option>
                  <Option value="1121">1121-外国人永久居留证</Option>
                  <Option value="1130">1130-旅行证件</Option>
                  <Option value="1140">1140-香港居民身份证</Option>
                  <Option value="1150">1150-澳门居民身份证</Option>
                  <Option value="1160">1160-台湾居民身份证</Option>
                  <Option value="1170">1170-边民证</Option>
                  <Option value="1180">1180-港澳台居民居住证</Option>
                  <Option value="1181">1181-港澳居民居住证</Option>
                  <Option value="1182">1182-台湾居民居住证</Option>
                  <Option value="1190">1190-外国身份证</Option>
                  <Option value="1998">1998-其他（原98类）</Option>
                  <Option value="1999">1999-其他证件（个人）</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="personalCertNo" label="个人证件号码">
                <Input disabled className="rounded-input" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={12}>
              <Form.Item name="categFlagCd" label="类别标识代码">
                <Select
                  style={{ width: "100%" }}
                  placeholder="请选择类别标识代码"
                  dropdownStyle={{ zIndex: 9999 }}
                  getPopupContainer={(triggerNode) => triggerNode.parentNode}
                  className="rounded-input"
                  value={categFlagCd}
                  onChange={(value) => handleSelectChange("categFlagCd", value)}
                >
                  <Option value="01">01-活期</Option>
                  <Option value="02">02-定期</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="waitSeqNo" label="等待序号">
                <Input className="rounded-input" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={12}>
              <Form.Item name="currCode" label="币种代码">
                <Select
                  style={{ width: "100%" }}
                  placeholder="请选择币种代码"
                  dropdownStyle={{ zIndex: 9999 }}
                  getPopupContainer={(triggerNode) => triggerNode.parentNode}
                  className="rounded-input"
                  value={currCode}
                  onChange={(value) => handleSelectChange("currCode", value)}
                >
                  <Option value="036">036-澳大利亚元</Option>
                  <Option value="124">124-加元</Option>
                  <Option value="344">344-香港元</Option>
                  <Option value="392">392-日元</Option>
                  <Option value="826">826-英镑</Option>
                  <Option value="840">840-美元</Option>
                  <Option value="978">978-欧元（EUR）</Option>
                  <Option value="156">156-人民币元</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="cashExgVatgCd" label="钞汇类别代码">
                <Select
                  style={{ width: "100%" }}
                  placeholder="请选择钞汇类别代码"
                  dropdownStyle={{ zIndex: 9999 }}
                  getPopupContainer={(triggerNode) => triggerNode.parentNode}
                  className="rounded-input"
                  value={cashExgVatgCd}
                  onChange={(value) =>
                    handleSelectChange("cashExgVatgCd", value)
                  }
                >
                  <Option value="2">2-钞</Option>
                  <Option value="3">3-汇</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={12}>
              <Form.Item name="appointExecAmt" label="约定执行金额">
                <Input className="rounded-input" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="needFrzAmt" label="需冻结金额">
                <Input className="rounded-input" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={12}>
              <Form.Item name="dededAmt" label="已扣金额">
                <Input className="rounded-input" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="frzBgnDt" label="冻结起始日期">
                <DatePicker
                  className="rounded-input"
                  style={{ width: "100%" }}
                  getPopupContainer={(triggerNode) => {
                    return triggerNode.parentNode instanceof HTMLElement
                      ? triggerNode.parentNode
                      : document.body;
                  }}
                  popupStyle={{ zIndex: 9999 }}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={12}>
              <Form.Item name="frzBgnDtTime" label="冻结起始日期时间">
                <Input className="rounded-input" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="frzEndDt" label="冻结终止日期">
                <DatePicker
                  className="rounded-input"
                  style={{ width: "100%" }}
                  getPopupContainer={(triggerNode) => {
                    return triggerNode.parentNode instanceof HTMLElement
                      ? triggerNode.parentNode
                      : document.body;
                  }}
                  popupStyle={{ zIndex: 9999 }}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={12}>
              <Form.Item name="ofrzEndDtTime" label="冻结终止日期时间">
                <Input className="rounded-input" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="invalDtAccti" label="失效日期账时">
                <Input className="rounded-input" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={12}>
              <Form.Item name="frzExecFileNo" label="冻结执行文号">
                <Input className="rounded-input" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="powInstTpCd" label="有权机关类型代码">
                <Select
                  style={{ width: "100%" }}
                  placeholder="请选择有权机关类型代码"
                  dropdownStyle={{ zIndex: 9999 }}
                  getPopupContainer={(triggerNode) => triggerNode.parentNode}
                  className="rounded-input"
                  value={powInstTpCd}
                  onChange={(value) => handleSelectChange("powInstTpCd", value)}
                >
                  <Option value="ATH01">ATH01-公安机关</Option>
                  <Option value="ATH02">ATH02-人民法院</Option>
                  <Option value="ATH03">ATH03-人民检查院子</Option>
                  <Option value="ATH04">ATH04-税务机关</Option>
                  <Option value="ATH05">ATH05-海关</Option>
                  <Option value="ATH06">ATH06-国家安全机关</Option>
                  <Option value="ATH07">ATH07-军队保卫部门</Option>
                  <Option value="ATH08">ATH08-监狱</Option>
                  <Option value="ATH09">ATH09-走私犯罪侦查机关</Option>
                  <Option value="ATH10">ATH10-证券监督管理机关</Option>
                  <Option value="ATH11">ATH11-监察机关</Option>
                  <Option value="ATH12">ATH12-审计机关</Option>
                  <Option value="ATH13">ATH13-工商行政管理机关</Option>
                  <Option value="ATH14">ATH14-律师</Option>
                  <Option value="ATH15">ATH15-银行保险监督管理机构</Option>
                  <Option value="ATH16">ATH16-人力资源社会保障行政部门</Option>
                  <Option value="ATH17">ATH17-破产管理人</Option>
                  <Option value="ATH18">ATH18-价格主管部门</Option>
                  <Option value="ATH19">
                    ATH19-国务院财政部门及其派出机构
                  </Option>
                  <Option value="ATH20">ATH20-外汇管理机构</Option>
                  <Option value="ATH21">
                    ATH21-中国人民银行及其省一级分支机构
                  </Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={12}>
              <Form.Item name="powInstName" label="有权机关名称">
                <Input className="rounded-input" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="powInstInaccDealOpnnCd"
                label="有权机关对账户处理意见代码"
              >
                <Select
                  style={{ width: "100%" }}
                  placeholder="请选择有权机关对账户处理意见代码"
                  dropdownStyle={{ zIndex: 9999 }}
                  getPopupContainer={(triggerNode) => triggerNode.parentNode}
                  className="rounded-input"
                  value={powInstInaccDealOpnnCd}
                  onChange={(value) =>
                    handleSelectChange("powInstInaccDealOpnnCd", value)
                  }
                >
                  <Option value="00">00-未明确</Option>
                  <Option value="01">01-允许尽调及管控</Option>
                  <Option value="02">02-不允许尽调及管控</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={12}>
              <Form.Item name="jstcCaseAttrCd" label="司法案件性质代码">
                <Select
                  style={{ width: "100%" }}
                  placeholder="请选择司法案件性质代码"
                  dropdownStyle={{ zIndex: 9999 }}
                  getPopupContainer={(triggerNode) => triggerNode.parentNode}
                  className="rounded-input"
                  value={jstcCaseAttrCd}
                  onChange={(value) =>
                    handleSelectChange("jstcCaseAttrCd", value)
                  }
                >
                  <Option value="00">00-未知</Option>
                  <Option value="01">01-刑事案件</Option>
                  <Option value="02">02-非刑事案件</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="contTel" label="联系电话">
                <Input className="rounded-input" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={24}>
              <Form.Item
                name="frzReason"
                label="冻结原因"
                labelCol={{ span: 4 }}
                wrapperCol={{ span: 20 }}
              >
                <TextArea className="rounded-input" rows={3} />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={12}>
              <Form.Item name="conFrzSeqNo" label="条件冻结序号">
                <Input className="rounded-input" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="oldFrzBgnDt" label="原冻结起始日期">
                <DatePicker
                  className="rounded-input"
                  style={{ width: "100%" }}
                  getPopupContainer={(triggerNode) => {
                    return triggerNode.parentNode instanceof HTMLElement
                      ? triggerNode.parentNode
                      : document.body;
                  }}
                  popupStyle={{ zIndex: 9999 }}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={12}>
              <Form.Item name="oldFrzEndDt" label="原冻结终止日期">
                <DatePicker
                  className="rounded-input"
                  style={{ width: "100%" }}
                  getPopupContainer={(triggerNode) => {
                    return triggerNode.parentNode instanceof HTMLElement
                      ? triggerNode.parentNode
                      : document.body;
                  }}
                  popupStyle={{ zIndex: 9999 }}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="notEffConFrzBalFlag"
                label="未生效条件冻结余额标志"
              >
                <Select
                  style={{ width: "100%" }}
                  placeholder="请选择未生效条件冻结余额标志"
                  dropdownStyle={{ zIndex: 9999 }}
                  getPopupContainer={(triggerNode) => triggerNode.parentNode}
                  className="rounded-input"
                  value={notEffConFrzBalFlag}
                  onChange={(value) =>
                    handleSelectChange("notEffConFrzBalFlag", value)
                  }
                >
                  <Option value="00">00-未知</Option>
                  <Option value="01">01-刑事案件</Option>
                  <Option value="02">02-非刑事案件</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={12}>
              <Form.Item name="frzChnKindCd" label="冻结渠道种类代码">
                <Select
                  style={{ width: "100%" }}
                  placeholder="请选择冻结渠道种类代码"
                  dropdownStyle={{ zIndex: 9999 }}
                  getPopupContainer={(triggerNode) => triggerNode.parentNode}
                  className="rounded-input"
                  value={frzChnKindCd}
                  onChange={(value) =>
                    handleSelectChange("frzChnKindCd", value)
                  }
                >
                  <Option value="01">01-网点柜面</Option>
                  <Option value="10">10-网上银行</Option>
                  <Option value="12">12-个人网银</Option>
                  <Option value="13">13-电视银行</Option>
                  <Option value="14">14-电话银行</Option>
                  <Option value="15">15-手机银行</Option>
                  <Option value="16">16-企业网银</Option>
                  <Option value="17">17-自助设备</Option>
                  <Option value="18">18-POS</Option>
                  <Option value="20">20-超级网银</Option>
                  <Option value="21">21-大小额支付</Option>
                  <Option value="22">22-银联前置</Option>
                  <Option value="24">24-管理端</Option>
                  <Option value="25">25-交易端</Option>
                  <Option value="26">26-商易通</Option>
                  <Option value="27">27-助农通</Option>
                  <Option value="29">29-外部系统</Option>
                  <Option value="30">30-系统自动</Option>
                  <Option value="31">31-电子汇兑系统</Option>
                  <Option value="32">32-理财规划终端</Option>
                  <Option value="34">34-网汇通</Option>
                  <Option value="35">35-同城支付</Option>
                  <Option value="36">36-移动终端-TSM（可信服务管理）</Option>
                  <Option value="37">37-移动终端-移动展业</Option>
                  <Option value="38">38-直销银行</Option>
                  <Option value="39">39-短信</Option>
                  <Option value="40">40-专属APP</Option>
                  <Option value="41">41-第三方线上渠道</Option>
                  <Option value="42">42-国际支付前置</Option>
                  <Option value="43">43-智能柜员机（ITM）</Option>
                  <Option value="44">44-邮储经营</Option>
                  <Option value="45">45-银银前置系统</Option>
                  <Option value="46">46-U链供应链</Option>
                  <Option value="47">47-油料保障结算系统</Option>
                  <Option value="48">48-银企直联</Option>
                  <Option value="91">91-微信银行</Option>
                  <Option value="99">99-其他</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="startSysOrCmptNo" label="发起系统或组件编号">
                <Input className="rounded-input" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={12}>
              <Form.Item name="openAccInstNo" label="开户机构号">
                <Input className="rounded-input" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="frzInstNo" label="冻结机构号">
                <Input className="rounded-input" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={12}>
              <Form.Item name="frzTellerNo" label="冻结柜员号">
                <Input className="rounded-input" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="frzAuthTellerNo" label="冻结授权柜员号">
                <Input className="rounded-input" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={12}>
              <Form.Item name="effStaCd" label="生效状态代码">
                <Select
                  style={{ width: "100%" }}
                  placeholder="请选择生效状态代码"
                  dropdownStyle={{ zIndex: 9999 }}
                  getPopupContainer={(triggerNode) => triggerNode.parentNode}
                  className="rounded-input"
                  value={effStaCd}
                  onChange={(value) => handleSelectChange("effStaCd", value)}
                >
                  <Option value="1">1-已生效</Option>
                  <Option value="2">2-已失效</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="lastTxDate" label="最后交易日期">
                <DatePicker
                  className="rounded-input"
                  style={{ width: "100%" }}
                  getPopupContainer={(triggerNode) => {
                    return triggerNode.parentNode instanceof HTMLElement
                      ? triggerNode.parentNode
                      : document.body;
                  }}
                  popupStyle={{ zIndex: 9999 }}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={12}>
              <Form.Item name="zoneVal" label="分片值">
                <Input disabled className="rounded-input" />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Spin>
    </Modal>
  );
};

export default DprgtFrzDetailEdit;
