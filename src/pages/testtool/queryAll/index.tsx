import React, { useState, useEffect } from "react";
import { KeepAlive } from "react-activation";
import { ConfigProvider, Ta<PERSON>, Button, Dropdown, message, Tooltip } from "antd";
import { MoreOutlined, InfoCircleOutlined } from "@ant-design/icons";
import { SearchSection } from "./components/SearchSection";
import { DataSection } from "./components/DataSection";
import { storage } from "@/utils/storageUtils";
import { useAccess } from "umi";
import "./style.less";
import {
  getGeneralFlagText,
  getMediumTpCdText,
  getCateFlagCdText,
  getCardMediumCodeText,
  getCardKinCdText,
  getSavTypeMclassCodeText,
  getCurrCodeText,
  getSignChnKindCdText,
  getDpContrTpCdText,
  getPersDepAccKindCdText,
  getSpecContrTpCdText,
  getMainContrFlagText,
  getPersDepAccTpCdText,
  getCashExgVatgCdText,
  getTermUnitCodeText,
  getExecCycValText,
  getRelCategFgCdText,
  getPerCertTpCdText,
  getNineItemInfoIncompGlagText,
} from "./constants/TabListMap";
import type { MediumFormValueType } from "./components/MediumFormEdit";
import type { DpmstCurtContFormValueType } from "./components/DpmstCurtContFormEdit";
import type { DpmstCurtAccFormValueType } from "./components/DpmstCurtAccFormEdit";
import type { DpmstFixContFormValueType } from "./components/DpmstFixContFormEdit";
import type { DpmstFixAccFormValueType } from "./components/DpmstFixAccFormEdit";
import type { DprgtMaslaveContRelatFormValueType } from "./components/DprgtMaslaveContRelatFormEdit";
import type { CustInfoFormValueType } from "./components/CustInfoFormEdit";

import MediumForm from "./components/MediumFormEdit";
import DpmstCurtContForm from "./components/DpmstCurtContFormEdit";
import DpmstCurtAccForm from "./components/DpmstCurtAccFormEdit";
import DpmstFixContForm from "./components/DpmstFixContFormEdit";
import DpmstFixAccForm from "./components/DpmstFixAccFormEdit";
import DprgtMaslaveContRelatForm from "./components/DprgtMaslaveContRelatFormEdit";
import CustInfoForm from "./components/CustInfoFormEdit";
import DetailDpdtlCurtFormEdit from "./components/DetailDpdtlCurtFormEdit";
import DetailDpdtlFixFormEdit from "./components/DetailDpdtlFixFormEdit";
import DprgtPersDepLifeCycEdit from "./components/DprgtPersDepLifeCycEdit";
import DprgtPersonJointAccFormEdit from "./components/DprgtPersonJointAccFormEdit";
import DprgtFrzFormEdit from "./components/DprgtFrzFormEdit";
import DprgtStopPayFormEdit from "./components/DprgtStopPayFormEdit";
import DprgtSignAddFormEdit from "./components/DprgtSignAddFormEdit";
import DprgtAccFreeFormEdit from "./components/DprgtAccFreeFormEdit";
import DprgtNoCardPayFormEdit from "./components/DprgtNoCardPayFormEdit";
import DprgtChgCardFormEdit from "./components/DprgtChgCardFormEdit";
import DprgtTobeLimtServFormEdit from "./components/DprgtTobeLimtServFormEdit";
import DprgtLimtServFormEdit from "./components/DprgtLimtServFormEdit";

import RelMediumTypeFlagCdEdit from "./components/RelMediumTypeFlagCdEdit";
import StatusFlagCdEdit from "./components/StatusFlagCdEdit";
import AttrbuteFlagCdEdit from "./components/AttrbuteFlagCdEdit";
import CrtModeFlagCdEdit from "./components/CrtModeFlagCdEdit";

import CurtMContrFlagCdEdit from "./components/CurtMContrFlagCdEdit";
import FixMContrFlagCdEdit from "./components/FixMContrFlagCdEdit";
import CurtContrCtrlFlagCdEdit from "./components/CurtContrCtrlFlagCdEdit";
import FixContrCtrlFlagCdEdit from "./components/FixContrCtrlFlagCdEdit";
import CurtContrAttrFgCdEdit from "./components/CurtContrAttrFgCdEdit";
import FixContrAttrFgCdEdit from "./components/FixContrAttrFgCdEdit";
import CurtContrStaFgCdEdit from "./components/CurtContrStaFgCdEdit";
import FixContrStaFgCdEdit from "./components/FixContrStaFgCdEdit";
import CurtAccStatusFlagCdEdit from "./components/CurtAccStatusFlagCdEdit";
import FixAccStatusFlagCdEdit from "./components/FixAccStatusFlagCdEdit";
import CurtAccFlagCdEdit from "./components/CurtAccFlagCdEdit";
import FixAccFlagCdEdit from "./components/FixAccFlagCdEdit";

import {
  TabData,
  TbDpmstMedium,
  TbDpmstCurtCont,
  TbDpmstCurtAcc,
  TbDpmstFixCont,
  TbDpmstFixAcc,
  TbDprgtMaslaveContRelat,
  CustInfo,
  TbDpdtlCurtAcc,
  TbDpdtlFixAcc,
  TbDprgtPersDepLifeCyc,
  PageResponse,
  DprgtPersonJointAcc,
  DprgtFrz,
  DprgtStopPay,
  TbDprgtSignAdd,
  DprgtAccFree,
  DprgtNoCardPay,
  DprgtChgCard,
  TbDprgtTobeLimtServ,
  TbDprgtLimtServ,
  Consum,
  SaleRet,
  AccUpdown,
  DprgtContrAsst,
  DprgtCflAddoffInfo,
  DprgtAccStar,
  DprgtCarryBalCalInt,
  TbDprgtFundsUsageControl,
  TbDprgtDepProve,
  TbDpdtlFundsUsageControlLoan,
} from "./data.d";

import {
  dpmstMediumUpdate,
  dpmstCurtContUpdate,
  dpmstCurtAccUpdate,
  dpmstFixContUpdate,
  dpmstFixAccUpdate,
  dprgtMaslaveContRelatUpdate,
  custInfoUpdate,
  queryAll,
  queryDprgtMaslaveContRelatPageInfo,
  queryDpRelmstCurtContDtoPageInfo,
  queryDpRelmstCurtAccDtoPageInfo,
  queryDpRelmstFixContDtoPageInfo,
  queryDpRelmstFixAccDtoPageInfo,
  getDpmstMedium,
  getDpmstCurtCont,
  getDpmstFixCont,
  getDpmstCurtAcc,
  getDpmstFixAcc,
  getDprgtMaslaveContRelat,
  getCustInfo,
  getDpdtlCurtAcc,
  getDpdtlFixAcc,
  getDprgtPersDepLifeCyc,
  getDprgtPersonJointAcc,
  getDprgtFrz,
  getTbDprgtStopPay,
  getTbDprgtSignAdd,
  getTbDprgtAccFree,
  getDprgtNoCardPay,
  getDprgtChgCard,
  getTobeLimtServ,
  getLimtServ,
  getConsum,
  getSaleRet,
  getAccUpdown,
  getDprgtContrAsst,
  getDprgtCflAddoffInfo,
  getDprgtAccBindRelat,
  getDpdtlFundsUsageControl,
  getDprgtFundsUsageControl,
  getDprgtFundsUsageControlLoan,
  getDprgtAccStar,
  getDprgtCarryBalCalInt,
  getDprgtDepProve,
  getDprgtCustLimtServ,
  queryTbDprgtDepProveOpenInfo,
  getDpdtlFundsUsageControlLoan,
} from "./service";
import * as service from "./service";

// 添加导入ConsumFormEdit组件
import ConsumFormEdit from "./components/ConsumFormEdit";

// 添加以下导入
import SaleRetFormEdit from "./components/SaleRetFormEdit";
import AccUpdownFormEdit from "./components/AccUpdownFormEdit";
import DprgtContrAsstFormEdit from "./components/DprgtContrAsstFormEdit";
import DprgtCflAddoffInfoFormEdit from "./components/DprgtCflAddoffInfoFormEdit";
import DprgtAccBindRelatFormEdit from "./components/DprgtAccBindRelatFormEdit";
import DpdtlFundsUsageControlFormEdit from "./components/DpdtlFundsUsageControlFormEdit";
import DprgtFundsUsageControlFormEdit from "./components/DprgtFundsUsageControlFormEdit";
import DprgtFundsUsageControlLoanFormEdit from "./components/DprgtFundsUsageControlLoanFormEdit";
import DprgtAccStarFormEdit from "./components/DprgtAccStarFormEdit";
import DprgtCarryBalCalIntFormEdit from "./components/DprgtCarryBalCalIntFormEdit";
import DepProveFormEdit from "./components/DepProveFormEdit";
import DpRgtCustLimtServFormEdit from "./components/DpRgtCustLimtServFormEdit";
import DprgtDepProveOpenInfoFormEdit from "./components/DprgtDepProveOpenInfoFormEdit";
import DpdtlFundsUsageControlLoanFormEdit from "./components/DpdtlFundsUsageControlLoanFormEdit";

interface PageInfo {
  records: any[];
  total: number;
  size: number;
  current: number;
}

// 声明返回的接口
interface QueryAllData {
  dpmstMedium: any;
  dpmstCurtContDto: any;
  dpmstCurtAccDto: any;
  dpmstFixContDto: any;
  dpmstFixAccDto: any;
  dprgtMaslaveContRelatPageInfo: PageInfo;
  dpRelmstCurtContDtoPageInfo: PageInfo;
  dpRelmstCurtAccDtoPageInfo: PageInfo;
  dpRelFixContDtoPageInfo: PageInfo;
  dpRelFixAccDtoPageInfo: PageInfo;
  custInfoDto: any;
}

const QueryList: React.FC = () => {
  const access = useAccess();
  const [activeTab, setActiveTab] = useState("1");
  const [mediumFormVisible, setMediumFormVisible] = useState<boolean>(false);
  const [dpmstCurtContFormVisible, setDpmstCurtContFormVisible] =
    useState<boolean>(false);
  const [dpmstCurtAccFormVisible, setDpmstCurtAccFormVisible] =
    useState<boolean>(false);
  const [dpmstFixContFormVisible, setDpmstFixContFormVisible] =
    useState<boolean>(false);
  const [dpmstFixAccFormVisible, setDpmstFixAccFormVisible] =
    useState<boolean>(false);
  const [
    dprgtMaslaveContRelatFormVisible,
    setDprgtMaslaveContRelatFormVisible,
  ] = useState<boolean>(false);
  const [custInfoFormVisible, setCustInfoFormVisible] =
    useState<boolean>(false);
  const [currentRow, setCurrentRow] = useState<any>();
  const [loading, setLoading] = useState(false);

  const [relatedMediaModalVisible, setRelatedMediaModalVisible] =
    useState(false);
  const [statusFlagCdVisible, setStatusFlagCdVisible] = useState(false);
  const [attrbuteFlagCdVisible, setAttrbuteFlagCdVisible] = useState(false);
  const [crtModeFlagCdVisible, setCrtModeFlagCdVisible] = useState(false);

  const [curtMContrFlagCdVisible, setCurtMContrFlagCdVisible] = useState(false);
  const [fixMContrFlagCdVisible, setFixMContrFlagCdVisible] = useState(false);
  const [curtContrCtrlFlagCdVisible, setCurtContrCtrlFlagCdVisible] =
    useState(false);
  const [fixContrCtrlFlagCdVisible, setFixContrCtrlFlagCdVisible] =
    useState(false);
  const [curtContrAttrFgCdVisible, setCurtContrAttrFgCdVisible] =
    useState(false);
  const [fixContrAttrFgCdVisible, setFixContrAttrFgCdVisible] = useState(false);
  const [curtContrStaFgCdVisible, setCurtContrStaFgCdVisible] = useState(false);
  const [fixContrStaFgCdVisible, setFixContrStaFgCdVisible] = useState(false);

  const [curtAccStatusFlagCdVisible, setCurtAccStatusFlagCdVisible] =
    useState(false);
  const [fixAccStatusFlagCdVisible, setFixAccStatusFlagCdVisible] =
    useState(false);
  const [curtAccFlagCdVisible, setCurtAccFlagCdVisible] = useState(false);
  const [fixAccFlagCdVisible, setFixAccFlagCdVisible] = useState(false);

  const [currentMediumNo, setCurrentMediumNo] = useState<string>("");
  const [currentProdtContractNo, setCurrentProdtContractNo] =
    useState<string>("");
  const [currentPersInnerAccno, setCurrentPersInnerAccno] =
    useState<string>("");
  // const [currentMainContrNo, setcCurrentMainContrNo] = useState<string>('');

  // const [currentCustNo, setCurrentCustNo] = useState<string>('');
  // const [currentPerCertTpCd, setCurrentPerCertTpCd] = useState<string>('');
  // const [currentPersonalCertNo, setCurrentPersonalCertNo] = useState<string>('');
  const [detailDpdtlCurtModalVisible, setDetailDpdtlCurtModalVisible] =
    useState(false);
  const [detailDpdtlFixModalVisible, setDetailDpdtlFixModalVisible] =
    useState(false);
  const [
    detailDprgtPersDepLifeCycModalVisible,
    setDetailDprgtPersDepLifeCycModalVisible,
  ] = useState(false);
  const [dprgtPersonJointAccModalVisible, setDprgtPersonJointAccModalVisible] =
    useState(false);
  const [dprgtFrzModalVisible, setDprgtFrzModalVisible] = useState(false);
  const [dprgtStopPayModalVisible, setDprgtStopPayModalVisible] =
    useState(false);
  const [dprgtSignAddModalVisible, setDprgtSignAddModalVisible] =
    useState(false);
  const [dprgtAccFreeModalVisible, setDprgtAccFreeModalVisible] =
    useState(false);
  const [dprgtNoCardPayModalVisible, setDprgtNoCardPayFormModalVisible] =
    useState(false);
  const [dprgtChgCardModalVisible, setDprgtChgCardFormModalVisible] =
    useState(false);
  const [tobeLimtServModalVisible, setTobeLimtServModalVisible] =
    useState(false);
  const [limtServModalVisible, setLimtServModalVisible] = useState(false);

  const [detailDpdtlCurtData, setDetailDpdtlCurtData] = useState<any>(null);
  const [detailDpdtlFixData, setDetailDpdtlFixData] = useState<any>(null);
  const [detailDprgtPersDepLifeCycData, setDetailDprgtPersDepLifeCycData] =
    useState<any>(null);
  const [dprgtPersonJointAccData, setDprgtPersonJointAccData] =
    useState<any>(null);
  const [dprgtFrzData, setDprgtFrzData] = useState<any>(null);
  const [dprgtStopPayData, setDprgtStopPayData] = useState<any>(null);
  const [dprgtSignAddData, setDprgtSignAddDataData] = useState<any>(null);
  const [dprgtAccFreeData, setDprgtAccFreeData] = useState<any>(null);
  const [dprgtNoCardPayData, setDprgtNoCardPayData] = useState<any>(null);
  const [dprgtChgCardData, setDprgtChgCardData] = useState<any>(null);
  const [tobeLimtServData, setTobeLimtServData] = useState<any>(null);
  const [limtServData, setLimtServData] = useState<any>(null);

  const [detailDpdtlCurtRefresh, setDetailDpdtlCurtRefresh] = useState(0);
  const [detailDpdtlFixRefresh, setDetailDpdtlFixRefresh] = useState(0);

  const [formLoading, setFormLoading] = useState(false); // 添加介质主档表模态框加载状态
  const [formSubmitting, setFormSubmitting] = useState(false); // 添加提交数据状态

  // 搜索表单状态初始化
  const [searchForm, setSearchForm] = useState(() => {
    const savedSearch = storage.get("queryAll_search");
    return (
      savedSearch || {
        mediumNo: "", // 介质编号
      }
    );
  });

  // 保存搜索条件到storage
  // useEffect(() => {
  //   if (searchForm.mediumNo !== undefined) {
  //     storage.set("queryAll_search", searchForm, 30 * 60); // 保存30分钟
  //   }
  // }, [searchForm]);

  // 1、在获取查询结果数据成功后立即保存到localStorage
  const handleResponse = (parsedData: any) => {
    // 映射数据结构
    const mappedData: QueryAllData = {
      dpmstMedium: parsedData?.dpmstMedium || {},
      dpmstCurtContDto: parsedData?.dpmstCurtContDto || {},
      dpmstCurtAccDto: parsedData?.dpmstCurtAccDto || {},
      dpmstFixContDto: parsedData?.dpmstFixContDto || null, // 修改为 null
      dpmstFixAccDto: parsedData?.dpmstFixAccDto || null, // 修改为 null
      dprgtMaslaveContRelatPageInfo: {
        records: parsedData?.dprgtMaslaveContRelatPageInfo?.records || [],
        total: parsedData?.dprgtMaslaveContRelatPageInfo?.total || 0,
        size: parsedData?.dprgtMaslaveContRelatPageInfo?.size || 10,
        current: parsedData?.dprgtMaslaveContRelatPageInfo?.current || 1,
      },
      dpRelmstCurtContDtoPageInfo: {
        records: parsedData?.dpRelmstCurtContDtoPageInfo?.records || [],
        total: parsedData?.dpRelmstCurtContDtoPageInfo?.total || 0,
        size: parsedData?.dpRelmstCurtContDtoPageInfo?.size || 10,
        current: parsedData?.dpRelmstCurtContDtoPageInfo?.current || 1,
      },
      dpRelmstCurtAccDtoPageInfo: {
        records: parsedData?.dpRelmstCurtAccDtoPageInfo?.records || [],
        total: parsedData?.dpRelmstCurtAccDtoPageInfo?.total || 0,
        size: parsedData?.dpRelmstCurtAccDtoPageInfo?.size || 10,
        current: parsedData?.dpRelmstCurtAccDtoPageInfo?.current || 1,
      },
      dpRelFixContDtoPageInfo: {
        records: parsedData?.dpRelFixContDtoPageInfo?.records || [],
        total: parsedData?.dpRelFixContDtoPageInfo?.total || 0,
        size: parsedData?.dpRelFixContDtoPageInfo?.size || 10,
        current: parsedData?.dpRelFixContDtoPageInfo?.current || 1,
      },
      dpRelFixAccDtoPageInfo: {
        records: parsedData?.dpRelFixAccDtoPageInfo?.records || [],
        total: parsedData?.dpRelFixAccDtoPageInfo?.total || 0,
        size: parsedData?.dpRelFixAccDtoPageInfo?.size || 10,
        current: parsedData?.dpRelFixAccDtoPageInfo?.current || 1,
      },
      custInfoDto: parsedData?.custInfoDto || {},
    };

    // 立即保存到localStorage
    storage.set("queryAll_data", mappedData, 30 * 60);
    // 更新状态
    setQueryAllData(mappedData);
    setCurrentSearchData(mappedData);
  };

  // 2、打开标签后从localStorage初始化QueryAllData
  const [QueryAllData, setQueryAllData] = useState<QueryAllData>(() => {
    // 组件挂载时从localStorage读取数据
    const savedInfo = storage.get("queryAll_data");
    if (savedInfo) {
      return savedInfo;
    }
    return {
      dpmstMedium: {},
      dpmstCurtContDto: {},
      dpmstCurtAccDto: {},
      dpmstFixContDto: {},
      dpmstFixAccDto: {},
      dprgtMaslaveContRelatPageInfo: {
        records: [],
        total: 0,
        size: 10,
        current: 1,
      },
      dpRelmstCurtContDtoPageInfo: {
        records: [],
        total: 0,
        size: 10,
        current: 1,
      },
      dpRelmstCurtAccDtoPageInfo: {
        records: [],
        total: 0,
        size: 10,
        current: 1,
      },
      dpRelFixContDtoPageInfo: {
        records: [],
        total: 0,
        size: 10,
        current: 1,
      },
      dpRelFixAccDtoPageInfo: {
        records: [],
        total: 0,
        size: 10,
        current: 1,
      },
      custInfoDto: {},
    };
  });

  // 3、组件挂载时从localStorage读取数据
  useEffect(() => {
    const savedInfo = storage.get("queryAll_data");
    if (savedInfo) {
      // 检查是否过期
      const savedData = savedInfo;
      if (storage.get("queryAll_data") === null) {
        return;
      }
      setQueryAllData(savedData);

      // 更新tabsData
      setTabsData((prevTabs) => {
        const newTabs = [...prevTabs];

        // 更新各个tab的数据
        if (savedData?.dpmstMedium) {
          newTabs[0].data = [savedData.dpmstMedium];
        } else {
          newTabs[0].data = [];
        }
        if (savedData?.dpmstCurtContDto) {
          newTabs[1].data = [savedData.dpmstCurtContDto];
        } else {
          newTabs[1].data = [];
        }
        if (savedData?.dpmstCurtAccDto) {
          newTabs[2].data = [savedData.dpmstCurtAccDto];
        } else {
          newTabs[2].data = [];
        }
        if (savedData?.dpmstFixContDto) {
          newTabs[3].data = [savedData.dpmstFixContDto];
        } else {
          newTabs[3].data = [];
        }
        if (savedData?.dpmstFixAccDto) {
          newTabs[4].data = [savedData.dpmstFixAccDto];
        } else {
          newTabs[4].data = [];
        }
        if (savedData?.dprgtMaslaveContRelatPageInfo) {
          newTabs[5].data =
            savedData.dprgtMaslaveContRelatPageInfo.records || [];
        } else {
          newTabs[5].data = [];
        }
        if (savedData?.dpRelmstCurtContDtoPageInfo) {
          newTabs[6].data = savedData.dpRelmstCurtContDtoPageInfo.records || [];
        } else {
          newTabs[6].data = [];
        }
        if (savedData?.dpRelmstCurtAccDtoPageInfo) {
          newTabs[7].data = savedData.dpRelmstCurtAccDtoPageInfo.records || [];
        } else {
          newTabs[7].data = [];
        }
        if (savedData?.dpRelFixContDtoPageInfo) {
          newTabs[8].data = savedData.dpRelFixContDtoPageInfo.records || [];
        } else {
          newTabs[8].data = [];
        }
        if (savedData?.dpRelFixAccDtoPageInfo) {
          newTabs[9].data = savedData.dpRelFixAccDtoPageInfo.records || [];
        } else {
          newTabs[9].data = [];
        }
        if (savedData?.custInfoDto) {
          newTabs[10].data = [savedData.custInfoDto];
        } else {
          newTabs[19].data = [];
        }
        return newTabs;
      });
    }
  }, []);

  // 4、当QueryAllData更新时保存到localStorage--监控机制
  useEffect(() => {
    if (QueryAllData && QueryAllData.dpmstMedium) {
      storage.set("queryAll_data", QueryAllData, 30 * 60);
    }
  }, [QueryAllData]);

  const [tabsData, setTabsData] = useState<TabData[]>([
    {
      key: "1",
      tab: "介质主档表",
      data: [],
      columns: [
        {
          title: "介质编号",
          dataIndex: "mediumNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "介质类型代码",
          dataIndex: "mediumTpCd",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) => getMediumTpCdText(record.mediumTpCd),
        },
        {
          title: "主合约编号",
          dataIndex: "mainContrNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "类别标识代码",
          dataIndex: "categFlagCd",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) =>
            getCateFlagCdText(record.categFlagCd),
        },
        {
          title: () => (
            <Tooltip
              title={
                <div style={{ width: "100%" }}>
                  <p>关联介质类型标志码说明</p>
                  <ul>
                    <li>
                      <strong>第1位：是否关联折： </strong>0-否，1-是
                    </li>
                    <li>
                      <strong>第2位：是否关联卡： </strong>0-否，1-是
                    </li>
                    <li>
                      <strong>第3位：是否关联普通活期对账簿：</strong>
                    </li>
                    <li>0-否，1-是</li>
                    <li>
                      <strong>第4位：是否关联普通定期对账簿：</strong>
                    </li>
                    <li>0-否，1-是</li>
                    <li>
                      <strong>第5位：是否关联行业应用子账户对账簿：</strong>
                    </li>
                    <li>0-否，1-是</li>
                    <li>
                      <strong>第6位：是否关联湖南省惠农补贴明白折：</strong>
                    </li>
                    <li>0-否，1-是</li>
                    <li>
                      <strong>第7位：是否关联湖南省扶贫补贴明白折：</strong>
                    </li>
                    <li>0-否，1-是</li>
                  </ul>
                </div>
              }
              placement="top"
              overlayClassName="custom-tooltip"
              color="#fff"
              overlayStyle={{ minWidth: "350px" }}
            >
              <span>
                关联介质类型标志码{" "}
                <InfoCircleOutlined
                  style={{ marginLeft: 4, color: "#1890ff" }}
                />
              </span>
            </Tooltip>
          ),
          dataIndex: "relMediumTypeFlagCd",
          ellipsis: true,
          align: "center",
          width: 100,
          render: (_: any, record: any) => (
            <Button
              type="link"
              onClick={async () => {
                try {
                  console.log("关联介质类型标志码点击，传入参数:", {
                    mediumNo: record.mediumNo,
                    zoneVal: record.zoneVal,
                  });

                  const params: TbDpmstMedium = {
                    mediumNo: record.mediumNo,
                    zoneVal: record.zoneVal,
                  };

                  const res = await getDpmstMedium(params);
                  console.log("获取详情响应:", res);

                  if (res.code === 200 && res.data) {
                    try {
                      const mediumData = JSON.parse(res.data);
                      console.log("解析后的数据:", mediumData);
                      setCurrentRow(mediumData);
                      setRelatedMediaModalVisible(true);
                    } catch (parseError) {
                      message.error("解析数据失败");
                    }
                  } else {
                    message.error(res.msg || "获取数据失败");
                  }
                } catch (error) {
                  message.error("获取详情失败");
                }
              }}
            >
              {record.relMediumTypeFlagCd}
            </Button>
          ),
        },
        {
          title: () => (
            <Tooltip
              title={
                <div style={{ width: "100%" }}>
                  <p>介质状态标志码说明</p>
                  <ul>
                    <li>
                      <strong>第1位 介质状态代码</strong>
                    </li>
                    <li>0-正常；1-注销（销户或换卡交易修改）；</li>
                    <li>2-已移出。</li>
                    <li>
                      <strong>第2位 挂失状态代码</strong>
                    </li>
                    <li>0-正常；1-凭证临时挂失；2-凭证正式挂失；</li>
                    <li>3-密码挂失；4-双挂失(挂失交易修改)。</li>
                    <li>
                      <strong>第3位 激活状态代码</strong>
                    </li>
                    <li>0-正常；1-未激活（激活交易修改）。</li>
                    <li>
                      <strong>第4位 吞没状态代码</strong>
                    </li>
                    <li>0-正常；1-吞没（吞没或领用时修改）。</li>
                    <li>
                      <strong>第5位 可疑状态代码</strong>
                    </li>
                    <li>0-正常；1-可疑</li>
                    <li>
                      <strong>第6位 密码状态代码</strong>
                    </li>
                    <li>0-正常；1-密码锁定</li>
                    <li>
                      <strong>第7位 需更换折/单标志</strong>
                    </li>
                    <li>0-否，1-是</li>
                    <li>
                      <strong>第8位 已换卡标志</strong>
                    </li>
                    <li>0-否，1-是</li>
                  </ul>
                </div>
              }
              placement="top"
              overlayClassName="custom-tooltip"
              color="#fff"
              overlayStyle={{ minWidth: "350px" }}
            >
              <span>
                介质状态标志码{" "}
                <InfoCircleOutlined
                  style={{ marginLeft: 4, color: "#1890ff" }}
                />
              </span>
            </Tooltip>
          ),
          dataIndex: "statusFlagCd",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) => (
            <Button
              type="link"
              onClick={async () => {
                try {
                  const params: TbDpmstMedium = {
                    mediumNo: record.mediumNo,
                    zoneVal: record.zoneVal,
                  };
                  const res = await getDpmstMedium(params);
                  if (res.code === 200 && res.data) {
                    try {
                      const mediumData = JSON.parse(res.data);
                      setCurrentRow(mediumData);
                      setStatusFlagCdVisible(true);
                    } catch (parseError) {
                      message.error("解析数据失败");
                    }
                  } else {
                    message.error(res.msg || "获取数据失败");
                  }
                } catch (error) {
                  message.error("获取详情失败");
                }
              }}
            >
              {record.statusFlagCd}
            </Button>
          ),
        },
        {
          title: () => (
            <Tooltip
              title={
                <div style={{ width: "100%" }}>
                  <p>介质属性标志码说明</p>
                  <ul>
                    <li>
                      <strong>第1位 个人联名账户标志</strong>
                    </li>
                    <li>0-否；1-是</li>
                    <li>
                      <strong>第2位 新旧介质标志代码</strong>
                    </li>
                    <li>0-无关；1-新；2-旧</li>
                    <li>
                      <strong>第3位 虚拟介质标志</strong>
                    </li>
                    <li>0-否；1-是</li>
                    <li>
                      <strong>第4位 特定范围挂失标志</strong>
                    </li>
                    <li>0-否；1-是</li>
                    <li>
                      <strong>第5位：是否不校验迁移介质有效性</strong>
                    </li>
                    <li>0-否，1-是</li>
                  </ul>
                </div>
              }
              placement="top"
              overlayClassName="custom-tooltip"
              color="#fff"
              overlayStyle={{ minWidth: "300px" }}
            >
              <span>
                介质属性标志码{" "}
                <InfoCircleOutlined
                  style={{ marginLeft: 4, color: "#1890ff" }}
                />
              </span>
            </Tooltip>
          ),
          dataIndex: "attrbuteFlagCd",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) => (
            <Button
              type="link"
              onClick={async () => {
                try {
                  const params: TbDpmstMedium = {
                    mediumNo: record.mediumNo,
                    zoneVal: record.zoneVal,
                  };
                  const res = await getDpmstMedium(params);
                  if (res.code === 200 && res.data) {
                    try {
                      const mediumData = JSON.parse(res.data);
                      setCurrentRow(mediumData);
                      setAttrbuteFlagCdVisible(true);
                    } catch (parseError) {
                      message.error("解析数据失败");
                    }
                  } else {
                    message.error(res.msg || "获取数据失败");
                  }
                } catch (error) {
                  message.error("获取详情失败");
                }
              }}
            >
              {record.attrbuteFlagCd}
            </Button>
          ),
        },
        {
          title: () => (
            <Tooltip
              title={
                <div style={{ width: "100%" }}>
                  <p>介质认证方式标志码说明</p>
                  <ul>
                    <li>
                      <strong>第1位</strong> 密码 0-无；1-密码。
                    </li>
                    <li>
                      <strong>第2位</strong> 印鉴 0-无；1-印鉴。
                    </li>
                    <li>
                      <strong>第3位</strong> 指纹 0-无；1-指纹。
                    </li>
                    <li>
                      <strong>第4位</strong> 人脸 0-无；1-人脸。
                    </li>
                    <li>
                      <strong>第5位</strong> 声纹 0-无；1-声纹。
                    </li>
                    <li>
                      <strong>第6位</strong> 指静脉 0-无；1-指静脉。
                    </li>
                  </ul>
                </div>
              }
              placement="top"
              overlayClassName="custom-tooltip"
              color="#fff"
              overlayStyle={{ minWidth: "260px" }}
            >
              <span>
                介质认证方式标志码{" "}
                <InfoCircleOutlined
                  style={{ marginLeft: 4, color: "#1890ff" }}
                />
              </span>
            </Tooltip>
          ),
          dataIndex: "crtModeFlagCd",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) => (
            <Button
              type="link"
              onClick={async () => {
                try {
                  const params: TbDpmstMedium = {
                    mediumNo: record.mediumNo,
                    zoneVal: record.zoneVal,
                  };
                  const res = await getDpmstMedium(params);
                  if (res.code === 200 && res.data) {
                    try {
                      const mediumData = JSON.parse(res.data);
                      setCurrentRow(mediumData);
                      setCrtModeFlagCdVisible(true);
                    } catch (parseError) {
                      message.error("解析数据失败");
                    }
                  } else {
                    message.error(res.msg || "获取数据失败");
                  }
                } catch (error) {
                  message.error("获取详情失败");
                }
              }}
            >
              {record.crtModeFlagCd}
            </Button>
          ),
        },
        {
          title: "客户编号",
          dataIndex: "custNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "加密密码",
          dataIndex: "encPwd",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "介质印刷号",
          dataIndex: "mediumPrintNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "卡序列号",
          dataIndex: "cardSeqNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "卡介质代码",
          dataIndex: "cardMediumCode",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) =>
            getCardMediumCodeText(record.cardMediumCode),
        },
        {
          title: "卡品种代码",
          dataIndex: "cardKindCd",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) => getCardKinCdText(record.cardKindCd),
        },
        {
          title: "借记卡类别编码",
          dataIndex: "dcardCategNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "已登记打印行数",
          dataIndex: "enrolledLineNum",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "开立日期",
          dataIndex: "openDt",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "开立机构号",
          dataIndex: "drawInstNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "申请制卡日期",
          dataIndex: "applyMakecardDt",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "介质有效日期",
          dataIndex: "mediumValidDt",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "最后交易日期",
          dataIndex: "lastTxDate",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "创建时间戳",
          dataIndex: "createStamp",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "最后修改时间戳",
          dataIndex: "lastModStamp",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "分片值",
          dataIndex: "zoneVal",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "操作",
          valueType: "option",
          width: 160,
          fixed: "right",
          align: "center",
          render: (_: any, record: any) => [
            <Button
              key="edit"
              type="link"
              size="small"
              disabled={
                !access.hasEnvPerms(
                  localStorage.getItem("currentEnv") as string
                )
              }
              onClick={() => handleMediumEdit(record)}
            >
              编辑
            </Button>,
            <Dropdown
              key="more"
              menu={{
                items: [
                  {
                    key: "unitQuery",
                    label: "1-查询联名账户信息",
                    onClick: () => handleQueryDprgtPersonJointAcc(record),
                  },
                  {
                    key: "lossQuery",
                    label: "2-查询签约加办关系",
                    onClick: () => handleQueryDprgtSignAdd(record),
                  },
                  {
                    key: "noCardPayQuery",
                    label: "3-查询无卡支付协议登记",
                    onClick: () => handleQueryDprgtNoCardPay(record),
                  },
                  {
                    key: "lifeCycQuery",
                    label: "4-查询存款生命周期",
                    onClick: () => handleQueryDprgtPersDepLifeCycDetail(record),
                  },
                ],
                style: {
                  maxWidth: "300px", // 设置下拉菜单最大宽度
                  minWidth: "200px", // 设置下拉菜单最小宽度
                },
              }}
              trigger={["click"]}
              placement="bottomLeft" // 设置下拉菜单出现位置
              overlayStyle={{
                maxHeight: "400px", // 设置下拉菜单最大高度
                overflowY: "auto", // 超出高度时显示滚动条
              }}
            >
              <Button type="link" size="small">
                查询
                <MoreOutlined />
              </Button>
            </Dropdown>,
            <Button key="operation" type="link" size="small">
              操作
            </Button>,
          ],
        },
      ],
    },
    {
      key: "2",
      tab: "活期产品合约表",
      data: [],
      columns: [
        {
          title: "子账户序号",
          dataIndex: "saccnoSeqNo",
          ellipsis: true,
          width: 80,
          align: "center",
        },
        {
          title: "客户编号",
          dataIndex: "custNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "产品合约编号",
          dataIndex: "prodtContractNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "基础产品编码",
          dataIndex: "baseProdtNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "可售产品编码",
          dataIndex: "vendibiProdtNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: () => (
            <Tooltip
              title={
                <div style={{ width: "100%" }}>
                  <p>主合约控制标志码说明</p>
                  <ul>
                    <li>
                      <strong>第1位 止付状态代码</strong> 0-正常；1-止付
                    </li>
                    <li>
                      <strong>第2位 冻结状态代码</strong> 0-正常；1-冻结
                    </li>
                    <li>
                      <strong>第3位 暂停非柜面交易状态代码</strong>{" "}
                      0-正常；1-暂停非柜面交易
                    </li>
                    <li>
                      <strong>第4位 停用状态代码</strong> 0-正常；1-停用
                    </li>
                    <li>
                      <strong>第5位 中止状态代码</strong> 0-正常；1-中止
                    </li>
                    <li>
                      <strong>第6位 账户群标志</strong> 0-否；1-是
                    </li>
                    <li>
                      <strong>第7位 自贸区标志</strong>{" "}
                      0-无关，1-FTI区内自贸区，2-FTF区内境外自贸区
                    </li>
                    <li>
                      <strong>第8位 个人账户长期不动户状态代码</strong>{" "}
                      0-正常；1-长期不动户；
                    </li>
                    <li>
                      <strong>第9位 客户有主动交易标志</strong> 0-否；1-是
                    </li>
                    <li>
                      <strong>第10位 非柜面渠道专项管控标志</strong> 0-否；1-是
                    </li>
                    <li>
                      <strong>第11位 养老金专户状态代码</strong>{" "}
                      0-无关，1-正常，2-转移
                    </li>
                    <li>
                      <strong>第12位 压缩标志</strong> 0-否，1-是
                    </li>
                    <li>
                      <strong>第13位 介质是否未领用标志</strong>{" "}
                      0-否，1-是（0表示领用，1表示未领用）
                    </li>
                  </ul>
                </div>
              }
              placement="top"
              overlayClassName="custom-tooltip"
              color="#fff"
              overlayStyle={{ minWidth: "320px" }}
            >
              <span>
                主合约控制标志码{" "}
                <InfoCircleOutlined
                  style={{ marginLeft: 4, color: "#1890ff" }}
                />
              </span>
            </Tooltip>
          ),
          // title: "主合约控制标志码",
          dataIndex: "mContrFlagCd",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) => (
            <Button
              type="link"
              onClick={async () => {
                try {
                  const params: TbDpmstCurtCont = {
                    prodtContractNo: record.prodtContractNo,
                    zoneVal: record.zoneVal,
                  };
                  console.log("请求参数:", params);
                  const res = await getDpmstCurtCont(params);
                  console.log("请求合约表的数据为：", res);
                  if (res.code === 200 && res.data) {
                    try {
                      const dpmstCurtContData = JSON.parse(res.data);
                      console.log("解析后的数据:", dpmstCurtContData);
                      setCurrentRow(dpmstCurtContData);
                      setCurrentProdtContractNo(record.prodtContractNo);
                      setCurtMContrFlagCdVisible(true);
                    } catch (parseError) {
                      console.error("解析错误:", parseError);
                      message.error("解析数据失败");
                    }
                  } else {
                    console.error("请求失败:", res);
                    message.error(res.msg || "获取数据失败");
                  }
                } catch (error) {
                  console.error("请求异常:", error);
                  message.error("获取详情失败");
                }
              }}
              style={{ color: "#1890ff" }}
            >
              {record.MContrFlagCd}
            </Button>
          ),
        },
        {
          title: () => (
            <Tooltip
              title={
                <div style={{ width: "100%" }}>
                  <p>合约控制标志码说明</p>
                  <ul>
                    <li>
                      <strong>第1位 余额限制类型代码：</strong>{" "}
                      0-无，1-余额设置上限；
                    </li>
                    <li>
                      <strong>第2位 资金 用途管控标志：</strong>0-不管控，1-管控
                    </li>
                    <li>
                      <strong>其他位置预留，用0补充</strong>
                    </li>
                  </ul>
                </div>
              }
              placement="top"
              overlayClassName="custom-tooltip"
              color="#fff"
              overlayStyle={{ minWidth: "400px" }}
            >
              <span>
                合约控制标志码{" "}
                <InfoCircleOutlined
                  style={{ marginLeft: 4, color: "#1890ff" }}
                />
              </span>
            </Tooltip>
          ),
          // title: "合约控制标志码",
          dataIndex: "contrCtrlFlagCd",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) => (
            <Button
              type="link"
              onClick={async () => {
                try {
                  const params: TbDpmstCurtCont = {
                    prodtContractNo: record.prodtContractNo,
                    zoneVal: record.zoneVal,
                  };
                  const res = await getDpmstCurtCont(params);
                  console.log("请求合约表的数据为：", res);
                  if (res.code === 200 && res.data) {
                    try {
                      const dpmstCurtContData = JSON.parse(res.data);
                      setCurrentRow(dpmstCurtContData);
                      setCurrentProdtContractNo(record.prodtContractNo);
                      setCurtContrCtrlFlagCdVisible(true);
                    } catch (parseError) {
                      message.error("解析数据失败");
                    }
                  } else {
                    message.error(res.msg || "获取数据失败");
                  }
                } catch (error) {
                  message.error("获取详情失败");
                }
              }}
            >
              {record.contrCtrlFlagCd}
            </Button>
          ),
        },
        {
          title: () => (
            <Tooltip
              title={
                <div style={{ width: "100%" }}>
                  <p>合约属性标志码说明</p>
                  <ul>
                    <li>
                      <strong>第1位 柜面核实标志</strong> 0-否，1-是
                    </li>
                    <li>
                      <strong>第2位 非绑定账户入金标志</strong> 0-否，1-是
                    </li>
                    <li>
                      <strong>第5位 卡贷通标志</strong> 0-否；1-是
                    </li>
                    <li>
                      <strong>第7位 约定转账标志</strong> 0-否，1-是
                    </li>
                    <li>
                      <strong>第10位 身份核实标志</strong> 0-无关 1-否 2-是
                    </li>
                    <li>
                      <strong>第11位 是否存在协议国税率</strong> 0-否，1-是
                    </li>
                    <li>
                      <strong>第12位 自动赎回标志</strong>{" "}
                      0-无关，1-允许，2-不允许；
                    </li>
                  </ul>
                </div>
              }
              placement="top"
              overlayClassName="custom-tooltip"
              color="#fff"
              overlayStyle={{ minWidth: "320px" }}
            >
              <span>
                合约属性标志码{" "}
                <InfoCircleOutlined
                  style={{ marginLeft: 4, color: "#1890ff" }}
                />
              </span>
            </Tooltip>
          ),
          // title: "合约属性标志码",
          dataIndex: "contrAttrFgCd",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) => (
            <Button
              type="link"
              onClick={async () => {
                try {
                  const params: TbDpmstCurtCont = {
                    prodtContractNo: record.prodtContractNo,
                    zoneVal: record.zoneVal,
                  };
                  const res = await getDpmstCurtCont(params);
                  if (res.code === 200 && res.data) {
                    try {
                      const dpmstCurtContData = JSON.parse(res.data);
                      setCurrentRow(dpmstCurtContData);
                      setCurtContrAttrFgCdVisible(true);
                    } catch (parseError) {
                      message.error("解析数据失败");
                    }
                  } else {
                    message.error(res.msg || "获取数据失败");
                  }
                } catch (error) {
                  message.error("获取详情失败");
                }
              }}
            >
              {record.contrAttrFgCd}
            </Button>
          ),
        },
        {
          title: () => (
            <Tooltip
              title={
                <div style={{ width: "100%" }}>
                  <p>合约状态标志码说明</p>
                  <ul>
                    <li>
                      <strong>第1位 合约状态代码</strong> 0-签约；1-解约
                    </li>
                    <li>
                      <strong>第2位 副卡停用状态代码</strong> 0-正常/无关；
                    </li>
                    <li>1-主卡办理副卡停用；2-副卡办理副卡停用</li>
                  </ul>
                </div>
              }
              placement="top"
              overlayClassName="custom-tooltip"
              color="#fff"
              overlayStyle={{ minWidth: "320px" }}
            >
              <span>
                合约状态标志码{" "}
                <InfoCircleOutlined
                  style={{ marginLeft: 4, color: "#1890ff" }}
                />
              </span>
            </Tooltip>
          ),
          // title: "合约状态标志码",
          dataIndex: "contrStaFgCd",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) => (
            <Button
              type="link"
              onClick={async () => {
                try {
                  const params: TbDpmstCurtCont = {
                    prodtContractNo: record.prodtContractNo,
                    zoneVal: record.zoneVal,
                  };
                  const res = await getDpmstCurtCont(params);
                  if (res.code === 200 && res.data) {
                    try {
                      const dpmstCurtContData = JSON.parse(res.data);
                      setCurrentRow(dpmstCurtContData);
                      setCurtContrStaFgCdVisible(true);
                    } catch (parseError) {
                      message.error("解析数据失败");
                    }
                  } else {
                    message.error(res.msg || "获取数据失败");
                  }
                } catch (error) {
                  message.error("获取详情失败");
                }
              }}
            >
              {record.contrStaFgCd}
            </Button>
          ),
        },
        {
          title: "储种中类代码",
          dataIndex: "savTypeMclassCode",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) =>
            getSavTypeMclassCodeText(record.savTypeMclassCode),
        },
        {
          title: "币种代码",
          dataIndex: "currCode",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) => getCurrCodeText(record.currCode),
        },
        {
          title: "协议国税率",
          dataIndex: "agrNtaxRate",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "合约签约日期",
          dataIndex: "contrSignDt",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "合约签约时间",
          dataIndex: "contrSignTime",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "签约机构号",
          dataIndex: "signContInstNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "签约渠道种类代码",
          dataIndex: "signChnKindCd",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) =>
            getSignChnKindCdText(record.signChnKindCd),
        },
        {
          title: "合约解约日期",
          dataIndex: "contrCacContDt",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "合约解约时间",
          dataIndex: "contrCacContTime",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "解约机构号",
          dataIndex: "cacContInstNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "最后动户交易日期",
          dataIndex: "lastMobilityTxDate",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "个人存款合约类型代码",
          dataIndex: "dpContrTpCd",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) =>
            getDpContrTpCdText(record.dpContrTpCd),
        },
        {
          title: "个人存款账户种类代码",
          dataIndex: "persDepAccKindCd",
          ellipsis: true,
          width: 100,
          align: "center",
          render: (_: any, record: any) =>
            getPersDepAccKindCdText(record.persDepAccKindCd),
        },
        {
          title: "特殊合约类型代码",
          dataIndex: "specContrTpCd",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) =>
            getSpecContrTpCdText(record.specContrTpCd),
        },
        {
          title: "产品合约名称",
          dataIndex: "prodtContractName",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "产品合约描述",
          dataIndex: "prodtContractDesc",
          ellipsis: true,
          width: 150,
          align: "center",
        },
        {
          title: "主合约标志",
          dataIndex: "mainContrFlag",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) =>
            getMainContrFlagText(record.mainContrFlag),
        },
        {
          title: "最大子账号序号",
          dataIndex: "maxSaccnoSeqNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "可售产品版本号",
          dataIndex: "vendibiProdtVerNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "最后交易日期",
          dataIndex: "lastTxDate",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "创建时间戳",
          dataIndex: "createStamp",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "最后修改时间戳",
          dataIndex: "lastModStamp",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "分片值",
          dataIndex: "zoneVal",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "操作",
          valueType: "option",
          width: 160,
          fixed: "right",
          align: "center",
          render: (_: any, record: any) => [
            <Button
              key="edit"
              type="link"
              size="small"
              onClick={() => handleDpmstCurtContEdit(record)}
              disabled={
                !access.hasEnvPerms(
                  localStorage.getItem("currentEnv") as string
                )
              }
            >
              编辑
            </Button>,
            <Dropdown
              key="more"
              menu={{
                items: [
                  {
                    key: "chgCardQuery",
                    label: "1-查询换卡信息",
                    onClick: () => handleQueryDprgtChgCard(record),
                  },
                  {
                    key: "frzquery",
                    label: "2-查询冻结信息",
                    onClick: () => handleQueryDprgtFrz(record),
                  },
                  {
                    key: "stopPayquery",
                    label: "3-查询止付信息",
                    onClick: () => handleQueryDprgtStopPay(record),
                  },
                  {
                    key: "tobeLimitServQuery",
                    label: "4-查询待限制服务信息",
                    onClick: () => handleQueryTobeLimtServ(record),
                  },
                  {
                    key: "limitServQuery",
                    label: "5-查询限制服务信息",
                    onClick: () => {
                      handleQueryLimtServ(record);
                    },
                  },
                  {
                    key: "cosumeQuery",
                    label: "6-查询消费信息",
                    onClick: () => handleQueryConsum(record),
                  },
                  {
                    key: "cosumeQuery",
                    label: "7-查询退货信息",
                    onClick: () => handleQuerySaleRet(record),
                  },
                  {
                    key: "updownQuery",
                    label: "8-查询升降级信息",
                    onClick: () => handleQueryAccUpdown(record),
                  },
                  {
                    key: "contrAsstQuery",
                    label: "9-查询合约辅助表",
                    onClick: () => handleQueryDprgtContrAsst(record),
                  },
                  {
                    key: "bindRelateQuery",
                    label: "10-查询绑定关系信息",
                    onClick: () => handleQueryDprgtAccBindRelat(record),
                  },
                ],
              }}
              trigger={["click"]}
            >
              <Button type="link" size="small">
                查询
                <MoreOutlined />
              </Button>
            </Dropdown>,
            <Button key="operation" type="link" size="small">
              操作
            </Button>,
          ],
        },
      ],
    },
    {
      key: "3",
      tab: "活期产品账户表",
      data: [],
      columns: [
        {
          title: "子账户序号",
          dataIndex: "saccnoSeqNo",
          ellipsis: true,
          width: 80,
          align: "center",
        },
        {
          title: "个人内部账号",
          dataIndex: "persInnerAccno",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "客户编号",
          dataIndex: "custNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "个人存款账户类型代码",
          dataIndex: "persDepAccTpCd",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) =>
            getPersDepAccTpCdText(record.persDepAccTpCd),
        },
        {
          title: "产品合约编号",
          dataIndex: "prodtContractNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "币种代码",
          dataIndex: "currCode",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) => getCurrCodeText(record.currCode),
        },
        {
          title: "钞汇类别代码",
          dataIndex: "cashExgVatgCd",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) =>
            getCashExgVatgCdText(record.cashExgVatgCd),
        },
        {
          title: () => (
            <Tooltip
              title={
                <div style={{ width: "100%" }}>
                  <p>账户状态标志码说明</p>
                  <ul>
                    <li>
                      <strong>第1位 账户状态代码</strong>
                    </li>
                    <li>0-正常</li>
                    <li>1-未启用（仅适用于万事达卡的人民币结算账户）</li>
                    <li>2-销户；</li>
                    <li>
                      <strong>第2位 冻结状态代码</strong> 0-正常；1-账户冻结
                    </li>
                    <li>
                      <strong>第3位 止付状态代码</strong> 0-正常；1-账户止付
                    </li>
                  </ul>
                </div>
              }
              placement="top"
              overlayClassName="custom-tooltip"
              color="#fff"
              overlayStyle={{ minWidth: "500px" }}
            >
              <span>
                账户状态标志码{" "}
                <InfoCircleOutlined
                  style={{ marginLeft: 4, color: "#1890ff" }}
                />
              </span>
            </Tooltip>
          ),
          // title: "账户状态标志码",
          dataIndex: "accStatusFlagCd",
          width: 100,
          ellipsis: true,
          align: "center",
          render: (_: any, record: any) => (
            <Button
              type="link"
              onClick={async () => {
                try {
                  const params: TbDpmstCurtAcc = {
                    persInnerAccno: record.persInnerAccno,
                    zoneVal: record.zoneVal,
                  };
                  const res = await getDpmstCurtAcc(params);
                  if (res.code === 200 && res.data) {
                    try {
                      const dpmstCurtAccData = JSON.parse(res.data);
                      setCurrentRow(dpmstCurtAccData);
                      setCurtAccStatusFlagCdVisible(true);
                    } catch (parseError) {
                      message.error("解析数据失败");
                    }
                  } else {
                    message.error(res.msg || "获取数据失败");
                  }
                } catch (error) {
                  message.error("获取详情失败");
                }
              }}
            >
              {record.accStatusFlagCd}
            </Button>
          ),
        },
        {
          title: () => (
            <Tooltip
              title={
                <div style={{ width: "100%" }}>
                  <p>账户标志码说明</p>
                  <ul>
                    <li>
                      <strong>第2位 免征小额管理费标志 </strong>
                    </li>
                    <li>0-不免除；1-免除小额管理费；</li>
                    <li>
                      <strong>第3位 免征年费标志 </strong>
                    </li>
                    <li>0-不免除；1-免除年费；</li>
                    <li>
                      <strong>第4位：免征VIP服务费标志</strong>
                    </li>
                    <li>0-不免除；1-免除VIP服务费；</li>
                    <li>
                      <strong>第5位：计息方式代码</strong>
                    </li>
                    <li>0-正常；1-结转计息；2-不计息</li>
                    <li>
                      <strong>第7位：账户星级代码</strong>
                    </li>
                    <li>0-无关；1-准一星；2-一星；3-二星；4-三星；</li>
                    <li>
                      <strong>第8位：利率启用方式代码：（活期预留）</strong>
                    </li>
                    <li>
                      0-利率值（利率不变更）；1-利率号（利率生效日当天生效）；2-无关：
                    </li>
                    <li>
                      <strong>第16位：账户产品标志码</strong>
                    </li>
                    <li>0-无关，1-养老金账户</li>
                  </ul>
                </div>
              }
              placement="top"
              overlayClassName="custom-tooltip"
              color="#fff"
              overlayStyle={{ minWidth: "400px" }}
            >
              <span>
                账户标志码{" "}
                <InfoCircleOutlined
                  style={{ marginLeft: 4, color: "#1890ff" }}
                />
              </span>
            </Tooltip>
          ),
          // title: "账户标志码",
          dataIndex: "accFlagCd",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) => (
            <Button
              type="link"
              onClick={async () => {
                try {
                  const params: TbDpmstCurtAcc = {
                    persInnerAccno: record.persInnerAccno,
                    zoneVal: record.zoneVal,
                  };
                  const res = await getDpmstCurtAcc(params);
                  if (res.code === 200 && res.data) {
                    try {
                      const dpmstCurtAccData = JSON.parse(res.data);
                      setCurrentRow(dpmstCurtAccData);
                      setCurtAccFlagCdVisible(true);
                    } catch (parseError) {
                      message.error("解析数据失败");
                    }
                  } else {
                    message.error(res.msg || "获取数据失败");
                  }
                } catch (error) {
                  message.error("获取详情失败");
                }
              }}
            >
              {record.accFlagCd}
            </Button>
          ),
        },
        {
          title: "开户日期",
          dataIndex: "openaccDate",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "开户机构号",
          dataIndex: "openAccInstNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "销户日期",
          dataIndex: "clsaccDate",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "销户机构号",
          dataIndex: "clsAccInstNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "账户余额",
          dataIndex: "accBal",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "账户可用余额",
          dataIndex: "accAvalBal",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "计息积数",
          dataIndex: "calIntAccu",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "利率",
          dataIndex: "intRate",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "利率号",
          dataIndex: "intRateNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "起息日期",
          dataIndex: "bgnIntDate",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "计息积数",
          dataIndex: "calIntAccu",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "上次交易日期",
          dataIndex: "lastTmTxDt",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "上日余额",
          dataIndex: "lastdayBal",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "当前明细序号",
          dataIndex: "curDtlNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "营销柜员号",
          dataIndex: "campTellerNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "最后交易日期",
          dataIndex: "lastTxDate",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "创建时间戳",
          dataIndex: "createStamp",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "最后修改时间戳",
          dataIndex: "lastModStamp",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "分片值",
          dataIndex: "zoneVal",
          ellipsis: true,
          width: 120,
          align: "center",
        },
        {
          title: "操作",
          valueType: "option",
          width: 160,
          fixed: "right",
          align: "center",
          render: (_: any, record: any) => [
            <Button
              key="edit"
              type="link"
              size="small"
              onClick={() => handleDpmstCurtAccEdit(record)}
              disabled={
                !access.hasEnvPerms(
                  localStorage.getItem("currentEnv") as string
                )
              }
            >
              编辑
            </Button>,
            <Dropdown
              key="more"
              menu={{
                items: [
                  {
                    key: "details",
                    label: "1-查询账户明细",
                    onClick: () => handleQueryDpdtlCurtDetail(record),
                  },
                  {
                    key: "foudsUsageControl",
                    label: "2-查询资金用途管控表",
                    onClick: () => handleQueryDprgtFundsUsageControl(record),
                  },
                  {
                    key: "usageControl",
                    label: "3-查询资金用途管控明细",
                    onClick: () => handleQueryDpdtlFundsUsageControl(record),
                  },
                  {
                    key: "usageControloanQuery",
                    label: "4-查询资金用途管控借据",
                    onClick: () =>
                      handleQueryDprgtFundsUsageControlLoan(record),
                  },
                  {
                    key: "usageControloanQuery",
                    label: "5-查询资金用途管控借据明细",
                    onClick: () =>
                      handleQueryDpdtlFundsUsageControlLoan(record),
                  },
                  {
                    key: "accStarQuery",
                    label: "6-查询账户星级登记簿",
                    onClick: () => handleQueryDprgtAccStar(record),
                  },
                  {
                    key: "accFreeQuery",
                    label: "7-查询账户免收登记簿",
                    onClick: () => handleQueryDprgtAccFree(record),
                  },
                  {
                    key: "dprgtDepProveOpenQuery",
                    label: "8-查询存款证明开立信息登记",
                    onClick: () => handleQueryDprgtDepProveOpenInfo(record),
                  },
                ],
              }}
              trigger={["click"]}
            >
              <Button type="link" size="small">
                查询
                <MoreOutlined />
              </Button>
            </Dropdown>,
            <Button key="operation" type="link" size="small">
              操作
            </Button>,
          ],
        },
      ],
    },
    {
      key: "4",
      tab: "定期合约主档表",
      data: [],
      columns: [
        {
          title: "子账户序号",
          dataIndex: "saccnoSeqNo",
          ellipsis: true,
          width: 80,
          align: "center",
        },
        {
          title: "产品合约编号",
          dataIndex: "prodtContractNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "基础产品编码",
          dataIndex: "baseProdtNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "可售产品编码",
          dataIndex: "vendibiProdtNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: () => (
            <Tooltip
              title={
                <div style={{ width: "100%" }}>
                  <p>主合约控制标志码说明</p>
                  <ul>
                    <li>
                      <strong>第1位 止付状态代码</strong> 0-正常；1-止付
                    </li>
                    <li>
                      <strong>第2位 冻结状态代码</strong> 0-正常；1-冻结
                    </li>
                    <li>
                      <strong>第3位 暂停非柜面交易状态代码</strong>
                    </li>
                    <li>0-正常；1-暂停非柜面交易</li>
                    <li>
                      <strong>第4位 停用状态代码</strong> 0-正常；1-停用
                    </li>
                    <li>
                      <strong>第5位 中止状态代码</strong> 0-正常；1-中止
                    </li>
                    <li>
                      <strong>第6位 账户群标志</strong> 0-否；1-是
                    </li>
                    <li>
                      <strong>第7位 自贸区标志</strong>{" "}
                      0-无关，1-FTI区内自贸区，2-FTF区内境外自贸区
                    </li>
                    <li>
                      <strong>第8位 个人账户长期不动户状态代码</strong>
                    </li>
                    <li>0-正常；1-长期不动户；</li>
                    <li>
                      <strong>第9位 客户有主动交易标志</strong> 0-否；1-是
                    </li>
                    <li>
                      <strong>第10位 非柜面渠道专项管控标志</strong>
                    </li>
                    <li>0-否；1-是</li>
                    <li>
                      <strong>第13位 介质是否未领用标志</strong>
                    </li>
                    <li>0-否，1-是（0表示领用，1表示未领用）</li>
                  </ul>
                </div>
              }
              placement="top"
              overlayClassName="custom-tooltip"
              color="#fff"
              overlayStyle={{ minWidth: "320px" }}
            >
              <span>
                主合约控制标志码{" "}
                <InfoCircleOutlined
                  style={{ marginLeft: 4, color: "#1890ff" }}
                />
              </span>
            </Tooltip>
          ),
          dataIndex: "mContrFlagCd",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) => (
            <Button
              type="link"
              onClick={async () => {
                try {
                  const params: TbDpmstFixCont = {
                    prodtContractNo: record.prodtContractNo,
                    zoneVal: record.zoneVal,
                  };
                  const res = await getDpmstFixCont(params);
                  if (res.code === 200 && res.data) {
                    try {
                      const dpmstFixContData = JSON.parse(res.data);
                      setCurrentRow(dpmstFixContData);
                      setCurrentProdtContractNo(record.prodtContractNo);
                      setFixMContrFlagCdVisible(true);
                    } catch (parseError) {
                      message.error("解析数据失败");
                    }
                  } else {
                    message.error(res.msg || "获取数据失败");
                  }
                } catch (error) {
                  message.error("获取详情失败");
                }
              }}
            >
              {record.MContrFlagCd}
            </Button>
          ),
        },
        {
          title: () => (
            <Tooltip
              title={
                <div style={{ width: "100%" }}>
                  <p>合约控制标志码说明</p>
                  <ul>
                    <li>
                      <strong>（活期使用）第1位： 余额限制类型代码：</strong>
                      0-无，1-余额设置上限
                    </li>
                    <li>
                      <strong>（活期使用）第2位： 资金用途管控标志：</strong>
                      0-不管控，1-管控
                    </li>
                    <li>
                      <strong>（定期预留）</strong>
                    </li>
                  </ul>
                </div>
              }
              placement="top"
              overlayClassName="custom-tooltip"
              color="#fff"
              overlayStyle={{ minWidth: "330px" }}
            >
              <span>
                合约控制标志码{" "}
                <InfoCircleOutlined
                  style={{ marginLeft: 4, color: "#1890ff" }}
                />
              </span>
            </Tooltip>
          ),
          dataIndex: "contrCtrlFlagCd",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) => (
            <Button
              type="link"
              onClick={async () => {
                try {
                  const params: TbDpmstFixCont = {
                    prodtContractNo: record.prodtContractNo,
                    zoneVal: record.zoneVal,
                  };
                  const res = await getDpmstFixCont(params);
                  if (res.code === 200 && res.data) {
                    try {
                      const dpmstFixContData = JSON.parse(res.data);
                      setCurrentRow(dpmstFixContData);
                      setFixContrCtrlFlagCdVisible(true);
                    } catch (parseError) {
                      message.error("解析数据失败");
                    }
                  } else {
                    message.error(res.msg || "获取数据失败");
                  }
                } catch (error) {
                  message.error("获取详情失败");
                }
              }}
            >
              {record.contrCtrlFlagCd}
            </Button>
          ),
        },
        {
          title: () => (
            <Tooltip
              title={
                <div style={{ width: "100%" }}>
                  <p>合约属性标志码说明</p>
                  <ul>
                    <li>
                      <strong>第3位 转存标志代码</strong>{" "}
                      0-否，1-到期自动转存；2-到期赎回；3-到期约定转存：
                    </li>
                    <li>
                      <strong>第8位 定活互转标志</strong>{" "}
                      0-否，1-是：签约后更新为1.解约或未签约时为0
                    </li>
                    <li>
                      <strong>第9位 合约来源代码</strong>{" "}
                      0-无关，1-卡内活转定，2-账户移入，3-现金新开
                    </li>
                    <li>
                      <strong>第11位：是否存在协议国税率</strong> 0-否，1-是
                    </li>
                  </ul>
                </div>
              }
              placement="top"
              overlayClassName="custom-tooltip"
              color="#fff"
              overlayStyle={{ minWidth: "300px" }}
            >
              <span>
                合约属性标志码{" "}
                <InfoCircleOutlined
                  style={{ marginLeft: 4, color: "#1890ff" }}
                />
              </span>
            </Tooltip>
          ),
          dataIndex: "contrAttrFgCd",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) => (
            <Button
              type="link"
              onClick={async () => {
                try {
                  const params: TbDpmstFixCont = {
                    prodtContractNo: record.prodtContractNo,
                    zoneVal: record.zoneVal,
                  };
                  const res = await getDpmstFixCont(params);
                  if (res.code === 200 && res.data) {
                    try {
                      const dpmstFixContData = JSON.parse(res.data);
                      setCurrentRow(dpmstFixContData);
                      setFixContrAttrFgCdVisible(true);
                    } catch (parseError) {
                      message.error("解析数据失败");
                    }
                  } else {
                    message.error(res.msg || "获取数据失败");
                  }
                } catch (error) {
                  message.error("获取详情失败");
                }
              }}
            >
              {record.contrAttrFgCd}
            </Button>
          ),
        },
        {
          title: () => (
            <Tooltip
              title={
                <div style={{ width: "100%" }}>
                  <p>合约状态标志码说明</p>
                  <ul>
                    <li>
                      <strong>第1位</strong> 合约状态代码 0-签约；1-解约
                    </li>
                  </ul>
                </div>
              }
              placement="top"
              overlayClassName="custom-tooltip"
              color="#fff"
              overlayStyle={{ minWidth: "300px" }}
            >
              <span>
                合约状态标志码{" "}
                <InfoCircleOutlined
                  style={{ marginLeft: 4, color: "#1890ff" }}
                />
              </span>
            </Tooltip>
          ),
          dataIndex: "contrStaFgCd",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) => (
            <Button
              type="link"
              onClick={async () => {
                try {
                  const params: TbDpmstFixCont = {
                    prodtContractNo: record.prodtContractNo,
                    zoneVal: record.zoneVal,
                  };
                  const res = await getDpmstFixCont(params);
                  if (res.code === 200 && res.data) {
                    try {
                      const dpmstFixContData = JSON.parse(res.data);
                      setCurrentRow(dpmstFixContData);
                      setFixContrStaFgCdVisible(true);
                    } catch (parseError) {
                      message.error("解析数据失败");
                    }
                  } else {
                    message.error(res.msg || "获取数据失败");
                  }
                } catch (error) {
                  message.error("获取详情失败");
                }
              }}
            >
              {record.contrStaFgCd}
            </Button>
          ),
        },
        {
          title: "储种中类代码",
          dataIndex: "savTypeMclassCode",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) =>
            getSavTypeMclassCodeText(record.savTypeMclassCode),
        },
        {
          title: "币种代码",
          dataIndex: "currCode",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) => getCurrCodeText(record.currCode),
        },
        {
          title: "钞汇类别代码",
          dataIndex: "cashExgVatgCd",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) =>
            getCashExgVatgCdText(record.cashExgVatgCd),
        },
        {
          title: "合约签约日期",
          dataIndex: "contrSignDt",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "合约签约时间",
          dataIndex: "contrSignTime",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "合约到期日期",
          dataIndex: "contrDueDate",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "期限单位代码",
          dataIndex: "termUnitCode",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) =>
            getTermUnitCodeText(record.termUnitCode),
        },
        {
          title: "合约期限",
          dataIndex: "contractTerm",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "签约机构号",
          dataIndex: "signContInstNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "签约渠道种类代码",
          dataIndex: "signChnKindCd",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) =>
            getSignChnKindCdText(record.signChnKindCd),
        },
        {
          title: "合约解约日期",
          dataIndex: "contrCacContDt",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "合约解约时间",
          dataIndex: "contrCacContTime",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "个人存款合约类型代码",
          dataIndex: "dpContrTpCd",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) =>
            getDpContrTpCdText(record.dpContrTpCd),
        },
        {
          title: "解约机构号",
          dataIndex: "cacContInstNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "最后动户交易日期",
          dataIndex: "lastMobilityTxDate",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "产品合约名称",
          dataIndex: "prodtContractName",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "产品合约描述",
          dataIndex: "prodtContractDesc",
          ellipsis: true,
          width: 150,
          align: "center",
        },
        {
          title: "主合约标志",
          dataIndex: "mainContrFlag",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) =>
            getMainContrFlagText(record.mainContrFlag),
        },
        {
          title: "最大子账号序号",
          dataIndex: "maxSaccnoSeqNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "可售产品版本号",
          dataIndex: "vendibiProdtVerNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "最后交易日期",
          dataIndex: "lastTxDate",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "创建时间戳",
          dataIndex: "createStamp",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "最后修改时间戳",
          dataIndex: "lastModStamp",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "分片值",
          dataIndex: "zoneVal",
          ellipsis: true,
          width: 120,
          align: "center",
        },
        {
          title: "操作",
          valueType: "option",
          width: 160,
          fixed: "right",
          align: "center",
          render: (_: any, record: any) => [
            <Button
              key="edit"
              type="link"
              size="small"
              onClick={() => handleDpmstFixContEdit(record)}
              disabled={
                !access.hasEnvPerms(
                  localStorage.getItem("currentEnv") as string
                )
              }
            >
              编辑
            </Button>,
            <Dropdown
              key="more"
              menu={{
                items: [
                  {
                    key: "frzquery",
                    label: "1-查询冻结信息",
                    onClick: () => handleQueryDprgtFrz(record),
                  },
                  {
                    key: "stopPayquery",
                    label: "2-查询止付信息",
                    onClick: () => handleQueryDprgtStopPay(record),
                  },
                  {
                    key: "tobeLimitServQuery",
                    label: "3-查询待限制服务信息",
                    onClick: () => handleQueryTobeLimtServ(record),
                  },
                  {
                    key: "limitServQuery",
                    label: "4-查询限制服务信息",
                    onClick: () => {
                      handleQueryLimtServ(record);
                    },
                  },
                  {
                    key: "contrAsstQuery",
                    label: "5-查询合约辅助表",
                    onClick: () => handleQueryDprgtContrAsst(record),
                  },
                ],
              }}
              trigger={["click"]}
            >
              <Button type="link" size="small">
                查询
                <MoreOutlined />
              </Button>
            </Dropdown>,
            <Button key="operation" type="link" size="small">
              操作
            </Button>,
          ],
        },
      ],
    },
    {
      key: "5",
      tab: "定期产品账户表",
      data: [],
      columns: [
        {
          title: "子账户序号",
          dataIndex: "saccnoSeqNo",
          ellipsis: true,
          width: 80,
          align: "center",
        },
        {
          title: "个人内部账号",
          dataIndex: "persInnerAccno",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "客户编号",
          dataIndex: "custNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "个人存款账户类型代码",
          dataIndex: "persDepAccTpCd",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) =>
            getPersDepAccTpCdText(record.persDepAccTpCd),
        },
        {
          title: "产品合约编号",
          dataIndex: "prodtContractNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "币种代码",
          dataIndex: "currCode",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) => getCurrCodeText(record.currCode),
        },
        {
          title: "钞汇类别代码",
          dataIndex: "cashExgVatgCd",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) =>
            getCashExgVatgCdText(record.cashExgVatgCd),
        },
        {
          title: "期限单位代码",
          dataIndex: "termUnitCode",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) =>
            getTermUnitCodeText(record.termUnitCode),
        },
        {
          title: "存款期限",
          dataIndex: "deptTerm",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: () => (
            <Tooltip
              title={
                <div style={{ width: "100%" }}>
                  <p>账户状态标志码说明</p>
                  <ul>
                    <li>
                      <strong>账户状态标志码</strong> 第1位 账户状态代码{" "}
                    </li>
                    <li>
                      0-正常；1-未启用（仅适用于万事达卡的人民币结算账户）；2-销户；
                    </li>
                    <li>
                      <strong>账户状态标志码</strong> 第2位 冻结状态代码{" "}
                    </li>
                    <li>0-正常；1-账户冻结</li>
                    <li>
                      <strong>账户状态标志码</strong> 第3位 止付状态代码{" "}
                    </li>
                    <li>0-正常；1-账户止付</li>
                  </ul>
                </div>
              }
              placement="top"
              overlayClassName="custom-tooltip"
              color="#fff"
              overlayStyle={{ minWidth: "300px" }}
            >
              <span>
                账户状态标志码{" "}
                <InfoCircleOutlined
                  style={{ marginLeft: 4, color: "#1890ff" }}
                />
              </span>
            </Tooltip>
          ),
          // title: "账户状态标志码",
          dataIndex: "accStatusFlagCd",
          width: 100,
          ellipsis: true,
          align: "center",
          render: (_: any, record: any) => (
            <Button
              type="link"
              onClick={async () => {
                try {
                  const params: TbDpmstFixAcc = {
                    persInnerAccno: record.persInnerAccno,
                    zoneVal: record.zoneVal,
                  };
                  const res = await getDpmstFixAcc(params);
                  if (res.code === 200 && res.data) {
                    try {
                      const dpmstFixAccData = JSON.parse(res.data);
                      setCurrentRow(dpmstFixAccData);
                      setFixAccStatusFlagCdVisible(true);
                    } catch (parseError) {
                      message.error("解析数据失败");
                    }
                  } else {
                    message.error(res.msg || "获取数据失败");
                  }
                } catch (error) {
                  message.error("获取详情失败");
                }
              }}
            >
              {record.accStatusFlagCd}
            </Button>
          ),
        },
        {
          title: () => (
            <Tooltip
              title={
                <div style={{ width: "100%" }}>
                  <p>账户标志码说明</p>
                  <ul>
                    <li>
                      <strong>第8位：利率启用方式代码</strong>{" "}
                      0-利率值（利率不变更）；
                    </li>
                    <li>1-利率号（利率生效日当天生效）；2-无关</li>
                    <li>
                      <strong>第10位 利率生效种类代码</strong> 1-协议利率
                      2-差异化利率 3-套餐利率 4-基础利率
                    </li>
                    <li>
                      <strong>第11位 特殊约转利率标志</strong> 0-否，1-是
                    </li>
                    <li>
                      <strong>第16位：账户产品标志码</strong>{" "}
                      0-无关，1-养老金账户
                    </li>
                  </ul>
                </div>
              }
              placement="top"
              overlayClassName="custom-tooltip"
              color="#fff"
              overlayStyle={{ minWidth: "300px" }}
            >
              <span>
                账户标志码{" "}
                <InfoCircleOutlined
                  style={{ marginLeft: 4, color: "#1890ff" }}
                />
              </span>
            </Tooltip>
          ),
          // title: "账户标志码",
          dataIndex: "accFlagCd",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) => (
            <Button
              type="link"
              onClick={async () => {
                try {
                  const params: TbDpmstFixAcc = {
                    persInnerAccno: record.persInnerAccno,
                    zoneVal: record.zoneVal,
                  };
                  const res = await getDpmstFixAcc(params);
                  if (res.code === 200 && res.data) {
                    try {
                      const dpmstFixAccData = JSON.parse(res.data);
                      setCurrentRow(dpmstFixAccData);
                      setFixAccFlagCdVisible(true);
                    } catch (parseError) {
                      message.error("解析数据失败");
                    }
                  } else {
                    message.error(res.msg || "获取数据失败");
                  }
                } catch (error) {
                  message.error("获取详情失败");
                }
              }}
            >
              {record.accFlagCd}
            </Button>
          ),
        },
        {
          title: "开户日期",
          dataIndex: "openaccDate",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "开户机构号",
          dataIndex: "openAccInstNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "销户日期",
          dataIndex: "clsaccDate",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "销户机构号",
          dataIndex: "clsAccInstNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "开户金额",
          dataIndex: "openaccAmt",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "账户余额",
          dataIndex: "accBal",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "账户可用余额",
          dataIndex: "accAvalBal",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "计息积数",
          dataIndex: "calIntAccu",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "利率",
          dataIndex: "intRate",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "利率号",
          dataIndex: "intRateNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "定期存款逾期利率号",
          dataIndex: "fixDeptOvdueIntRateNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "产品类型编码",
          dataIndex: "prodTpNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "起息日期",
          dataIndex: "bgnIntDate",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "到期日期",
          dataIndex: "dueDate",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "已部提次数",
          dataIndex: "prtExtdTimes",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "上次交易日期",
          dataIndex: "lastTmTxDt",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "上日余额",
          dataIndex: "lastdayBal",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "当前明细序号",
          dataIndex: "curDtlNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "执行周期代码",
          dataIndex: "execCycleCd",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) =>
            getExecCycValText(record.execCycleCd),
        },
        {
          title: "执行周期值",
          dataIndex: "execCycVal",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "营销柜员号",
          dataIndex: "campTellerNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "最后交易日期",
          dataIndex: "lastTxDate",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "创建时间戳",
          dataIndex: "createStamp",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "最后修改时间戳",
          dataIndex: "lastModStamp",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "分片值",
          dataIndex: "zoneVal",
          ellipsis: true,
          width: 120,
          align: "center",
        },
        {
          title: "操作",
          valueType: "option",
          width: 160,
          fixed: "right",
          align: "center",
          render: (_: any, record: any) => [
            <Button
              key="edit"
              type="link"
              size="small"
              onClick={() => handleDpmstFixAccEdit(record)}
              disabled={
                !access.hasEnvPerms(
                  localStorage.getItem("currentEnv") as string
                )
              }
            >
              编辑
            </Button>,
            <Dropdown
              key="more"
              menu={{
                items: [
                  {
                    key: "details",
                    label: "1-查询账户明细",
                    onClick: () => handleQueryDpdtlFixDetail(record),
                  },
                  {
                    key: "carryBalCalIntQuery",
                    label: "2-查询结转余额表",
                    onClick: () => handleQueryDprgtCarryBalCalInt(record),
                  },
                  {
                    key: "dprgtDepProveOpenQuery",
                    label: "3-查询存款证明开立信息登记",
                    onClick: () => handleQueryDprgtDepProveOpenInfo(record),
                  },
                ],
              }}
              trigger={["click"]}
            >
              <Button type="link" size="small">
                查询
                <MoreOutlined />
              </Button>
            </Dropdown>,
            <Button key="operation" type="link" size="small">
              操作
            </Button>,
          ],
        },
      ],
    },
    {
      key: "6",
      tab: "主从合约关系表",
      data: [],
      columns: [
        {
          title: "子账户序号",
          dataIndex: "saccnoSeqNo",
          ellipsis: true,
          width: 80,
          align: "center",
          fixed: "left",
        },
        {
          title: "关联产品合约编号",
          dataIndex: "relContrNo",
          ellipsis: true,
          width: 200,
          align: "center",
          fixed: "left",
        },
        {
          title: "主合约编号",
          dataIndex: "mainContrNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "基础产品编码",
          dataIndex: "baseProdtNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "可售产品编码",
          dataIndex: "vendibiProdtNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "币种代码",
          dataIndex: "currCode",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) => getCurrCodeText(record.currCode),
        },
        {
          title: "钞汇类别代码",
          dataIndex: "cashExgVatgCd",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) =>
            getCashExgVatgCdText(record.cashExgVatgCd),
        },
        {
          title: "个人存款合约类型代码",
          dataIndex: "dpContrTpCd",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) =>
            getDpContrTpCdText(record.dpContrTpCd),
        },
        {
          title: "关联类别标识代码",
          dataIndex: "relCategFgCd",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) =>
            getRelCategFgCdText(record.relCategFgCd),
        },
        {
          title: "分片值",
          dataIndex: "zoneVal",
          ellipsis: true,
          width: 120,
          align: "center",
        },
        {
          title: "操作",
          valueType: "option",
          width: 160,
          fixed: "right",
          align: "center",
          render: (_: any, record: any) => [
            <Button
              key="edit"
              type="link"
              size="small"
              onClick={() => handleDprgtMaslaveContRelatEdit(record)}
              disabled={
                !access.hasEnvPerms(
                  localStorage.getItem("currentEnv") as string
                )
              }
            >
              编辑
            </Button>,
            <Dropdown
              key="more"
              menu={{
                items: [
                  {
                    key: "other",
                    label: "查询其他",
                    onClick: () => console.log("查询其他:", record),
                  },
                ],
              }}
              trigger={["click"]}
            >
              <Button type="link" size="small">
                查询
                <MoreOutlined />
              </Button>
            </Dropdown>,
            <Button key="operation" type="link" size="small">
              操作
            </Button>,
          ],
        },
      ],
      pagination: {
        total: 0,
        pageSize: 10,
        current: 1,
      },
    },
    {
      key: "7",
      tab: "活期从合约表",
      data: [],
      columns: [
        {
          title: "子账户序号",
          dataIndex: "saccnoSeqNo",
          ellipsis: true,
          width: 80,
          align: "center",
          fixed: "left",
        },
        {
          title: "客户编号",
          dataIndex: "custNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "产品合约编号",
          dataIndex: "prodtContractNo",
          ellipsis: true,
          width: 200,
          align: "center",
          fixed: "left",
        },
        {
          title: "基础产品编码",
          dataIndex: "baseProdtNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "可售产品编码",
          dataIndex: "vendibiProdtNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: () => (
            <Tooltip
              title={
                <div style={{ width: "100%" }}>
                  <p>主合约控制标志码说明</p>
                  <ul>
                    <li>
                      <strong>第1位 止付状态代码</strong> 0-正常；1-止付
                    </li>
                    <li>
                      <strong>第2位 冻结状态代码</strong> 0-正常；1-冻结
                    </li>
                    <li>
                      <strong>第3位 暂停非柜面交易状态代码</strong>{" "}
                      0-正常；1-暂停非柜面交易
                    </li>
                    <li>
                      <strong>第4位 停用状态代码</strong> 0-正常；1-停用
                    </li>
                    <li>
                      <strong>第5位 中止状态代码</strong> 0-正常；1-中止
                    </li>
                    <li>
                      <strong>第6位 账户群标志</strong> 0-否；1-是
                    </li>
                    <li>
                      <strong>第7位 自贸区标志</strong>{" "}
                      0-无关，1-FTI区内自贸区，2-FTF区内境外自贸区
                    </li>
                    <li>
                      <strong>第8位 个人账户长期不动户状态代码</strong>{" "}
                      0-正常；1-长期不动户；
                    </li>
                    <li>
                      <strong>第9位 客户有主动交易标志</strong> 0-否；1-是
                    </li>
                    <li>
                      <strong>第10位 非柜面渠道专项管控标志</strong> 0-否；1-是
                    </li>
                    <li>
                      <strong>第11位 养老金专户状态代码</strong>{" "}
                      0-无关，1-正常，2-转移
                    </li>
                    <li>
                      <strong>第12位 压缩标志</strong> 0-否，1-是
                    </li>
                    <li>
                      <strong>第13位 介质是否未领用标志</strong>{" "}
                      0-否，1-是（0表示领用，1表示未领用）
                    </li>
                  </ul>
                </div>
              }
              placement="top"
              overlayClassName="custom-tooltip"
              color="#fff"
              overlayStyle={{ minWidth: "320px" }}
            >
              <span>
                主合约控制标志码{" "}
                <InfoCircleOutlined
                  style={{ marginLeft: 4, color: "#1890ff" }}
                />
              </span>
            </Tooltip>
          ),
          dataIndex: "mContrFlagCd",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) => (
            <Button
              type="link"
              onClick={async () => {
                try {
                  const params: TbDpmstCurtCont = {
                    prodtContractNo: record.prodtContractNo,
                    zoneVal: record.zoneVal,
                  };
                  const res = await getDpmstCurtCont(params);
                  if (res.code === 200 && res.data) {
                    try {
                      const dpmstCurtContData = JSON.parse(res.data);
                      setCurrentRow(dpmstCurtContData);
                      setCurrentProdtContractNo(record.prodtContractNo);
                      setCurtMContrFlagCdVisible(true);
                    } catch (parseError) {
                      message.error("解析数据失败");
                    }
                  } else {
                    message.error(res.msg || "获取数据失败");
                  }
                } catch (error) {
                  message.error("获取详情失败");
                }
              }}
            >
              {record.MContrFlagCd}
            </Button>
          ),
        },
        {
          title: () => (
            <Tooltip
              title={
                <div style={{ width: "100%" }}>
                  <p>合约控制标志码说明</p>
                  <ul>
                    <li>
                      <strong>第1位 余额限制类型代码：</strong>{" "}
                      0-无，1-余额设置上限；
                    </li>
                    <li>
                      <strong>第2位 资金 用途管控标志：</strong>0-不管控，1-管控
                    </li>
                    <li>
                      <strong>其他位置预留，用0补充</strong>
                    </li>
                  </ul>
                </div>
              }
              placement="top"
              overlayClassName="custom-tooltip"
              color="#fff"
              overlayStyle={{ minWidth: "400px" }}
            >
              <span>
                合约控制标志码{" "}
                <InfoCircleOutlined
                  style={{ marginLeft: 4, color: "#1890ff" }}
                />
              </span>
            </Tooltip>
          ),
          dataIndex: "contrCtrlFlagCd",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) => (
            <Button
              type="link"
              onClick={async () => {
                try {
                  const params: TbDpmstCurtCont = {
                    prodtContractNo: record.prodtContractNo,
                    zoneVal: record.zoneVal,
                  };
                  const res = await getDpmstCurtCont(params);
                  if (res.code === 200 && res.data) {
                    try {
                      const dpmstCurtContData = JSON.parse(res.data);
                      setCurrentRow(dpmstCurtContData);
                      setCurtContrCtrlFlagCdVisible(true);
                    } catch (parseError) {
                      message.error("解析数据失败");
                    }
                  } else {
                    message.error(res.msg || "获取数据失败");
                  }
                } catch (error) {
                  message.error("获取详情失败");
                }
              }}
            >
              {record.contrCtrlFlagCd}
            </Button>
          ),
        },
        {
          title: () => (
            <Tooltip
              title={
                <div style={{ width: "100%" }}>
                  <p>合约属性标志码说明</p>
                  <ul>
                    <li>
                      <strong>第1位 柜面核实标志</strong> 0-否，1-是
                    </li>
                    <li>
                      <strong>第2位 非绑定账户入金标志</strong> 0-否，1-是
                    </li>
                    <li>
                      <strong>第5位 卡贷通标志</strong> 0-否；1-是
                    </li>
                    <li>
                      <strong>第7位 约定转账标志</strong> 0-否，1-是
                    </li>
                    <li>
                      <strong>第10位 身份核实标志</strong> 0-无关 1-否 2-是
                    </li>
                    <li>
                      <strong>第11位 是否存在协议国税率</strong> 0-否，1-是
                    </li>
                    <li>
                      <strong>第12位 自动赎回标志</strong>{" "}
                      0-无关，1-允许，2-不允许；
                    </li>
                  </ul>
                </div>
              }
              placement="top"
              overlayClassName="custom-tooltip"
              color="#fff"
              overlayStyle={{ minWidth: "320px" }}
            >
              <span>
                合约属性标志码{" "}
                <InfoCircleOutlined
                  style={{ marginLeft: 4, color: "#1890ff" }}
                />
              </span>
            </Tooltip>
          ),
          dataIndex: "contrAttrFgCd",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) => (
            <Button
              type="link"
              onClick={async () => {
                try {
                  const params: TbDpmstCurtCont = {
                    prodtContractNo: record.prodtContractNo,
                    zoneVal: record.zoneVal,
                  };
                  const res = await getDpmstCurtCont(params);
                  if (res.code === 200 && res.data) {
                    try {
                      const dpmstCurtContData = JSON.parse(res.data);
                      setCurrentRow(dpmstCurtContData);
                      setCurtContrAttrFgCdVisible(true);
                    } catch (parseError) {
                      message.error("解析数据失败");
                    }
                  } else {
                    message.error(res.msg || "获取数据失败");
                  }
                } catch (error) {
                  message.error("获取详情失败");
                }
              }}
            >
              {record.contrAttrFgCd}
            </Button>
          ),
        },
        {
          title: () => (
            <Tooltip
              title={
                <div style={{ width: "100%" }}>
                  <p>合约状态标志码说明</p>
                  <ul>
                    <li>
                      <strong>第1位 合约状态代码</strong> 0-签约；1-解约
                    </li>
                    <li>
                      <strong>第2位 副卡停用状态代码</strong> 0-正常/无关；
                    </li>
                    <li>1-主卡办理副卡停用；2-副卡办理副卡停用</li>
                  </ul>
                </div>
              }
              placement="top"
              overlayClassName="custom-tooltip"
              color="#fff"
              overlayStyle={{ minWidth: "320px" }}
            >
              <span>
                合约状态标志码{" "}
                <InfoCircleOutlined
                  style={{ marginLeft: 4, color: "#1890ff" }}
                />
              </span>
            </Tooltip>
          ),
          dataIndex: "contrStaFgCd",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) => (
            <Button
              type="link"
              onClick={async () => {
                try {
                  const params: TbDpmstCurtCont = {
                    prodtContractNo: record.prodtContractNo,
                    zoneVal: record.zoneVal,
                  };
                  const res = await getDpmstCurtCont(params);
                  if (res.code === 200 && res.data) {
                    try {
                      const dpmstCurtContData = JSON.parse(res.data);
                      setCurrentRow(dpmstCurtContData);
                      setCurtContrStaFgCdVisible(true);
                    } catch (parseError) {
                      message.error("解析数据失败");
                    }
                  } else {
                    message.error(res.msg || "获取数据失败");
                  }
                } catch (error) {
                  message.error("获取详情失败");
                }
              }}
            >
              {record.contrStaFgCd}
            </Button>
          ),
        },
        {
          title: "可售产品版本号",
          dataIndex: "vendibiProdtVerNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "储种中类代码",
          dataIndex: "savTypeMclassCode",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) =>
            getSavTypeMclassCodeText(record.savTypeMclassCode),
        },
        {
          title: "币种代码",
          dataIndex: "currCode",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) => getCurrCodeText(record.currCode),
        },
        {
          title: "协议国税率",
          dataIndex: "agrNtaxRate",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "合约签约日期",
          dataIndex: "contrSignDt",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "合约签约时间",
          dataIndex: "contrSignTime",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "签约机构号",
          dataIndex: "signContInstNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "签约渠道种类代码",
          dataIndex: "signChnKindCd",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) =>
            getSignChnKindCdText(record.signChnKindCd),
        },
        {
          title: "合约解约日期",
          dataIndex: "contrCacContDt",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "合约解约时间",
          dataIndex: "contrCacContTime",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "个人存款合约类型代码",
          dataIndex: "dpContrTpCd",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) =>
            getDpContrTpCdText(record.dpContrTpCd),
        },
        {
          title: "个人存款账户种类代码",
          dataIndex: "persDepAccKindCd",
          ellipsis: true,
          width: 100,
          align: "center",
          render: (_: any, record: any) =>
            getPersDepAccKindCdText(record.persDepAccKindCd),
        },
        {
          title: "特殊合约类型代码",
          dataIndex: "specContrTpCd",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) =>
            getSpecContrTpCdText(record.specContrTpCd),
        },
        {
          title: "产品合约名称",
          dataIndex: "prodtContractName",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "产品合约描述",
          dataIndex: "prodtContractDesc",
          ellipsis: true,
          width: 150,
          align: "center",
        },
        {
          title: "主合约标志",
          dataIndex: "mainContrFlag",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) =>
            getMainContrFlagText(record.mainContrFlag),
        },
        {
          title: "最大子账号序号",
          dataIndex: "maxSaccnoSeqNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "最后交易日期",
          dataIndex: "lastTxDate",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "创建时间戳",
          dataIndex: "createStamp",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "最后修改时间戳",
          dataIndex: "lastModStamp",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "分片值",
          dataIndex: "zoneVal",
          ellipsis: true,
          width: 120,
          align: "center",
        },
        {
          title: "操作",
          valueType: "option",
          width: 160,
          fixed: "right",
          align: "center",
          render: (_: any, record: any) => [
            <Button
              key="edit"
              type="link"
              size="small"
              onClick={() => handleDpmstCurtContEdit(record)}
              disabled={
                !access.hasEnvPerms(
                  localStorage.getItem("currentEnv") as string
                )
              }
            >
              编辑
            </Button>,
            <Dropdown
              key="more"
              menu={{
                items: [
                  {
                    key: "frzquery",
                    label: "1-查询冻结信息",
                    onClick: () => handleQueryDprgtFrz(record),
                  },
                  {
                    key: "stopPayquery",
                    label: "2-查询止付信息",
                    onClick: () => handleQueryDprgtStopPay(record),
                  },
                  {
                    key: "cosumeQuery",
                    label: "3-查询消费信息",
                    onClick: () => handleQueryConsum(record),
                  },
                  {
                    key: "cosumeQuery",
                    label: "4-查询退货信息",
                    onClick: () => handleQuerySaleRet(record),
                  },
                  {
                    key: "contrAsstQuery",
                    label: "5-查询合约辅助表",
                    onClick: () => handleQueryDprgtContrAsst(record),
                  },
                  {
                    key: "addOffInfoQuery",
                    label: "6-查询圈存加办信息",
                    onClick: () => handleQueryDprgtCflAddoffInfo(record),
                  },
                ],
              }}
              trigger={["click"]}
            >
              <Button type="link" size="small">
                查询
                <MoreOutlined />
              </Button>
            </Dropdown>,
            <Button key="operation" type="link" size="small">
              操作
            </Button>,
          ],
        },
      ],
      pagination: {
        total: 0,
        pageSize: 10,
        current: 1,
      },
    },
    {
      key: "8",
      tab: "活期从账户表",
      data: [],
      columns: [
        {
          title: "子账户序号",
          dataIndex: "saccnoSeqNo",
          ellipsis: true,
          width: 80,
          align: "center",
          fixed: "left",
        },
        {
          title: "个人内部账号",
          dataIndex: "persInnerAccno",
          ellipsis: true,
          width: 200,
          align: "center",
          fixed: "left",
        },
        {
          title: "客户编号",
          dataIndex: "custNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "个人存款账户类型代码",
          dataIndex: "persDepAccTpCd",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) =>
            getPersDepAccTpCdText(record.persDepAccTpCd),
        },
        {
          title: "产品合约编号",
          dataIndex: "prodtContractNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "币种代码",
          dataIndex: "currCode",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) => getCurrCodeText(record.currCode),
        },
        {
          title: "钞汇类别代码",
          dataIndex: "cashExgVatgCd",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) =>
            getCashExgVatgCdText(record.cashExgVatgCd),
        },
        {
          title: () => (
            <Tooltip
              title={
                <div style={{ width: "100%" }}>
                  <p>账户状态标志码说明</p>
                  <ul>
                    <li>
                      <strong>第1位 账户状态代码</strong>
                    </li>
                    <li>0-正常</li>
                    <li>1-未启用（仅适用于万事达卡的人民币结算账户）</li>
                    <li>2-销户；</li>
                    <li>
                      <strong>第2位 冻结状态代码</strong> 0-正常；1-账户冻结
                    </li>
                    <li>
                      <strong>第3位 止付状态代码</strong> 0-正常；1-账户止付
                    </li>
                  </ul>
                </div>
              }
              placement="top"
              overlayClassName="custom-tooltip"
              color="#fff"
              overlayStyle={{ minWidth: "500px" }}
            >
              <span>
                账户状态标志码{" "}
                <InfoCircleOutlined
                  style={{ marginLeft: 4, color: "#1890ff" }}
                />
              </span>
            </Tooltip>
          ),
          dataIndex: "accStatusFlagCd",
          width: 100,
          ellipsis: true,
          align: "center",
          render: (_: any, record: any) => (
            <Button
              type="link"
              onClick={async () => {
                try {
                  const params: TbDpmstCurtAcc = {
                    persInnerAccno: record.persInnerAccno,
                    zoneVal: record.zoneVal,
                  };
                  const res = await getDpmstCurtAcc(params);
                  if (res.code === 200 && res.data) {
                    try {
                      const dpmstCurtAccData = JSON.parse(res.data);
                      setCurrentRow(dpmstCurtAccData);
                      setCurtAccStatusFlagCdVisible(true);
                    } catch (parseError) {
                      message.error("解析数据失败");
                    }
                  } else {
                    message.error(res.msg || "获取数据失败");
                  }
                } catch (error) {
                  message.error("获取详情失败");
                }
              }}
            >
              {record.accStatusFlagCd}
            </Button>
          ),
        },
        {
          title: () => (
            <Tooltip
              title={
                <div style={{ width: "100%" }}>
                  <p>账户标志码说明</p>
                  <ul>
                    <li>
                      <strong>第2位 免征小额管理费标志 </strong>
                    </li>
                    <li>0-不免除；1-免除小额管理费；</li>
                    <li>
                      <strong>第3位 免征年费标志 </strong>
                    </li>
                    <li>0-不免除；1-免除年费；</li>
                    <li>
                      <strong>第4位：免征VIP服务费标志</strong>
                    </li>
                    <li>0-不免除；1-免除VIP服务费；</li>
                    <li>
                      <strong>第5位：计息方式代码</strong>
                    </li>
                    <li>0-正常；1-结转计息；2-不计息</li>
                    <li>
                      <strong>第7位：账户星级代码</strong>
                    </li>
                    <li>0-无关；1-准一星；2-一星；3-二星；4-三星；</li>
                    <li>
                      <strong>第8位：利率启用方式代码：（活期预留）</strong>
                    </li>
                    <li>
                      0-利率值（利率不变更）；1-利率号（利率生效日当天生效）；2-无关：
                    </li>
                    <li>
                      <strong>第16位：账户产品标志码</strong>
                    </li>
                    <li>0-无关，1-养老金账户</li>
                  </ul>
                </div>
              }
              placement="top"
              overlayClassName="custom-tooltip"
              color="#fff"
              overlayStyle={{ minWidth: "400px" }}
            >
              <span>
                账户标志码{" "}
                <InfoCircleOutlined
                  style={{ marginLeft: 4, color: "#1890ff" }}
                />
              </span>
            </Tooltip>
          ),
          dataIndex: "accFlagCd",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) => (
            <Button
              type="link"
              onClick={async () => {
                try {
                  const params: TbDpmstCurtAcc = {
                    persInnerAccno: record.persInnerAccno,
                    zoneVal: record.zoneVal,
                  };
                  const res = await getDpmstCurtAcc(params);
                  if (res.code === 200 && res.data) {
                    try {
                      const dpmstCurtAccData = JSON.parse(res.data);
                      setCurrentRow(dpmstCurtAccData);
                      setCurtAccFlagCdVisible(true);
                    } catch (parseError) {
                      message.error("解析数据失败");
                    }
                  } else {
                    message.error(res.msg || "获取数据失败");
                  }
                } catch (error) {
                  message.error("获取详情失败");
                }
              }}
            >
              {record.accFlagCd}
            </Button>
          ),
        },
        {
          title: "开户日期",
          dataIndex: "openaccDate",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "开户机构号",
          dataIndex: "openAccInstNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "销户日期",
          dataIndex: "clsaccDate",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "销户机构号",
          dataIndex: "clsAccInstNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "开户金额",
          dataIndex: "openaccAmt",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "账户余额",
          dataIndex: "accBal",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "账户可用余额",
          dataIndex: "accAvalBal",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "计息积数",
          dataIndex: "calIntAccu",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "利率",
          dataIndex: "intRate",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "利率号",
          dataIndex: "intRateNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "起息日期",
          dataIndex: "bgnIntDate",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "计息积数",
          dataIndex: "calIntAccu",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "上次交易日期",
          dataIndex: "lastTmTxDt",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "上日余额",
          dataIndex: "lastdayBal",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "当前明细序号",
          dataIndex: "curDtlNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "营销柜员号",
          dataIndex: "campTellerNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "最后交易日期",
          dataIndex: "lastTxDate",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "创建时间戳",
          dataIndex: "createStamp",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "最后修改时间戳",
          dataIndex: "lastModStamp",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "操作",
          valueType: "option",
          width: 160,
          fixed: "right",
          align: "center",
          render: (_: any, record: any) => [
            <Button
              key="edit"
              type="link"
              size="small"
              onClick={() => handleDpmstCurtAccEdit(record)}
              disabled={
                !access.hasEnvPerms(
                  localStorage.getItem("currentEnv") as string
                )
              }
            >
              编辑
            </Button>,
            <Dropdown
              key="more"
              menu={{
                items: [
                  {
                    key: "details",
                    label: "1-查询账户明细",
                    onClick: () => handleQueryDpdtlCurtDetail(record),
                  },
                  {
                    key: "frzQuery",
                    label: "2-查询冻结信息",
                    onClick: () => handleQueryDprgtFrz(record),
                  },
                  {
                    key: "stopQuery",
                    label: "3-查询止付信息",
                    onClick: () => handleQueryDprgtStopPay(record),
                  },
                  {
                    key: "accFreeQuery",
                    label: "4-查询账户免收登记簿",
                    onClick: () => handleQueryDprgtAccFree(record),
                  },
                  {
                    key: "carryBalCalIntQuery",
                    label: "5-查询结转余额表",
                    onClick: () => handleQueryDprgtCarryBalCalInt(record),
                  },
                  {
                    key: "dprgtDepProveOpenQuery",
                    label: "6-查询存款证明开立信息登记",
                    onClick: () => handleQueryDprgtDepProveOpenInfo(record),
                  },
                ],
              }}
              trigger={["click"]}
            >
              <Button type="link" size="small">
                查询
                <MoreOutlined />
              </Button>
            </Dropdown>,
            <Button key="operation" type="link" size="small">
              操作
            </Button>,
          ],
        },
        {
          title: "分片值",
          dataIndex: "zoneVal",
          ellipsis: true,
          width: 120,
          align: "center",
        },
      ],
      pagination: {
        total: 0,
        pageSize: 10,
        current: 1,
      },
    },
    {
      key: "9",
      tab: "定期从合约表",
      data: [],
      columns: [
        {
          title: "子账户序号",
          dataIndex: "saccnoSeqNo",
          ellipsis: true,
          width: 80,
          align: "center",
          fixed: "left",
        },
        {
          title: "客户编号",
          dataIndex: "custNo",
          ellipsis: true,
          width: 80,
          align: "center",
        },
        {
          title: "产品合约编号",
          dataIndex: "prodtContractNo",
          ellipsis: true,
          width: 80,
          align: "center",
          fixed: "left",
        },
        {
          title: "基础产品编码",
          dataIndex: "baseProdtNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "可售产品编码",
          dataIndex: "vendibiProdtNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: () => (
            <Tooltip
              title={
                <div style={{ width: "100%" }}>
                  <p>主合约控制标志码说明</p>
                  <ul>
                    <li>
                      <strong>第1位 止付状态代码</strong> 0-正常；1-止付
                    </li>
                    <li>
                      <strong>第2位 冻结状态代码</strong> 0-正常；1-冻结
                    </li>
                    <li>
                      <strong>第3位 暂停非柜面交易状态代码</strong>
                    </li>
                    <li>0-正常；1-暂停非柜面交易</li>
                    <li>
                      <strong>第4位 停用状态代码</strong> 0-正常；1-停用
                    </li>
                    <li>
                      <strong>第5位 中止状态代码</strong> 0-正常；1-中止
                    </li>
                    <li>
                      <strong>第6位 账户群标志</strong> 0-否；1-是
                    </li>
                    <li>
                      <strong>第7位 自贸区标志</strong>{" "}
                      0-无关，1-FTI区内自贸区，2-FTF区内境外自贸区
                    </li>
                    <li>
                      <strong>第8位 个人账户长期不动户状态代码</strong>
                    </li>
                    <li>0-正常；1-长期不动户；</li>
                    <li>
                      <strong>第9位 客户有主动交易标志</strong> 0-否；1-是
                    </li>
                    <li>
                      <strong>第10位 非柜面渠道专项管控标志</strong>
                    </li>
                    <li>0-否；1-是</li>
                    <li>
                      <strong>第13位 介质是否未领用标志</strong>
                    </li>
                    <li>0-否，1-是（0表示领用，1表示未领用）</li>
                  </ul>
                </div>
              }
              placement="top"
              overlayClassName="custom-tooltip"
              color="#fff"
              overlayStyle={{ minWidth: "320px" }}
            >
              <span>
                主合约控制标志码{" "}
                <InfoCircleOutlined
                  style={{ marginLeft: 4, color: "#1890ff" }}
                />
              </span>
            </Tooltip>
          ),
          dataIndex: "mContrFlagCd",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) => (
            <Button
              type="link"
              onClick={async () => {
                try {
                  const params: TbDpmstFixCont = {
                    prodtContractNo: record.prodtContractNo,
                    zoneVal: record.zoneVal,
                  };
                  const res = await getDpmstFixCont(params);
                  if (res.code === 200 && res.data) {
                    try {
                      const dpmstFixContData = JSON.parse(res.data);
                      setCurrentRow(dpmstFixContData);
                      setFixMContrFlagCdVisible(true);
                    } catch (parseError) {
                      message.error("解析数据失败");
                    }
                  } else {
                    message.error(res.msg || "获取数据失败");
                  }
                } catch (error) {
                  message.error("获取详情失败");
                }
              }}
            >
              {record.MContrFlagCd}
            </Button>
          ),
        },
        {
          title: () => (
            <Tooltip
              title={
                <div style={{ width: "100%" }}>
                  <p>合约控制标志码说明</p>
                  <ul>
                    <li>
                      <strong>（活期使用）第1位： 余额限制类型代码：</strong>
                      0-无，1-余额设置上限
                    </li>
                    <li>
                      <strong>（活期使用）第2位： 资金用途管控标志：</strong>
                      0-不管控，1-管控
                    </li>
                    <li>
                      <strong>（定期预留）</strong>
                    </li>
                  </ul>
                </div>
              }
              placement="top"
              overlayClassName="custom-tooltip"
              color="#fff"
              overlayStyle={{ minWidth: "330px" }}
            >
              <span>
                合约控制标志码{" "}
                <InfoCircleOutlined
                  style={{ marginLeft: 4, color: "#1890ff" }}
                />
              </span>
            </Tooltip>
          ),
          dataIndex: "contrCtrlFlagCd",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) => (
            <Button
              type="link"
              onClick={async () => {
                try {
                  const params: TbDpmstFixCont = {
                    prodtContractNo: record.prodtContractNo,
                    zoneVal: record.zoneVal,
                  };
                  const res = await getDpmstFixCont(params);
                  if (res.code === 200 && res.data) {
                    try {
                      const dpmstFixContData = JSON.parse(res.data);
                      setCurrentRow(dpmstFixContData);
                      setFixContrCtrlFlagCdVisible(true);
                    } catch (parseError) {
                      message.error("解析数据失败");
                    }
                  } else {
                    message.error(res.msg || "获取数据失败");
                  }
                } catch (error) {
                  message.error("获取详情失败");
                }
              }}
            >
              {record.contrCtrlFlagCd}
            </Button>
          ),
        },
        {
          title: () => (
            <Tooltip
              title={
                <div style={{ width: "100%" }}>
                  <p>合约属性标志码说明</p>
                  <ul>
                    <li>
                      <strong>第3位 转存标志代码</strong>{" "}
                      0-否，1-到期自动转存；2-到期赎回；3-到期约定转存：
                    </li>
                    <li>
                      <strong>第8位 定活互转标志</strong>{" "}
                      0-否，1-是：签约后更新为1.解约或未签约时为0
                    </li>
                    <li>
                      <strong>第9位 合约来源代码</strong>{" "}
                      0-无关，1-卡内活转定，2-账户移入，3-现金新开
                    </li>
                    <li>
                      <strong>第11位：是否存在协议国税率</strong> 0-否，1-是
                    </li>
                  </ul>
                </div>
              }
              placement="top"
              overlayClassName="custom-tooltip"
              color="#fff"
              overlayStyle={{ minWidth: "300px" }}
            >
              <span>
                合约属性标志码{" "}
                <InfoCircleOutlined
                  style={{ marginLeft: 4, color: "#1890ff" }}
                />
              </span>
            </Tooltip>
          ),
          dataIndex: "contrAttrFgCd",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) => (
            <Button
              type="link"
              onClick={async () => {
                try {
                  const params: TbDpmstFixCont = {
                    prodtContractNo: record.prodtContractNo,
                    zoneVal: record.zoneVal,
                  };
                  const res = await getDpmstFixCont(params);
                  if (res.code === 200 && res.data) {
                    try {
                      const dpmstFixContData = JSON.parse(res.data);
                      setCurrentRow(dpmstFixContData);
                      setFixContrAttrFgCdVisible(true);
                    } catch (parseError) {
                      message.error("解析数据失败");
                    }
                  } else {
                    message.error(res.msg || "获取数据失败");
                  }
                } catch (error) {
                  message.error("获取详情失败");
                }
              }}
            >
              {record.contrAttrFgCd}
            </Button>
          ),
        },
        {
          title: () => (
            <Tooltip
              title={
                <div style={{ width: "100%" }}>
                  <p>合约状态标志码说明</p>
                  <ul>
                    <li>
                      <strong>第1位</strong> 合约状态代码 0-签约；1-解约
                    </li>
                  </ul>
                </div>
              }
              placement="top"
              overlayClassName="custom-tooltip"
              color="#fff"
              overlayStyle={{ minWidth: "300px" }}
            >
              <span>
                合约状态标志码{" "}
                <InfoCircleOutlined
                  style={{ marginLeft: 4, color: "#1890ff" }}
                />
              </span>
            </Tooltip>
          ),
          dataIndex: "contrStaFgCd",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) => (
            <Button
              type="link"
              onClick={async () => {
                try {
                  const params: TbDpmstFixCont = {
                    prodtContractNo: record.prodtContractNo,
                    zoneVal: record.zoneVal,
                  };
                  const res = await getDpmstFixCont(params);
                  if (res.code === 200 && res.data) {
                    try {
                      const dpmstFixContData = JSON.parse(res.data);
                      setCurrentRow(dpmstFixContData);
                      setFixContrStaFgCdVisible(true);
                    } catch (parseError) {
                      message.error("解析数据失败");
                    }
                  } else {
                    message.error(res.msg || "获取数据失败");
                  }
                } catch (error) {
                  message.error("获取详情失败");
                }
              }}
            >
              {record.contrStaFgCd}
            </Button>
          ),
        },
        {
          title: "可售产品版本号",
          dataIndex: "vendibiProdtVerNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "储种中类代码",
          dataIndex: "savTypeMclassCode",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) =>
            getSavTypeMclassCodeText(record.savTypeMclassCode),
        },
        {
          title: "币种代码",
          dataIndex: "currCode",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) => getCurrCodeText(record.currCode),
        },
        {
          title: "钞汇类别代码",
          dataIndex: "cashExgVatgCd",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) =>
            getCashExgVatgCdText(record.cashExgVatgCd),
        },
        {
          title: "合约签约日期",
          dataIndex: "contrSignDt",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "合约签约时间",
          dataIndex: "contrSignTime",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "合约到期日期",
          dataIndex: "contrDueDate",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "期限单位代码",
          dataIndex: "termUnitCode",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) =>
            getTermUnitCodeText(record.termUnitCode),
        },
        {
          title: "合约期限",
          dataIndex: "contractTerm",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "签约机构号",
          dataIndex: "signContInstNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "签约渠道种类代码",
          dataIndex: "signChnKindCd",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) =>
            getSignChnKindCdText(record.signChnKindCd),
        },
        {
          title: "合约解约日期",
          dataIndex: "contrCacContDt",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "合约解约时间",
          dataIndex: "contrCacContTime",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "个人存款合约类型代码",
          dataIndex: "dpContrTpCd",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) =>
            getDpContrTpCdText(record.dpContrTpCd),
        },
        {
          title: "解约机构号",
          dataIndex: "cacContInstNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "最后动户交易日期",
          dataIndex: "lastMobilityTxDate",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "产品合约名称",
          dataIndex: "prodtContractName",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "产品合约描述",
          dataIndex: "prodtContractDesc",
          ellipsis: true,
          width: 150,
          align: "center",
        },
        {
          title: "主合约标志",
          dataIndex: "mainContrFlag",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) =>
            getMainContrFlagText(record.mainContrFlag),
        },
        {
          title: "最大子账号序号",
          dataIndex: "maxSaccnoSeqNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "最后交易日期",
          dataIndex: "lastTxDate",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "创建时间戳",
          dataIndex: "createStamp",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "最后修改时间戳",
          dataIndex: "lastModStamp",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "分片值",
          dataIndex: "zoneVal",
          ellipsis: true,
          width: 120,
          align: "center",
        },
        {
          title: "操作",
          valueType: "option",
          width: 160,
          fixed: "right",
          align: "center",
          render: (_: any, record: any) => [
            <Button
              key="edit"
              type="link"
              size="small"
              onClick={() => handleDpmstFixContEdit(record)}
              disabled={
                !access.hasEnvPerms(
                  localStorage.getItem("currentEnv") as string
                )
              }
            >
              编辑
            </Button>,
            <Dropdown
              key="more"
              menu={{
                items: [
                  {
                    key: "frzquery",
                    label: "1-查询冻结信息",
                    onClick: () => handleQueryDprgtFrz(record),
                  },
                  {
                    key: "stopPayquery",
                    label: "2-查询止付信息",
                    onClick: () => handleQueryDprgtStopPay(record),
                  },
                  {
                    key: "tobeLimitServQuery",
                    label: "3-查询待限制服务信息",
                    onClick: () => handleQueryTobeLimtServ(record),
                  },
                  {
                    key: "limitServQuery",
                    label: "4-查询限制服务信息",
                    onClick: () => {
                      handleQueryLimtServ(record);
                    },
                  },
                  {
                    key: "contrAsstQuery",
                    label: "5-查询合约辅助表",
                    onClick: () => handleQueryDprgtContrAsst(record),
                  },
                ],
              }}
              trigger={["click"]}
            >
              <Button type="link" size="small">
                查询
                <MoreOutlined />
              </Button>
            </Dropdown>,
            <Button key="operation" type="link" size="small">
              操作
            </Button>,
          ],
        },
      ],
      pagination: {
        total: 0,
        pageSize: 10,
        current: 1,
      },
    },
    {
      key: "10",
      tab: "定期从账户表",
      data: [],
      columns: [
        {
          title: "子账户序号",
          dataIndex: "saccnoSeqNo",
          ellipsis: true,
          width: 80,
          align: "center",
          fixed: "left",
        },
        {
          title: "个人内部账号",
          dataIndex: "persInnerAccno",
          ellipsis: true,
          width: 200,
          align: "center",
          fixed: "left",
        },
        {
          title: "客户编号",
          dataIndex: "custNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "个人存款账户类型代码",
          dataIndex: "persDepAccTpCd",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) =>
            getPersDepAccTpCdText(record.persDepAccTpCd),
        },
        {
          title: "产品合约编号",
          dataIndex: "prodtContractNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "币种代码",
          dataIndex: "currCode",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) => getCurrCodeText(record.currCode),
        },
        {
          title: "钞汇类别代码",
          dataIndex: "cashExgVatgCd",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) =>
            getCashExgVatgCdText(record.cashExgVatgCd),
        },
        {
          title: "期限单位代码",
          dataIndex: "termUnitCode",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) =>
            getTermUnitCodeText(record.termUnitCode),
        },
        {
          title: "存款期限",
          dataIndex: "deptTerm",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: () => (
            <Tooltip
              title={
                <div style={{ width: "100%" }}>
                  <p>账户状态标志码说明</p>
                  <ul>
                    <li>
                      <strong>账户状态标志码</strong> 第1位 账户状态代码{" "}
                    </li>
                    <li>
                      0-正常；1-未启用（仅适用于万事达卡的人民币结算账户）；2-销户；
                    </li>
                    <li>
                      <strong>账户状态标志码</strong> 第2位 冻结状态代码{" "}
                    </li>
                    <li>0-正常；1-账户冻结</li>
                    <li>
                      <strong>账户状态标志码</strong> 第3位 止付状态代码{" "}
                    </li>
                    <li>0-正常；1-账户止付</li>
                  </ul>
                </div>
              }
              placement="top"
              overlayClassName="custom-tooltip"
              color="#fff"
              overlayStyle={{ minWidth: "300px" }}
            >
              <span>
                账户状态标志码{" "}
                <InfoCircleOutlined
                  style={{ marginLeft: 4, color: "#1890ff" }}
                />
              </span>
            </Tooltip>
          ),
          dataIndex: "accStatusFlagCd",
          width: 100,
          ellipsis: true,
          align: "center",
          render: (_: any, record: any) => (
            <Button
              type="link"
              onClick={async () => {
                try {
                  const params: TbDpmstFixAcc = {
                    persInnerAccno: record.persInnerAccno,
                    zoneVal: record.zoneVal,
                  };
                  const res = await getDpmstFixAcc(params);
                  if (res.code === 200 && res.data) {
                    try {
                      const dpmstFixAccData = JSON.parse(res.data);
                      setCurrentRow(dpmstFixAccData);
                      setFixAccStatusFlagCdVisible(true);
                    } catch (parseError) {
                      message.error("解析数据失败");
                    }
                  } else {
                    message.error(res.msg || "获取数据失败");
                  }
                } catch (error) {
                  message.error("获取详情失败");
                }
              }}
            >
              {record.accStatusFlagCd}
            </Button>
          ),
        },
        {
          title: () => (
            <Tooltip
              title={
                <div style={{ width: "100%" }}>
                  <p>账户标志码说明</p>
                  <ul>
                    <li>
                      <strong>第8位：利率启用方式代码</strong>{" "}
                      0-利率值（利率不变更）；
                    </li>
                    <li>1-利率号（利率生效日当天生效）；2-无关</li>
                    <li>
                      <strong>第10位 利率生效种类代码</strong> 1-协议利率
                      2-差异化利率 3-套餐利率 4-基础利率
                    </li>
                    <li>
                      <strong>第11位 特殊约转利率标志</strong> 0-否，1-是
                    </li>
                    <li>
                      <strong>第16位：账户产品标志码</strong>{" "}
                      0-无关，1-养老金账户
                    </li>
                  </ul>
                </div>
              }
              placement="top"
              overlayClassName="custom-tooltip"
              color="#fff"
              overlayStyle={{ minWidth: "300px" }}
            >
              <span>
                账户标志码{" "}
                <InfoCircleOutlined
                  style={{ marginLeft: 4, color: "#1890ff" }}
                />
              </span>
            </Tooltip>
          ),
          dataIndex: "accFlagCd",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) => (
            <Button
              type="link"
              onClick={async () => {
                try {
                  const params: TbDpmstFixAcc = {
                    persInnerAccno: record.persInnerAccno,
                    zoneVal: record.zoneVal,
                  };
                  const res = await getDpmstFixAcc(params);
                  if (res.code === 200 && res.data) {
                    try {
                      const dpmstFixAccData = JSON.parse(res.data);
                      setCurrentRow(dpmstFixAccData);
                      setFixAccFlagCdVisible(true);
                    } catch (parseError) {
                      message.error("解析数据失败");
                    }
                  } else {
                    message.error(res.msg || "获取数据失败");
                  }
                } catch (error) {
                  message.error("获取详情失败");
                }
              }}
            >
              {record.accFlagCd}
            </Button>
          ),
        },
        {
          title: "开户日期",
          dataIndex: "openaccDate",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "开户机构号",
          dataIndex: "openAccInstNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "销户日期",
          dataIndex: "clsaccDate",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "销户机构号",
          dataIndex: "clsAccInstNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "开户金额",
          dataIndex: "openaccAmt",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "账户余额",
          dataIndex: "accBal",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "账户可用余额",
          dataIndex: "accAvalBal",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "计息积数",
          dataIndex: "calIntAccu",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "利率",
          dataIndex: "intRate",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "利率号",
          dataIndex: "intRateNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "定期存款逾期利率号",
          dataIndex: "fixDeptOvdueIntRateNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "产品类型编码",
          dataIndex: "prodTpNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "起息日期",
          dataIndex: "bgnIntDate",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "到期日期",
          dataIndex: "dueDate",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "已部提次数",
          dataIndex: "prtExtdTimes",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "上次交易日期",
          dataIndex: "lastTmTxDt",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "上日余额",
          dataIndex: "lastdayBal",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "当前明细序号",
          dataIndex: "curDtlNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "执行周期代码",
          dataIndex: "execCycleCd",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) =>
            getExecCycValText(record.execCycleCd),
        },
        {
          title: "执行周期值",
          dataIndex: "execCycVal",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "营销柜员号",
          dataIndex: "campTellerNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "最后交易日期",
          dataIndex: "lastTxDate",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "创建时间戳",
          dataIndex: "createStamp",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "最后修改时间戳",
          dataIndex: "lastModStamp",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "操作",
          valueType: "option",
          width: 160,
          fixed: "right",
          align: "center",
          render: (_: any, record: any) => [
            <Button
              key="edit"
              type="link"
              size="small"
              onClick={() => handleDpmstFixAccEdit(record)}
              disabled={
                !access.hasEnvPerms(
                  localStorage.getItem("currentEnv") as string
                )
              }
            >
              编辑
            </Button>,
            <Dropdown
              key="more"
              menu={{
                items: [
                  {
                    key: "details",
                    label: "1-查询账户明细",
                    onClick: () => handleQueryDpdtlFixDetail(record),
                  },
                  {
                    key: "carryBalCalIntQuery",
                    label: "2-查询结转余额表",
                    onClick: () => handleQueryDprgtCarryBalCalInt(record),
                  },
                  {
                    key: "dprgtDepProveOpenQuery",
                    label: "3-查询存款证明开立信息登记",
                    onClick: () => handleQueryDprgtDepProveOpenInfo(record),
                  },
                ],
              }}
              trigger={["click"]}
            >
              <Button type="link" size="small">
                查询
                <MoreOutlined />
              </Button>
            </Dropdown>,
            <Button key="operation" type="link" size="small">
              操作
            </Button>,
          ],
        },
        {
          title: "分片值",
          dataIndex: "zoneVal",
          ellipsis: true,
          width: 120,
          align: "center",
        },
      ],
      pagination: {
        total: 0,
        pageSize: 10,
        current: 1,
      },
    },
    {
      key: "11",
      tab: "客户账户信息",
      data: [],
      columns: [
        {
          title: "所处分表",
          dataIndex: "shardingId",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "客户编号",
          dataIndex: "custNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "客户名称",
          dataIndex: "custNm",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "证件类型",
          dataIndex: "perCertTpCd",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) =>
            getPerCertTpCdText(record.perCertTpCd),
        },
        {
          title: "证件号码",
          dataIndex: "personalCertNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "证件有效期",
          dataIndex: "certValidDlineDate",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "正常卡数量",
          dataIndex: "ordinaryCardNumShee",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "特殊卡数量",
          dataIndex: "specCardNumShee",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "活期账户数",
          dataIndex: "curtAccnum",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "I类账户数",
          dataIndex: "iLaccNum",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "II类账户数",
          dataIndex: "iiLaccNum",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "III类账户数",
          dataIndex: "iiiLaccNum",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "手机号码",
          dataIndex: "mobileNo",
          ellipsis: true,
          width: 200,
          align: "center",
        },
        {
          title: "客户身份待核实标识",
          dataIndex: "custIdentityToVerifyFlag",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) =>
            getGeneralFlagText(record.custIdentityToVerifyFlag),
        },
        {
          title: "同一手机号多客户标识",
          dataIndex: "sameMobilenoMcustFlag",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) =>
            getGeneralFlagText(record.sameMobilenoMcustFlag),
        },
        {
          title: "九项信息不全标识",
          dataIndex: "nineItmInfoIncompFlag",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) =>
            getNineItemInfoIncompGlagText(record.nineItmInfoIncompFlag),
        },
        {
          title: "客户信息不符标识",
          dataIndex: "custInfoUnfitFlag",
          ellipsis: true,
          width: 200,
          align: "center",
          render: (_: any, record: any) =>
            getGeneralFlagText(record.custInfoUnfitFlag),
        },
        {
          title: "操作",
          valueType: "option",
          width: 160,
          fixed: "right",
          align: "center",
          render: (_: any, record: any) => [
            <Button
              key="edit"
              type="link"
              size="small"
              onClick={() => handleCustInfoEdit(record)}
              disabled={
                !access.hasEnvPerms(
                  localStorage.getItem("currentEnv") as string
                )
              }
            >
              编辑
            </Button>,
            <Dropdown
              key="more"
              menu={{
                items: [
                  {
                    key: "depProve",
                    label: "1-查询存款证明",
                    onClick: () => handleQueryTbDprgtDepProve(record),
                  },
                  {
                    key: "limitServQuery",
                    label: "2-查询客户限制服务登记",
                    onClick: () => handleQueryDprgtCustLimtServ(record),
                  },
                ],
              }}
              trigger={["click"]}
            >
              <Button type="link" size="small">
                查询
                <MoreOutlined />
              </Button>
            </Dropdown>,
            <Button key="operation" type="link" size="small">
              操作
            </Button>,
          ],
        },
      ],
    },
  ]);

  // 处理分页变化
  const handlePageChange = async (
    tabKey: string,
    current: number = 1,
    pageSize: number = 10
  ) => {
    const pageInfoKeys = {
      "6": "dprgtMaslaveContRelatPageInfo",
      "7": "dpRelmstCurtContDtoPageInfo",
      "8": "dpRelmstCurtAccDtoPageInfo",
      "9": "dpRelFixContDtoPageInfo",
      "10": "dpRelFixAccDtoPageInfo",
    };

    const pageInfoKey = pageInfoKeys[tabKey as keyof typeof pageInfoKeys];
    if (!pageInfoKey) {
      console.error("不支持的标签页:", tabKey);
      return;
    }

    // 确保分页参数合法
    const validCurrent = Math.max(1, current);
    const validPageSize = Math.max(1, Math.min(100, pageSize)); // 限制每页最大条数为100

    setLoading(true);
    try {
      // 对于分页表，使用主合约编号查询
      let mainContrNo = "";
      let zoneVal = "";
      if (
        tabKey === "6" ||
        tabKey === "7" ||
        tabKey === "8" ||
        tabKey === "9" ||
        tabKey === "10"
      ) {
        // 从介质主档表获取主合约编号
        console.log("tabsData:", tabsData);
        const dpmstMedium = tabsData.find((tab) => tab.key === "1");
        console.log("介质主档表数据:", dpmstMedium?.data);

        if (
          dpmstMedium?.data &&
          dpmstMedium.data.length > 0 &&
          dpmstMedium.data[0].mainContrNo &&
          dpmstMedium.data[0].zoneVal
        ) {
          mainContrNo = dpmstMedium.data[0].mainContrNo;
          zoneVal = dpmstMedium.data[0].zoneVal;
          console.log("获取到的主合约编号:", mainContrNo);
        }

        if (!mainContrNo) {
          message.error(
            "无法获取主合约编号，请确保活期产品合约表中包含产品合约编号"
          );
          return;
        }
      }

      console.log("分页查询参数:", {
        mainContrNo,
        zoneVal,
        pageInfoKey,
        current: validCurrent,
        pageSize: validPageSize,
      });

      let res;
      if (tabKey === "6") {
        // 主从合约关系表分页查询
        res = await queryDprgtMaslaveContRelatPageInfo(
          mainContrNo,
          zoneVal,
          pageInfoKey,
          {
            current: validCurrent,
            pageSize: validPageSize,
          }
        );
      } else if (tabKey === "7") {
        // 活期从合约主档表分页查询数据
        res = await queryDpRelmstCurtContDtoPageInfo(
          mainContrNo,
          zoneVal,
          pageInfoKey,
          {
            current: validCurrent,
            pageSize: validPageSize,
          }
        );
      } else if (tabKey === "8") {
        // 活期从账户主档表分页查询数据
        res = await queryDpRelmstCurtAccDtoPageInfo(
          mainContrNo,
          zoneVal,
          pageInfoKey,
          {
            current: validCurrent,
            pageSize: validPageSize,
          }
        );
      } else if (tabKey === "9") {
        // 定期从合约主档表分页查询数据
        res = await queryDpRelmstFixContDtoPageInfo(
          mainContrNo,
          zoneVal,
          pageInfoKey,
          {
            current: validCurrent,
            pageSize: validPageSize,
          }
        );
      } else if (tabKey === "10") {
        // 定期从账户主档表分页查询数据
        res = await queryDpRelmstFixAccDtoPageInfo(
          mainContrNo,
          zoneVal,
          pageInfoKey,
          {
            current: validCurrent,
            pageSize: validPageSize,
          }
        );
      }

      console.log("分页查询响应:", res);

      if (res.code === 200 && res.data) {
        const parsedData = JSON.parse(res.data);
        console.log("解析后的数据:", parsedData);
        const updatedTabsData = [...tabsData];
        const tabIndex = updatedTabsData.findIndex((tab) => tab.key === tabKey);

        if (tabIndex !== -1) {
          updatedTabsData[tabIndex] = {
            ...updatedTabsData[tabIndex],
            data: parsedData.records || [],
            pagination: {
              total: Math.max(0, parsedData.total || 0),
              pageSize: parsedData.size || validPageSize,
              current: parsedData.current || validCurrent,
              onChange: (page: number, size: number) =>
                handlePageChange(tabKey, page, size),
            },
          };
          setTabsData((prevTabsData) => {
            const newTabsData = [...prevTabsData];
            newTabsData[tabIndex] = updatedTabsData[tabIndex];
            return newTabsData;
          });
        }
      } else {
        message.error(res.msg || "获取分页数据失败");
      }
    } catch (error) {
      console.error("分页查询失败:", error);
      message.error("获取分页数据失败");
    } finally {
      setLoading(false);
    }
  };

  const onTabChange = (key: string) => {
    setActiveTab(key);
  };

  // 处理介质主档表编辑
  const handleMediumEdit = async (record: any) => {
    try {
      console.log("编辑按钮点击，传入参数:", {
        mediumNo: record.mediumNo,
        zoneVal: record.zoneVal,
      });

      const params: TbDpmstMedium = {
        mediumNo: record.mediumNo,
        zoneVal: record.zoneVal,
      };
      setMediumFormVisible(true);
      setFormLoading(true); // 设置加载状态为true

      const res = await getDpmstMedium(params);
      console.log("获取详情响应:", res);
      if (res.code === 200 && res.data) {
        try {
          const mediumData = JSON.parse(res.data);
          console.log("解析后的数据:", mediumData);
          setCurrentRow(mediumData);
        } catch (parseError) {
          message.error("解析数据失败");
        }
      } else {
        message.error(res.msg || "获取数据失败");
      }
      setFormLoading(false); // 数据加载完成，设置加载状态为
    } catch (error) {
      message.error("获取详情失败");
      setFormLoading(false); // 数据加载完成，设置加载状态为
    }
  };

  // 处理活期产品合约表编辑
  const handleDpmstCurtContEdit = async (record: any) => {
    try {
      console.log("编辑按钮点击，传入参数:", {
        prodtContractNo: record.prodtContractNo,
        zoneVal: record.zoneVal,
      });

      const params: TbDpmstCurtCont = {
        prodtContractNo: record.prodtContractNo,
        zoneVal: record.zoneVal,
      };
      setDpmstCurtContFormVisible(true);
      setFormLoading(true); // 设置加载状态为true

      const res = await getDpmstCurtCont(params);
      console.log("获取详情响应:", res);

      if (res.code === 200 && res.data) {
        try {
          const dpmstCurtContData = JSON.parse(res.data);
          console.log("解析后的数据:", dpmstCurtContData);
          setCurrentRow(dpmstCurtContData);
        } catch (parseError) {
          message.error("解析数据失败");
        }
      } else {
        message.error(res.msg || "获取数据失败");
      }
      setFormLoading(false); // 设置加载状态为true
    } catch (error) {
      message.error("获取详情失败");
      setFormLoading(false); // 设置加载状态为true
    }
  };

  // 处理活期产品合约账户表编辑
  const handleDpmstCurtAccEdit = async (record: any) => {
    try {
      console.log("编辑按钮点击，传入参数:", {
        persInnerAccno: record.persInnerAccno,
        zoneVal: record.zoneVal,
      });

      const params: TbDpmstCurtAcc = {
        persInnerAccno: record.persInnerAccno,
        zoneVal: record.zoneVal,
      };
      setDpmstCurtAccFormVisible(true);
      setFormLoading(true); // 设置加载状态为true

      const res = await getDpmstCurtAcc(params);
      console.log("获取详情响应:", res);

      if (res.code === 200 && res.data) {
        try {
          const dpmstCurtAccData = JSON.parse(res.data);
          console.log("解析后的数据:", dpmstCurtAccData);
          setCurrentRow(dpmstCurtAccData);
        } catch (parseError) {
          message.error("解析数据失败");
        }
      } else {
        message.error(res.msg || "获取数据失败");
      }
      setFormLoading(false); // 数据加载完成，设置加载状态为
    } catch (error) {
      message.error("获取详情失败");
      setFormLoading(false); // 数据加载完成，设置加载状态为
    }
  };

  // 处理定期产品合约表编辑
  const handleDpmstFixContEdit = async (record: any) => {
    try {
      console.log("编辑按钮点击，传入参数:", {
        prodtContractNo: record.prodtContractNo,
        zoneVal: record.zoneVal,
      });

      const params: TbDpmstFixCont = {
        prodtContractNo: record.prodtContractNo,
        zoneVal: record.zoneVal,
      };
      setDpmstFixContFormVisible(true);
      setFormLoading(true); // 设置加载状态为true

      const res = await getDpmstFixCont(params);
      console.log("获取详情响应:", res);

      if (res.code === 200 && res.data) {
        try {
          const dpmstFixContData = JSON.parse(res.data);
          console.log("解析后的数据:", dpmstFixContData);
          setCurrentRow(dpmstFixContData);
        } catch (parseError) {
          message.error("解析数据失败");
        }
      } else {
        message.error(res.msg || "获取数据失败");
      }
      setFormLoading(false); // 数据加载完成，设置加载状态为
    } catch (error) {
      message.error("获取详情失败");
      setFormLoading(false); // 数据加载完成，设置加载状态为
    }
  };

  // 处理定期产品合约账户表编辑
  const handleDpmstFixAccEdit = async (record: any) => {
    try {
      console.log("编辑按钮点击，传入参数:", {
        persInnerAccno: record.persInnerAccno,
        zoneVal: record.zoneVal,
      });

      const params: TbDpmstFixAcc = {
        persInnerAccno: record.persInnerAccno,
        zoneVal: record.zoneVal,
      };
      setDpmstFixAccFormVisible(true);
      setFormLoading(true); // 设置加载状态为true

      const res = await getDpmstFixAcc(params);
      console.log("获取详情响应:", res);

      if (res.code === 200 && res.data) {
        try {
          const dpmstFixAccData = JSON.parse(res.data);
          console.log("解析后的数据:", dpmstFixAccData);
          setCurrentRow(dpmstFixAccData);
        } catch (parseError) {
          message.error("解析数据失败");
        }
      } else {
        message.error(res.msg || "获取数据失败");
      }
      setFormLoading(false); // 数据加载完成，设置加载状态为false
    } catch (error) {
      message.error("获取详情失败");
      setFormLoading(false); // 数据加载完成，设置加载状态为false
    }
  };

  // 处理主从合约关系表编辑
  const handleDprgtMaslaveContRelatEdit = async (record: any) => {
    try {
      console.log("编辑按钮点击，传入参数:", {
        mainContrNo: record.mainContrNo,
        saccnoSeqNo: record.saccnoSeqNo,
        zoneVal: record.zoneVal,
      });

      const params: TbDprgtMaslaveContRelat = {
        mainContrNo: record.mainContrNo,
        saccnoSeqNo: record.saccnoSeqNo,
        zoneVal: record.zoneVal,
      };
      setDprgtMaslaveContRelatFormVisible(true);
      setFormLoading(true); // 设置加载状态为true

      const res = await getDprgtMaslaveContRelat(params);
      console.log("获取详情响应:", res);

      if (res.code === 200 && res.data) {
        try {
          const dprgtMaslaveContRelatData = JSON.parse(res.data);
          console.log("解析后的数据:", dprgtMaslaveContRelatData);
          setCurrentRow(dprgtMaslaveContRelatData);
        } catch (parseError) {
          message.error("解析数据失败");
        }
      } else {
        message.error(res.msg || "获取数据失败");
      }
      setFormLoading(false); // 数据加载完成，设置加载状态为false
    } catch (error) {
      message.error("获取详情失败");
      setFormLoading(false); // 数据加载完成，设置加载状态为false
    }
  };

  // 处理客户账户信息编辑
  const handleCustInfoEdit = async (record: any) => {
    try {
      console.log("编辑按钮点击，传入参数:", {
        custNo: record.custNo,
        perCertTpCd: record.perCertTpCd,
        personalCertNo: record.personalCertNo,
      });

      const params: CustInfo = {
        custNo: record.custNo,
        perCertTpCd: record.perCertTpCd,
        personalCertNo: record.personalCertNo,
      };
      setCustInfoFormVisible(true);
      setFormLoading(true); // 设置加载状态为true

      const res = await getCustInfo(params);
      console.log("获取详情响应:", res);

      if (res.code === 200 && res.data) {
        try {
          const custInfoData = JSON.parse(res.data);
          console.log("解析后的数据:", custInfoData);
          setCurrentRow(custInfoData);
        } catch (parseError) {
          message.error("解析数据失败");
        }
      } else {
        message.error(res.msg || "获取数据失败");
      }
      setFormLoading(false); // 数据加载完成，设置加载状态为
    } catch (error) {
      message.error("获取详情失败");
      setFormLoading(false); // 数据加载完成，设置加载状态为
    }
  };

  // 提交介质主档表数据修改
  const handleMediumFormSubmit = async (values: MediumFormValueType) => {
    try {
      setFormSubmitting(true); // 设置提交状态为true
      const res = await dpmstMediumUpdate(values);
      if (res.code === 200) {
        message.success("更新成功");
        setMediumFormVisible(false);
        setCurrentRow(undefined);
        refreshData();
      } else {
        message.error(res.msg || "更新失败");
      }
    } catch (error) {
      message.error("更新失败，请重试");
    } finally {
      setFormSubmitting(false); // 无论成功或失败，都需要关闭加载状态
    }
  };

  // 提交活期产品合约主档表信息
  const handleDpmstCurtContSubmit = async (
    values: DpmstCurtContFormValueType
  ) => {
    try {
      setFormSubmitting(true); // 设置提交状态为true
      const res = await dpmstCurtContUpdate(values);
      if (res.code === 200) {
        message.success("更新成功");
        setDpmstCurtContFormVisible(false);
        setCurrentRow(undefined);
        refreshData();
      } else {
        message.error(res.msg || "更新失败");
      }
    } catch (error) {
      message.error("更新失败，请重试");
    } finally {
      setFormSubmitting(false); // 无论成功或失败，都需要关闭提交状态
    }
  };

  // 提交活期产品合约账户主档表信息
  const handleDpmstCurtAccSubmit = async (
    values: DpmstCurtAccFormValueType
  ) => {
    try {
      setFormSubmitting(true); // 设置提交状态为true
      const res = await dpmstCurtAccUpdate(values);
      if (res.code === 200) {
        message.success("更新成功");
        setDpmstCurtAccFormVisible(false);
        setCurrentRow(undefined);
        refreshData();
      } else {
        message.error(res.msg || "更新失败");
      }
    } catch (error) {
      message.error("更新失败，请重试");
    } finally {
      setFormSubmitting(false); // 无论成功或失败，都需要关闭提交状态
    }
  };

  // 提交定期产品合约主档表信息
  const handleDpmstFixContSubmit = async (
    values: DpmstFixContFormValueType
  ) => {
    try {
      setFormSubmitting(true); // 设置提交状态为true
      const res = await dpmstFixContUpdate(values);
      if (res.code === 200) {
        message.success("更新成功");
        setDpmstFixContFormVisible(false);
        setCurrentRow(undefined);
        refreshData();
      } else {
        message.error(res.msg || "更新失败");
      }
    } catch (error) {
      message.error("更新失败，请重试");
    } finally {
      setFormSubmitting(false); // 无论成功或失败，都需要关闭提交状态
    }
  };

  // 提交定期产品合约账户主档表信息
  const handleDpmstFixAccSubmit = async (values: DpmstFixAccFormValueType) => {
    try {
      setFormSubmitting(true); // 设置提交状态为true
      const res = await dpmstFixAccUpdate(values);
      if (res.code === 200) {
        message.success("更新成功");
        setDpmstFixAccFormVisible(false);
        setCurrentRow(undefined);
        refreshData();
      } else {
        message.error(res.msg || "更新失败");
      }
    } catch (error) {
      message.error("更新失败，请重试");
    } finally {
      setFormSubmitting(false); // 无论成功或失败，都需要关闭提交状态
    }
  };

  // 提交主从合约关系表信息
  const handleDprgtMaslaveContRelatSubmit = async (
    values: DprgtMaslaveContRelatFormValueType
  ) => {
    try {
      setFormSubmitting(true); // 设置提交状态为true
      const res = await dprgtMaslaveContRelatUpdate(values);
      if (res.code === 200) {
        message.success("更新成功");
        setDprgtMaslaveContRelatFormVisible(false);
        setCurrentRow(undefined);
        refreshData();
      } else {
        message.error(res.msg || "更新失败");
      }
    } catch (error) {
      message.error("更新失败，请重试");
    } finally {
      setFormSubmitting(false); // 无论成功或失败，都需要关闭提交状态
    }
  };

  // 提交客户账户信息
  const handleCustInfoSubmit = async (values: CustInfoFormValueType) => {
    try {
      setFormSubmitting(true); // 设置提交状态为true
      const res = await custInfoUpdate(values);
      if (res.code === 200) {
        message.success("更新成功");
        setCustInfoFormVisible(false);
        setCurrentRow(undefined);
        refreshData();
      } else {
        message.error(res.msg || "更新失败");
      }
    } catch (error) {
      message.error("更新失败，请重试");
    } finally {
      setFormSubmitting(false); // 无论成功或失败，都需要关闭提交状态
    }
  };

  // 编辑关联介质类型代码-介质主档表
  const handleRelatedMediaEditSuccess = () => {
    setRelatedMediaModalVisible(false);
    setCurrentRow(undefined);
    refreshData();
  };

  // 编辑介质状态标志码-介质主档表
  const handleStatusFlagCdEditSuccess = () => {
    setStatusFlagCdVisible(false);
    setCurrentRow(undefined);
    refreshData();
  };

  // 编辑介质属性标志码-介质主档表
  const handleAttrbuteFlagCdEditSuccess = () => {
    setAttrbuteFlagCdVisible(false);
    setCurrentRow(undefined);
    refreshData();
  };

  // 编辑介质认证方式标志码-介质主档表
  const handleCrtModeFlagCdEditSuccess = () => {
    setCrtModeFlagCdVisible(false);
    setCurrentRow(undefined);
    refreshData();
  };

  // 编辑活期合约主档表-主合约控制标志码
  const handleCurtMContrFlagCdEditSuccess = () => {
    setCurtMContrFlagCdVisible(false);
    setCurrentRow(undefined);
    refreshData();
  };

  // 编辑定期合约主档表-主合约控制标志码
  const handleFixMContrFlagCdEditSuccess = () => {
    setFixMContrFlagCdVisible(false);
    setCurrentRow(undefined);
    refreshData();
  };

  // 编辑活期合约主档表-合约控制标志码
  const handleCurtContrCtrlFlagCdEditSuccess = () => {
    setCurtContrCtrlFlagCdVisible(false);
    setCurrentRow(undefined);
    refreshData();
  };

  // 编辑定期合约主档表-合约控制标志码
  const handleFixContrCtrlFlagCdEditSuccess = () => {
    setFixContrCtrlFlagCdVisible(false);
    setCurrentRow(undefined);
    refreshData();
  };

  // 编辑活期合约主档表-合约属性标志码
  const handleCurtContrAttrFgCdEditSuccess = () => {
    setCurtContrAttrFgCdVisible(false);
    setCurrentRow(undefined);
    refreshData();
  };

  // 编辑定期合约主档表-合约属性标志码
  const handleFixContrAttrFgCdEditSuccess = () => {
    setFixContrAttrFgCdVisible(false);
    setCurrentRow(undefined);
    refreshData();
  };

  // 编辑活期合约主档表-合约状态标志码
  const handleCurtContrStaFgCdEditSuccess = () => {
    setCurtContrStaFgCdVisible(false);
    setCurrentRow(undefined);
    refreshData();
  };

  // 编辑定期合约账户主档表-合约状态标志码
  const handleFixContrStaFgCdEditSuccess = () => {
    setFixAccStatusFlagCdVisible(false);
    setCurrentRow(undefined);
    refreshData();
  };

  // 编辑活期合约账户主档表-账户状态标志码
  const handleCurtAccStatusFlagCdEditSuccess = () => {
    setCurtAccStatusFlagCdVisible(false);
    setCurrentRow(undefined);
    refreshData();
  };

  // 编辑定期合约账户主档表-账户状态标志码
  const handleFixAccStatusFlagCdEditSuccess = () => {
    setFixAccStatusFlagCdVisible(false);
    setCurrentRow(undefined);
    refreshData();
  };

  // 编辑活期合约账户主档表-账户标志码
  const handleCurtAccFlagCdEditSuccess = () => {
    setCurtAccFlagCdVisible(false);
    setCurrentRow(undefined);
    refreshData();
  };

  // 编辑定期合约账户主档表-账户标志码
  const handleFixAccFlagCdEditSuccess = () => {
    setFixAccFlagCdVisible(false);
    setCurrentRow(undefined);
    refreshData();
  };

  // 主查询接口，获取介质对应的全部数据
  const handleSearch = async (mediumNo: string) => {
    setCurrentMediumNo(mediumNo);
    setLoading(true);
    try {
      // 保存搜索条件
      setSearchForm({ mediumNo });

      const res = await queryAll(mediumNo);
      if (res.code === 200) {
        storage.set("queryAll_search", { mediumNo }, 30 * 60);
      }
      console.log("查询响应:", res);

      if (res.code === 200 && res.data) {
        const parsedData = JSON.parse(res.data);
        const {
          dpmstMedium = {},
          dpmstCurtContDto = {},
          dpmstCurtAccDto = {},
          dpmstFixContDto = {},
          dpmstFixAccDto = {},
          dprgtMaslaveContRelatPageInfo = {
            records: [],
            total: 0,
            size: 10,
            current: 1,
          },
          dpRelmstCurtContDtoPageInfo = {
            records: [],
            total: 0,
            size: 10,
            current: 1,
          },
          dpRelmstCurtAccDtoPageInfo = {
            records: [],
            total: 0,
            size: 10,
            current: 1,
          },
          dpRelFixContDtoPageInfo = {
            records: [],
            total: 0,
            size: 10,
            current: 1,
          },
          dpRelFixAccDtoPageInfo = {
            records: [],
            total: 0,
            size: 10,
            current: 1,
          },
          custInfoDto = {},
        } = parsedData;

        // 在API调用成功后立刻保存到localStorage
        handleResponse(parsedData);

        const updatedTabsData = [...tabsData];

        // 更新非分页数据的tab
        updatedTabsData[0].data = dpmstMedium ? [dpmstMedium] : [];
        updatedTabsData[1].data = dpmstCurtContDto ? [dpmstCurtContDto] : [];
        updatedTabsData[2].data = dpmstCurtAccDto ? [dpmstCurtAccDto] : [];
        updatedTabsData[3].data = dpmstFixContDto ? [dpmstFixContDto] : [];
        updatedTabsData[4].data = dpmstFixAccDto ? [dpmstFixAccDto] : [];
        updatedTabsData[10].data = custInfoDto ? [custInfoDto] : []; // 更新客户信息tab

        // 更新分页数据的tab
        const pageInfos = [
          { index: 5, key: "6", data: dprgtMaslaveContRelatPageInfo },
          { index: 6, key: "7", data: dpRelmstCurtContDtoPageInfo },
          { index: 7, key: "8", data: dpRelmstCurtAccDtoPageInfo },
          { index: 8, key: "9", data: dpRelFixContDtoPageInfo },
          { index: 9, key: "10", data: dpRelFixAccDtoPageInfo },
        ];

        pageInfos.forEach(({ index, key, data }) => {
          if (data) {
            updatedTabsData[index] = {
              ...updatedTabsData[index],
              data: data.records || [],
              pagination: {
                total: data.total,
                pageSize: data.size,
                current: data.current,
                onChange: (page: number, size: number) =>
                  handlePageChange(key, page, size),
              },
            };
          }
        });
        setTabsData(updatedTabsData);
      } else {
        message.error(res.msg || "查询失败");
      }
    } catch (error) {
      message.error("未查询出数据，请检查介质号与环境是否正确");
    } finally {
      setLoading(false);
    }
  };

  const refreshData = () => {
    if (currentMediumNo) {
      handleSearch(currentMediumNo);
    }
  };

  // 查询活期明细
  const handleQueryDpdtlCurtDetail = async (record: any) => {
    try {
      let res;
      // 活期产品账户表
      if (!record || !record.persInnerAccno || !record.zoneVal) {
        message.error("无法获取账户信息");
        return;
      }
      setLoading(true);
      res = await getDpdtlCurtAcc(
        record.persInnerAccno,
        record.zoneVal,
        "dpdtlCurtAccPageInfo",
        {
          current: 1,
          pageSize: 10,
        }
      );
      if (res.code === 200 && res.data) {
        const detailData = JSON.parse(res.data);
        console.log("解析后的明细数据:", detailData);
        setDetailDpdtlCurtData(detailData);
        setDetailDpdtlCurtModalVisible(true);
      } else {
        message.error(res.msg || "获取明细失败");
      }
    } catch (error) {
      message.error("查询明细失败");
    } finally {
      setLoading(false);
    }
  };

  // 查询定期明细
  const handleQueryDpdtlFixDetail = async (record: any) => {
    try {
      let res;
      // 定期产品账户表
      if (!record || !record.persInnerAccno || !record.zoneVal) {
        message.error("无法获取账户信息");
        return;
      }
      setLoading(true);
      res = await getDpdtlFixAcc(
        record.persInnerAccno,
        record.zoneVal,
        "dpdtlFixAccPageInfo",
        {
          current: 1,
          pageSize: 10,
        }
      );
      if (res.code === 200 && res.data) {
        const detailData = JSON.parse(res.data);
        console.log("解析后的明细数据:", detailData);
        setDetailDpdtlFixData(detailData);
        setDetailDpdtlFixModalVisible(true);
      } else {
        message.error(res.msg || "获取明细失败");
      }
    } catch (error) {
      message.error("查询明细失败");
    } finally {
      setLoading(false);
    }
  };

  // 查询个人存款生命周期
  const handleQueryDprgtPersDepLifeCycDetail = async (record: any) => {
    try {
      let res;
      // 介质主档表
      if (!record || !record.mediumNo || !record.zoneVal) {
        message.error("无法获取个人存款生命周期");
        return;
      }
      setLoading(true);
      res = await getDprgtPersDepLifeCyc(
        record.mediumNo,
        record.zoneVal,
        "dpdtlFixAccPageInfo",
        {
          current: 1,
          pageSize: 10,
        }
      );
      if (res.code === 200 && res.data) {
        const detailData = JSON.parse(res.data);
        console.log("解析后的个人存款生命周期数据:", detailData);
        setDetailDprgtPersDepLifeCycData(detailData);
        setDetailDprgtPersDepLifeCycModalVisible(true);
      } else {
        message.error(res.msg || "获取个人存款生命周期失败");
      }
    } catch (error) {
      message.error("查询个人存款生命周期失败");
    } finally {
      setLoading(false);
    }
  };

  // 查询个人联名账户信息
  const handleQueryDprgtPersonJointAcc = async (record: any) => {
    try {
      let res;
      // 介质主档表
      if (!record || !record.mediumNo || !record.zoneVal) {
        message.error("无法获取个人联名账户信息记录表");
        return;
      }
      setLoading(true);
      res = await getDprgtPersonJointAcc(
        record.mediumNo,
        record.zoneVal,
        "dprgtPersonJointAccPageInfo",
        {
          current: 1,
          pageSize: 10,
        }
      );
      if (res.code === 200 && res.data) {
        const detailData = JSON.parse(res.data);
        console.log("解析后的个人联名账户信息记录表数据:", detailData);
        setDprgtPersonJointAccData(detailData);
        setDprgtPersonJointAccModalVisible(true);
      } else {
        message.error(res.msg || "获取个人联名账户信息记录表失败");
      }
    } catch (error) {
      message.error("查询个人联名账户信息记录表失败");
    } finally {
      setLoading(false);
    }
  };

  const [dprgtFrzRefresh, setDprgtFrzRefresh] = useState<number>(0);

  const handleQueryDprgtFrz = async (record: any) => {
    if (!record?.prodtContractNo) {
      message.error("合约号不能为空");
      return;
    }
    setLoading(true);
    try {
      const result = await getDprgtFrz(
        record.prodtContractNo,
        record.zoneVal || "",
        "dprgtFrzPageInfo",
        { current: 1, pageSize: 10 }
      );

      if (result && result.code === 200) {
        try {
          // 解析嵌套的JSON字符串
          const parsedData =
            typeof result.data === "string"
              ? JSON.parse(result.data)
              : result.data;

          // 设置解析后的数据
          setDprgtFrzData({
            ...parsedData,
            records: parsedData.records,
            total: parsedData.total,
            size: parsedData.size,
            current: parsedData.current,
          });
          setDprgtFrzRefresh((prev) => prev + 1);
          setDprgtFrzModalVisible(true);
        } catch (parseError) {
          console.error("Error parsing account star data:", parseError);
          message.error("解析冻结登记簿失败");
        }
      } else {
        message.error(result?.msg || "查询冻结登记簿失败");
      }
    } catch (error) {
      message.error("查询冻结登记簿失败");
      console.error("Error fetching account star data:", error);
    } finally {
      setLoading(false);
    }
  };

  // 查询止付登记簿
  const [dprgtStopPayRefresh, setDprgtStopPayRefresh] = useState<number>(0);
  const handleQueryDprgtStopPay = async (record: any) => {
    if (!record?.prodtContractNo) {
      message.error("合约号不能为空");
      return;
    }
    setLoading(true);
    try {
      const result = await getTbDprgtStopPay(
        record.prodtContractNo,
        record.zoneVal || "",
        "dprgtFrzPageInfo",
        { current: 1, pageSize: 10 }
      );

      if (result && result.code === 200) {
        try {
          // 解析嵌套的JSON字符串
          const parsedData =
            typeof result.data === "string"
              ? JSON.parse(result.data)
              : result.data;

          // 设置解析后的数据
          setDprgtStopPayData({
            ...parsedData,
            records: parsedData.records,
            total: parsedData.total,
            size: parsedData.size,
            current: parsedData.current,
          });
          setDprgtStopPayRefresh((prev) => prev + 1);
          setDprgtStopPayModalVisible(true);
        } catch (parseError) {
          console.error("Error parsing account star data:", parseError);
          message.error("解析止付登记簿失败");
        }
      } else {
        message.error(result?.msg || "查询止付登记簿失败");
      }
    } catch (error) {
      message.error("查询止付登记簿失败");
      console.error("Error fetching account star data:", error);
    } finally {
      setLoading(false);
    }
  };

  // 查询签约加办关系登记
  const handleQueryDprgtSignAdd = async (record: any) => {
    try {
      let res;
      // 介质主档表
      if (!record || !record.mediumNo || !record.zoneVal) {
        message.error("无法获取签约加办关系登记");
        return;
      }
      setLoading(true);
      res = await service.getTbDprgtSignAdd(
        record.mediumNo,
        record.zoneVal,
        "dprgtSignAddPageInfo",
        {
          current: 1,
          pageSize: 10,
        }
      );
      if (res.code === 200 && res.data) {
        const detailData = JSON.parse(res.data);
        console.log("解析后的签约加办关系登记数据:", detailData);
        setDprgtSignAddDataData(detailData);
        setDprgtSignAddModalVisible(true);
      } else {
        message.error(res.msg || "获取签约加办关系登记失败");
      }
    } catch (error) {
      console.log("查询签约加办关系登记失败", error);
      message.error("查询签约加办关系登记失败");
    } finally {
      setLoading(false);
    }
  };

  // 查询账户免收登记簿
  const handleQueryDprgtAccFree = async (record: any) => {
    try {
      let res;
      // 介质主档表
      if (!record || !record.persInnerAccno || !record.zoneVal) {
        message.error("无法获取账户免收登记簿");
        return;
      }
      setLoading(true);
      res = await service.getTbDprgtAccFree(
        record.persInnerAccno,
        record.zoneVal,
        "dprgtAccFreePageInfo",
        {
          current: 1,
          pageSize: 10,
        }
      );
      if (res.code === 200 && res.data) {
        const detailData = JSON.parse(res.data);
        console.log("解析后的账户免收登记簿数据:", detailData);
        setDprgtAccFreeData(detailData);
        setDprgtAccFreeModalVisible(true);
      } else {
        message.error(res.msg || "获取账户免收登记簿失败");
      }
    } catch (error) {
      message.error("查询账户免收登记簿失败");
    } finally {
      setLoading(false);
    }
  };

  // 查询无卡支付协议登记
  const handleQueryDprgtNoCardPay = async (record: any) => {
    try {
      let res;
      // 介质主档表
      if (!record || !record.mediumNo || !record.zoneVal) {
        message.error("无法获取无卡支付协议登记");
        return;
      }
      setLoading(true);
      res = await service.getDprgtNoCardPay(
        record.mediumNo,
        record.zoneVal,
        "dprgtNoCardPayPageInfo",
        {
          current: 1,
          pageSize: 10,
        }
      );
      if (res.code === 200 && res.data) {
        const detailData = JSON.parse(res.data);
        console.log("解析后的无卡支付协议登记数据:", detailData);
        setDprgtNoCardPayData(detailData);
        setDprgtNoCardPayFormModalVisible(true);
      } else {
        message.error(res.msg || "获取无卡支付协议登记失败");
      }
    } catch (error) {
      message.error("查询无卡支付协议登记失败");
    } finally {
      setLoading(false);
    }
  };

  // 查询换卡登记簿
  const handleQueryDprgtChgCard = async (record: any) => {
    if (!record || !record.prodtContractNo || !record.zoneVal) {
      message.error("缺少必要参数，无法查询换卡信息");
      return;
    }

    // 传递数据时将prodtContractNo映射为mainContrNo
    const initialData = {
      records: [
        {
          mainContrNo: record.prodtContractNo, // 关键是这里的映射
          zoneVal: record.zoneVal,
        },
      ],
      total: 0,
      size: 10,
      current: 1,
    };

    setDprgtChgCardData(initialData);
    setDprgtChgCardFormModalVisible(true);
  };

  // 查询待限制服务登记簿
  const handleQueryTobeLimtServ = async (record: any) => {
    try {
      // 检查合约信息
      if (record.prodtContractNo && record.zoneVal) {
        // 从合约信息查询待限制服务登记簿
        // 传递数据时将prodtContractNo映射为mainContrNo
        const initialData = {
          records: [
            {
              mainContrNo: record.prodtContractNo,
              zoneVal: record.zoneVal,
            },
          ],
          total: 0,
          size: 10,
          current: 1,
        };

        setTobeLimtServData(initialData);
        setTobeLimtServModalVisible(true);
        return;
      }

      // 从合约主档表查询
      if (!record || !record.mainContrNo || !record.zoneVal) {
        message.error("无法获取待限制服务登记簿数据，缺少必要参数");
        return;
      }

      setLoading(true);
      const res = await getTobeLimtServ(
        record.mainContrNo,
        record.zoneVal,
        "tobeLimtServPageInfo",
        {
          current: 1,
          pageSize: 10,
        }
      );
      if (res.code === 200 && res.data) {
        const detailData = JSON.parse(res.data);
        console.log("解析后的待限制服务登记簿数据:", detailData);
        setTobeLimtServData(detailData);
        setTobeLimtServModalVisible(true);
      } else {
        message.error(res.msg || "获取待限制服务登记簿失败");
      }
    } catch (error) {
      console.error("查询待限制服务登记簿失败:", error);
      message.error("查询待限制服务登记簿失败");
    } finally {
      setLoading(false);
    }
  };

  // 查询限制服务登记簿
  const handleQueryLimtServ = async (record: any) => {
    console.log("handleQueryLimtServ被调用，参数:", record);
    try {
      // 检查合约信息
      if (record.prodtContractNo && record.zoneVal) {
        console.log(
          "使用产品合约编号和分片值初始化数据:",
          record.prodtContractNo,
          record.zoneVal
        );

        setLoading(true);
        // 直接调用API获取数据，而不是只设置初始化数据
        const res = await getLimtServ(
          record.prodtContractNo,
          record.zoneVal,
          "limtServPageInfo",
          {
            current: 1,
            pageSize: 10,
          }
        );
        console.log("getLimtServ API响应:", res);
        if (res.code === 200 && res.data) {
          const detailData = JSON.parse(res.data);
          console.log("解析后的限制服务登记簿数据:", detailData);
          setLimtServData(detailData);
          setLimtServModalVisible(true);
        } else {
          // 如果API调用失败，仍然显示空的初始数据
          console.log("API调用失败，显示空的初始数据");
          const initialData = {
            records: [
              {
                mainContrNo: record.prodtContractNo,
                zoneVal: record.zoneVal,
              },
            ],
            total: 0,
            size: 10,
            current: 1,
          };
          console.log("设置初始化数据:", initialData);
          setLimtServData(initialData);
          setLimtServModalVisible(true);
          message.warning("获取限制服务登记簿数据为空，可手动添加记录");
        }
        setLoading(false);
        return;
      }

      // 从合约主档表查询
      if (!record || !record.mainContrNo || !record.zoneVal) {
        console.error("缺少必要参数:", record);
        message.error("无法获取限制服务登记簿数据，缺少必要参数");
        return;
      }

      console.log(
        "使用mainContrNo和zoneVal查询数据:",
        record.mainContrNo,
        record.zoneVal
      );
      setLoading(true);
      const res = await getLimtServ(
        record.mainContrNo,
        record.zoneVal,
        "limtServPageInfo",
        {
          current: 1,
          pageSize: 10,
        }
      );
      console.log("getLimtServ API响应:", res);
      if (res.code === 200 && res.data) {
        const detailData = JSON.parse(res.data);
        console.log("解析后的限制服务登记簿数据:", detailData);
        setLimtServData(detailData);
        setLimtServModalVisible(true);
      } else {
        message.error(res.msg || "获取限制服务登记簿失败");
      }
    } catch (error) {
      console.error("查询限制服务登记簿失败:", error);
      message.error("查询限制服务登记簿失败");
    } finally {
      setLoading(false);
    }
  };

  // 添加处理消费信息查询的方法
  const handleQueryConsum = async (record: any) => {
    try {
      console.log("查询消费信息:", record);
      // 使用一个默认的pageInfoKey
      const pageInfoKey = "consumPageInfo";

      // 检查是否有必要的参数
      if (!record) {
        message.error("无法获取消费信息数据，缺少必要参数");
        return;
      }

      // 检查合约信息 - 如果是从产品合约表点击过来的
      if (record.prodtContractNo && record.zoneVal) {
        console.log(
          "使用产品合约编号和分片值查询:",
          record.prodtContractNo,
          record.zoneVal
        );

        setLoading(true);
        try {
          const response = await getConsum(
            record.prodtContractNo, // 使用prodtContractNo作为mainContrNo
            record.zoneVal,
            pageInfoKey,
            { current: 1, pageSize: 10 }
          );

          if (response.code === 200 && response.data) {
            const parsedData =
              typeof response.data === "string"
                ? JSON.parse(response.data)
                : response.data;

            setConsumData(parsedData);
            setConsumModalVisible(true);
          } else {
            // 如果API调用失败，仍然显示空的初始数据，以便能够添加新记录
            console.log("API调用失败，显示空的初始数据");
            const initialData = {
              records: [
                {
                  mainContrNo: record.prodtContractNo,
                  zoneVal: record.zoneVal,
                },
              ],
              total: 0,
              size: 10,
              current: 1,
            };
            console.log("设置初始化数据:", initialData);
            setConsumData(initialData);
            setConsumModalVisible(true);
            message.warning("获取消费信息数据为空，可手动添加记录");
          }
        } catch (error) {
          console.error("查询消费信息出错:", error);
          // 出错时也显示空的初始数据，以便能够添加新记录
          const initialData = {
            records: [
              {
                mainContrNo: record.prodtContractNo,
                zoneVal: record.zoneVal,
              },
            ],
            total: 0,
            size: 10,
            current: 1,
          };
          setConsumData(initialData);
          setConsumModalVisible(true);
          message.warning("获取消费信息失败，可手动添加记录");
        } finally {
          setLoading(false);
        }
        return;
      }

      // 从合约主档表查询 - 如果是从合约主档表点击过来的
      if (record.mainContrNo && record.zoneVal) {
        console.log(
          "使用主合约编号和分片值查询:",
          record.mainContrNo,
          record.zoneVal
        );

        setLoading(true);
        try {
          const response = await getConsum(
            record.mainContrNo,
            record.zoneVal,
            pageInfoKey,
            { current: 1, pageSize: 10 }
          );

          if (response.code === 200 && response.data) {
            const parsedData =
              typeof response.data === "string"
                ? JSON.parse(response.data)
                : response.data;

            setConsumData(parsedData);
            setConsumModalVisible(true);
          } else {
            message.error(response.msg || "获取消费信息失败");
          }
        } catch (error) {
          console.error("查询消费信息出错:", error);
          message.error("查询消费信息失败");
        } finally {
          setLoading(false);
        }
        return;
      }

      // 如果既没有prodtContractNo也没有mainContrNo
      message.error("无法获取消费信息数据，缺少合约编号或分片值");
    } catch (error) {
      console.error("查询消费信息出错:", error);
      message.error("查询消费信息失败");
    }
  };

  // 添加消费登记簿模态框相关状态
  const [consumModalVisible, setConsumModalVisible] = useState<boolean>(false);
  const [consumData, setConsumData] = useState<PageResponse<Consum> | null>(
    null
  );

  // 在state变量声明区域添加销退登记簿相关的状态
  const [saleRetModalVisible, setSaleRetModalVisible] = useState(false);
  const [saleRetData, setSaleRetData] = useState<any>(null);

  // 添加查询退货登记簿函数
  const handleQuerySaleRet = async (record: any) => {
    console.log("handleQuerySaleRet被调用，参数:", record);

    // 使用一个默认的pageInfoKey
    const pageInfoKey = "SALE_RET_ALL_QUERYPAGES";

    // 检查是否有必要的参数
    if (!record) {
      message.error("无法获取退货登记簿数据，缺少必要参数");
      return;
    }

    // 检查合约信息 - 如果是从产品合约表点击过来的
    if (record.prodtContractNo && record.zoneVal) {
      console.log(
        "使用产品合约编号和分片值查询:",
        record.prodtContractNo,
        record.zoneVal
      );

      setLoading(true);
      try {
        const res = await getSaleRet(
          record.prodtContractNo, // 使用prodtContractNo作为mainContrNo
          record.zoneVal,
          pageInfoKey,
          { current: 1, pageSize: 10 }
        );

        console.log("getSaleRet API响应:", res);
        if (res.code === 200 && res.data) {
          const detailData = JSON.parse(res.data);
          console.log("解析后的退货登记簿数据:", detailData);
          setSaleRetData(detailData);
          setSaleRetModalVisible(true);
        } else {
          // 如果API调用失败，仍然显示空的初始数据，以便能够添加新记录
          console.log("API调用失败，显示空的初始数据");
          const initialData = {
            records: [
              {
                mainContrNo: record.prodtContractNo,
                zoneVal: record.zoneVal,
              },
            ],
            total: 0,
            size: 10,
            current: 1,
          };
          console.log("设置初始化数据:", initialData);
          setSaleRetData(initialData);
          setSaleRetModalVisible(true);
          message.warning("获取退货登记簿数据为空，可手动添加记录");
        }
      } catch (error) {
        console.error("查询退货登记簿出错:", error);
        // 出错时也显示空的初始数据，以便能够添加新记录
        const initialData = {
          records: [
            {
              mainContrNo: record.prodtContractNo,
              zoneVal: record.zoneVal,
            },
          ],
          total: 0,
          size: 10,
          current: 1,
        };
        setSaleRetData(initialData);
        setSaleRetModalVisible(true);
        message.warning("获取退货登记簿失败，可手动添加记录");
      } finally {
        setLoading(false);
      }
      return;
    }

    // 从合约主档表查询 - 如果是从合约主档表点击过来的
    if (record.mainContrNo && record.zoneVal) {
      console.log(
        "使用主合约编号和分片值查询:",
        record.mainContrNo,
        record.zoneVal
      );

      setLoading(true);
      try {
        const res = await getSaleRet(
          record.mainContrNo,
          record.zoneVal,
          pageInfoKey,
          {
            current: 1,
            pageSize: 10,
          }
        );

        console.log("getSaleRet API响应:", res);
        if (res.code === 200 && res.data) {
          const detailData = JSON.parse(res.data);
          console.log("解析后的退货登记簿数据:", detailData);
          setSaleRetData(detailData);
          setSaleRetModalVisible(true);
        } else {
          message.error(res.msg || "获取退货登记簿失败");
        }
      } catch (error) {
        console.error("查询退货登记簿失败:", error);
        message.error("查询退货登记簿失败");
      } finally {
        setLoading(false);
      }
      return;
    }

    // 如果既没有prodtContractNo也没有mainContrNo
    message.error("无法获取退货登记簿数据，缺少合约编号或分片值");
  };

  // 添加账户升降级登记簿相关的状态
  const [accUpdownModalVisible, setAccUpdownModalVisible] = useState(false);
  const [accUpdownData, setAccUpdownData] = useState<any>(null);

  // 添加查询账户升降级登记簿方法
  const handleQueryAccUpdown = async (record: any) => {
    console.log("handleQueryAccUpdown被调用，参数:", record);

    // 使用一个默认的pageInfoKey
    const pageInfoKey = "ACC_UPDOWN_ALL_QUERYPAGES";

    // 检查是否有必要的参数
    if (!record) {
      message.error("无法获取账户升降级登记簿数据，缺少必要参数");
      return;
    }

    // 避免重复处理相同的请求
    if (accUpdownModalVisible) {
      console.log("模态框已显示，避免重复请求");
      return;
    }

    setLoading(true);

    try {
      let contractNo, zoneValue;

      // 检查合约信息 - 优先使用prodtContractNo
      if (record.prodtContractNo && record.zoneVal) {
        console.log(
          "使用产品合约编号和分片值查询:",
          record.prodtContractNo,
          record.zoneVal
        );
        contractNo = record.prodtContractNo;
        zoneValue = record.zoneVal;
      }
      // 其次使用mainContrNo
      else if (record.mainContrNo && record.zoneVal) {
        console.log(
          "使用主合约编号和分片值查询:",
          record.mainContrNo,
          record.zoneVal
        );
        contractNo = record.mainContrNo;
        zoneValue = record.zoneVal;
      }
      // 如果都没有，则提示错误
      else {
        message.error("无法获取账户升降级登记簿数据，缺少合约编号或分片值");
        setLoading(false);
        return;
      }

      // 统一处理API调用
      const res = await getAccUpdown(contractNo, zoneValue, pageInfoKey, {
        current: 1,
        pageSize: 10,
      });

      console.log("getAccUpdown API响应:", res);
      if (res.code === 200 && res.data) {
        const detailData = JSON.parse(res.data);
        console.log("解析后的账户升降级登记簿数据:", detailData);
        setAccUpdownData(detailData);
        setAccUpdownModalVisible(true);
      } else {
        // 如果API调用失败，仍然显示空的初始数据，以便能够添加新记录
        console.log("API调用失败，显示空的初始数据");
        const initialData = {
          records: [
            {
              prodtContractNo: contractNo,
              zoneVal: zoneValue,
            },
          ],
          total: 0,
          size: 10,
          current: 1,
        };
        console.log("设置初始化数据:", initialData);
        setAccUpdownData(initialData);
        setAccUpdownModalVisible(true);
        message.warning("获取账户升降级登记簿数据为空，可手动添加记录");
      }
    } catch (error) {
      console.error("查询账户升降级登记簿出错:", error);
      // 出错时也显示空的初始数据，以便能够添加新记录
      const initialData = {
        records: [
          {
            prodtContractNo: record.prodtContractNo || record.mainContrNo,
            zoneVal: record.zoneVal,
          },
        ],
        total: 0,
        size: 10,
        current: 1,
      };
      setAccUpdownData(initialData);
      setAccUpdownModalVisible(true);
      message.warning("获取账户升降级登记簿失败，可手动添加记录");
    } finally {
      setLoading(false);
    }
  };

  // 在state中添加合约辅助表模态框控制变量
  const [dprgtContrAsstModalVisible, setDprgtContrAsstModalVisible] =
    useState(false);
  const [dprgtContrAsstData, setDprgtContrAsstData] =
    useState<PageResponse<DprgtContrAsst> | null>(null);

  // 添加查询合约辅助表的处理函数
  const handleQueryDprgtContrAsst = async (record: any) => {
    let prodtContractNo = record.prodtContractNo;
    let zoneVal = record.zoneVal || "";

    // 如果记录中没有产品合约编号，尝试从其他字段获取
    if (!prodtContractNo) {
      if (record.mainContrNo) {
        prodtContractNo = record.mainContrNo;
      } else if (currentSearchData?.dpmstCurtContDto?.prodtContractNo) {
        prodtContractNo = currentSearchData.dpmstCurtContDto.prodtContractNo;
      } else if (currentSearchData?.dpmstFixContDto?.prodtContractNo) {
        prodtContractNo = currentSearchData.dpmstFixContDto.prodtContractNo;
      }
    }

    // 确保有分片值
    if (!zoneVal && currentSearchData?.dpmstMedium?.zoneVal) {
      zoneVal = currentSearchData.dpmstMedium.zoneVal;
    }

    if (!prodtContractNo) {
      message.error("缺少合约编号，无法查询");
      return;
    }

    setLoading(true);
    try {
      const result = await getDprgtContrAsst(
        prodtContractNo,
        zoneVal,
        "DPRGT_CONTR_ASST_ALL_QUERYPAGES",
        {
          current: 1,
          pageSize: 10,
        }
      );

      if (result && result.data) {
        // 解析返回的数据
        const detailData = JSON.parse(result.data);
        setDprgtContrAsstData(detailData);
        setDprgtContrAsstModalVisible(true);
      } else {
        // API调用失败或无数据
        setDprgtContrAsstData(null);
        setDprgtContrAsstModalVisible(true);
        message.warning("获取合约辅助表数据失败，可手动添加记录");
      }
    } catch (error) {
      console.error("获取合约辅助表数据出错:", error);
      setDprgtContrAsstData(null);
      setDprgtContrAsstModalVisible(true);
      message.warning("获取合约辅助表数据失败，可手动添加记录");
    } finally {
      setLoading(false);
    }
  };

  // 圈存加办信息模态框状态
  const [dprgtCflAddoffInfoModalVisible, setDprgtCflAddoffInfoModalVisible] =
    useState(false);
  const [dprgtCflAddoffInfoData, setDprgtCflAddoffInfoData] =
    useState<PageResponse<DprgtCflAddoffInfo> | null>(null);

  // 查询圈存加办信息
  const handleQueryDprgtCflAddoffInfo = async (record: any) => {
    if (!record?.prodtContractNo) {
      message.error("缺少合约编号，无法查询");
      return;
    }

    setLoading(true);
    try {
      const result = await getDprgtCflAddoffInfo(
        record.prodtContractNo,
        record.zoneVal || "",
        "DPRGT_CFL_ADDOFF_INFO_ALL_QUERYPAGES",
        {
          current: 1,
          pageSize: 10,
        }
      );

      if (result && result.data) {
        // 解析返回的数据
        const detailData = JSON.parse(result.data);
        setDprgtCflAddoffInfoData(detailData);
        setDprgtCflAddoffInfoModalVisible(true);
      } else {
        // API调用失败或无数据
        setDprgtCflAddoffInfoData(null);
        setDprgtCflAddoffInfoModalVisible(true);
        message.warning("获取圈存加办信息数据失败，可手动添加记录");
      }
    } catch (error) {
      console.error("获取圈存加办信息数据出错:", error);
      setDprgtCflAddoffInfoData(null);
      setDprgtCflAddoffInfoModalVisible(true);
      message.warning("获取圈存加办信息数据失败，可手动添加记录");
    } finally {
      setLoading(false);
    }
  };

  // Add this near the other state declarations
  const [currentSearchData, setCurrentSearchData] =
    useState<QueryAllData | null>(null);

  // Add this after other similar state variables
  const [accBindRelatData, setAccBindRelatData] = useState<any>(null);
  const [DprgtAccBindRelatFormVisible, setDprgtAccBindRelatFormVisible] =
    useState(false);

  // Add this function with other similar handler functions
  const handleQueryDprgtAccBindRelat = async (record: any) => {
    try {
      // 先显示模态框并设置加载状态
      setAccBindRelatData({
        records: [],
        total: 0,
        size: 10,
        current: 1,
        loading: true,
      });
      setDprgtAccBindRelatFormVisible(true);

      // 然后请求数据
      const result = await getDprgtAccBindRelat(
        record.prodtContractNo,
        record.zoneVal,
        "DPRGT_ACC_BIND_RELAT_ALL_QUERYPAGES",
        {
          current: 1,
          pageSize: 10,
        }
      );

      if (result) {
        const parsedData = JSON.parse(result.data);
        // 设置数据并关闭加载状态
        setAccBindRelatData({ ...parsedData, loading: false });
      } else {
        // 如果没有数据，关闭加载状态
        setAccBindRelatData({
          records: [],
          total: 0,
          size: 10,
          current: 1,
          loading: false,
        });
      }
    } catch (error) {
      console.error("获取账户绑定关系信息失败:", error);
      message.error("获取账户绑定关系信息失败");
      // 出错时也要关闭加载状态
      setAccBindRelatData({
        records: [],
        total: 0,
        size: 10,
        current: 1,
        loading: false,
      });
    }
  };

  const [
    dpdtlFundsUsageControlModalVisible,
    setDpdtlFundsUsageControlModalVisible,
  ] = useState(false);
  const [fundsUsageControlData, setFundsUsageControlData] = useState<any>(null);

  // 查询资金管控明细表
  const handleQueryDpdtlFundsUsageControl = async (record: any) => {
    try {
      // 先显示模态框并设置加载状态
      setFundsUsageControlData({
        records: [],
        total: 0,
        size: 10,
        current: 1,
        loading: true,
      });
      setDpdtlFundsUsageControlModalVisible(true);

      // 然后请求数据
      const result = await getDpdtlFundsUsageControl(
        record.persInnerAccno,
        record.zoneVal,
        "DPDTL_FUNDS_USAGE_CONTROL_ALL_QUERYPAGES",
        {
          current: 1,
          pageSize: 10,
        }
      );

      if (result) {
        const parsedData = JSON.parse(result.data);
        // 设置数据并关闭加载状态
        setFundsUsageControlData({ ...parsedData, loading: false });
      } else {
        // 如果没有数据，关闭加载状态
        setFundsUsageControlData({
          records: [],
          total: 0,
          size: 10,
          current: 1,
          loading: false,
        });
      }
    } catch (error) {
      console.error("获取资金用途管控明细信息失败:", error);
      message.error("获取资金用途管控明细信息失败");
      // 出错时也要关闭加载状态
      setFundsUsageControlData({
        records: [],
        total: 0,
        size: 10,
        current: 1,
        loading: false,
      });
    }
  };

  // Add state for controlling the modal visibility
  const [dprgtFundsUsageControlVisible, setDprgtFundsUsageControlVisible] =
    useState<boolean>(false);
  const [dprgtFundsUsageControlData, setDprgtFundsUsageControlData] =
    useState<PageResponse<TbDprgtFundsUsageControl> | null>(null);

  // 查询资金管控配置表
  const handleQueryDprgtFundsUsageControl = async (record: any) => {
    if (!record.persInnerAccno) {
      message.error("个人内部账号为空，无法查询资金用途管控表数据");
      return;
    }

    setDprgtFundsUsageControlData({
      records: [],
      total: 0,
      size: 10,
      current: 1,
      loading: true,
    });
    setDprgtFundsUsageControlVisible(true);

    try {
      const res = await getDprgtFundsUsageControl(
        record.persInnerAccno,
        record.zoneVal,
        "DPRGT_FUNDS_USAGE_CONTROL_ALL_QUERYPAGES",
        {
          current: 1,
          pageSize: 10,
        }
      );

      if (res.code === 200 && res.data) {
        try {
          const parsedData = JSON.parse(res.data);
          setDprgtFundsUsageControlData({ ...parsedData, loading: false });
        } catch (parseError) {
          message.error("解析数据失败");
          setDprgtFundsUsageControlData({
            records: [],
            total: 0,
            size: 10,
            current: 1,
            loading: false,
          });
        }
      } else {
        message.error(res.msg || "获取资金用途管控表数据失败");
        setDprgtFundsUsageControlData({
          records: [],
          total: 0,
          size: 10,
          current: 1,
          loading: false,
        });
      }
    } catch (error) {
      message.error("获取数据失败");
      setDprgtFundsUsageControlData({
        records: [],
        total: 0,
        size: 10,
        current: 1,
        loading: false,
      });
    }
  };

  // 资金用途管控借据表状态变量
  const [dprgtFundsUsageControlLoanData, setDprgtFundsUsageControlLoanData] =
    useState<any>(null);
  const [
    dprgtFundsUsageControlLoanVisible,
    setDprgtFundsUsageControlLoanVisible,
  ] = useState(false);

  // 查询资金用途管控借据表
  const handleQueryDprgtFundsUsageControlLoan = async (record: any) => {
    if (!record.persInnerAccno) {
      message.error("个人内部账号为空，无法查询资金用途管控借据表数据");
      return;
    }

    setDprgtFundsUsageControlLoanData({
      records: [],
      total: 0,
      size: 10,
      current: 1,
      loading: true,
    });
    setDprgtFundsUsageControlLoanVisible(true);

    try {
      const res = await getDprgtFundsUsageControlLoan(
        record.persInnerAccno,
        record.zoneVal,
        "DPRGT_FUNDS_USAGE_CONTROL_LOAN_ALL_QUERYPAGES",
        {
          current: 1,
          pageSize: 10,
        }
      );

      if (res.code === 200 && res.data) {
        try {
          const parsedData = JSON.parse(res.data);
          setDprgtFundsUsageControlLoanData({ ...parsedData, loading: false });
        } catch (parseError) {
          message.error("解析数据失败");
          setDprgtFundsUsageControlLoanData({
            records: [],
            total: 0,
            size: 10,
            current: 1,
            loading: false,
          });
        }
      } else {
        message.error(res.msg || "获取资金用途管控借据表数据失败");
        setDprgtFundsUsageControlLoanData({
          records: [],
          total: 0,
          size: 10,
          current: 1,
          loading: false,
        });
      }
    } catch (error) {
      message.error("获取数据失败");
      setDprgtFundsUsageControlLoanData({
        records: [],
        total: 0,
        size: 10,
        current: 1,
        loading: false,
      });
    }
  };

  const [
    dpdtlFundsUsageControlLoanModalVisible,
    setDpdtlFundsUsageControlLoanModalVisible,
  ] = useState(false);
  const [dpdtlFundsUsageControlLoanData, setDpdtlFundsUsageControlLoanData] = useState<any>(null);

  // 查询资金管控借据明细表
  const handleQueryDpdtlFundsUsageControlLoan = async (record: any) => {
    try {
      // 先显示模态框并设置加载状态
      setDpdtlFundsUsageControlLoanData({
        records: [],
        total: 0,
        size: 10,
        current: 1,
        loading: true,
      });
      setDpdtlFundsUsageControlModalVisible(true);

      // 然后请求数据
      const result = await getDpdtlFundsUsageControlLoan(
        record.persInnerAccno,
        record.zoneVal,
        "DPDTL_FUNDS_USAGE_CONTROL_ALL_QUERYPAGES",
        {
          current: 1,
          pageSize: 10,
        }
      );

      if (result) {
        const parsedData = JSON.parse(result.data);
        // 设置数据并关闭加载状态
        setDpdtlFundsUsageControlLoanData({ ...parsedData, loading: false });
      } else {
        // 如果没有数据，关闭加载状态
        setDpdtlFundsUsageControlLoanData({
          records: [],
          total: 0,
          size: 10,
          current: 1,
          loading: false,
        });
      }
    } catch (error) {
      console.error("获取资金用途管控借据明细信息失败:", error);
      message.error("获取资金用途管控借据明细信息失败");
      // 出错时也要关闭加载状态
      setDpdtlFundsUsageControlLoanData({
        records: [],
        total: 0,
        size: 10,
        current: 1,
        loading: false,
      });
    }
  };

  const [accStarModalVisible, setAccStarModalVisible] =
    useState<boolean>(false);
  const [accStarData, setAccStarData] =
    useState<PageResponse<DprgtAccStar> | null>(null);
  const [accStarRefresh, setAccStarRefresh] = useState<number>(0);

  // 查询账户星级登记簿
  const handleQueryDprgtAccStar = async (record: any) => {
    if (!record?.persInnerAccno) {
      message.error("个人内部账号不能为空");
      return;
    }
    setLoading(true);
    try {
      const result = await getDprgtAccStar(
        record.persInnerAccno,
        record.zoneVal || "",
        "dprgtAccStarPageInfo",
        { current: 1, pageSize: 10 }
      );

      if (result && result.code === 200) {
        try {
          // 解析嵌套的JSON字符串
          const parsedData =
            typeof result.data === "string"
              ? JSON.parse(result.data)
              : result.data;

          // 设置解析后的数据
          setAccStarData({
            ...parsedData,
            records: parsedData.records,
            total: parsedData.total,
            size: parsedData.size,
            current: parsedData.current,
          });
          setAccStarRefresh((prev) => prev + 1);
          setAccStarModalVisible(true);
        } catch (parseError) {
          console.error("Error parsing account star data:", parseError);
          message.error("解析账户星级数据失败");
        }
      } else {
        message.error(result?.msg || "查询账户星级登记簿失败");
      }
    } catch (error) {
      message.error("查询账户星级登记簿失败");
      console.error("Error fetching account star data:", error);
    } finally {
      setLoading(false);
    }
  };

  // 添加结转余额表相关状态
  const [carryBalCalIntModalVisible, setCarryBalCalIntModalVisible] =
    useState<boolean>(false);
  const [carryBalCalIntData, setCarryBalCalIntData] =
    useState<PageResponse<DprgtCarryBalCalInt> | null>(null);
  const [carryBalCalIntRefresh, setCarryBalCalIntRefresh] = useState<number>(0);

  // 查询活期产品合约账户结转余额表
  const handleQueryDprgtCarryBalCalInt = async (record: any) => {
    if (!record?.persInnerAccno) {
      message.error("个人内部账号不能为空");
      return;
    }
    setLoading(true);
    try {
      const result = await getDprgtCarryBalCalInt(
        record.persInnerAccno,
        record.zoneVal || "",
        "dprgtCarryBalCalIntPageInfo",
        { current: 1, pageSize: 10 }
      );

      if (result && result.code === 200) {
        try {
          // 解析嵌套的JSON字符串
          const parsedData =
            typeof result.data === "string"
              ? JSON.parse(result.data)
              : result.data;

          // 设置解析后的数据
          setCarryBalCalIntData({
            ...parsedData,
            records: parsedData.records,
            total: parsedData.total,
            size: parsedData.size,
            current: parsedData.current,
          });
          setCarryBalCalIntRefresh((prev) => prev + 1);
          setCarryBalCalIntModalVisible(true);
        } catch (error) {
          console.error("解析数据失败:", error);
          message.error("解析数据失败");
        }
      } else {
        message.error(result?.msg || "获取数据失败");
      }
    } catch (error) {
      console.error("获取数据失败:", error);
      message.error("获取数据失败");
    } finally {
      setLoading(false);
    }
  };

  // 添加存款证明相关状态
  const [dprgtDepProveModalVisible, setDprgtDepProveModalVisible] =
    useState<boolean>(false);
  const [dprgtDepProveData, setDprgtDepProveData] =
    useState<PageResponse<TbDprgtDepProve> | null>(null);
  const [dprgtDepProveRefresh, setDprgtDepProveRefresh] = useState<number>(0);

  // 查询存款证明
  const handleQueryTbDprgtDepProve = async (record: any) => {
    if (!record?.custNo) {
      message.error("客户号不能为空");
      return;
    }
    setLoading(true);
    try {
      const result = await getDprgtDepProve(
        record.custNo,
        record.custNo || "",
        "dprgtDepProvePageInfo",
        { current: 1, pageSize: 10 }
      );

      if (result && result.code === 200) {
        try {
          // 解析嵌套的JSON字符串
          const parsedData =
            typeof result.data === "string"
              ? JSON.parse(result.data)
              : result.data;

          // 设置解析后的数据
          setDprgtDepProveData({
            ...parsedData,
            records: parsedData.records,
            total: parsedData.total,
            size: parsedData.size,
            current: parsedData.current,
          });
          setDprgtDepProveRefresh((prev) => prev + 1);
          setDprgtDepProveModalVisible(true);
        } catch (error) {
          console.error("解析数据失败:", error);
          message.error("解析数据失败");
        }
      } else {
        message.error(result?.msg || "获取数据失败");
      }
    } catch (error) {
      console.error("获取数据失败:", error);
      message.error("获取数据失败");
    } finally {
      setLoading(false);
    }
  };

  // 添加存款证明相关状态
  const [dprgtCustLimtServModalVisible, setDprgtCustLimtServModalVisible] =
    useState<boolean>(false);
  const [dprgtCustLimtServData, setDprgtCustLimtServData] =
    useState<PageResponse<TbDprgtDepProve> | null>(null);
  const [dprgtCustLimtServRefresh, setDprgtCustLimtServRefresh] = useState<number>(0);

  const handleQueryDprgtCustLimtServ = async (record: any) => {
    if (!record?.custNo) {
      message.error("客户号不能为空");
      return;
    }
    setLoading(true);
    try {
      const result = await getDprgtCustLimtServ(
        record.custNo,
        record.custNo || "",
        "dprgtCustLimtServPageInfo",
        { current: 1, pageSize: 10 }
      );

      if (result && result.code === 200) {
        try {
          // 解析嵌套的JSON字符串
          const parsedData =
            typeof result.data === "string"
              ? JSON.parse(result.data)
              : result.data;

          // 设置解析后的数据
          setDprgtCustLimtServData({
            ...parsedData,
            records: parsedData.records,
            total: parsedData.total,
            size: parsedData.size,
            current: parsedData.current,
          });
          setDprgtCustLimtServRefresh((prev) => prev + 1);
          setDprgtCustLimtServModalVisible(true);
        } catch (error) {
          console.error("解析数据失败:", error);
          message.error("解析数据失败");
        }
      } else {
        message.error(result?.msg || "获取数据失败");
      }
    } catch (error) {
      console.error("获取数据失败:", error);
      message.error("获取数据失败");
    } finally {
      setLoading(false);
    }
  };

  // 添加存款证明开立信息登记相关状态
  const [dprgtDepProveOpenInfoModalVisible, setDprgtDepProveOpenInfoModalVisible] =
    useState<boolean>(false);
  const [dprgtDepProveOpenInfoData, setDprgtDepProveOpenInfoData] =
    useState<PageResponse<TbDprgtDepProve> | null>(null);
  const [dprgtDepProveOpenInfoRefresh, setDprgtDepProveOpenInfoRefresh] = useState<number>(0);

  const handleQueryDprgtDepProveOpenInfo = async (record: any) => {
    if (!record?.persInnerAccno) {
      message.error("个人内部账号不能为空");
      return;
    }
    setLoading(true);
    try {
      const result = await queryTbDprgtDepProveOpenInfo(
        record.persInnerAccno,
        record.custNo || "",
        "dprgtDepProveOpenInfoPageInfo",
        { current: 1, pageSize: 10 }
      );

      if (result && result.code === 200) {
        try {
          // 解析嵌套的JSON字符串
          const parsedData =
            typeof result.data === "string"
              ? JSON.parse(result.data)
              : result.data;

          // 设置解析后的数据
          setDprgtDepProveOpenInfoData({
            ...parsedData,
            records: parsedData.records,
            total: parsedData.total,
            size: parsedData.size,
            current: parsedData.current,
          });
          setDprgtDepProveOpenInfoRefresh((prev) => prev + 1);
          setDprgtDepProveOpenInfoModalVisible(true);
        } catch (error) {
          console.error("解析数据失败:", error);
          message.error("解析数据失败");
        }
      } else {
        message.error(result?.msg || "获取数据失败");
      }
    } catch (error) {
      console.error("获取数据失败:", error);
      message.error("获取数据失败");
    } finally {
      setLoading(false);
    }
  };


  return (
    <KeepAlive
      saveScrollPosition={false}
      when={() => true}
      cacheKey={"queryAll"}
    >
      <ConfigProvider>
        <div className="app-container">
          <div className="content-wrapper">
            <div className="search-section">
              <SearchSection onSearch={handleSearch} />
            </div>
            <div className="table-wrapper">
              <Tabs
                activeKey={activeTab}
                onChange={onTabChange}
                style={{
                  width: "100%",
                  overflowX: "auto",
                }}
                items={tabsData.map((tab) => ({
                  key: tab.key,
                  label: tab.tab,
                  children: (
                    <DataSection
                      data={tab.data}
                      loading={loading}
                      columns={tab.columns}
                      onRefresh={refreshData}
                      tabKey={tab.key} // 传入标签页key
                      pagination={tab.pagination}
                    />
                  ),
                }))}
              />
            </div>
          </div>
        </div>

        <MediumForm
          visible={mediumFormVisible}
          values={currentRow || {}}
          loading={formLoading} // 传递加载状态
          submitting={formSubmitting} // 传递提交状态
          onSubmit={handleMediumFormSubmit}
          onCancel={() => {
            setMediumFormVisible(false);
            setCurrentRow(undefined);
          }}
        />

        <DpmstCurtContForm
          visible={dpmstCurtContFormVisible}
          values={currentRow || {}}
          loading={formLoading} // 传递加载状态到表单组件
          submitting={formSubmitting} // 传递提交状态
          onSubmit={handleDpmstCurtContSubmit}
          onCancel={() => {
            setDpmstCurtContFormVisible(false);
            setCurrentRow(undefined);
          }}
        />

        <DpmstCurtAccForm
          visible={dpmstCurtAccFormVisible}
          values={currentRow || {}}
          loading={formLoading} // 传递加载状态到表单组件
          submitting={formSubmitting} // 传递提交状态
          onSubmit={handleDpmstCurtAccSubmit}
          onCancel={() => {
            setDpmstCurtAccFormVisible(false);
            setCurrentRow(undefined);
          }}
        />

        <DpmstFixContForm
          visible={dpmstFixContFormVisible}
          values={currentRow || {}}
          loading={formLoading} // 传递加载状态到表单组件
          submitting={formSubmitting} // 传递提交状态
          onSubmit={handleDpmstFixContSubmit}
          onCancel={() => {
            setDpmstFixContFormVisible(false);
            setCurrentRow(undefined);
          }}
        />

        <DpmstFixAccForm
          visible={dpmstFixAccFormVisible}
          values={currentRow || {}}
          loading={formLoading} // 传递加载状态到表单组件
          submitting={formSubmitting} // 传递提交状态
          onSubmit={handleDpmstFixAccSubmit}
          onCancel={() => {
            setDpmstFixAccFormVisible(false);
            setCurrentRow(undefined);
          }}
        />

        <DprgtMaslaveContRelatForm
          visible={dprgtMaslaveContRelatFormVisible}
          values={currentRow || {}}
          loading={formLoading} // 传递加载状态到表单组件
          submitting={formSubmitting} // 传递提交状态
          onSubmit={handleDprgtMaslaveContRelatSubmit}
          onCancel={() => {
            setDprgtMaslaveContRelatFormVisible(false);
            setCurrentRow(undefined);
          }}
        />

        <CustInfoForm
          visible={custInfoFormVisible}
          values={currentRow || {}}
          loading={formLoading} // 传递加载状态到表单组件
          submitting={formSubmitting} // 传递提交状态
          onSubmit={handleCustInfoSubmit}
          onCancel={() => {
            setCustInfoFormVisible(false);
            setCurrentRow(undefined);
          }}
        />

        <RelMediumTypeFlagCdEdit
          visible={relatedMediaModalVisible} // 关联介质类型代码
          record={currentRow}
          mediumNo={currentMediumNo}
          onCancel={() => setRelatedMediaModalVisible(false)}
          onSuccess={handleRelatedMediaEditSuccess}
        />

        <StatusFlagCdEdit
          visible={statusFlagCdVisible} // 介质状态标志码
          record={currentRow}
          mediumNo={currentMediumNo}
          onCancel={() => setStatusFlagCdVisible(false)}
          onSuccess={handleStatusFlagCdEditSuccess}
        />

        <AttrbuteFlagCdEdit
          visible={attrbuteFlagCdVisible} // 介质属性标志码
          record={currentRow}
          mediumNo={currentMediumNo}
          onCancel={() => setAttrbuteFlagCdVisible(false)}
          onSuccess={handleAttrbuteFlagCdEditSuccess}
        />

        <CrtModeFlagCdEdit
          visible={crtModeFlagCdVisible} // 介质认证方式标志码
          record={currentRow}
          mediumNo={currentMediumNo}
          onCancel={() => setCrtModeFlagCdVisible(false)}
          onSuccess={handleCrtModeFlagCdEditSuccess}
        />

        <CurtMContrFlagCdEdit
          visible={curtMContrFlagCdVisible} // 活期合约主档表-主合约控制标志码
          record={currentRow}
          mediumNo={currentMediumNo}
          prodtContractNo={currentProdtContractNo}
          onCancel={() => setCurtMContrFlagCdVisible(false)}
          onSuccess={handleCurtMContrFlagCdEditSuccess}
        />

        <FixMContrFlagCdEdit
          visible={fixMContrFlagCdVisible} // 定期合约主档表-主合约控制标志码
          record={currentRow}
          mediumNo={currentMediumNo}
          prodtContractNo={currentProdtContractNo}
          onCancel={() => setFixMContrFlagCdVisible(false)}
          onSuccess={handleFixMContrFlagCdEditSuccess}
        />

        <CurtContrCtrlFlagCdEdit
          visible={curtContrCtrlFlagCdVisible} // 活期合约主档表-合约控制标志码
          record={currentRow}
          mediumNo={currentMediumNo}
          prodtContractNo={currentProdtContractNo}
          onCancel={() => setCurtContrCtrlFlagCdVisible(false)}
          onSuccess={handleCurtContrCtrlFlagCdEditSuccess}
        />

        <FixContrCtrlFlagCdEdit
          visible={fixContrCtrlFlagCdVisible} // 定期合约主档表-合约控制标志码
          record={currentRow}
          mediumNo={currentMediumNo}
          prodtContractNo={currentProdtContractNo}
          onCancel={() => setFixContrCtrlFlagCdVisible(false)}
          onSuccess={handleFixContrCtrlFlagCdEditSuccess}
        />

        <CurtContrAttrFgCdEdit
          visible={curtContrAttrFgCdVisible} // 活期合约主档表-合约属性标志码
          record={currentRow}
          mediumNo={currentMediumNo}
          prodtContractNo={currentProdtContractNo}
          onCancel={() => setCurtContrAttrFgCdVisible(false)}
          onSuccess={handleCurtContrAttrFgCdEditSuccess}
        />

        <FixContrAttrFgCdEdit
          visible={fixContrAttrFgCdVisible} // 定期合约主档表-合约属性标志码
          record={currentRow}
          mediumNo={currentMediumNo}
          prodtContractNo={currentProdtContractNo}
          onCancel={() => setFixContrAttrFgCdVisible(false)}
          onSuccess={handleFixContrAttrFgCdEditSuccess}
        />

        <CurtContrStaFgCdEdit
          visible={curtContrStaFgCdVisible} // 活期合约主档表-合约状态标志码
          record={currentRow}
          mediumNo={currentMediumNo}
          prodtContractNo={currentProdtContractNo}
          onCancel={() => setCurtContrStaFgCdVisible(false)}
          onSuccess={handleCurtContrStaFgCdEditSuccess}
        />

        <FixContrStaFgCdEdit
          visible={fixContrStaFgCdVisible} // 定期合约主档表-合约状态标志码
          record={currentRow}
          mediumNo={currentMediumNo}
          prodtContractNo={currentProdtContractNo}
          onCancel={() => setFixContrStaFgCdVisible(false)}
          onSuccess={handleFixContrStaFgCdEditSuccess}
        />

        <CurtAccStatusFlagCdEdit
          visible={curtAccStatusFlagCdVisible} // 活期合约账户主档表-账户状态标志码
          record={currentRow}
          mediumNo={currentMediumNo}
          persInnerAccno={currentPersInnerAccno}
          onCancel={() => setCurtAccStatusFlagCdVisible(false)}
          onSuccess={handleCurtAccStatusFlagCdEditSuccess}
        />

        <FixAccStatusFlagCdEdit
          visible={fixAccStatusFlagCdVisible} // 定期合约账户主档表-账户状态标志码
          record={currentRow}
          mediumNo={currentMediumNo}
          persInnerAccno={currentPersInnerAccno}
          onCancel={() => setFixAccStatusFlagCdVisible(false)}
          onSuccess={handleFixAccStatusFlagCdEditSuccess}
        />

        <CurtAccFlagCdEdit
          visible={curtAccFlagCdVisible} // 活期合约账户主档表-账户标志码
          record={currentRow}
          mediumNo={currentMediumNo}
          persInnerAccno={currentPersInnerAccno}
          onCancel={() => setCurtAccFlagCdVisible(false)}
          onSuccess={handleCurtAccFlagCdEditSuccess}
        />

        <FixAccFlagCdEdit
          visible={fixAccFlagCdVisible} // 定期合约账户主档表-账户标志码
          record={currentRow}
          mediumNo={currentMediumNo}
          persInnerAccno={currentPersInnerAccno}
          onCancel={() => setFixAccFlagCdVisible(false)}
          onSuccess={handleFixAccFlagCdEditSuccess}
        />

        <DetailDpdtlCurtFormEdit
          open={detailDpdtlCurtModalVisible}
          data={detailDpdtlCurtData}
          refresh={detailDpdtlCurtRefresh}
          // values={currentRow || {}}
          onCancel={() => {
            setDetailDpdtlCurtModalVisible(false);
            setCurrentRow(undefined);
          }}
        />

        <DetailDpdtlFixFormEdit
          open={detailDpdtlFixModalVisible}
          data={detailDpdtlFixData}
          refresh={detailDpdtlFixRefresh}
          onCancel={() => {
            setDetailDpdtlFixModalVisible(false);
            setCurrentRow(undefined);
          }}
        />

        <DprgtPersDepLifeCycEdit
          open={detailDprgtPersDepLifeCycModalVisible}
          data={detailDprgtPersDepLifeCycData}
          // refresh={detailDpdtlFixRefresh}
          onCancel={() => {
            setDetailDprgtPersDepLifeCycModalVisible(false);
            setCurrentRow(undefined);
          }}
        />

        {/* 个人联名账户信息组件 */}
        <DprgtPersonJointAccFormEdit
          open={dprgtPersonJointAccModalVisible}
          data={dprgtPersonJointAccData}
          // refresh={detailDpdtlFixRefresh}
          onCancel={() => {
            setDprgtPersonJointAccModalVisible(false);
            setCurrentRow(undefined);
          }}
        />

        {/* 冻结登记簿组件 */}
        <DprgtFrzFormEdit
          open={dprgtFrzModalVisible}
          onCancel={() => setDprgtFrzModalVisible(false)}
          data={dprgtFrzData}
        />

        {/* 止付登记簿组件 */}
        <DprgtStopPayFormEdit
          open={dprgtStopPayModalVisible}
          onCancel={() => setDprgtStopPayModalVisible(false)}
          data={dprgtStopPayData}
        />

        {/* 签约加办关系登记簿组件 */}
        <DprgtSignAddFormEdit
          open={dprgtSignAddModalVisible}
          onCancel={() => setDprgtSignAddModalVisible(false)}
          data={dprgtSignAddData}
        />

        <DprgtAccFreeFormEdit
          open={dprgtAccFreeModalVisible}
          onCancel={() => setDprgtAccFreeModalVisible(false)}
          data={dprgtAccFreeData}
        />

        {/* 无卡支付登记簿组件 */}
        <DprgtNoCardPayFormEdit
          open={dprgtNoCardPayModalVisible}
          onCancel={() => setDprgtNoCardPayFormModalVisible(false)}
          data={dprgtNoCardPayData}
        />
        {/* 换卡登记簿组件 */}
        <DprgtChgCardFormEdit
          open={dprgtChgCardModalVisible}
          onCancel={() => setDprgtChgCardFormModalVisible(false)}
          data={dprgtChgCardData}
        />

        {/* 冻结登记簿组件 */}
        <DprgtFrzFormEdit
          open={dprgtFrzModalVisible}
          onCancel={() => setDprgtFrzModalVisible(false)}
          data={dprgtFrzData}
        />

        {/* 待限制服务登记簿组件 */}
        <DprgtTobeLimtServFormEdit
          open={tobeLimtServModalVisible}
          onCancel={() => setTobeLimtServModalVisible(false)}
          data={tobeLimtServData}
        />
        {/* 限制服务登记簿组件 */}
        <DprgtLimtServFormEdit
          open={limtServModalVisible}
          onCancel={() => setLimtServModalVisible(false)}
          data={limtServData}
        />

        {/* 消费登记簿模态框 */}
        <ConsumFormEdit
          open={consumModalVisible}
          onCancel={() => setConsumModalVisible(false)}
          data={consumData}
        />

        {/* 退货登记簿模态框 */}
        <SaleRetFormEdit
          open={saleRetModalVisible}
          onCancel={() => setSaleRetModalVisible(false)}
          data={saleRetData}
        />

        {/* 账户升降级登记簿模态框 */}
        <AccUpdownFormEdit
          open={accUpdownModalVisible}
          onCancel={() => setAccUpdownModalVisible(false)}
          data={accUpdownData}
        />

        {/* 合约辅助表模态框 */}
        <DprgtContrAsstFormEdit
          open={dprgtContrAsstModalVisible}
          onCancel={() => setDprgtContrAsstModalVisible(false)}
          data={dprgtContrAsstData}
        />
        {/* 圈存加办模态框 */}
        <DprgtCflAddoffInfoFormEdit
          open={dprgtCflAddoffInfoModalVisible}
          onCancel={() => setDprgtCflAddoffInfoModalVisible(false)}
          data={dprgtCflAddoffInfoData}
        />
        {/* 绑定关系模态框 */}
        <DprgtAccBindRelatFormEdit
          open={DprgtAccBindRelatFormVisible}
          onCancel={() => setDprgtAccBindRelatFormVisible(false)}
          data={accBindRelatData}
        />
        {/* 资金用途管控明细表 */}
        <DpdtlFundsUsageControlFormEdit
          open={dpdtlFundsUsageControlModalVisible}
          onCancel={() => setDpdtlFundsUsageControlModalVisible(false)}
          data={fundsUsageControlData}
        />
        {/* 资金用途管控表模态框 */}
        <DprgtFundsUsageControlFormEdit
          open={dprgtFundsUsageControlVisible}
          onCancel={() => setDprgtFundsUsageControlVisible(false)}
          data={dprgtFundsUsageControlData}
        />

        {/* 资金用途管控借据表模态框 */}
        <DprgtFundsUsageControlLoanFormEdit
          open={dprgtFundsUsageControlLoanVisible}
          onCancel={() => setDprgtFundsUsageControlLoanVisible(false)}
          data={dprgtFundsUsageControlLoanData}
        />

        {/* 资金用途管控借据明细表模态框 */}
        <DpdtlFundsUsageControlLoanFormEdit
          open={dpdtlFundsUsageControlLoanModalVisible}
          onCancel={() => setDpdtlFundsUsageControlLoanModalVisible(false)}
          data={dpdtlFundsUsageControlLoanData}
        />

        {/* 账户星级登记簿模态框 */}
        <DprgtAccStarFormEdit
          open={accStarModalVisible}
          onCancel={() => setAccStarModalVisible(false)}
          data={accStarData}
        />

        {/* 活期产品合约账户结转余额表模态框 */}
        <DprgtCarryBalCalIntFormEdit
          open={carryBalCalIntModalVisible}
          onCancel={() => setCarryBalCalIntModalVisible(false)}
          data={carryBalCalIntData}
        />

        {/* 存款证明模态框 */}
        <DepProveFormEdit
          open={dprgtDepProveModalVisible}
          onCancel={() => setDprgtDepProveModalVisible(false)}
          data={dprgtDepProveData}
        />

        {/* 客户限制服务登记簿模态框 */}
        <DpRgtCustLimtServFormEdit
          open={dprgtCustLimtServModalVisible}
          onCancel={() => setDprgtCustLimtServModalVisible(false)}
          data={dprgtCustLimtServData}
        />

        {/* 存款证明开立信息登记模态框 */}
        <DprgtDepProveOpenInfoFormEdit
          open={dprgtDepProveOpenInfoModalVisible}
          onCancel={() => setDprgtDepProveOpenInfoModalVisible(false)}
          data={dprgtDepProveOpenInfoData}
        />
      </ConfigProvider>
    </KeepAlive>
  );
};

export default QueryList;
