import request from '@/utils/request';
import {
  DataItem,
  TbDpmstMedium,
  TbDpmstCurtCont,
  TbDpmstCurtAcc,
  TbDpmstFixCont,
  TbDpmstFixAcc,
  TbDprgtMaslaveContRelat,
  CustInfo,
  TbDpdtlCurtAcc,
  TbDpdtlFixAcc,
  TbDprgtPersDepLifeCyc,
  DprgtPersonJointAcc,
  DprgtFrz,
  DprgtStopPay,
  DprgtRlsStopPay,
  TbDprgtSignAdd,
  DprgtAccFree,
  DprgtNoCardPay,
  DprgtChgCard,
  TbDprgtTobeLimtServ,
  TbDprgtLimtServ,
  Consum,
  SaleRet,
  AccUpdown,
  DprgtContrAsst,
  DprgtCflAddoffInfo,
  DprgtAccBindRelat,
  TbDpdtlFundsUsageControl,
  TbDprgtFundsUsageControl,
  TbDprgtFundsUsageControlLoan,
  DprgtAccStar,
  DprgtCarryBalCalInt,
  TbDprgtDepProve,
  TbDprgtCustLimtServ,
  TbDprgtDepProveOpenInfo,
  TbDpdtlFundsUsageControlLoan,
  PageResponse,
} from './data.d';

// API 通用返回结果类型
interface ResultType {
  success: boolean;
  message?: string;
  data?: any;
}

/* *
 *
 * <AUTHOR>
 * @datetime  2021/09/16
 *
 * 该文件包含了介质查询相关的所有 API 服务函数
 *
 * */

/**
 * 查询用户信息详细
 *
 * @param mediumNo 介质号
 * @returns 返回当前介质相关的所有详细信息
 */
export async function queryAll(mediumNo: string) {
  return request(`/testtool/accQuery/cardQuery?mediumNo=${mediumNo}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * 主从合约登记簿分页查询数据
 * @param mainContrNo 主合约号
 * @param zoneVal 分区值
 * @param pageInfoKey 分页信息key，用于标识不同的分页数据类型
 * @param params 分页参数 {current: 当前页码, pageSize: 每页条数}
 * @returns 返回分页数据 {
 *   records: 数据记录数组,
 *   total: 总记录数,
 *   size: 每页条数,
 *   current: 当前页码
 * }
 */
export async function queryDprgtMaslaveContRelatPageInfo(mainContrNo: string, zoneVal: String, pageInfoKey: string, params: { current: number; pageSize: number }) {
  return request(`/testtool/accQuery/dprgtMaslaveContRelatPageInfoQuery`, {
    method: 'GET',
    params: {
      mainContrNo,
      zoneVal,
      pageInfoKey,
      current: params.current,
      pageSize: params.pageSize
    },
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * 活期从合约主档表分页查询数据
 * @param mainContrNo 主合约号
 * @param zoneVal 分区值
 * @param pageInfoKey 分页信息key，用于标识不同的分页数据类型
 * @param params 分页参数 {current: 当前页码, pageSize: 每页条数}
 * @returns 返回分页数据 {
 *   records: 数据记录数组,
 *   total: 总记录数,
 *   size: 每页条数,
 *   current: 当前页码
 * }
 */
export async function queryDpRelmstCurtContDtoPageInfo(mainContrNo: string, zoneVal: String, pageInfoKey: string, params: { current: number; pageSize: number }) {
  return request(`/testtool/accQuery/dpRelmstCurtContDtoPageInfoQuery`, {
    method: 'GET',
    params: {
      mainContrNo,
      zoneVal,
      pageInfoKey,
      current: params.current,
      pageSize: params.pageSize
    },
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * 活期从账户主档表分页查询数据
 * @param mainContrNo 主合约号
 * @param zoneVal 分区值
 * @param pageInfoKey 分页信息key，用于标识不同的分页数据类型
 * @param params 分页参数 {current: 当前页码, pageSize: 每页条数}
 * @returns 返回分页数据 {
 *   records: 数据记录数组,
 *   total: 总记录数,
 *   size: 每页条数,
 *   current: 当前页码
 * }
 */
export async function queryDpRelmstCurtAccDtoPageInfo(mainContrNo: string, zoneVal: String, pageInfoKey: string, params: { current: number; pageSize: number }) {
  return request(`/testtool/accQuery/dpRelmstCurtAccDtoPageInfoQuery`, {
    method: 'GET',
    params: {
      mainContrNo,
      zoneVal,
      pageInfoKey,
      current: params.current,
      pageSize: params.pageSize
    },
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * 查询介质主档表
 *
 * @param params 查询参数，包括分页、过滤和排序信息
 * @returns 返回用户列表数据，包括分页信息
 */
export async function getDpmstMedium(params?: TbDpmstMedium) {
  const queryParams: Record<string, string> = {};
  if (params?.mediumNo) {
    queryParams.mediumNo = params.mediumNo;
  }
  if (params?.zoneVal) {
    queryParams.zoneVal = params.zoneVal;
  }
  const queryString = new URLSearchParams(queryParams).toString();
  return request(`/testtool/accQuery/mediuQuery?${queryString}`, {
    data: params,
    method: 'GET',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * 更新介质主档表
 *
 * @param TbDpmstMedium 介质主档表信息
 * @returns 返回操作结果
 */
export async function dpmstMediumUpdate (params: TbDpmstMedium) {
  return request('/testtool/accQuery/mediumUpdate', {
    method: 'POST',
    data: params,
  });
}

/**
 * 查询活期产品合约主档表
 *
 * @param params 查询参数，包括分页、过滤和排序信息
 * @returns 返回用户列表数据，包括分页信息
 */
export async function getDpmstCurtCont(params?: TbDpmstCurtCont) {
  const queryParams: Record<string, string> = {};
  if (params?.prodtContractNo) {
    queryParams.prodtContractNo = params.prodtContractNo;
  }
  if (params?.zoneVal) {
    queryParams.zoneVal = params.zoneVal;
  }
  const queryString = new URLSearchParams(queryParams).toString();
  return request(`/testtool/accQuery/dpmstCurtContQuery?${queryString}`, {
    data: params,
    method: 'GET',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * 更新活期合约主档表
 *
 * @param TbDpmstCurtCont 活期合约主档表信息
 * @returns 返回操作结果
 */
export async function dpmstCurtContUpdate (params: TbDpmstCurtCont) {
  return request('/testtool/accQuery/dpmstCurtContUpdate', {
    method: 'POST',
    data: params,
  });
}

/**
 * 查询活期产品合约账户主档表
 *
 * @param params 查询参数，包括分页、过滤和排序信息
 * @returns 返回用户列表数据，包括分页信息
 */
export async function getDpmstCurtAcc(params?: TbDpmstCurtAcc) {
  const queryParams: Record<string, string> = {};
  if (params?.persInnerAccno) {
    queryParams.persInnerAccno = params.persInnerAccno;
  }
  if (params?.zoneVal) {
    queryParams.zoneVal = params.zoneVal;
  }
  const queryString = new URLSearchParams(queryParams).toString();
  return request(`/testtool/accQuery/dpmstCurtAccQuery?${queryString}`, {
    data: params,
    method: 'GET',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * 更新活期账户主档表
 *
 * @param TbDpmstCurtAcc 活期账户主档表信息
 * @returns 返回操作结果
 */
export async function dpmstCurtAccUpdate (params: TbDpmstCurtAcc) {
  return request('/testtool/accQuery/dpmstCurtAccUpdate', {
    method: 'POST',
    data: params,
  });
}

/**
 * 查询定期产品合约主档表
 *
 * @param params 查询参数，包括分页、过滤和排序信息
 * @returns 返回用户列表数据，包括分页信息
 */
export async function getDpmstFixCont(params?: TbDpmstFixCont) {
  const queryParams: Record<string, string> = {};
  if (params?.prodtContractNo) {
    queryParams.prodtContractNo = params.prodtContractNo;
  }
  if (params?.zoneVal) {
    queryParams.zoneVal = params.zoneVal;
  }
  const queryString = new URLSearchParams(queryParams).toString();
  return request(`/testtool/accQuery/dpmstFixContQuery?${queryString}`, {
    data: params,
    method: 'GET',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * 更新定期合约主档表信息
 *
 * @param TbDpmstFixCont 定期合约主档表信息
 * @returns 返回操作结果
 */
export async function dpmstFixContUpdate (params: TbDpmstFixCont) {
  return request('/testtool/accQuery/dpmstFixContUpdate', {
    method: 'POST',
    data: params,
  });
}

/**
 * 查询定期产品合约账户主档表
 *
 * @param params 查询参数，包括分页、过滤和排序信息
 * @returns 返回用户列表数据，包括分页信息
 */
export async function getDpmstFixAcc(params?: TbDpmstFixAcc) {
  const queryParams: Record<string, string> = {};
  if (params?.persInnerAccno) {
    queryParams.persInnerAccno = params.persInnerAccno;
  }
  if (params?.zoneVal) {
    queryParams.zoneVal = params.zoneVal;
  }
  const queryString = new URLSearchParams(queryParams).toString();
  return request(`/testtool/accQuery/dpmstFixAccQuery?${queryString}`, {
    data: params,
    method: 'GET',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * 更新定期账户主档表信息
 *
 * @param TbDpmstFixAcc 定期账户主档表信息
 * @returns 返回操作结果
 */
export async function dpmstFixAccUpdate (params: TbDpmstFixAcc) {
  return request('/testtool/accQuery/dpmstFixAccUpdate', {
    method: 'POST',
    data: params,
  });
}

/**
 * 查询主从合约关系表
 *
 * @param params 查询参数，包括分页、过滤和排序信息
 * @returns 返回用户列表数据，包括分页信息
 */
export async function getDprgtMaslaveContRelat(params?: TbDprgtMaslaveContRelat) {
  const queryParams: Record<string, string> = {};
  if (params?.mainContrNo) {
    queryParams.mainContrNo = params.mainContrNo;
  }
  if (params?.saccnoSeqNo) {
    queryParams.saccnoSeqNo = params.saccnoSeqNo;
  }
  if (params?.zoneVal) {
    queryParams.zoneVal = params.zoneVal;
  }
  const queryString = new URLSearchParams(queryParams).toString();
  return request(`/testtool/accQuery/dprgtMaslaveContRelatQuery?${queryString}`, {
    data: params,
    method: 'GET',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * 更新主从合约关系表
 *
 * @param TbDprgtMaslaveContRelat 主从合约关系表
 * @returns 返回操作结果
 */
export async function dprgtMaslaveContRelatUpdate (params: TbDprgtMaslaveContRelat) {
  return request('/testtool/accQuery/dprgtMaslaveContRelatUpdate', {
    method: 'POST',
    data: params,
  });
}

/**
 * 查询客户账户信息表
 *
 * @param params 查询参数，包括分页、过滤和排序信息
 * @returns 返回用户列表数据，包括分页信息
 */
export async function getCustInfo(params?: CustInfo) {
  const queryParams: Record<string, string> = {};
  if (params?.custNo) {
    queryParams.custNo = params.custNo;
  }
  if (params?.perCertTpCd) {
    queryParams.perCertTpCd = params.perCertTpCd;
  }
  if (params?.personalCertNo) {
    queryParams.personalCertNo = params.personalCertNo;
  }
  const queryString = new URLSearchParams(queryParams).toString();
  return request(`/testtool/accQuery/custInfoQuery?${queryString}`, {
    data: params,
    method: 'GET',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * 更新客户账户信息
 *
 * @param CustInfo 客户账户信息
 * @returns 返回操作结果
 */
export async function custInfoUpdate (params: CustInfo) {
  return request('/testtool/accQuery/custInfoUpdate', {
    method: 'POST',
    data: params,
  });
}

/**
 * 查询活期产品合约账户交易明细表
 *
 * @param params 查询参数，包括分页、过滤和排序信息
 * @returns 返回用户列表数据，包括分页信息
 */
export async function getDpdtlCurtAcc(persInnerAccno: string, zoneVal: string, pageInfoKey: string, params: { current: number; pageSize: number }) {
  return request(`/testtool/accQuery/dpdtlCurtAccQuery`, {
    method: 'GET',
    params: {
      persInnerAccno,
      zoneVal,
      pageInfoKey,
      current: params.current,
      pageSize: params.pageSize
    },
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * 插入活期产品合约账户交易明细表
 *
 * @param params 查询参数，包括分页、过滤和排序信息
 * @returns 返回用户列表数据，包括分页信息
 */
export async function insertDpdtlCurtAcc(params: TbDpdtlCurtAcc) {
  return request(`/testtool/accQuery/dpdtlCurtAccInsert`, {
    method: 'POST',
    data: params,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * 更新活期产品合约账户交易明细表
 *
 * @param TbDpdtlCurtAcc 活期产品合约账户交易明细表
 * @returns 返回操作结果
 */
export async function dpdtlCurtAccUpdate (params: TbDpdtlCurtAcc) {
  return request('/testtool/accQuery/dpdtlCurtAccUpdate', {
    method: 'POST',
    data: params,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * 删除活期产品合约账户交易明细表
 *
 * @param TbDpdtlCurtAcc 活期产品合约账户交易明细表
 * @returns 返回操作结果
 */
export async function dpdtlCurtAccDelete (params: TbDpdtlCurtAcc) {
  return request('/testtool/accQuery/dpdtlCurtAccDelete', {
    method: 'POST',
    data: params,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * 查询定期产品合约账户交易明细表
 *
 * @param params 查询参数，包括分页、过滤和排序信息
 * @returns 返回用户列表数据，包括分页信息
 */
export async function getDpdtlFixAcc(persInnerAccno: string, zoneVal: string, pageInfoKey: string, params: { current: number; pageSize: number }) {
  return request(`/testtool/accQuery/dpdtlFixAccQuery`, {
    method: 'GET',
    params: {
      persInnerAccno,
      zoneVal,
      pageInfoKey,
      current: params.current,
      pageSize: params.pageSize
    },
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * 插入定期产品合约账户交易明细表
 *
 * @param TbDpdtlFixAcc 定期产品合约账户交易明细表
 * @returns 返回用户列表数据，包括分页信息
 */
export async function insertDpdtlFixAcc(params: TbDpdtlFixAcc) {
  return request(`/testtool/accQuery/dpdtlFixAccInsert`, {
    method: 'POST',
    data: params,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * 更新定期产品合约账户交易明细表
 *
 * @param TbDpdtlFixAcc 定期产品合约账户交易明细表
 * @returns 返回操作结果
 */
export async function dpdtlFixAccUpdate (params: TbDpdtlFixAcc) {
  return request('/testtool/accQuery/dpdtlFixAccUpdate', {
    method: 'POST',
    data: params,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * 删除定期产品合约账户交易明细表
 *
 * @param TbDpdtlFixAcc 定期产品合约账户交易明细表
 * @returns 返回操作结果
 */
export async function dpdtlFixAccDelete (params: TbDpdtlFixAcc) {
  return request('/testtool/accQuery/dpdtlFixAccDelete', {
    method: 'POST',
    data: params,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * 查询个人存款生命周期
 *
 * @param params 查询参数，包括分页、过滤和排序信息
 * @returns 返回用户列表数据，包括分页信息
 */
export async function getDprgtPersDepLifeCyc(mediumNo: string, zoneVal: string, pageInfoKey: string, params: { current: number; pageSize: number }) {
  return request(`/testtool/accQuery/dprgtPersDepLifeCycQuery`, {
    method: 'GET',
    params: {
      mediumNo,
      zoneVal,
      pageInfoKey,
      current: params.current,
      pageSize: params.pageSize
    },
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * 插入个人存款生命周期
 *
 * @param TbDprgtPersDepLifeCyc 个人存款生命周期
 * @returns 返回用户列表数据，包括分页信息
 */
export async function insertDprgtPersDepLifeCyc(params: TbDprgtPersDepLifeCyc) {
  return request(`/testtool/accQuery/dprgtPersDepLifeCycInsert`, {
    method: 'POST',
    data: params,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * 更新个人存款生命周期
 *
 * @param TbDprgtPersDepLifeCyc 个人存款生命周期
 * @returns 返回操作结果
 */
export async function dprgtPersDepLifeCycUpdate (params: TbDprgtPersDepLifeCyc) {
  return request('/testtool/accQuery/dprgtPersDepLifeCycUpdate', {
    method: 'POST',
    data: params,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * 删除个人存款生命周期
 *
 * @param TbDprgtPersDepLifeCyc 个人存款生命周期
 * @returns 返回操作结果
 */
export async function dprgtPersDepLifeCycDelete (params: TbDprgtPersDepLifeCyc) {
  return request('/testtool/accQuery/dprgtPersDepLifeCycDelete', {
    method: 'POST',
    data: params,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * 查询个人联名账户信息记录表
 *
 * @param params 查询参数，包括分页、过滤和排序信息
 * @returns 返回用户列表数据，包括分页信息
 */
export async function getDprgtPersonJointAcc(mediumNo: string, zoneVal: string, pageInfoKey: string, params: { current: number; pageSize: number }) {
  return request(`/testtool/accQuery/dprgtPersonJointAccQuery`, {
    method: 'GET',
    params: {
      mediumNo,
      zoneVal,
      pageInfoKey,
      current: params.current,
      pageSize: params.pageSize
    },
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * 插入个人联名账户信息记录表
 *
 * @param TbDprgtPersDepLifeCyc 个人存款生命周期
 * @returns 返回用户列表数据，包括分页信息
 */
export async function insertDprgtPersonJointAcc(params: DprgtPersonJointAcc) {
  return request(`/testtool/accQuery/dprgtPersonJointAccInsert`, {
    method: 'POST',
    data: params,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * 更新个人联名账户信息记录表
 *
 * @param TbDprgtPersDepLifeCyc 个人存款生命周期
 * @returns 返回操作结果
 */
export async function dprgtDprgtPersonJointAccUpdate (params: DprgtPersonJointAcc) {
  return request('/testtool/accQuery/dprgtPersonJointAccUpdate', {
    method: 'POST',
    data: params,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * 删除个人联名账户信息记录表
 *
 * @param TbDprgtPersDepLifeCyc 个人存款生命周期
 * @returns 返回操作结果
 */
export async function dprgtPersonJointAccDelete (params: DprgtPersonJointAcc) {
  return request('/testtool/accQuery/dprgtPersonJointAccDelete', {
    method: 'POST',
    data: params,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}


/**
 * 查询冻结登记簿
 *
 * @param params 查询参数，包括分页、过滤和排序信息
 * @returns 返回用户列表数据，包括分页信息
 */
export async function getDprgtFrz(mainContrNo: string, zoneVal: string, pageInfoKey: string, params: { current: number; pageSize: number }) {
  return request(`/testtool/accQuery/dprgtFrzQuery`, {
    method: 'GET',
    params: {
      mainContrNo,
      zoneVal,
      pageInfoKey,
      current: params.current,
      pageSize: params.pageSize
    },
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * 插入冻结登记簿
 *
 * @param TbDprgtPersDepLifeCyc 冻结登记簿
 * @returns 返回用户列表数据，包括分页信息
 */
export async function insertDprgtFrz(params: DprgtFrz) {
  return request(`/testtool/accQuery/dprgtFrzInsert`, {
    method: 'POST',
    data: params,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * 更新冻结登记簿
 *
 * @param DprgtFrz 冻结登记簿
 * @returns 返回操作结果
 */
export async function dprgtFrzUpdate (params: DprgtFrz) {
  return request('/testtool/accQuery/dprgtFrzUpdate', {
    method: 'POST',
    data: params,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * 删除冻结登记簿
 *
 * @param DprgtFrz 冻结登记簿
 * @returns 返回操作结果
 */
export async function dprgtFrzDelete (params: DprgtFrz) {
  return request('/testtool/accQuery/dprgtFrzDelete', {
    method: 'POST',
    data: params,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}


/**
 * 查询止付登记簿
 *
 * @param params 查询参数，包括分页、过滤和排序信息
 * @returns 返回用户列表数据，包括分页信息
 */
export async function getTbDprgtStopPay(mainContrNo: string, zoneVal: string, pageInfoKey: string, params: { current: number; pageSize: number }) {
  return request(`/testtool/accQuery/dprgtStopPayQuery`, {
    method: 'GET',
    params: {
      mainContrNo,
      zoneVal,
      pageInfoKey,
      current: params.current,
      pageSize: params.pageSize
    },
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * 插入止付登记簿
 *
 * @param TbDprgtStopPay 止付登记簿
 * @returns 返回用户列表数据，包括分页信息
 */
export async function insertTbDprgtStopPay(params: DprgtStopPay) {
  return request(`/testtool/accQuery/dprgtFrzInsert`, {
    method: 'POST',
    data: params,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * 更新止付登记簿
 *
 * @param TbDprgtStopPay 止付登记簿
 * @returns 返回操作结果
 */
export async function tbDprgtStopPayUpdate (params: DprgtStopPay) {
  return request('/testtool/accQuery/dprgtFrzUpdate', {
    method: 'POST',
    data: params,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * 删除止付登记簿
 *
 * @param TbDprgtStopPay 止付登记簿
 * @returns 返回操作结果
 */
export async function tbDprgtStopPayDelete (params: DprgtStopPay) {
  return request('/testtool/accQuery/dprgtStopPayDelete', {
    method: 'POST',
    data: params,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * 查询解止付登记簿
 *
 * @param params 查询参数，包括分页、过滤和排序信息
 * @returns 返回用户列表数据，包括分页信息
 */
export async function getTbDprgtRlsStopPay(stopPayNo: string, zoneVal: string, pageInfoKey: string, params: { current: number; pageSize: number }) {
  return request(`/testtool/accQuery/dprgtRlsStopPayQuery`, {
    method: 'GET',
    params: {
      stopPayNo,
      zoneVal,
      pageInfoKey,
      current: params.current,
      pageSize: params.pageSize
    },
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * 插入解止付登记簿
 *
 * @param TbDprgtRlsStopPay 解止付登记簿
 * @returns 返回用户列表数据，包括分页信息
 */
export async function insertTbDprgtRlsStopPay(params: DprgtRlsStopPay) {
  return request(`/testtool/accQuery/dprgtFrzInsert`, {
    method: 'POST',
    data: params,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * 更新解止付登记簿
 *
 * @param TbDprgtRlsStopPay 解止付登记簿
 * @returns 返回操作结果
 */
export async function TbDprgtRlsStopPayUpdate (params: DprgtRlsStopPay) {
  return request('/testtool/accQuery/dprgtFrzUpdate', {
    method: 'POST',
    data: params,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * 删除解止付登记簿
 *
 * @param TbDprgtRlsStopPay 解止付登记簿
 * @returns 返回操作结果
 */
export async function tbDprgtRlsStopPayDelete (params: DprgtRlsStopPay) {
  return request('/testtool/accQuery/dprgtStopPayDelete', {
    method: 'POST',
    data: params,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * 查询签约加办关系登记
 *
 * @param params 查询参数，包括分页、过滤和排序信息
 * @returns 返回用户列表数据，包括分页信息
 */
export async function getTbDprgtSignAdd(mediumNo: string, zoneVal: string, pageInfoKey: string, params: { current: number; pageSize: number }) {
  return request(`/testtool/accQuery/dprgtSignAddQuery`, {
    method: 'GET',
    params: {
      mediumNo,
      zoneVal,
      pageInfoKey,
      current: params.current,
      pageSize: params.pageSize
    },
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * 插入签约加办关系登记
 *
 * @param TbDprgtSignAdd 签约加办关系登记
 * @returns 返回用户列表数据，包括分页信息
 */
export async function insertTbDprgtSignAdd(params: TbDprgtSignAdd) {
  return request(`/testtool/accQuery/dprgtSignAddInsert`, {
    method: 'POST',
    data: params,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * 更新签约加办关系登记
 *
 * @param TbDprgtSignAdd 签约加办关系登记
 * @returns 返回操作结果
 */
export async function TbDprgtSignAddUpdate (params: TbDprgtSignAdd) {
  return request('/testtool/accQuery/dprgtSignAddUpdate', {
    method: 'POST',
    data: params,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * 删除签约加办关系登记
 *
 * @param TbDprgtSignAdd 签约加办关系登记
 * @returns 返回操作结果
 */
export async function tbDprgtSignAddDelete (params: TbDprgtSignAdd) {
  return request('/testtool/accQuery/dprgtSignAddDelete', {
    method: 'POST',
    data: params,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * 查询账户免收登记簿
 *
 * @param params 查询参数，包括分页、过滤和排序信息
 * @returns 返回用户列表数据，包括分页信息
 */
export async function getTbDprgtAccFree(persInnerAccno: string, zoneVal: string, pageInfoKey: string, params: { current: number; pageSize: number }) {
  return request(`/testtool/accQuery/dprgtAccFreeQuery`, {
    method: 'GET',
    params: {
      persInnerAccno,
      zoneVal,
      pageInfoKey,
      current: params.current,
      pageSize: params.pageSize
    },
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * 插入账户免收登记簿
 *
 * @param DprgtAccFree 账户免收登记簿
 * @returns 返回用户列表数据，包括分页信息
 */
export async function insertTbDprgtAccFree(params: DprgtAccFree) {
  return request(`/testtool/accQuery/dprgtAccFreeInsert`, {
    method: 'POST',
    data: params,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * 更新账户免收登记簿
 * @param DprgtAccFree 账户免收登记簿
 * @returns 返回操作结果
 */
export async function tbDprgtAccFreeUpdate (params: DprgtAccFree) {
  return request('/testtool/accQuery/dprgtAccFreeUpdate', {
    method: 'POST',
    data: params,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * 删除账户免收登记簿
 *
 * @param DprgtAccFree 账户免收登记簿
 * @returns 返回操作结果
 */
export async function tbDprgtAccFreeDelete (params: DprgtAccFree) {
  return request('/testtool/accQuery/dprgtAccFreeDelete', {
    method: 'POST',
    data: params,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}


/**
 * 查询无卡支付协议登记
 *
 * @param params 查询参数，包括分页、过滤和排序信息
 * @returns 返回用户列表数据，包括分页信息
 */
export async function getDprgtNoCardPay(mediumNo: string, zoneVal: string, pageInfoKey: string, params: { current: number; pageSize: number }) {
  return request(`/testtool/accQuery/dprgtNoCardPayQuery`, {
    method: 'GET',
    params: {
      mediumNo,
      zoneVal,
      pageInfoKey,
      current: params.current,
      pageSize: params.pageSize
    },
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * 插入无卡支付协议登记
 *
 * @param DprgtNoCardPay 无卡支付协议登记
 * @returns 返回用户列表数据，包括分页信息
 */
export async function insertDprgtNoCardPay(params: DprgtNoCardPay) {
  return request(`/testtool/accQuery/dprgtNoCardPayInsert`, {
    method: 'POST',
    data: params,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * 更新无卡支付协议登记
 * @param DprgtNoCardPay 无卡支付协议登记
 * @returns 返回操作结果
 */
export async function dprgtNoCardPayUpdate (params: DprgtNoCardPay) {
  return request('/testtool/accQuery/dprgtNoCardPayUpdate', {
    method: 'POST',
    data: params,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * 删除无卡支付协议登记
 *
 * @param DprgtNoCardPay 无卡支付协议登记
 * @returns 返回操作结果
 */
export async function dprgtNoCardPayDelete (params: DprgtNoCardPay) {
  return request('/testtool/accQuery/dprgtNoCardPayDelete', {
    method: 'POST',
    data: params,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * 重置DAC
 *
 * @param mediumNo 介质号
 * @returns 返回操作结果
 */
export async function dacReset(params: { mediumNo: string }) {
  return request('/testtool/accQuery/dacReset', {
    method: 'POST',
    data: params,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * 一键销户
 *
 * @param mediumNo 介质号
 * @returns 返回操作结果
 */
export async function oneKeyClose(params: { mediumNo: string }) {
  return request('/testtool/accQuery/oneKeyClose', {
    method: 'POST',
    data: params,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * 取消一键销户
 *
 * @param mediumNo 介质号
 * @returns 返回操作结果
 */
export async function cacelOneKeyClose(params: { mediumNo: string }) {
  return request('/testtool/accQuery/cacelOneKeyClose', {
    method: 'POST',
    data: params,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * 查询换卡登记簿
 *
 * @param params 查询参数，包括分页、过滤和排序信息
 * @returns 返回用户列表数据，包括分页信息
 */
export async function getDprgtChgCard(mainContrNo: string, zoneVal: string, pageInfoKey: string, params: { current: number; pageSize: number }) {
  return request(`/testtool/accQuery/dprgtChgCardQuery`, {
    method: 'GET',
    params: {
      mainContrNo,
      zoneVal,
      pageInfoKey,
      current: params.current,
      pageSize: params.pageSize
    },
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * 插入换卡登记簿
 *
 * @param DprgtChgCard 换卡登记簿
 * @returns 返回用户列表数据，包括分页信息
 */
export async function insertDprgtChgCard(params: DprgtChgCard) {
  return request(`/testtool/accQuery/dprgtChgCardInsert`, {
    method: 'POST',
    data: params,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * 更新换卡登记簿
 * @param DprgtChgCard 换卡登记簿
 * @returns 返回操作结果
 */
export async function dprgtChgCardUpdate (params: DprgtChgCard) {
  return request('/testtool/accQuery/dprgtChgCardUpdate', {
    method: 'POST',
    data: params,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * 删除换卡登记簿
 *
 * @param DprgtChgCard 换卡登记簿
 * @returns 返回操作结果
 */
export async function dprgtChgCardDelete (params: DprgtChgCard) {
  return request('/testtool/accQuery/dprgtChgCardDelete', {
    method: 'POST',
    data: params,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * @description: 获取待限制服务登记簿信息
 * @param prodtContractNo 产品合约编号
 * @param zoneVal 分片值
 * @param pageInfoKey 分页key
 * @param params 分页参数
 */
export async function getTobeLimtServ(prodtContractNo: string, zoneVal: string, pageInfoKey: string, params: { current: number; pageSize: number }) {
  return request('/testtool/accQuery/dprgtTobeLimtServQuery', {
    method: 'GET',
    params: {
      prodtContractNo,
      zoneVal,
      pageInfoKey,
      ...params,
    },
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * @description: 新增待限制服务登记簿信息
 * @param TbDprgtTobeLimtServ 待限制服务登记簿
 */
export async function insertTobeLimtServ(params: TbDprgtTobeLimtServ) {
  return request('/testtool/accQuery/dprgtTobeLimtServInsert', {
    method: 'POST',
    data: params,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * @description: 更新待限制服务登记簿信息
 * @param TbDprgtTobeLimtServ 待限制服务登记簿
 */
export async function updateTobeLimtServ(params: TbDprgtTobeLimtServ) {
  return request('/testtool/accQuery/dprgtTobeLimtServUpdate', {
    method: 'POST',
    data: params,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * @description: 删除待限制服务登记簿信息
 * @param TbDprgtTobeLimtServ 待限制服务登记簿
 */
export async function deleteTobeLimtServ(params: TbDprgtTobeLimtServ) {
  return request('/testtool/accQuery/dprgtTobeLimtServDelete', {
    method: 'POST',
    data: params,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * @description: 获取限制服务登记簿信息
 * @param prodtContractNo 产品合约编号
 * @param zoneVal 分片值
 * @param pageInfoKey 分页key
 * @param params 分页参数
 */
export async function getLimtServ(prodtContractNo: string, zoneVal: string, pageInfoKey: string, params: { current: number; pageSize: number }) {
  return request('/testtool/accQuery/dprgtLimtServQuery', {
    method: 'GET',
    params: {
      prodtContractNo,
      zoneVal,
      pageInfoKey,
      ...params,
    },
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * @description: 新增限制服务登记簿信息
 * @param TbDprgtLimtServ 限制服务登记簿
 */
export async function insertLimtServ(params: TbDprgtLimtServ) {
  return request('/testtool/accQuery/dprgtLimtServInsert', {
    method: 'POST',
    data: params,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * @description: 更新限制服务登记簿信息
 * @param TbDprgtLimtServ 限制服务登记簿
 */
export async function updateLimtServ(params: TbDprgtLimtServ) {
  return request('/testtool/accQuery/dprgtLimtServUpdate', {
    method: 'POST',
    data: params,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * @description: 删除限制服务登记簿信息
 * @param TbDprgtLimtServ 限制服务登记簿
 */
export async function deleteLimtServ(params: TbDprgtLimtServ) {
  return request('/testtool/accQuery/dprgtLimtServDelete', {
    method: 'POST',
    data: params,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * @description: 获取消费登记簿信息
 * @param pageInfoKey 分页key
 * @param params 分页参数
 */
export async function getConsum(mainContrNo: string, zoneVal: string, pageInfoKey: string, params: { current: number; pageSize: number }) {
  return request('/testtool/accQuery/dprgtConsumQuery', {
    method: 'GET',
    params: {
      mainContrNo,
      zoneVal,
      pageInfoKey,
      ...params,
    },
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * @description: 新增消费登记簿信息
 * @param Consum 消费登记簿
 */
export async function insertConsum(params: Consum) {
  return request('/testtool/accQuery/dprgtConsumInsert', {
    method: 'POST',
    data: params,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * @description: 更新消费登记簿信息
 * @param Consum 消费登记簿
 */
export async function updateConsum(params: Consum) {
  return request('/testtool/accQuery/dprgtConsumUpdate', {
    method: 'POST',
    data: params,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * @description: 删除消费登记簿信息
 * @param Consum 消费登记簿
 */
export async function deleteConsum(params: Consum) {
  return request('/testtool/accQuery/dprgtConsumDelete', {
    method: 'POST',
    data: params,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * 获取退货登记簿数据
 * @param mainContrNo 主合约编号
 * @param zoneVal 分片值
 * @param pageInfoKey 页面信息键
 * @param params 分页参数
 * @returns 退货登记簿数据
 */
export async function getSaleRet(mainContrNo: string, zoneVal: string, pageInfoKey: string, params: { current: number; pageSize: number }) {
  return request(`/testtool/accQuery/dprgtSaleRetQuery`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      mainContrNo,
      zoneVal,
      pageInfoKey,
      ...params,
    },
  });
}

/**
 * 插入退货登记簿数据
 * @param params 退货登记簿数据
 * @returns 插入结果
 */
export async function insertSaleRet(params: SaleRet) {
  return request('/testtool/accQuery/dprgtSaleRetInsert', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: params,
  });
}

/**
 * 更新退货登记簿数据
 * @param params 退货登记簿数据
 * @returns 更新结果
 */
export async function updateSaleRet(params: SaleRet) {
  return request('/testtool/accQuery/dprgtSaleRetUpdate', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: params,
  });
}

/**
 * 删除退货登记簿数据
 * @param params 退货登记簿数据
 * @returns 删除结果
 */
export async function deleteSaleRet(params: SaleRet) {
  return request('/testtool/accQuery/dprgtSaleRetDelete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: params,
  });
}

/**
 * @description: 获取账户升降级登记簿信息
 * @param mainContrNo 主合约编号
 * @param zoneVal 分片值
 * @param pageInfoKey 分页key
 * @param params 分页参数
 */
export async function getAccUpdown(mainContrNo: string, zoneVal: string, pageInfoKey: string, params: { current: number; pageSize: number }) {
  return request('/testtool/accQuery/dprgtAccUpdownQuery', {
    method: 'GET',
    params: {
      mainContrNo,
      zoneVal,
      pageInfoKey,
      ...params,
    },
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * @description: 新增账户升降级登记簿
 * @param params 账户升降级登记簿数据
 */
export async function insertAccUpdown(params: AccUpdown) {
  return request('/testtool/accQuery/dprgtAccUpdownInsert', {
    method: 'POST',
    data: params,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * @description: 更新账户升降级登记簿
 * @param params 账户升降级登记簿数据
 */
export async function updateAccUpdown(params: AccUpdown) {
  return request('/testtool/accQuery/dprgtAccUpdownUpdate', {
    method: 'POST',
    data: params,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * @description: 删除账户升降级登记簿
 * @param params 账户升降级登记簿数据
 */
export async function deleteAccUpdown(params: AccUpdown) {
  return request('/testtool/accQuery/dprgtAccUpdownDelete', {
    method: 'POST',
    data: params,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * 获取合约辅助表数据
 * @param prodtContractNo 产品合约编号
 * @param zoneVal 分片值
 * @param pageInfoKey 分页key
 * @param params 分页参数
 */
export async function getDprgtContrAsst(prodtContractNo: string, zoneVal: string, pageInfoKey: string, params: { current: number; pageSize: number }) {
  return request('/testtool/accQuery/dprgtContrAsstQuery', {
    method: 'GET',
    params: {
      prodtContractNo,
      zoneVal,
      pageInfoKey,
      ...params,
    },
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * 新增合约辅助表记录
 * @param params 合约辅助表数据
 */
export async function insertDprgtContrAsst(params: DprgtContrAsst) {
  return request('/testtool/accQuery/dprgtContrAsstInsert', {
    method: 'POST',
    data: {
      ...params,
    },
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * 更新合约辅助表记录
 * @param params 合约辅助表数据
 */
export async function updateDprgtContrAsst(params: DprgtContrAsst) {
  return request('/testtool/accQuery/dprgtContrAsstUpdate', {
    method: 'POST',
    data: {
      ...params,
    },
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * 删除合约辅助表记录
 * @param params 合约辅助表数据
 */
export async function deleteDprgtContrAsst(params: DprgtContrAsst) {
  return request('/testtool/accQuery/dprgtContrAsstDelete', {
    method: 'POST',
    data: {
      ...params,
    },
  });
}

// 查询圈存加办信息
export async function getDprgtCflAddoffInfo(prodtContractNo: string, zoneVal: string, pageInfoKey: string, params: { current: number; pageSize: number }) {
  return request('/testtool/accQuery/dprgtCflAddoffQuery', {
    method: 'GET',
    params: {
      prodtContractNo,
      zoneVal,
      pageInfoKey,
      ...params,
    },
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

// 新增圈存加办信息
export async function insertDprgtCflAddoffInfo(params: DprgtCflAddoffInfo) {
  return request('/testtool/accQuery/dprgtCflInsert', {
    method: 'POST',
    data: {
      ...params,
    },
  });
}

// 更新圈存加办信息
export async function updateDprgtCflAddoffInfo(params: DprgtCflAddoffInfo) {
  return request('/testtool/accQuery/dprgtCflUpdate', {
    method: 'POST',
    data: {
      ...params,
    },
  });
}

// 删除圈存加办信息
export async function deleteDprgtCflAddoffInfo(params: DprgtCflAddoffInfo) {
  return request('/testtool/accQuery/dprgtCflDelete', {
    method: 'POST',
    data: {
      ...params,
    },
  });
}

/**
 * 定期从合约主档表分页查询数据
 * @param mainContrNo 主合约号
 * @param zoneVal 分区值
 * @param pageInfoKey 分页信息key，用于标识不同的分页数据类型
 * @param params 分页参数 {current: 当前页码, pageSize: 每页条数}
 * @returns 返回分页数据 {
 *   records: 数据记录数组,
 *   total: 总记录数,
 *   size: 每页条数,
 *   current: 当前页码
 * }
 */
export async function queryDpRelmstFixContDtoPageInfo(mainContrNo: string, zoneVal: string, pageInfoKey: string, params: { current: number; pageSize: number }) {
  return request(`/testtool/accQuery/dpRelmstFixContDtoPageInfoQuery`, {
    method: 'GET',
    params: {
      mainContrNo,
      zoneVal,
      pageInfoKey,
      current: params.current,
      pageSize: params.pageSize
    },
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * 定期从账户主档表分页查询数据
 * @param mainContrNo 主合约号
 * @param zoneVal 分区值
 * @param pageInfoKey 分页信息key，用于标识不同的分页数据类型
 * @param params 分页参数 {current: 当前页码, pageSize: 每页条数}
 * @returns 返回分页数据 {
 *   records: 数据记录数组,
 *   total: 总记录数,
 *   size: 每页条数,
 *   current: 当前页码
 * }
 */
export async function queryDpRelmstFixAccDtoPageInfo(mainContrNo: string, zoneVal: string, pageInfoKey: string, params: { current: number; pageSize: number }) {
  return request(`/testtool/accQuery/dpRelmstFixAccDtoPageInfoQuery`, {
    method: 'GET',
    params: {
      mainContrNo,
      zoneVal,
      pageInfoKey,
      current: params.current,
      pageSize: params.pageSize
    },
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

// 账户绑定关系登记表 - 查询
export async function getDprgtAccBindRelat(prodtContractNo: string, zoneVal: string, pageInfoKey: string, params: { current: number; pageSize: number }) {
  return request('/testtool/accQuery/dprgtAccBindRelatQuery', {
    method: 'GET',
    params: {
      prodtContractNo,
      zoneVal,
      pageInfoKey,
      ...params,
    },
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

// 账户绑定关系登记表 - 插入
export async function insertDprgtAccBindRelat(params: DprgtAccBindRelat) {
  return request('/testtool/accQuery/dprgtAccBindRelatInsert', {
    method: 'POST',
    data: {
      ...params,
    },
  });
}

// 账户绑定关系登记表 - 更新
export async function updateDprgtAccBindRelat(params: DprgtAccBindRelat) {
  return request('/testtool/accQuery/dprgtAccBindRelatUpdate', {
    method: 'POST',
    data: {
      ...params,
    },
  });
}

// 账户绑定关系登记表 - 删除
export async function deleteDprgtAccBindRelat(params: DprgtAccBindRelat) {
  return request('/testtool/accQuery/dprgtAccBindRelatDelete', {
    method: 'POST',
    data: {
      ...params,
    },
  });
}

/**
 * 查询资金用途管控明细表
 *
 * @param persInnerAccno 个人内部账号
 * @param zoneVal 分片值
 * @param pageInfoKey 页面信息键
 * @param params 查询参数，包括分页信息
 * @returns 返回查询结果，包括分页信息
 */
export async function getDpdtlFundsUsageControl(persInnerAccno: string, zoneVal: string, pageInfoKey: string, params: { current: number; pageSize: number }) {
  return request(`/testtool/accQuery/dpdtlFundsUsageControlQuery`, {
    method: 'GET',
    params: {
      persInnerAccno,
      zoneVal,
      pageInfoKey,
      current: params.current,
      pageSize: params.pageSize
    },
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * 插入资金用途管控明细
 *
 * @param params 资金用途管控明细数据
 * @returns 返回操作结果
 */
export async function insertDpdtlFundsUsageControl(params: TbDpdtlFundsUsageControl) {
  return request('/testtool/accQuery/dpdtlFundsUsageControlInsert', {
    method: 'POST',
    data: params,
  });
}

/**
 * 更新资金用途管控明细
 *
 * @param params 资金用途管控明细数据
 * @returns 返回操作结果
 */
export async function updateDpdtlFundsUsageControl(params: TbDpdtlFundsUsageControl) {
  return request('/testtool/accQuery/dpdtlFundsUsageControlUpdate', {
    method: 'POST',
    data: params,
  });
}

/**
 * 删除资金用途管控明细
 *
 * @param params 资金用途管控明细数据
 * @returns 返回操作结果
 */
export async function deleteDpdtlFundsUsageControl(params: TbDpdtlFundsUsageControl) {
  return request('/testtool/accQuery/dpdtlFundsUsageControlDelete', {
    method: 'POST',
    data: params,
  });
}

// 查询资金用途管控表
export async function getDprgtFundsUsageControl(persInnerAccno: string, zoneVal: string, pageInfoKey: string, params: { current: number; pageSize: number }) {
  return request(`/testtool/accQuery/dprgtFundsUsageControlQuery`, {
    method: 'GET',
    params: {
      persInnerAccno,
      zoneVal,
      pageInfoKey,
      current: params.current,
      pageSize: params.pageSize
    },
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

// 新增资金用途管控表数据
export async function insertDprgtFundsUsageControl(params: TbDprgtFundsUsageControl) {
  return request('/testtool/accQuery/dprgtFundsUsageControlInsert', {
    method: 'POST',
    data: params,
  });
}

// 修改资金用途管控表数据
export async function updateDprgtFundsUsageControl(params: TbDprgtFundsUsageControl) {
  return request('/testtool/accQuery/dprgtFundsUsageControlUpdate', {
    method: 'POST',
    data: params,
  });
}

// 删除资金用途管控表数据
export async function deleteDprgtFundsUsageControl(params: TbDprgtFundsUsageControl) {
  return request('/testtool/accQuery/dprgtFundsUsageControlDelete', {
    method: 'POST',
    data: params,
  });
}

// 获取资金用途管控借据表数据
export async function getDprgtFundsUsageControlLoan(persInnerAccno: string, zoneVal: string, pageInfoKey: string, params: { current: number; pageSize: number }) {
  return request(`/testtool/accQuery/dprgtFundsUsageControlLoanQuery`, {
    method: 'GET',
    params: {
      persInnerAccno,
      zoneVal,
      pageInfoKey,
      current: params.current,
      pageSize: params.pageSize
    },
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

// 添加资金用途管控借据表记录
export async function insertDprgtFundsUsageControlLoan(params: TbDprgtFundsUsageControlLoan) {
  return request('/testtool/accQuery/dprgtFundsUsageControlLoanInsert', {
    method: 'POST',
    data: params,
  });
}

// 更新资金用途管控借据表记录
export async function updateDprgtFundsUsageControlLoan(params: TbDprgtFundsUsageControlLoan) {
  return request('/testtool/accQuery/dprgtFundsUsageControlLoanUpdate', {
    method: 'POST',
    data: params,
  });
}

// 删除资金用途管控借据表记录
export async function deleteDprgtFundsUsageControlLoan(params: TbDprgtFundsUsageControlLoan) {
  return request('/testtool/accQuery/dprgtFundsUsageControlLoanDelete', {
    method: 'POST',
    data: params,
  });
}

/**
 * 查询资金用途管控借据明细表
 *
 * @param persInnerAccno 个人内部账号
 * @param zoneVal 分片值
 * @param pageInfoKey 页面信息键
 * @param params 查询参数，包括分页信息
 * @returns 返回查询结果，包括分页信息
 */
export async function getDpdtlFundsUsageControlLoan(persInnerAccno: string, zoneVal: string, pageInfoKey: string, params: { current: number; pageSize: number }) {
  return request(`/testtool/accQuery/dpdtlFundsUsageControlLoanQuery`, {
    method: 'GET',
    params: {
      persInnerAccno,
      zoneVal,
      pageInfoKey,
      current: params.current,
      pageSize: params.pageSize
    },
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

/**
 * 插入资金用途管控借据明细
 *
 * @param params 资金用途管控借据明细数据
 * @returns 返回操作结果
 */
export async function insertDpdtlFundsUsageControlLoan(params: TbDpdtlFundsUsageControlLoan) {
  return request('/testtool/accQuery/dpdtlFundsUsageControlLoanInsert', {
    method: 'POST',
    data: params,
  });
}

/**
 * 更新资金用途管控借据明细
 *
 * @param params 资金用途管控借据明细数据
 * @returns 返回操作结果
 */
export async function updateDpdtlFundsUsageControlLoan(params: TbDpdtlFundsUsageControlLoan) {
  return request('/testtool/accQuery/dpdtlFundsUsageControlLoanUpdate', {
    method: 'POST',
    data: params,
  });
}

/**
 * 删除资金用途管控借据明细
 *
 * @param params 资金用途管控借据明细数据
 * @returns 返回操作结果
 */
export async function deleteDpdtlFundsUsageControlLoan(params: TbDpdtlFundsUsageControlLoan) {
  return request('/testtool/accQuery/dpdtlFundsUsageControlLoanDelete', {
    method: 'POST',
    data: params,
  });
}

// 查询账户星级登记簿
export async function getDprgtAccStar(persInnerAccno: string, zoneVal: string, pageInfoKey: string, params: { current: number; pageSize: number }) {
  return request(`/testtool/accQuery/dprgtAccStarQuery`, {
    method: 'GET',
    params: {
      persInnerAccno,
      zoneVal,
      pageInfoKey,
      current: params.current,
      pageSize: params.pageSize
    },
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

// 插入账户星级登记簿
export async function insertDprgtAccStar(params: DprgtAccStar) {
  return request('/testtool/accQuery/dprgtAccStarInsert', {
    method: 'POST',
    data: params,
  });
}

// 更新账户星级登记簿
export async function updateDprgtAccStar(params: DprgtAccStar) {
  return request('/testtool/accQuery/dprgtAccStarUpdate', {
    method: 'POST',
    data: params,
  });
}

// 删除账户星级登记簿
export async function deleteDprgtAccStar(params: DprgtAccStar) {
  return request('/testtool/accQuery/deleteDprgtAccStar', {
    method: 'POST',
    data: {
      ...params,
    },
  });
}

// 查询活期产品合约账户结转余额表
export async function getDprgtCarryBalCalInt(persInnerAccno: string, zoneVal: string, pageInfoKey: string, params: { current: number; pageSize: number }) {
  return request(`/testtool/accQuery/dprgtCarryBalCalIntQuery`, {
    method: 'GET',
    params: {
      persInnerAccno,
      zoneVal,
      pageInfoKey,
      current: params.current,
      pageSize: params.pageSize
    },
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

// 插入活期产品合约账户结转余额表
export async function insertDprgtCarryBalCalInt(params: DprgtCarryBalCalInt) {
  return request('/testtool/accQuery/dprgtCarryBalCalIntInsert', {
    method: 'POST',
    data: params,
  });
}

// 更新活期产品合约账户结转余额表
export async function updateDprgtCarryBalCalInt(params: DprgtCarryBalCalInt) {
  return request('/testtool/accQuery/dprgtCarryBalCalIntUpdate', {
    method: 'POST',
    data: params,
  });
}

// 删除活期产品合约账户结转余额表
export async function deleteDprgtCarryBalCalInt(params: DprgtCarryBalCalInt) {
  return request('/testtool/accQuery/dprgtCarryBalCalIntDelete', {
    method: 'POST',
    data: params,
  });
}

// 查询存款证明
export async function getDprgtDepProve(custNo: string, zoneVal: string, pageInfoKey: string, params: { current: number; pageSize: number }) {
  return request(`/testtool/accQuery/dprgtDepProveQuery`, {
    method: 'GET',
    params: {
      custNo,
      zoneVal,
      pageInfoKey,
      current: params.current,
      pageSize: params.pageSize
    },
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

// 插入存款证明
export async function insertDprgtDepProve(params: TbDprgtDepProve) {
  return request('/testtool/accQuery/dprgtDepProveInsert', {
    method: 'POST',
    data: params,
  });
}

// 更新存款证明
export async function updateDprgtDepProve(params: TbDprgtDepProve) {
  return request('/testtool/accQuery/dprgtDepProveUpdate', {
    method: 'POST',
    data: params,
  });
}

// 删除存款证明
export async function deleteDprgtDepProve(params: TbDprgtDepProve) {
  return request('/testtool/accQuery/dprgtDepProveDelete', {
    method: 'POST',
    data: params,
  });
}

// 获取客户限制服务登记簿列表
export async function getDprgtCustLimtServ(custNo: string, zoneVal: string, pageInfoKey: string, params: { current: number; pageSize: number }) {
  return request(`/testtool/accQuery/dprgtCustLimtServQuery`, {
    method: 'GET',
    params: {
      custNo,
      zoneVal,
      pageInfoKey,
      current: params.current,
      pageSize: params.pageSize
    },
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

// 添加客户限制服务登记
export async function insertDprgtCustLimtServ(params: TbDprgtCustLimtServ) {
  return request('/testtool/accQuery/dprgtCustLimtServInsert', {
    method: 'POST',
    data: params,
  });
}

// 更新客户限制服务登记
export async function updateDprgtCustLimtServ(params: TbDprgtCustLimtServ) {
  return request('/testtool/accQuery/dprgtCustLimtServUpdate', {
    method: 'POST',
    data: params,
  });
}

// 删除客户限制服务登记
export async function deleteDprgtCustLimtServ(params: TbDprgtCustLimtServ) {
  return request('/testtool/accQuery/dprgtCustLimtServDelete', {
    method: 'POST',
    data: params,
  });
}

// 查询存款证明开立信息登记
export async function queryTbDprgtDepProveOpenInfo(persInnerAccno: string, zoneVal: string, pageInfoKey: string, params: { current: number; pageSize: number }) {
  return request(`/testtool/accQuery/dprgtDepProveOpenInfoQuery`, {
    method: 'GET',
    params: {
      persInnerAccno,
      zoneVal,
      pageInfoKey,
      current: params.current,
      pageSize: params.pageSize
    },
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

// 添加存款证明开立信息登记
export async function tbDprgtDepProveOpenInfoAdd(params: TbDprgtDepProveOpenInfo) {
  return request('/testtool/accQuery/dprgtDepProveOpenInfoInsert', {
    method: 'POST',
    data: params,
  });
}

// 修改存款证明开立信息登记
export async function tbDprgtDepProveOpenInfoMod(params: TbDprgtDepProveOpenInfo) {
  return request('/testtool/accQuery/dprgtDepProveOpenInfoUpdate', {
    method: 'POST',
    data: params,
  });
}

// 删除存款证明开立信息登记
export async function tbDprgtDepProveOpenInfoDel(params: TbDprgtDepProveOpenInfo) {
  return request('/testtool/accQuery/dprgtDepProveOpenInfoDelete', {
    method: 'POST',
    data: params,
  });
}

// 根据冻结编号和分片值查询冻结记录详情
export async function queryDprgtFrzDetail(frzNo: string, zoneVal: string) {
  return request('/testtool/accQuery/dprgtFrzQueryByFrzNo', {
    method: 'GET',
    params: {
      frzNo,
      zoneVal,
    },
  });
}