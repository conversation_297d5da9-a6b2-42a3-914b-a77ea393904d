import React, { useState, useEffect } from "react";
import { KeepAlive, useActivate, useUnactivate } from "react-activation";
import { storage } from "@/utils/storageUtils";
import {
  ProForm,
  ProFormText,
} from "@ant-design/pro-components";
import { Card, Typography, Form, message, Spin, Row, Col } from "antd";
import "./style.less";
import { queryCustomerRoute } from "./service";
import type { RouteQueryResult } from "./data.d";

const { Paragraph } = Typography;

interface RouteQueryInfo {
  result: RouteQueryResult | null;
}

const RouteQuery: React.FC = () => {
  const [form] = Form.useForm();
  // 添加动画状态
  const [isVisible, setIsVisible] = useState(false);
  // 添加一个key状态用于强制重新渲染ProForm
  const [formKey, setFormKey] = useState(Date.now());
  // 添加新状态用于强制更新结果区域
  const [resultKey, setResultKey] = useState(Date.now());

  // 添加数据验证函数
  const validateInput = (value: string) => {
    if (!value) return true; // 允许为空
    if (value.length > 50) return false; // 添加长度限制
    return /^[a-zA-Z0-9]+$/.test(value);
  };

  // 从localStorage初始化route_query_response
  const [routeQueryInfo, setRouteQueryInfo] = useState<RouteQueryInfo>(() => {
    const savedInfo = storage.get("route_query_response");
    if (savedInfo) {
      return savedInfo;
    }
    return {
      result: null,
    };
  });

  // 添加初始化 useEffect
  useEffect(() => {
    const savedSearch = storage.get("route_query_search");
    if (savedSearch) {
      form.setFieldsValue(savedSearch);
    }
    const savedInfo = storage.get("route_query_response");
    if (savedInfo) {
      setRouteQueryInfo(savedInfo);
    }
    // 添加动画触发
    setTimeout(() => {
      setIsVisible(true);
    }, 100);
  }, [form]);

  // 监听 routeQueryInfo 变化
  useEffect(() => {
    if (routeQueryInfo && routeQueryInfo.result) {
      storage.set("route_query_response", routeQueryInfo, 30 * 60);
      // 强制更新结果区域
      setResultKey(Date.now());
    }
  }, [routeQueryInfo]);

  // 修改 useActivate 的实现
  useActivate(() => {
    // 使用 setTimeout 确保在组件完全激活后再加载数据
    setTimeout(() => {
      const savedSearch = storage.get("route_query_search");
      if (savedSearch) {
        form.setFieldsValue(savedSearch);
      }
      const savedInfo = storage.get("route_query_response");
      if (savedInfo) {
        setRouteQueryInfo(savedInfo);
      }
      setIsVisible(true);
      // 重新生成key以强制重新渲染表单
      setFormKey(Date.now());
    }, 0);
  });

  // 添加 useUnactivate 清理逻辑
  useUnactivate(() => {
    setLoading(false);
    setError(null);
    setIsVisible(false);
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 定义结果展示项
  const resultItems = [
    { label: "介质编号", key: "mediumNo" },
    { label: "客户编号", key: "custNo" },
    { label: "路由分片", key: "routeId" },
    { label: "客户分片", key: "shardingId" },
  ];

  return (
    <KeepAlive>
      <div className={`app-container ${isVisible ? 'fade-in-up' : ''}`}>
        <div
          className="content-wrapper"
          style={{
            maxWidth: "100%", // 设置最大宽度为100%以贴近浏览器边角
            minWidth: "800px", // 设置最小宽度
            margin: "0 auto", // 水平居中
            padding: "0 16px", // 减小左右padding，使内容更贴近边缘
            width: "100%", // 占满可用宽度
          }}
        >
          <div className={`card-container ${isVisible ? 'fade-in-up' : ''}`} style={{ animationDelay: '0.2s' }}>
            <Card
              title="路由查询"
              bordered={false}
              className="input-section"
              style={{
                marginBottom: "24px",
                marginTop: "24px",
                height: "min-content",
                padding: "24px",
                backgroundColor: "#ffffff",
                borderRadius: "16px", // 确保圆角一致
              }}
            >
              <ProForm
                key={formKey} // 添加key属性以强制重新渲染
                form={form}
                onValuesChange={(changedValues, allValues) => {
                  // 互斥逻辑：如果修改了介质编号，则清空客户编号
                  if (changedValues.mediumNo !== undefined && changedValues.mediumNo) {
                    form.setFieldsValue({ custNo: '' });
                  }
                  // 互斥逻辑：如果修改了客户编号，则清空介质编号
                  if (changedValues.custNo !== undefined && changedValues.custNo) {
                    form.setFieldsValue({ mediumNo: '' });
                  }
                  // 设置30分钟后过期
                  storage.set("route_query_search", form.getFieldsValue(), 30 * 60);
                }}
                onFinish={async (values) => {
                  console.log('提交查询，参数:', values); // 添加调试日志
                  setLoading(true);
                  setError(null);
                  try {
                    // 检查是否至少有一个字段有值
                    if ((!values.mediumNo || values.mediumNo.trim() === '') &&
                        (!values.custNo || values.custNo.trim() === '')) {
                      message.error("请至少填写介质编号或客户编号中的一个");
                      setLoading(false);
                      return false;
                    }

                    // 确保两个字段都有定义，即使是空字符串
                    const requestData = {
                      mediumNo: values.mediumNo?.trim() || '',
                      custNo: values.custNo?.trim() || '',
                    };

                    console.log('准备发送请求，数据:', requestData); // 添加调试日志
                    const res = await queryCustomerRoute(requestData);
                    console.log('请求返回结果:', res); // 添加调试日志

                    if (res.code === 200 && res.data) {
                      message.success("查询成功");
                      try {
                        // 将返回的JSON字符串解析为对象
                        const result = typeof res.data === 'string' ? JSON.parse(res.data) : res.data;
                        setRouteQueryInfo({
                          result: result,
                        });
                        storage.set(
                          "route_query_response",
                          {
                            result: result,
                          },
                          30 * 60
                        );
                        // 强制更新结果区域
                        setResultKey(Date.now());
                      } catch (parseError) {
                        message.error("返回数据格式错误");
                        return false;
                      }
                    } else {
                      message.error(res.msg || "查询失败");
                    }
                    return true;
                  } catch (err: any) {
                    const errorMessage = err?.message || "查询失败";
                    setError(errorMessage);
                    message.error(errorMessage);
                    return false;
                  } finally {
                    setLoading(false);
                  }
                }}
                submitter={{
                  searchConfig: {
                    submitText: "查询",
                    resetText: "重置",
                  },
                  submitButtonProps: {
                    style: { minWidth: "80px" },
                  },
                  resetButtonProps: {
                    style: { minWidth: "80px" },
                    onClick: () => {
                      form.resetFields();
                      setRouteQueryInfo({
                        result: null,
                      });
                      storage.remove("route_query_search");
                      storage.remove("route_query_response");
                    },
                  },
                  render: (props, dom) => {
                    return (
                      <div
                        className="button-container"
                        style={{
                          display: "flex",
                          gap: "12px",
                          marginTop: "24px",
                          justifyContent: "flex-start", // 改为左对齐
                          marginLeft: "24px",  // 使用 marginLeft 替代 paddingLeft
                        }}
                      >
                        {dom.map((btn, index) => (
                          <span key={index} style={{ marginRight: index === 0 ? '12px' : '0' }}>
                            {btn}
                          </span>
                        ))}
                      </div>
                    );
                  },
                }}
              >
                <ProForm.Group style={{ display: 'flex', flexDirection: 'row', gap: '24px' }}>
                  <ProFormText
                    name="mediumNo"
                    label="介质编号"
                    placeholder="请输入介质编号"
                    rules={[
                      { max: 50, message: "介质编号长度不能超过50个字符" },
                      {
                        pattern: /^[a-zA-Z0-9]*$/,
                        message: "介质编号只能包含数字和大小写字母",
                      },
                      {
                        validator: async (_, value) => {
                          if (value && !validateInput(value)) {
                            throw new Error("介质编号格式不正确");
                          }
                        },
                      },
                    ]}
                    width="md"
                    fieldProps={{
                      className: "hover-input",
                      onChange: (e) => {
                        const value = e.target.value.replace(/[^a-zA-Z0-9]/g, "");
                        e.target.value = value;
                        // 如果输入了介质编号，则清空客户编号
                        if (value) {
                          form.setFieldsValue({ custNo: '' });
                        }
                      },
                    }}
                  />
                  <ProFormText
                    name="custNo"
                    label="客户编号"
                    placeholder="请输入客户编号"
                    rules={[
                      { max: 50, message: "客户编号长度不能超过50个字符" },
                      {
                        pattern: /^[a-zA-Z0-9]*$/,
                        message: "客户编号只能包含数字和大小写字母",
                      },
                      {
                        validator: async (_, value) => {
                          if (value && !validateInput(value)) {
                            throw new Error("客户编号格式不正确");
                          }
                        },
                      },
                    ]}
                    width="md"
                    fieldProps={{
                      className: "hover-input",
                      onChange: (e) => {
                        const value = e.target.value.replace(/[^a-zA-Z0-9]/g, "");
                        e.target.value = value;
                        // 如果输入了客户编号，则清空介质编号
                        if (value) {
                          form.setFieldsValue({ mediumNo: '' });
                        }
                      },
                    }}
                  />
                </ProForm.Group>
              </ProForm>
            </Card>
          </div>

          <div key={resultKey} className={`card-container ${isVisible ? 'fade-in-up' : ''}`} style={{ animationDelay: '0.4s' }}>
            <Card
              title="查询结果"
              bordered={false}
              className="result-section"
              style={{
                height: "min-content",
                padding: "24px",
                backgroundColor: "#ffffff",
                marginBottom: "24px",
                position: "relative", // 添加相对定位
                borderRadius: "16px", // 确保圆角一致
              }}
            >
              {loading ? (
                <div style={{ textAlign: "center", padding: "40px" }}>
                  <Spin size="large" tip="处理中..." />
                </div>
              ) : error ? (
                <div
                  style={{ textAlign: "center", padding: "20px", color: "red" }}
                >
                  <Typography.Text>{error}</Typography.Text>
                </div>
              ) : routeQueryInfo.result ? (
                <div className="result-animation">
                  <Row gutter={[24, 16]}>
                    {resultItems.map((item, index) => (
                      <Col span={8} key={item.key}>
                        <Card
                          size="small"
                          title={item.label}
                          className="result-item-card"
                          style={{
                            borderRadius: "8px",
                            animation: `fadeInUp 0.5s ease-out forwards ${0.1 * index}s`
                          }}
                        >
                          <Paragraph copyable>
                            {routeQueryInfo.result?.[item.key as keyof RouteQueryResult] || "暂无数据"}
                          </Paragraph>
                        </Card>
                      </Col>
                    ))}
                  </Row>
                </div>
              ) : (
                <div style={{ textAlign: "center", padding: "40px" }}>
                  <Typography.Text type="secondary">暂无数据，请提交查询</Typography.Text>
                </div>
              )}
            </Card>
          </div>
        </div>
      </div>
    </KeepAlive>
  );
};

export default RouteQuery;