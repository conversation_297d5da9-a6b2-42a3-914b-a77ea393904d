import request from '@/utils/request';
import type { RouteQueryParams } from './data.d';

// 客户路由查询
export async function queryCustomerRoute(params: RouteQueryParams) {
  console.log('开始执行查询请求，参数:', params);
  try {
    const queryParams: Record<string, string> = {};
    
    // 确保只有有值的参数才会被添加到查询字符串中
    if (params?.mediumNo && params.mediumNo.trim() !== '') {
      queryParams.mediumNo = params.mediumNo.trim();
    }
    
    if (params?.custNo && params.custNo.trim() !== '') {
      queryParams.custNo = params.custNo.trim();
    }
    
    // 检查是否至少有一个参数
    if (Object.keys(queryParams).length === 0) {
      throw new Error('请至少提供一个查询参数');
    }
    
    const queryString = new URLSearchParams(queryParams).toString();
    
    // 添加更多错误处理
    try {
      const response = await request(`/testtool/routeQuery?${queryString}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json;charset=UTF-8',
        },
        // 添加超时设置
        timeout: 30000, // 30秒超时
      });
      return response;
    } catch (requestError) {
      console.error('请求失败:', requestError);
      throw requestError;
    }
  } catch (error) {
    console.error('查询客户路由出错:', error);
    throw error; // 重新抛出错误以便调用方处理
  }
}
