@import '~antd/es/style/themes/default.less';

// 添加容器动画样式
.container {
  padding: 20px;
  animation: fadeInUp 0.5s ease-out;
}

// 添加卡片样式
.card {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border-radius: 16px !important; // 增加 !important 提高优先级
  transition: all 0.3s;
  overflow: hidden;

  &:hover {
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
  }

  // 确保Card内部元素也有圆角
  :global {
    .ant-card-body {
      padding: 24px;
      border-radius: 16px !important; // 增加 !important 提高优先级
    }
    
    .ant-card-head {
      border-bottom: 1px solid #f0f0f0;
      border-radius: 16px 16px 0 0 !important; // 增加 !important 提高优先级
    }
    
    // 添加卡片内容区域的圆角保护
    .ant-card-bordered {
      border-radius: 16px !important;
    }
    
    // 确保卡片容器也有圆角
    .ant-card {
      border-radius: 16px !important;
    }
  }
}

// 标题容器样式
.titleContainer {
  animation: slideInLeft 0.5s ease-out;
}

// 表单项动画
.formItem {
  animation: fadeIn 0.5s ease-out;
  margin-bottom: 16px;
}

// 按钮容器动画
.buttonContainer {
  display: flex;
  justify-content: center;
  margin-top: 24px;
  animation: fadeInUp 0.5s ease-out;
}

// 添加动画关键帧
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

// 文本区域样式
.textArea {
  border-radius: 8px;
  resize: none;
  transition: all 0.3s;

  &:hover, &:focus {
    border-color: @primary-color;
    box-shadow: 0 0 0 2px fade(@primary-color, 20%);
  }
}

// 圆角选择器样式
.roundedSelect {
  :global {
    .ant-select-selector {
      border-radius: 12px !important; // 从8px增加到12px，使边角更圆
    }
    
    // 确保清除图标正常显示和工作
    .ant-select-clear {
      opacity: 0.3;
      
      &:hover {
        opacity: 1;
      }
    }
  }
}

:global {
  // 表格样式优化
  .ant-pro-table {
    .ant-pro-table-search {
      margin-bottom: 12px;
      padding: 24px 24px 0;
      background-color: #fff;
      border-radius: 16px !important; // 从12px增加到16px
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
    }
    
    .ant-card {
      border-radius: 12px !important; // 增加 !important 提高优先级
      overflow: hidden;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
    }
    
    .ant-table-container {
      border-radius: 12px !important; // 增加 !important 提高优先级
      overflow: hidden;
    }
    
    // 添加表格包装器的圆角
    .ant-table-wrapper {
      border-radius: 12px !important;
      overflow: hidden;
    }
    
    // 添加表格本身的圆角
    .ant-table {
      border-radius: 12px !important;
      overflow: hidden;
    }
    
    .ant-table-thead > tr > th {
      background-color: #fafafa;
    }
    
    .ant-pagination {
      margin: 16px 0;
    }
    
    .ant-form-item-control-input {
      min-height: 32px;
    }
    
    .ant-form-item {
      margin-bottom: 24px;
    }
  }
  
  // 按钮样式
  .ant-btn {
    border-radius: 4px;
    
    &.ant-btn-primary {
      background-color: @primary-color;
      border-color: @primary-color;
      
      &:hover, &:focus {
        background-color: lighten(@primary-color, 10%);
        border-color: lighten(@primary-color, 10%);
      }
    }
  }
  
  // 表单项样式
  .ant-form-item-label > label {
    color: rgba(0, 0, 0, 0.85);
    font-weight: 500;
  }
  
  // 搜索表单样式
  .ant-pro-table-search {
    .ant-form-item {
      margin-bottom: 16px !important;
    }
    
    .ant-form-item-label {
      padding-bottom: 8px !important;
    }
  }
  
  // 输入框样式优化
  .ant-input {
    border-radius: 12px !important; // 保持外部边框圆角
    transition: all 0.3s;
    
    &:hover, &:focus {
      border-color: @primary-color;
      box-shadow: 0 0 0 2px fade(@primary-color, 20%);
    }
  }
  
  // 为所有表单控件添加更圆的边角
  .ant-form-item-control-input-content {
    .ant-input-number,
    .ant-picker,
    .ant-input-affix-wrapper {
      border-radius: 12px !important;
      overflow: hidden;
    }
  }
  
  // 确保下拉菜单也有圆角
  .ant-select-dropdown {
    border-radius: 12px !important;
    overflow: hidden;
  }
  
  // 确保按钮也有更圆的边角
  .ant-btn {
    border-radius: 12px; // 从4px增加到12px
    
    &.ant-btn-primary {
      background-color: @primary-color;
      border-color: @primary-color;
      
      &:hover, &:focus {
        background-color: lighten(@primary-color, 10%);
        border-color: lighten(@primary-color, 10%);
      }
    }
  }
  
  // 表格行动画效果
  .ant-table-tbody > tr {
    transition: all 0.3s;
    
    &:hover {
      background-color: fade(@primary-color, 5%);
    }
  }
  
  // 表格单元格样式
  .ant-table-cell {
    padding: 16px 8px;
  }

  // 修复输入框内部文本区域的边角问题
  .ant-input-wrapper {
    .ant-input {
      border-radius: 0 !important; // 内部输入框使用直角
    }
  }
  
  // 确保输入框内部元素使用直角
  .ant-input-affix-wrapper > input.ant-input {
    border-radius: 0 !important;
  }
  
  // 修复前缀后缀输入框的内部元素
  .ant-input-affix-wrapper .ant-input {
    border-radius: 0 !important;
  }
  
  // 修复搜索框内部输入框
  .ant-input-search .ant-input {
    border-radius: 0 !important;
  }
  
  // 修复数字输入框内部元素
  .ant-input-number-input {
    border-radius: 0 !important;
  }
  
  // 确保输入框获得焦点时内部元素仍然是直角
  .ant-input:focus {
    border-radius: 12px !important; // 外部保持圆角
  }
  
  // 确保输入框内部的文本区域不受圆角影响
  .ant-input-affix-wrapper-focused .ant-input,
  .ant-input-affix-wrapper:focus .ant-input,
  .ant-input-affix-wrapper:hover .ant-input {
    border-radius: 0 !important;
  }
}

// 特殊字段样式
.mediumNoCell {
  font-family: 'Consolas', 'Monaco', monospace;
  color: #1890ff;
}

.msgCdCell {
  font-weight: 500;
  color: #52c41a;
}

.noticeMsgNoCell {
  font-family: 'Consolas', 'Monaco', monospace;
  color: #722ed1;
}

.accountingDateCell {
  color: #fa8c16;
  font-weight: 500;
}

// 添加原始数据相关样式
.emptyCell {
  color: #d9d9d9;
}

.viewMessageButton {
  padding: 0 8px;
  height: auto;
  
  &:hover {
    color: @primary-color;
    background-color: fade(@primary-color, 5%);
  }
}

// 消息模态框样式
.messageModal {
  :global {
    .ant-modal-content {
      border-radius: 16px;
      overflow: hidden;
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    }
    
    .ant-modal-header {
      border-radius: 16px 16px 0 0;
      border-bottom: 1px solid #f0f0f0;
      padding: 16px 24px;
    }
    
    .ant-modal-body {
      padding: 24px;
      max-height: 70vh;
      overflow-y: auto;
    }
    
    .ant-modal-footer {
      border-radius: 0 0 16px 16px;
      border-top: 1px solid #f0f0f0;
      padding: 12px 24px;
    }
    
    .ant-modal-close {
      color: rgba(0, 0, 0, 0.45);
      
      &:hover {
        color: rgba(0, 0, 0, 0.75);
      }
    }
  }
}

.messageContent {
  position: relative;
}

.copyButton {
  position: absolute;
  top: 0;
  right: 0;
  margin: 0;
}

.messageText {
  margin-top: 32px;
  padding: 16px;
  background-color: #f5f5f5;
  border-radius: 8px;
  font-family: 'Consolas', 'Monaco', monospace;
  white-space: pre-wrap;
  word-break: break-all;
  max-height: 60vh;
  overflow-y: auto;
  font-size: 14px;
  line-height: 1.6;
  border: 1px solid #e8e8e8;
}

// 自定义关闭按钮样式
.closeButton {
  background-color: #13c2c2; // 使用蓝绿色
  color: white;
  border: none;
  border-radius: 12px;
  padding: 0 16px;
  height: 32px;
  transition: all 0.3s;
  
  &:hover, &:focus {
    background-color: #36cfc9; // 悬停时颜色变浅
    color: white;
    border: none;
  }
}

// Add the roundButton style to match LimitGloTrackBussNoQuery
.roundButton {
  border-radius: 12px;
  margin-right: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}