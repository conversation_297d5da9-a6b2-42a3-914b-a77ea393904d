import React, { useState, useRef, useEffect } from "react";
import { Button, message, Row, Col, Card, Typography, Modal } from 'antd';
import {
  SearchOutlined,
  ReloadOutlined,
  EyeOutlined,
} from "@ant-design/icons";
import { useIntl, FormattedMessage } from "umi";
import type { ProColumns, ActionType } from "@ant-design/pro-table";
import ProTable from "@ant-design/pro-table";
import type { FormInstance } from "antd";
import WrapContent from "@/components/WrapContent";
import { KeepAlive } from "react-activation";
import { storage } from "@/utils/storageUtils";
import type { MsgResult } from "./data.d";
import { msgQuery, msgTypeMap } from "./service";
import styles from "./style.less";
const { Title, Paragraph } = Typography;

interface FormValueType {
  globalBusiTrackNo?: string;
  custNo?: string;
}

const MsgQuery: React.FC = () => {
  const formTableRef = useRef<FormInstance>();
  const actionRef = useRef<ActionType>();
  const [formValues, setFormValues] = useState<FormValueType>({});
  const [tableData, setTableData] = useState<MsgResult[]>([]);
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const [isFirstRequest, setIsFirstRequest] = useState(true);
  const [manualSearch, setManualSearch] = useState(false);

  const intl = useIntl();

  // 搜索表单状态初始化
  const [searchForm, setSearchForm] = useState(() => {
    const savedSearch = storage.get("msgquery_search");
    return savedSearch || {};
  });

  // 加载缓存数据的函数
  const loadCacheData = () => {
    try {
      const savedData = storage.get("msgquery_data");
      const savedSearch = storage.get("msgquery_search");

      if (savedData?.records && savedSearch) {
        const records = Array.isArray(savedData.records)
          ? savedData.records
          : [];

        // 设置表单值
        setFormValues(savedSearch);
        
        // 确保表单值被正确设置
        setTimeout(() => {
          if (formTableRef.current) {
            formTableRef.current.setFieldsValue(savedSearch || {});
          }
        }, 100);
        
        setTableData(records);
      }
    } catch (error) {
      console.error("加载缓存数据失败:", error);
    }
    setIsInitialLoad(false);
  };

  // 初始化数据
  useEffect(() => {
    loadCacheData();
    
    // 添加一个延迟设置表单值的操作，确保表单已经渲染完成
    const timer = setTimeout(() => {
      const savedSearch = storage.get("msgquery_search");
      if (savedSearch && formTableRef.current) {
        formTableRef.current.setFieldsValue(savedSearch);
      }
    }, 300);
    
    return () => {
      clearTimeout(timer);
      setIsFirstRequest(true);
      setIsInitialLoad(true);
    };
  }, []);

  // 处理重置
  const handleReset = (form: FormInstance | undefined) => {
    // 重置所有状态
    setFormValues({});
    setTableData([]);
    setIsFirstRequest(true);
    setManualSearch(false);
    
    // 完全清空搜索表单，包括分页信息
    setSearchForm({
      current: 1,
      pageSize: 10
    });

    // 重置表单
    setTimeout(() => {
      form?.resetFields();
      formTableRef.current?.resetFields();

      // 清除本地存储
      storage.set("msgquery_data", null, 0);
      storage.set("msgquery_search", null, 0);
      
      // 重置表格数据，但不触发新的请求
      if (actionRef.current) {
        actionRef.current.clearSelected?.();
      }
    }, 0);
  };

  // 处理搜索按钮点击
  const handleSearch = () => {
    formTableRef.current?.validateFields().then(values => {
      // 记录表单值，确保它们被正确传递
      setFormValues(values);
      
      // 重要：重置分页参数到第一页
      const newSearchForm = {
        ...values,
        current: 1,  // 重置为第一页
        pageSize: searchForm.pageSize || 10
      };
      
      // 一次性更新所有状态，避免多次渲染
      setSearchForm(newSearchForm);
      setManualSearch(true);
      setIsFirstRequest(false);
      
      // 使用 reload 方法
      actionRef.current?.reload();
    }).catch(error => {
      console.error('表单验证失败:', error);
    });
  };

  // 处理请求
  const handleRequest = async (params: any) => {
    // 如果是首次加载且未手动搜索，返回空数据
    if (isFirstRequest && !manualSearch) {
      return {
        data: [],
        total: 0,
        success: true,
      };
    }

    // 获取当前表单值，确保使用最新的表单数据
    const formData = formTableRef.current?.getFieldsValue() || {};

    const requestParams = {
      globalBusiTrackNo: formData.globalBusiTrackNo || "",
      custNo: formData.custNo || "",
    };

    try {
      // 更新缓存
      setSearchForm(formData);
      storage.set("msgquery_search", formData, 30 * 60);
      
      setIsFirstRequest(false);

      const res = await msgQuery(requestParams);
      // 添加日志，帮助调试
      console.log("响应结果:", res);

      if (res.code === 200) {
        const parsedData =
          typeof res.data === "string" ? JSON.parse(res.data) : res.data;
        const records = Array.isArray(parsedData)
          ? parsedData
          : [];
        const total = records.length || 0;

        storage.set("msgquery_data", { records, total }, 30 * 60);
        setTableData(records);
        return {
          data: records,
          total: total,
          success: true,
        };
      } else {
        message.error(res.msg || "查询失败");
      }
      return {
        data: [],
        total: 0,
        success: false,
      };
    } catch (error) {
      console.error("查询失败:", error);
      return {
        data: [],
        total: 0,
        success: false,
      };
    }
  };

  // 添加状态用于控制Modal的显示和内容
  const [messageModalVisible, setMessageModalVisible] = useState(false);
  const [currentMessage, setCurrentMessage] = useState('');
  const [messageTitle, setMessageTitle] = useState('');

  const columns: ProColumns<MsgResult>[] = [
    {
      title: (
        <FormattedMessage id="autoTest.msgquery.globalBusiTrackNo" defaultMessage="全局业务跟踪号" />
      ),
      dataIndex: "globalBusiTrackNo",
      valueType: "text",
      hideInTable: true,
      fieldProps: {
        style: {
          width: "300px",
          borderRadius: "8px",  
        },
        placeholder: "请输入流水号",
        onKeyPress: (e: React.KeyboardEvent<HTMLInputElement>) => {
          const regex = /[a-zA-Z0-9_\.]/;
          const key = String.fromCharCode(e.charCode);
          if (!regex.test(key)) {
            e.preventDefault();
          }
        },
        onChange: (e: React.ChangeEvent<HTMLInputElement>) => {
          const value = e.target.value;
          if (value && !/^[a-zA-Z0-9_\.]*$/.test(value)) {
            e.target.value = value.replace(/[^a-zA-Z0-9_\.]/g, '');
          }
        },
      },
      formItemProps: {
        rules: [
          {
            pattern: /^[a-zA-Z0-9_\.]*$/,
            message: '全局业务跟踪号只能包含大小写字母、数字',
          },
          {
            required: true,
            message: "请输入全局业务跟踪号",
          },
        ],
      },
    },
    {
      title: (
        <FormattedMessage id="autoTest.msgquery.custNo" defaultMessage="客户编号" />
      ),
      dataIndex: "custNo",
      valueType: "text",
      hideInTable: true,
      fieldProps: {
        style: {
          width: "300px",
          borderRadius: "8px",  
        },
        placeholder: "请输入客户编号",
      },
      formItemProps: {
        rules: [
          {
            required: true,
            message: "请输入客户编号",
          },
        ],
      },
    },
    {
      title: (
        <FormattedMessage id="autoTest.accquery.mediumNo" defaultMessage="介质编号" />
      ),
      dataIndex: "mediumNo",
      valueType: "text",
      hideInSearch: true,
      width: 220,
      align: "center",
      render: (text) => <span className={styles.mediumNoCell}>{text}</span>,
    },
    {
      title: (
        <FormattedMessage
          id="autoTest.msgquery.msgCd"
          defaultMessage="通知消息类型"
        />
      ),
      dataIndex: "msgCd",
      valueType: "text",
      hideInSearch: true,
      width: 300, // 增加宽度以适应更长的文本
      align: "center",
      render: (text) => {
        // 将消息代码转换为对应的消息类型描述
        const msgType = text ? msgTypeMap[text.toString()] || text : '-';
        return <span className={styles.msgCdCell}>{msgType}</span>;
      },
    },
    {
      title: (
        <FormattedMessage
          id="autoTest.msgquery.noticeMsgNo"
          defaultMessage="通知消息编号"
        />
      ),
      dataIndex: "noticeMsgNo",
      valueType: "text",
      hideInSearch: true,
      width: 220,
      align: "center",
      render: (text) => <span className={styles.noticeMsgNoCell}>{text}</span>,
    },
    {
      title: (
        <FormattedMessage id="autoTest.msgquery.accountingDate" defaultMessage="会计日期" />
      ),
      dataIndex: "accountingDate",
      valueType: "text",
      hideInSearch: true,
      width: 120,
      align: "center",
      render: (_, record) => {
        // 格式化日期显示
        const date = record.accountingDate;
        if (date && date.length === 8) {
          return <span className={styles.accountingDateCell}>
            {`${date.substring(0, 4)}-${date.substring(4, 6)}-${date.substring(6, 8)}`}
          </span>;
        }
        return <span className={styles.accountingDateCell}>{date}</span>;
      },
    },
    // 添加新列：原始数据
    {
      title: (
        <FormattedMessage id="autoTest.msgquery.message" defaultMessage="原始数据" />
      ),
      dataIndex: "message",
      valueType: "text",
      hideInSearch: true,
      width: 150,
      align: "center",
      render: (text, record) => {
        // 如果消息内容为空，则显示占位符
        if (!text) return <span className={styles.emptyCell}>-</span>;

        // 确保 text 是字符串类型
        const messageText = typeof text === 'string' ? text : JSON.stringify(text);

        // 截取前20个字符作为预览
        const preview = messageText.length > 20
          ? `${messageText.substring(0, 20)}...`
          : messageText;

        return (
          <Button
            type="link"
            className={styles.viewMessageButton}
            icon={<EyeOutlined />}
            onClick={() => {
              // 设置当前消息内容和标题，确保传入字符串
              setCurrentMessage(messageText);
              setMessageTitle("消息详情");
              // 显示模态框
              setMessageModalVisible(true);
            }}
          >
            {preview}
          </Button>
        );
      },
    },
  ];

  return (
    <KeepAlive
      saveScrollPosition="screen"
      when={() => true}
      cacheKey="msgquery"
      onActivate={() => {
        loadCacheData();
      }}
    >
      <WrapContent>
        <div className={styles.container}>
          <Card
            title={
              <div className={styles.titleContainer}>
                <Title level={4}>
                  <FormattedMessage
                    id="pages.searchTable.msgQueryTitle"
                    defaultMessage="消息查询"
                  />
                </Title>
              </div>
            }
            bordered={false}
            className={styles.card}
            style={{ borderRadius: '16px', overflow: 'hidden' }}
          >
            <div style={{ borderRadius: '16px', overflow: 'hidden' }}>
              <Row gutter={[16, 24]}>
                <Col lg={24} md={24}>
                  <ProTable<MsgResult>
                    headerTitle={false}
                    actionRef={actionRef}
                    formRef={formTableRef}
                    form={{
                      validateMessages: {
                        required: '${label}为必填项',
                      },
                      initialValues: formValues,
                      requiredMark: true,
                    }}
                    rowKey={(record, index) => index?.toString() || '0'}
                    key="msgQueryList"
                    search={{
                      labelWidth: 120,
                      defaultCollapsed: false,
                      span: 8,
                      optionRender: ({ searchText, resetText }, { form }) => [
                        <Button
                          type="primary"
                          key="search"
                          className={styles.roundButton}
                          icon={<SearchOutlined />}
                          onClick={() => handleSearch()}
                        >
                          查询
                        </Button>,
                        <Button
                          key="reset"
                          className={styles.roundButton}
                          icon={<ReloadOutlined />}
                          onClick={() => handleReset(form)}
                        >
                          重置
                        </Button>,
                      ],
                    }}
                    pagination={{
                      defaultPageSize: 10,
                      showSizeChanger: true,
                      showQuickJumper: true,
                    }}
                    manualRequest={true}
                    request={handleRequest}
                    dataSource={tableData}
                    scroll={{ x: 800 }}
                    columnEmptyText="-"
                    columns={columns}
                    columnsState={{
                      persistenceKey: 'msgquery-table',
                      persistenceType: 'localStorage',
                    }}
                    rowClassName={() => 'table-row-animation'}
                    toolBarRender={false}
                  />
                </Col>
              </Row>
            </div>
          </Card>

          {/* 添加消息详情模态框 */}
          <Modal
            title={messageTitle}
            open={messageModalVisible}
            onCancel={() => setMessageModalVisible(false)}
            footer={[
              <Button
                key="close"
                onClick={() => setMessageModalVisible(false)}
                className={styles.closeButton}
              >
                关闭
              </Button>
            ]}
            width={800}
            className={styles.messageModal}
            destroyOnClose
          >
            <div className={styles.messageContent}>
              <Paragraph copyable={{ text: currentMessage }} className={styles.copyButton}>
                复制全部
              </Paragraph>
              <div className={styles.messageText}>
                {currentMessage}
              </div>
            </div>
          </Modal>
        </div>
      </WrapContent>
    </KeepAlive>
  );
};

export default MsgQuery;