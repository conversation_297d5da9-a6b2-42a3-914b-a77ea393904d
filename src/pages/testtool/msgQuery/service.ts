import request from '@/utils/request';
import type { MsgQueryPara<PERSON>, MsgResult, MsgTypeMapping } from './data.d';

// 消息类型映射表
export const msgTypeMap: MsgTypeMapping = {
  "102219900000001": "102219900000001-账户余额变动通知消息",
  "102219900000002": "102219900000002-欠费户存款通知消息",
  "102219900000003": "102219900000003-账户状态变动通知消息",
  "102219900000004": "102219900000004-批量结果文件通知",
  "102219900000009": "102219900000009-换卡通知消息",
  "102219900000017": "102219900000017-开销户通知消息",
  "102219900000025": "102219900000025-长期不动户激活通知消息",
  "102219900000030": "102219900000030-IC卡激活通知消息",
  "102219900000031": "102219900000031-IC卡挂失相关通知消息",
  "102219900000032": "102219900000032-IC卡换卡相关通知消息",
  "102219900000033": "102219900000033-IC卡开卡相关通知消息",
  "102219900000043": "102219900000043-账户真实性通知消息",
  "102219900000044": "102219900000044-账户加办类通知消息",
  "102219900000048": "102219900000048-I/II/III类账户升降级通知消息",
  "102219900000060": "102219900000060-映射卡状态变动通知消息",
  "102219900000065": "102219900000065-客户星级变动通知消息",
  "102219900000068": "102219900000068-跨行开户通知消息",
  "102219900000069": "102219900000069-转入类账户余额变动通知消息",
  "102219900000071": "102219900000071-同号换卡/补卡通知消息",
  "102219900000072": "102219900000072-转出类账户余额变动通知消息",
  "102219900000073": "102219900000073-大额磁条风险告知通知消息",
  "102219900000077": "102219900000077-虚拟卡开户短信通知消息",
  "102219900000094": "102219900000094-对账文件通知消息",
  "102219900000095": "102219900000095-取制卡文件信息通知消息",
  "102219900000096": "102219900000096-批量开户文件通知消息",
  "102219900000098": "102219900000098-账户余额变动通知消息（协查）",
  "102219900000099": "102219900000099-修改客户三要素通知消息",
  "102219900000079": "102219900000079-短信发送",
  "102219900000045": "102219900000045-账户撤办通知消息",
  "102219900000092": "102219900000092-客户合并通知消息",
  "102219900000061": "102219900000061-养老金状态变更通知消息",
  "102219900000005": "102219900000005-账户余额变动通知消息（反欺诈）",
  "102219900000016": "102219900000016-定期账户变动通知消息",
  "102219900000034": "102219900000034-IC卡冻结相关通知消息",
  "102219900000008": "102219900000008-客户体验相关通知消息",
  "102219900000007": "102219900000007-信贷反欺诈通知消息",
  "102219900000010": "102219900000010-撤销延迟转账通知消息",
  "102219900000011": "102219900000011-养老金账户余额变动通知消息",
  "102219900000035": "102219900000035-社保卡暂非相关通知消息",
  "102219900000012": "102219900000012-金融反诈账户状态变动通知消息",
  "102219900000013": "102219900000013-养老金储蓄账户余额变动通知消息",
  "102219900000014": "102219900000014-限制服务相关通知消息",
  "102219900000015": "102219900000015-挂账通知消息",
  "102219900000018": "102219900000018-动账流水通知",
  "102219900000019": "102219900000019-万事网联止付通知消息",
  "102219900000020": "102219900000020-客户级客户合并通知消息",
  "102219900000021": "102219900000021-限额调整通知消息",
};

// 消息查询
export async function msgQuery(params: Partial<MsgQueryParams>) {
  // 设置默认值
  const defaultParams: MsgQueryParams = {
    globalBusiTrackNo: '',
    custNo: '',
    current: 1,
    pageSize: 10,
    ...params  // 使用传入的参数覆盖默认值
  };

  // 将所有参数转换为字符串类型
  const requestParams: Record<string, string> = {};
  Object.keys(defaultParams).forEach(key => {
    let value = defaultParams[key as keyof MsgQueryParams];

    if (value !== undefined && value !== null) {
      requestParams[key] = value.toString();
    } else {
      requestParams[key] = '';
    }
  });

  // 添加日志，帮助调试
  console.log('发送请求参数:', requestParams);

  try {
    const response = await request('/testtool/msgQuery', {
      method: 'GET',
      params: requestParams,
      headers: {
        'Content-Type': 'application/json;charset=UTF-8',
      },
    });

    console.log('请求响应:', response);
    return response;
  } catch (error) {
    console.error('请求错误:', error);
    throw error;
  }
}