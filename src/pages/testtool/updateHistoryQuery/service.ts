import request from '@/utils/request';
import type { UpdateHistoryParams } from './data.d';

// 查询更新历史列表
export async function updateHistoryList(params: Partial<UpdateHistoryParams>) {
  console.log('service接收到的参数:', params);
  
  // 设置默认值
  const defaultParams: UpdateHistoryParams = {
    tableName: '',
    env: '',
    oprationDate: '',
    rollbackDate: '',
  };
  
  // 合并参数，确保传入的参数优先级更高
  const mergedParams = {
    ...defaultParams,
    ...params
  };
  
  console.log('合并后的参数:', mergedParams);
  
  // 处理参数
  const requestBody: Record<string, any> = {};
  Object.keys(mergedParams).forEach(key => {
    const value = mergedParams[key as keyof UpdateHistoryParams];
    
    if (value !== undefined && value !== null) {
      requestBody[key] = value.toString();
    } else {
      requestBody[key] = '';
    }
  });
  
  console.log('最终请求体:', requestBody);
  
  try {
    const response = await request('/testtool/updateHistory/list', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json;charset=UTF-8',
      },
      data: requestBody,
    });
    
    return response;
  } catch (error) {
    console.error('请求错误:', error);
    throw error;
  }
}

// 查询更新历史详情
export async function updateHistoryDetail(params: { id: string | number }) {
  return request(`/testtool/updateHistory/${params.id}`, {
    method: 'GET',
    params,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

// 回退操作
export async function rollbackUpdateHistory(params: { id: string | number }) {
  return request(`/testtool/updateHistory/rollback/${params.id}`, {
    method: 'POST',
    data: params,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}