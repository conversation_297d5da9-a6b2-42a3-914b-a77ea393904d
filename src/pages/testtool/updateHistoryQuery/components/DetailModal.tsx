import React, { useRef } from 'react';
import { Modal, Descriptions, Typography, Button, message, Spin } from 'antd';
import type { UpdateHistoryData } from '../data.d';
import moment from 'moment';
import styles from '../style.less';
import { CopyOutlined } from '@ant-design/icons';

const { Text, Paragraph } = Typography;

interface DetailModalProps {
  visible: boolean;
  onCancel: () => void;
  data: UpdateHistoryData | null;
  loading: boolean; // 添加loading属性
}

const DetailModal: React.FC<DetailModalProps> = ({ visible, onCancel, data, loading }) => {
  // 添加引用来存储临时textarea元素
  const textAreaRef = useRef<HTMLTextAreaElement | null>(null);

  // 增强的复制文本到剪贴板功能
  const copyToClipboard = (text: string, label: string) => {
    if (!text) {
      message.error(`${label}为空，无法复制`);
      return;
    }

    // 优先使用现代API
    if (navigator.clipboard && window.isSecureContext) {
      navigator.clipboard.writeText(text)
        .then(() => {
          message.success(`已复制${label}到剪贴板`);
        })
        .catch(() => {
          // 如果现代API失败，回退到传统方法
          fallbackCopyTextToClipboard(text, label);
        });
    } else {
      // 在非安全上下文中使用传统方法
      fallbackCopyTextToClipboard(text, label);
    }
  };

  // 传统复制方法作为备选
  const fallbackCopyTextToClipboard = (text: string, label: string) => {
    try {
      // 创建临时文本区域
      if (!textAreaRef.current) {
        const textArea = document.createElement('textarea');
        textArea.style.position = 'fixed';
        textArea.style.top = '0';
        textArea.style.left = '0';
        textArea.style.width = '2em';
        textArea.style.height = '2em';
        textArea.style.padding = '0';
        textArea.style.border = 'none';
        textArea.style.outline = 'none';
        textArea.style.boxShadow = 'none';
        textArea.style.background = 'transparent';
        textArea.style.opacity = '0';
        textAreaRef.current = textArea;
        document.body.appendChild(textArea);
      }

      // 设置文本并选择
      const textArea = textAreaRef.current;
      textArea.value = text;
      textArea.select();

      // 执行复制命令
      const successful = document.execCommand('copy');
      if (successful) {
        message.success(`已复制${label}到剪贴板`);
      } else {
        message.error(`复制${label}失败`);
      }
    } catch (err) {
      console.error('无法复制文本:', err);
      message.error(`复制${label}失败: ${err}`);
    }
  };

  // 确保组件卸载时移除临时元素
  React.useEffect(() => {
    return () => {
      if (textAreaRef.current) {
        document.body.removeChild(textAreaRef.current);
        textAreaRef.current = null;
      }
    };
  }, []);

  return (
    <Modal
      title="更新历史详情"
      open={visible}
      onCancel={onCancel}
      width={900}
      bodyStyle={{ maxHeight: 'calc(90vh - 200px)', overflowY: 'auto' }}
      className={styles.detailModal}
      footer={[
        <Button
          key="close"
          onClick={onCancel}
          className={styles.closeButton}
        >
          关闭
        </Button>
      ]}
      centered
    >
      {loading ? (
        <div className={styles.loadingContainer}>
          <Spin size="large" tip="一大波数据正在加载中..." />
        </div>
      ) : data ? (
        <Descriptions
          bordered
          column={2}
          size="small"
          className={styles.detailDescriptions}
          labelStyle={{ minWidth: '120px', width: '120px', textAlign: 'center' }}
          contentStyle={{ textAlign: 'center' }}
        >
          <Descriptions.Item label="ID" span={2}>{data.id}</Descriptions.Item>
          <Descriptions.Item label="表名">{data.tableName}</Descriptions.Item>
          <Descriptions.Item label="主键字段">{data.primaryKey}</Descriptions.Item>
          <Descriptions.Item label="主键值">{data.primaryValue}</Descriptions.Item>
          <Descriptions.Item label="分片值">{data.zoneVal}</Descriptions.Item>
          <Descriptions.Item label="环境">{data.env}</Descriptions.Item>
          <Descriptions.Item label="操作类型">
            {data.operationType === 'U' ? '更新' :
             data.operationType === 'I' ? '插入' :
             data.operationType === 'D' ? '删除' : data.operationType}
          </Descriptions.Item>
          <Descriptions.Item label="操作用户">{data.userName}</Descriptions.Item>
          <Descriptions.Item label="操作IP">{data.oprationIp}</Descriptions.Item>
          <Descriptions.Item label="操作日期">{data.oprationDate}</Descriptions.Item>
          <Descriptions.Item label="操作时间">
            {data.oprationTime ? moment(data.oprationTime).format('YYYY-MM-DD HH:mm:ss') : '-'}
          </Descriptions.Item>
          <Descriptions.Item label="回退用户">{data.rollbackName || '-'}</Descriptions.Item>
          <Descriptions.Item label="回退IP">{data.rollbackIp || '-'}</Descriptions.Item>
          <Descriptions.Item label="回退日期">{data.rollbackDate || '-'}</Descriptions.Item>
          <Descriptions.Item label="回退时间">
            {data.rollbackTime ? moment(data.rollbackTime).format('YYYY-MM-DD HH:mm:ss') : '-'}
          </Descriptions.Item>
          <Descriptions.Item label="原始SQL" span={2}>
            <div className={styles.sqlContainer}>
              <Paragraph className={styles.sqlTextArea}>
                <Text code>{data.originalSql || '暂无SQL'}</Text>
              </Paragraph>
              <Button
                type="text"
                size="small"
                icon={<CopyOutlined />}
                className={styles.copyButton}
                onClick={() => copyToClipboard(data.originalSql || '', '原始SQL')}
              />
            </div>
          </Descriptions.Item>
          <Descriptions.Item label="回退SQL" span={2}>
            <div className={styles.sqlContainer}>
              <Paragraph className={styles.sqlTextArea}>
                <Text code>{data.rollbackSql || '暂无SQL'}</Text>
              </Paragraph>
              <Button
                type="text"
                size="small"
                icon={<CopyOutlined />}
                className={styles.copyButton}
                onClick={() => copyToClipboard(data.rollbackSql || '', '回退SQL')}
              />
            </div>
          </Descriptions.Item>
        </Descriptions>
      ) : (
        <div className={styles.noDataContainer}>
          <p>暂无数据</p>
        </div>
      )}
    </Modal>
  );
};

export default DetailModal;