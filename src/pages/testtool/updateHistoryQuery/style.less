.container {
  padding: 16px;
  background-color: #f0f2f5;
  min-height: calc(100vh - 120px);
  animation: fadeInUp 0.5s ease-out; // 添加容器动画
  
  .card {
    box-shadow:
      0 1px 2px -2px rgba(0, 0, 0, 0.16),
 
      0 3px 6px 0 rgba(0, 0, 0, 0.12),
 
      0 5px 12px 4px rgba(0, 0, 0, 0.09);
    border-radius: 8px;
    transition: all 0.3s; // 添加过渡效果
    
    &:hover {
      box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12); // 添加悬停效果
    }
    
    .titleContainer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding-bottom: 16px;
      border-bottom: 1px solid #f0f0f0;
      animation: slideInLeft 0.5s ease-out; // 添加标题动画
    }
    
    .table {
      margin-top: 16px;
      animation: fadeIn 0.5s ease-out; // 添加表格动画
      
      :global {
        .ant-pro-table-search {
          margin-bottom: 16px;
          padding: 24px 24px 0;
          background-color: #fff;
          border-radius: 8px;
          animation: fadeIn 0.5s ease-out; // 添加搜索表单动画
        }
        
        .ant-table-thead > tr > th {
          background-color: #fafafa;
          font-weight: 500;
        }
        
        .ant-btn-link {
          padding: 0 8px;
        }
        
        .ant-form-item-label > label {
          font-weight: 500;
        }
        
        .ant-select-selector,
        .ant-input,
        .ant-picker {
          border-radius: 8px;
        }
        
        .ant-table-container {
          border-radius: 8px;
          overflow: hidden;
        }
        
        .ant-pagination {
          margin-top: 16px;
        }
      }
    }
  }
}

// 圆角按钮样式
.roundButton {
  border-radius: 12px;
  margin-right: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

// 详情模态框样式
.detailModal {
  :global {
    .ant-modal-content {
      border-radius: 12px;
      overflow: hidden;
    }
    
    .ant-modal-header {
      background-color: #f7f7f7;
      border-bottom: 1px solid #f0f0f0;
      padding: 16px 24px;
      border-radius: 12px 12px 0 0;
      text-align: center;
    }
    
    .ant-modal-body {
      padding: 24px;
    }
    
    .ant-modal-footer {
      border-top: 1px solid #f0f0f0;
      border-radius: 0 0 12px 12px;
      text-align: center;
      padding: 12px 24px;
    }
  }
}

// 关闭按钮样式
.closeButton {
  border-radius: 12px;
  background-color: #e6f7f0;
  border-color: #d9f2e6;
  color: #52c41a;
  padding: 0 20px;
  height: 32px;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  
  &:hover, &:focus {
    background-color: #d4f0e5;
    border-color: #b7e8cc;
    color: #389e0d;
  }
  
  &:active {
    background-color: #c2e8d9;
    border-color: #a3e0c0;
  }
}

.detailDescriptions {
  width: 100%;
  
  :global {
    .ant-descriptions-item-label {
      font-weight: 500;
      color: #666;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      text-align: center;
    }
    
    .ant-descriptions-item-content {
      color: #333;
      word-break: break-word;
      text-align: center;
    }
    
    .ant-descriptions-row > th, 
    .ant-descriptions-row > td {
      padding: 12px 8px;
      vertical-align: middle;
    }
  }
}

.sqlContainer {
  position: relative;
  width: 100%;
  
  .sqlTextArea {
    margin-bottom: 0;
    max-height: 200px;
    overflow-y: auto;
    text-align: center;
    
    :global {
      code {
        display: block;
        white-space: pre-wrap;
        word-break: break-word;
        max-width: 100%;
        padding: 12px;
        background: #f5f5f5;
        border-radius: 8px;
        text-align: left;
      }
    }
  }
  
  .copyButton {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 28px;
    height: 28px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(255, 255, 255, 0.7);
    border-radius: 4px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    opacity: 0.6;
    transition: opacity 0.3s;
    
    &:hover {
      opacity: 1;
    }
  }
}

// 响应式调整
@media screen and (max-width: 768px) {
  .container {
    padding: 8px;
    
    .card {
      .table {
        :global {
          .ant-pro-table-search {
            padding: 16px 16px 0;
          }
        }
      }
    }
  }
}
.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  
  :global {
    .ant-spin-text {
      margin-top: 8px;
      color: #1890ff;
      font-size: 14px;
    }
  }
}

.noDataContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  color: #999;
  font-size: 14px;
}
// 添加动画关键帧
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}