import React, { useState, useRef, useEffect } from "react";
import { But<PERSON>, message, Card, Typography, Popconfirm } from "antd";
import {
  SearchOutlined,
  ReloadOutlined,
  EyeOutlined,
  UndoOutlined,
} from "@ant-design/icons";
import type { ProColumns, ActionType } from "@ant-design/pro-table";
import ProTable from "@ant-design/pro-table";
import type { FormInstance } from "antd";
import WrapContent from "@/components/WrapContent";
import { KeepAlive } from "react-activation";
import { storage } from "@/utils/storageUtils";
import type { UpdateHistoryData } from "./data.d";
import {
  updateHistoryList,
  updateHistoryDetail,
  rollbackUpdateHistory,
} from "./service";
import DetailModal from "./components/DetailModal";
import moment from "moment";
import styles from "./style.less";

const { Title } = Typography;

interface FormValueType {
  tableName?: string;
  env?: string;
  oprationDate?: string;
  rollbackDate?: string;
}

// 在文件顶部添加接口定义
interface SearchFormType {
  current?: number;
  pageSize?: number;
  tableName?: string;
  env?: string;
  oprationDate?: string;
  rollbackDate?: string;
}

const UpdateHistoryQuery: React.FC = () => {
  const formTableRef = useRef<FormInstance>();
  const actionRef = useRef<ActionType>();
  const [formValues, setFormValues] = useState<FormValueType>({});
  const [tableData, setTableData] = useState<UpdateHistoryData[]>([]);
  const [isFirstRequest, setIsFirstRequest] = useState(true);
  const [manualSearch, setManualSearch] = useState(false);
  
   // 详情模态框相关状态
   const [detailModalVisible, setDetailModalVisible] = useState(false);
   const [detailRecord, setDetailRecord] = useState<UpdateHistoryData | null>(
     null
   );
   const [detailLoading, setDetailLoading] = useState(false); // 添加加载状态
   
  // 搜索表单状态初始化
  const [searchForm, setSearchForm] = useState<SearchFormType>(() => {
    const savedSearch = storage.get("updateHistory_search");
    return savedSearch || {};
  });

  // 加载缓存数据的函数
  const loadCacheData = () => {
    try {
      const savedData = storage.get("updateHistory_data");
      const savedSearch = storage.get("updateHistory_search");

      console.log("加载缓存数据:", savedData, savedSearch);

      if (savedData?.records && savedSearch) {
        // 处理日期回显
        const formValues: any = { ...savedSearch };

        // 如果有日期，转换为 moment 对象
        if (savedSearch.oprationDate) {
          formValues.oprationDate = moment(
            savedSearch.oprationDate,
            "YYYYMMDD"
          );
        }
        if (savedSearch.rollbackDate) {
          formValues.rollbackDate = moment(
            savedSearch.rollbackDate,
            "YYYYMMDD"
          );
        }

        // 设置表单值 - 这里使用处理过日期的formValues
        setFormValues(formValues);

        // 确保表单值被正确设置
        setTimeout(() => {
          if (formTableRef.current) {
            formTableRef.current.setFieldsValue(formValues);
          }
        }, 100);

        // 设置表格数据
        setTableData(savedData.records || []);
        setSearchForm(savedSearch);
        
        // 标记为非首次请求，允许显示数据
        setIsFirstRequest(false);
        setManualSearch(true);
      }
    } catch (error) {
      console.error("加载缓存数据失败:", error);
    }
  };

  // 初始化数据
  useEffect(() => {
    loadCacheData();

    // 添加一个延迟设置表单值的操作，确保表单已经渲染完成
    const timer = setTimeout(() => {
      const savedSearch = storage.get("updateHistory_search");
      if (savedSearch && formTableRef.current) {
        const formValues: any = { ...savedSearch };
        
        // 如果有日期，转换为 moment 对象
        if (savedSearch.oprationDate) {
          formValues.oprationDate = moment(
            savedSearch.oprationDate,
            "YYYYMMDD"
          );
        }
        if (savedSearch.rollbackDate) {
          formValues.rollbackDate = moment(
            savedSearch.rollbackDate,
            "YYYYMMDD"
          );
        }
        
        // 再次设置表单值
        formTableRef.current.setFieldsValue(formValues);
      }
    }, 300);

    return () => {
      clearTimeout(timer);
      setIsFirstRequest(true);
    };
  }, []);

  // 处理重置
  const handleReset = (form: FormInstance | undefined) => {

    // 重置所有状态
    setFormValues({});
    setTableData([]);
    setIsFirstRequest(true); // 重置为首次请求状态
    setManualSearch(false); // 重置手动搜索标志
    
    // 完全清空搜索表单，包括分页信息
    setSearchForm({
      current: 1,
      pageSize: 10
    });

    // 重置表单
    setTimeout(() => {
      form?.resetFields();
      formTableRef.current?.resetFields();

      // 清除本地存储
      storage.set("updateHistory_data", null, 0);
      storage.set("updateHistory_search", null, 0);
      
      // 重置表格数据，但不触发新的请求
      if (actionRef.current) {
        actionRef.current.clearSelected?.();
      }
      
      // 手动更新表格数据为空
      setTableData([]);
    }, 0);
  };

  // 处理详情按钮点击
  const handleDetailClick = async (record: UpdateHistoryData) => {
    // 先显示模态框，并设置加载状态
    setDetailModalVisible(true);
    setDetailLoading(true);
    
    try {
      const res = await updateHistoryDetail({ id: record.id });
      if (res.code === 200) {
        const detailData =
          typeof res.data === "string" ? JSON.parse(res.data) : res.data;
        setDetailRecord(detailData);
      } else {
        message.error(res.msg || "获取详情失败");
      }
    } catch (error) {
      message.error("获取详情失败");
      console.error("获取详情失败:", error);
    } finally {
      // 无论成功失败，都结束加载状态
      setDetailLoading(false);
    }
  };

  // 处理模态框取消
  const handleDetailModalCancel = () => {
    setDetailModalVisible(false);
    // 延迟清空数据，避免模态框关闭时的闪烁
    setTimeout(() => {
      setDetailRecord(null);
    }, 300);
  };

  // 处理回退按钮点击
  const handleRollbackClick = async (id: string) => {
    try {
      const res = await rollbackUpdateHistory({ id });
      if (res.code === 200) {
        message.success("回退成功");
        actionRef.current?.reload();
      } else {
        message.error(res.msg || "回退失败");
      }
    } catch (error) {
      message.error("回退失败");
    }
  };

  // 处理搜索按钮点击
  const handleSearch = () => {
    
    formTableRef.current?.validateFields().then(values => {
      // 记录表单值，确保它们被正确传递
      setFormValues(values);
      
      // 重要：重置分页参数到第一页
      const newSearchForm = {
        ...values,
        current: 1,  // 重置为第一页
        pageSize: searchForm.pageSize || 10
      };
      
      // 一次性更新所有状态，避免多次渲染
      setSearchForm(newSearchForm);
      setManualSearch(true);
      
      // 使用 reload 方法
      actionRef.current?.reload();
    }).catch(error => {
      console.error('表单验证失败:', error);
    });
  };

  // 处理请求
  const handleRequest = async (params: any) => {
    
    // 如果是首次加载且未手动搜索，但有缓存数据，则返回缓存数据
    if (isFirstRequest && !manualSearch) {
      const cachedData = storage.get("updateHistory_data");
      if (cachedData) {
        // 处理缓存数据，确保是数组格式
        const records = Array.isArray(cachedData) ? cachedData : 
                       (cachedData.records ? cachedData.records : [cachedData]);
        setTableData(records);
        return {
          data: records,
          total: records.length,
          success: true,
        };
      }
      return {
        data: [],
        total: 0,
        success: true,
      };
    }

    // 获取当前表单值，确保使用最新的表单数据
    const formData = formTableRef.current?.getFieldsValue() || {};
    
    // 处理日期 - 修改日期处理逻辑
    let oprationDate = "";
    let rollbackDate = "";

    if (formData.oprationDate) {
      if (moment.isMoment(formData.oprationDate)) {
        oprationDate = formData.oprationDate.format("YYYYMMDD");
      } else if (typeof formData.oprationDate === 'string') {
        // 处理字符串格式的日期
        oprationDate = moment(formData.oprationDate).format("YYYYMMDD");
      } else if (formData.oprationDate instanceof Date) {
        // 处理Date对象
        oprationDate = moment(formData.oprationDate).format("YYYYMMDD");
      }
    }

    if (formData.rollbackDate) {
      if (moment.isMoment(formData.rollbackDate)) {
        rollbackDate = formData.rollbackDate.format("YYYYMMDD");
      } else if (typeof formData.rollbackDate === 'string') {
        // 处理字符串格式的日期
        rollbackDate = moment(formData.rollbackDate).format("YYYYMMDD");
      } else if (formData.rollbackDate instanceof Date) {
        // 处理Date对象
        rollbackDate = moment(formData.rollbackDate).format("YYYYMMDD");
      }
    }

    const requestParams = {
      tableName: formData.tableName || "",
      env: formData.env || "",
      oprationDate,
      rollbackDate,
      // 移除分页参数，因为使用前端分页
    };

    try {
      // 更新缓存
      setSearchForm(formData);
      storage.set("updateHistory_search", formData, 30 * 60);
      
      setIsFirstRequest(false);

      const res = await updateHistoryList(requestParams);

      if (res.code === 200) {
        // 处理新的响应格式 - 现在是JSON字符串数组
        const parsedData = typeof res.data === "string" ? JSON.parse(res.data) : res.data;
        
        // 确保数据是数组格式
        const records = Array.isArray(parsedData) ? parsedData : [parsedData];
        
        // 更新缓存
        storage.set("updateHistory_data", records, 30 * 60);
        setTableData(records);
        
        return {
          data: records,
          total: records.length,
          success: true,
        };
      } else {
        message.error(res.msg || "查询失败");
      }
      return {
        data: [],
        total: 0,
        success: false,
      };
    } catch (error) {
      console.error("查询失败:", error);
      return {
        data: [],
        total: 0,
        success: false,
      };
    }
  };

  const columns: ProColumns<UpdateHistoryData>[] = [
    // 列定义保持不变
    {
      title: "ID",
      dataIndex: "id",
      valueType: "text",
      fixed: "left",
      hideInSearch: true,
      width: 80,
      align: "center",
    },
    {
      title: "数据库表名",
      dataIndex: "tableName",
      valueType: "text",
      hideInSearch: false,
      width: 220,
      align: "center",
      fieldProps: {
        style: {
          width: "300px",
          borderRadius: "8px",  
        },
        placeholder: "请输入表名",
        // 修改输入校验方式
        onKeyPress: (e: React.KeyboardEvent<HTMLInputElement>) => {
          const regex = /[a-zA-Z0-9_\.]/;
          const key = String.fromCharCode(e.charCode);
          if (!regex.test(key)) {
            e.preventDefault();
          }
        },
        onChange: (e: React.ChangeEvent<HTMLInputElement>) => {
          const value = e.target.value;
          // 如果输入的内容不符合规则，则替换为符合规则的内容
          if (value && !/^[a-zA-Z0-9_\.]*$/.test(value)) {
            e.target.value = value.replace(/[^a-zA-Z0-9_\.]/g, '');
          }
        },
      },
      formItemProps: {
        rules: [
          {
            pattern: /^[a-zA-Z0-9_\.]*$/,
            message: '表名只能包含大小写字母、数字、点号和下划线',
          }
        ],
      },
    },
    {
      title: "主键字段名称",
      dataIndex: "primaryKey",
      valueType: "text",
      hideInSearch: true,
      width: 240,
      align: "center",
    },
    {
      title: "主键字段值",
      dataIndex: "primaryValue",
      valueType: "text",
      hideInSearch: true,
      width: 300,
      align: "center",
    },
    {
      title: "分片值",
      dataIndex: "zoneVal",
      valueType: "text",
      hideInSearch: true,
      width: 180,
      align: "center",
    },
    {
      title: "历史更新环境",
      dataIndex: "env",
      valueType: "select",
      valueEnum: {
        DEV1: "DEV1",
        DEV2: "DEV2",
        TEST: "TEST",
        DEVS: "DEVS",
        DEV5: "DEV5",
        SITA: "SITA",
        SITB: "SITB",
        T1: "T1",
        T2: "T2",
        T3: "T3",
        T4: "T4",
        ET: "ET",
        PREPROD: "PREPROD",
      },
      hideInSearch: false,
      width: 120,
      align: "center",
      fieldProps: {
        style: {
          width: "220px",
          borderRadius: "8px",
        },
        placeholder: "请选择历史更新环境",
      },
      formItemProps: {
        rules: [
          {
            required: true,
            message: '请选择历史更新环境',
          },
        ],
      },
    },
    {
      title: "操作类型",
      dataIndex: "operationType",
      valueType: "select",
      valueEnum: {
        U: "U-更新",
        I: "I-插入",
        D: "D-删除",
      },
      hideInSearch: true,
      width: 80,
      align: "center",
    },
    {
      title: "操作日期",
      dataIndex: "oprationDate",  
      valueType: "date",
      hideInSearch: false,
      width: 120,
      align: "center",
      fieldProps: {
        style: {
          width: "220px",
          borderRadius: "8px",
        },
        format: "YYYY-MM-DD",
        placeholder: "请选择操作日期",
        onChange: (date: moment.Moment | null, dateString: string) => {
          if (date) {
            // 手动触发表单值更新
            const currentValues = formTableRef.current?.getFieldsValue() || {};
            formTableRef.current?.setFieldsValue({
              ...currentValues,
              oprationDate: date,
            });
          }
        },
      },
      render: (_, record) => record.oprationDate || "-",
    },
    {
      title: "操作时间",
      dataIndex: "oprationTime",
      valueType: "dateTime",
      hideInSearch: true, 
      width: 220,
      align: "center",
      render: (_, record) =>
        record.oprationTime
          ? moment(record.oprationTime).format("YYYY-MM-DD HH:mm:ss")
          : "-",
    },
    {
      title: "回退日期",
      dataIndex: "rollbackDate",
      valueType: "date",
      hideInSearch: false,
      width: 120,
      align: "center",
      fieldProps: {
        style: {
          width: "300px",
          borderRadius: "8px",
        },
        format: "YYYY-MM-DD",
        placeholder: "请选择回退日期",
        onChange: (date: moment.Moment | null, dateString: string) => {
          if (date) {
            // 手动触发表单值更新
            const currentValues = formTableRef.current?.getFieldsValue() || {};
            formTableRef.current?.setFieldsValue({
              ...currentValues,
              rollbackDate: date,
            });
          }
        },
      },
      render: (_, record) => record.rollbackDate || "-",
    },
    {
      title: "回退时间",
      dataIndex: "rollbackTime",
      valueType: "dateTime",
      hideInSearch: true,
      width: 220,
      align: "center",
      render: (_, record) =>
        record.rollbackTime
          ? moment(record.rollbackTime).format("YYYY-MM-DD HH:mm:ss")
          : "-",
    },
    {
      title: "操作",
      dataIndex: "option",
      valueType: "option",
      fixed: "right",
      width: 180,
      align: "center", // 添加这一行，实现居中对齐  
      render: (_, record) => [
        <Button
          key="detail"
          type="link"
          icon={<EyeOutlined />}
          onClick={() => handleDetailClick(record)}
        >
          详细
        </Button>,
        <Popconfirm
          key="rollback"
          title="是否回退数据?"
          onConfirm={() => handleRollbackClick(record.id)}
          okText="确认"
          cancelText="取消"
        >
          <Button type="link" danger icon={<UndoOutlined />}>
            回退
          </Button>
        </Popconfirm>,
      ],
    },
  ];

  return (
    <KeepAlive name="updateHistoryQuery" saveScrollPosition="screen">
      <WrapContent>
        <div className={styles.container}>
          <Card className={styles.card}>
            <div className={styles.titleContainer}>
              <Title level={4}>操作历史查询</Title>
            </div>
            <ProTable<UpdateHistoryData>
              headerTitle="更新历史列表"
              actionRef={actionRef}
              formRef={formTableRef}
              rowKey="id"
              search={{
                labelWidth: 120,
                defaultCollapsed: false,
                span: 8,
                optionRender: ({ searchText, resetText }, { form }) => [
                  <Button
                    key="search"
                    type="primary"
                    onClick={() => handleSearch()}
                    icon={<SearchOutlined />}
                    className={styles.roundButton}
                  >
                    查询
                  </Button>,
                  <Button
                    key="reset"
                    onClick={() => handleReset(form)}
                    icon={<ReloadOutlined />}
                    className={styles.roundButton}
                  >
                    重置
                  </Button>,
                ],
              }}
              manualRequest={false}
              form={{
                validateMessages: {
                  required: '${label}为必填项',
                },  
                initialValues: formValues,
              }}
              toolBarRender={() => []}
              request={handleRequest}
              columns={columns}
              rowSelection={false}
              params={searchForm}
              // 修改分页配置，使用前端分页
              pagination={{
                pageSize: 10,
                showQuickJumper: true,
                showSizeChanger: true,
                // 不需要onChange事件，由ProTable自己处理
              }}
              scroll={{ x: 1500 }}
              options={{
                density: true,
                fullScreen: true,
                setting: true,
              }}
              dateFormatter="string"
              className={styles.table}
            />
          </Card>
        </div>
        <DetailModal
          visible={detailModalVisible}
          onCancel={handleDetailModalCancel}
          data={detailRecord}
          loading={detailLoading}
        />
      </WrapContent>
    </KeepAlive>
  );
};

export default UpdateHistoryQuery;