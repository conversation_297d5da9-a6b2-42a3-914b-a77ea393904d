export interface UpdateHistoryData {
  id: string;
  tableName: string;
  primaryKey: string;
  primaryValue: string;
  zoneVal: string;
  env: string;
  originalSql: string;
  rollbackSql: string;
  operationType: string;
  userName: string;
  oprationIp: string;
  oprationDate: string;
  oprationTime: Date;
  rollbackName: string;
  rollbackIp: string;
  rollbackDate: string;
  rollbackTime: Date;
}

export interface UpdateHistoryParams {
  tableName?: string;
  env?: string;
  oprationDate?: string;
  rollbackDate?: string;
  [key: string]: any;
}