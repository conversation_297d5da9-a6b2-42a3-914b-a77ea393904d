import React, { useState } from 'react';
import { Input, Tree, Tooltip, Badge } from 'antd';
import { DownOutlined, QuestionCircleOutlined, EditOutlined } from '@ant-design/icons';
import styles from '../style.less';
import { FieldMapping } from '../service';

// 定义树节点的类型
interface TreeNode {
  title: React.ReactNode;
  key: string;
  children?: TreeNode[];
  isLeaf?: boolean;
  selectable: boolean;
}

interface JsonFieldProps {
  data: any;
  parentKey?: string;
  onValueChange: (path: string, value: any) => void;
  fieldMappings?: FieldMapping[];
}

const JsonFieldTree: React.FC<JsonFieldProps> = ({
  data,
  parentKey = '',
  onValueChange,
  fieldMappings = []
}) => {
  const [focusedField, setFocusedField] = useState<string | null>(null);

  // 创建字段映射查找表
  const fieldMappingMap = React.useMemo(() => {
    const map: Record<string, string> = {};
    fieldMappings.forEach(mapping => {
      map[mapping.fieldCode] = mapping.fieldName;
    });
    return map;
  }, [fieldMappings]);

  // 获取字段的中文名称
  const getFieldChineseName = (fieldPath: string): string => {
    // 先尝试完整路径匹配
    if (fieldMappingMap[fieldPath]) {
      return fieldMappingMap[fieldPath];
    }

    // 如果完整路径没有匹配，尝试获取最后一段的字段名
    const lastField = fieldPath.split('.').pop() || '';
    if (fieldMappingMap[lastField]) {
      return fieldMappingMap[lastField];
    }

    // 尝试驼峰命名转换（例如：txBody.custNo 可能映射为 custNo）
    const lowerLastField = lastField.toLowerCase();
    for (const key in fieldMappingMap) {
      if (key.toLowerCase() === lowerLastField) {
        return fieldMappingMap[key];
      }
    }

    return '';
  };

  // 递归构建树形数据
  const buildTreeData = (obj: any, currentPath: string = ''): TreeNode[] => {
    return Object.entries(obj).map(([key, value]) => {
      const path = currentPath ? `${currentPath}.${key}` : key;
      const chineseName = getFieldChineseName(path);

      if (value && typeof value === 'object') {
        // 对象类型，递归处理
        return {
          title: (
            <div className={styles.objectNodeContainer}>
              <span className={styles.objectNodeTitle}>{key}</span>
              {chineseName && (
                <span className={styles.chineseLabel}>
                  {chineseName}
                </span>
              )}
            </div>
          ),
          key: path,
          children: buildTreeData(value, path),
          selectable: false
        };
      } else {
        // 基本类型，显示输入框
        const isFocused = focusedField === path;

        // 检查是否为特殊处理的数字字段
        const isSpecialNumericField = path.endsWith('.bgnIndexNo') ||
                                     path.endsWith('.curQryReqNum') ||
                                     path.endsWith('.execRetryTimes');

        // 修改这里：不再在输入框中显示双引号，但保持内部值的处理逻辑
        const displayValue = String(value);

        return {
          title: (
            <div className={styles.leafNodeContainer}>
              <div className={styles.leafNodeLabelContainer}>
                <span className={styles.leafNodeKey}>{key}</span>
                {chineseName ? (
                  <Badge
                    count={chineseName}
                    style={{
                      backgroundColor: isFocused ? '#40a9ff' : 'rgba(255, 255, 255, 0.1)',
                      color: isFocused ? '#ffffff' : 'rgba(255, 255, 255, 0.85)',
                      boxShadow: 'none',
                      borderRadius: '4px',
                      padding: '0 8px',
                      fontSize: '12px'
                    }}
                  />
                ) : (
                  <Tooltip title="暂无中文名称">
                    <QuestionCircleOutlined className={styles.noMappingIcon} />
                  </Tooltip>
                )}
              </div>
              <Input
                className={styles.leafNodeInput}
                value={value === null ? '' : displayValue}
                onChange={(e) => {
                  // 对于特殊数字字段，保持原有处理逻辑
                  if (isSpecialNumericField) {
                    onValueChange(path, e.target.value);
                  } else {
                    // 对于其他字段，不再需要移除引号，因为输入框中已经不显示引号
                    const newValue = e.target.value.trim() === '' ? null : e.target.value;
                    onValueChange(path, newValue);
                  }
                }}
                onFocus={() => setFocusedField(path)}
                onBlur={() => setFocusedField(null)}
                size="small"
                prefix={isFocused ? <EditOutlined style={{ color: '#40a9ff' }} /> : null}
                bordered={true}
                allowClear
                style={{
                  backgroundColor: 'rgba(0, 0, 0, 0.25)',
                  borderColor: isFocused ? '#40a9ff' : 'rgba(255, 255, 255, 0.15)',
                  color: '#ffffff',
                  fontFamily: '"JetBrains Mono", "Courier New", monospace',
                  fontSize: '13px'
                }}
              />
            </div>
          ),
          key: path,
          isLeaf: true,
          selectable: false
        };
      }
    });
  };

  const treeData = buildTreeData(data, parentKey);

  return (
    <div className={styles.jsonTreeWrapper}>
      <Tree
        showLine={{ showLeafIcon: false }}
        switcherIcon={<DownOutlined style={{ color: 'rgba(255, 255, 255, 0.85)' }} />}
        defaultExpandAll
        treeData={treeData}
        className={styles.jsonTree}
        showIcon={false}
        icon={null}
        blockNode={true}
      />
    </div>
  );
};

export default JsonFieldTree;