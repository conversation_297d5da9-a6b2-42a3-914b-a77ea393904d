import React, { useState, useEffect } from 'react';
import {
  Button,
  Modal,
  Input,
  Space,
  Table,
  Form,
  Tooltip,
  Dropdown,
  Menu,
  message,
  Select,
} from 'antd';
import {
  SearchOutlined,
  ReloadOutlined,
  CopyOutlined,
  ImportOutlined,
  MoreOutlined,
} from '@ant-design/icons';
import styles from '../style.less';
import { getPcsSendRecords } from '../service';

// 历史报文记录接口
export interface HistoryRecord {
  id: string;
  tenantId: string;
  pcsNo: string;
  caseNo: string;
  request: string;
  response: string;
  servRespCd: string;
  servRespDescInfo: string;
  globalBusiTrackNo: string;
  tester: string;
  caseName: string;
  createTime?: string;
  updateTime?: string;
}

// 添加TransactionCode接口
interface TransactionCode {
  code: string;
  name: string;
}

// 更新HistoryMessageModalProps接口，添加transactionList参数
interface HistoryMessageModalProps {
  visible: boolean;
  onCancel: () => void;
  onUse: (record: HistoryRecord) => void;
  copyToClipboard: (text: string, isRequest: boolean) => void;
  transactionList: TransactionCode[]; // 添加交易码列表
  currentTransactionCode?: string; // 添加当前选中的交易码
}

const HistoryMessageModal: React.FC<HistoryMessageModalProps> = ({
  visible,
  onCancel,
  onUse,
  copyToClipboard,
  transactionList, // 新增参数
  currentTransactionCode, // 新增参数
}) => {
  const [historyForm] = Form.useForm();
  const [historyRecords, setHistoryRecords] = useState<HistoryRecord[]>([]);
  const [historyLoading, setHistoryLoading] = useState<boolean>(false);
  const [historyPagination, setHistoryPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  // 获取历史报文记录
  const fetchHistoryRecords = async (params: any = {}) => {
    try {
      setHistoryLoading(true);
      const { current = 1, pageSize = 10 } = params;
      const formValues = historyForm.getFieldsValue();

      // 使用service中的getPcsSendRecords方法
      const response = await getPcsSendRecords({
        current: current, // 使用current代替pageNum
        pageSize: pageSize, // 确保传递每页大小
        pcsNo: formValues.pcsNo || undefined,
        caseNo: formValues.caseNo || undefined,
        tester: formValues.tester || undefined,
        tenantId: formValues.tenantId || undefined // 添加tenantId参数
      });

      if (response && response.data) {
        // 解析data字段（它是一个JSON字符串）
        const parsedData = typeof response.data === 'string'
          ? JSON.parse(response.data)
          : response.data;

        // 从解析后的数据中获取records作为列表数据和total作为总数
        let records = parsedData.records || [];

        // 确保request和response字段是有效的JSON字符串
        records = records.map((record: HistoryRecord) => {
          try {
            // 尝试解析并重新格式化request和response字段，确保是有效的JSON
            const requestJson = typeof record.request === 'string'
              ? JSON.parse(record.request)
              : record.request;

            const responseJson = typeof record.response === 'string'
              ? JSON.parse(record.response)
              : record.response;

            return {
              ...record,
              request: typeof requestJson === 'object' ? JSON.stringify(requestJson) : record.request,
              response: typeof responseJson === 'object' ? JSON.stringify(responseJson) : record.response
            };
          } catch (e) {
            console.error('解析历史记录JSON失败:', e);
            return record; // 如果解析失败，返回原始记录
          }
        });

        setHistoryRecords(records);
        setHistoryPagination({
          current,
          pageSize,
          total: parsedData.total || 0,
        });
      } else {
        setHistoryRecords([]);
        setHistoryPagination({
          current: 1,
          pageSize: 10,
          total: 0,
        });
      }
    } catch (error) {
      console.error('获取历史报文记录失败:', error);
      message.error('获取历史报文记录失败');
    } finally {
      setHistoryLoading(false);
    }
  };

  // 重置历史报文搜索表单
  const resetHistoryForm = () => {
    historyForm.resetFields();
    // 同时清空历史记录和分页信息
    setHistoryRecords([]);
    setHistoryPagination({
      current: 1,
      pageSize: 10,
      total: 0,
    });
  };

  // 搜索历史报文
  const searchHistoryRecords = () => {
    historyForm.validateFields().then(values => {
      setHistoryPagination(prev => ({
        ...prev,
        current: 1, // 搜索时重置到第一页
      }));
      fetchHistoryRecords({ current: 1, pageSize: historyPagination.pageSize });
    }).catch(error => {
      console.error('表单验证失败:', error);
    });
  };

  // 分页变化处理
  const handleTableChange = (pagination: any) => {
    setHistoryPagination(prev => ({
      ...prev,
      current: pagination.current,
      pageSize: pagination.pageSize,
    }));
    fetchHistoryRecords({
      current: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  // 查看历史报文详情
  const viewHistoryDetails = (record: HistoryRecord) => {
    // 尝试解析请求和响应JSON
    let formattedRequest = record.request;
    let formattedResponse = record.response;

    try {
      const requestObj = JSON.parse(record.request);
      formattedRequest = JSON.stringify(requestObj, null, 2);
    } catch (e) {
      console.error('解析请求报文失败:', e);
    }

    try {
      const responseObj = JSON.parse(record.response);
      formattedResponse = JSON.stringify(responseObj, null, 2);
    } catch (e) {
      console.error('解析响应报文失败:', e);
    }

    Modal.info({
      title: '报文详情',
      width: 800,
      content: (
        <div style={{ maxHeight: '70vh', overflow: 'auto' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <h3>请求报文</h3>
            <Button
              type="link"
              icon={<CopyOutlined />}
              onClick={(e) => {
                e.stopPropagation();
                copyToClipboard(formattedRequest, true);
              }}
            >
              复制
            </Button>
          </div>
          <pre style={{ background: '#f5f5f5', padding: '10px', borderRadius: '8px', overflow: 'auto' }}>
            {formattedRequest}
          </pre>

          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginTop: '20px' }}>
            <h3>响应报文</h3>
            <Button
              type="link"
              icon={<CopyOutlined />}
              onClick={(e) => {
                e.stopPropagation();
                copyToClipboard(formattedResponse, false);
              }}
            >
              复制
            </Button>
          </div>
          <pre style={{ background: '#f5f5f5', padding: '10px', borderRadius: '8px', overflow: 'auto' }}>
            {formattedResponse}
          </pre>
        </div>
      ),
      okText: '关闭',
      maskClosable: true,
      style: { borderRadius: '16px', overflow: 'hidden' },
      bodyStyle: { padding: '20px' },
    });
  };

  // 当模态框打开时，设置交易码为当前选中的交易码
  useEffect(() => {
    if (visible && currentTransactionCode) {
      historyForm.setFieldsValue({ pcsNo: currentTransactionCode });
    }
  }, [visible, currentTransactionCode]);

  // 当模态框关闭时重置表单和数据
  useEffect(() => {
    if (!visible) {
      resetHistoryForm();
    }
  }, [visible]);

  // 历史报文表格列定义
  const historyColumns = [
    {
      title: '环境',
      dataIndex: 'tenantId',
      key: 'tenantId',
      width: 80,
      ellipsis: true,
      align: 'center' as const,
      fixed: 'left' as const,
    },
    {
      title: '测试人',
      dataIndex: 'tester',
      key: 'tester',
      width: 160,
      ellipsis: true,
      align: 'center' as const,
    },
    {
      title: '流水号',
      dataIndex: 'globalBusiTrackNo',
      key: 'globalBusiTrackNo',
      width: 300,
      ellipsis: true,
      align: 'center' as const,
    },
    {
      title: '交易码',
      dataIndex: 'pcsNo',
      key: 'pcsNo',
      width: 150,
      ellipsis: true,
      align: 'center' as const,
    },
    {
      title: '案例编号',
      dataIndex: 'caseNo',
      key: 'caseNo',
      width: 180,
      ellipsis: true,
      align: 'center' as const,
    },
    {
      title: '案例名称',
      dataIndex: 'caseName',
      key: 'caseName',
      width: 220,
      ellipsis: {
        showTitle: false,
      },
      align: 'center' as const,
      render: (text: string) => (
        <Tooltip placement="topLeft" title={text}>
          <span>{text}</span>
        </Tooltip>
      ),
    },
    {
      title: '响应码',
      dataIndex: 'servRespCd',
      key: 'servRespCd',
      width: 180,
      align: 'center' as const,
      render: (text: string) => (
        <span style={{
          color: text === '000000' ? '#52c41a' : '#f5222d',
          fontWeight: 'bold'
        }}>
          {text}
        </span>
      ),
    },
    {
      title: '响应信息',
      dataIndex: 'servRespDescInfo',
      key: 'servRespDescInfo',
      width: 220,
      ellipsis: {
        showTitle: false,
      },
      align: 'center' as const,
      render: (text: string) => (
        <Tooltip placement="topLeft" title={text}>
          <span>{text}</span>
        </Tooltip>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 180,
      ellipsis: true,
      align: 'center' as const,
      render: (text: string) => (
        <Tooltip placement="topLeft" title={text}>
          <span>{text || '-'}</span>
        </Tooltip>
      ),
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      key: 'updateTime',
      width: 180,
      ellipsis: true,
      align: 'center' as const,
      render: (text: string) => (
        <Tooltip placement="topLeft" title={text}>
          <span>{text || '-'}</span>
        </Tooltip>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      fixed: 'right' as const,
      align: 'center' as const,
      render: (_: any, record: HistoryRecord) => (
        <Space size="small">
          <Button
            type="default"
            size="small"
            icon={<ImportOutlined />}
            onClick={() => onUse(record)}
            className={styles.useButton}
          >
            使用
          </Button>
          <Dropdown overlay={
            <Menu>
              <Menu.Item key="view" onClick={() => viewHistoryDetails(record)}>
                查看报文详情
              </Menu.Item>
            </Menu>
          }>
            <Button size="small" icon={<MoreOutlined />} />
          </Dropdown>
        </Space>
      ),
    },
  ];

  return (
    <Modal
      title="历史报文查询"
      open={visible}
      onCancel={onCancel}
      width={1200}
      footer={null}
      destroyOnClose
      maskClosable={false}
      className={styles.historyModal}
      bodyStyle={{
        padding: '20px',
        maxHeight: '80vh',
        overflow: 'hidden',
      }}
      style={{
        top: '50px',
        maxWidth: '95vw',
      }}
    >
      <div className={styles.historySearchSection}>
        <Form
          form={historyForm}
          layout="inline"
          onFinish={searchHistoryRecords}
          className={styles.historyForm}
        >
          <Form.Item
            name="pcsNo"
            label="交易码"
            rules={[
              {
                required: true,
                message: '请输入交易码!'
              }
            ]}
            style={{ marginBottom: '0', height: '64px' }}
          >
            <Select
              showSearch
              allowClear
              placeholder="请选择交易码"
              optionFilterProp="children"
              filterOption={(input, option) =>
                (option?.label ?? "").toLowerCase().includes(input.toLowerCase())
              }
              options={transactionList.map((item) => ({
                value: item.code,
                label: `${item.code}-${item.name}`,
              }))}
              style={{ width: '350px' }}
              className={styles.historySelectInput}
            />
          </Form.Item>
          <Form.Item name="tenantId" label="测试环境" style={{ marginBottom: '0', height: '64px' }}>
            <Select
              allowClear
              placeholder="请选择测试环境"
              optionFilterProp="children"
              className={styles.historySelectInput}
              options={[
                { value: 'DEV1', label: 'DEV1' },
                { value: 'TEST', label: 'TEST' },
                { value: 'DEVS', label: 'DEVS' },
                { value: 'DEV2', label: 'DEV2' },
                { value: 'DEV5', label: 'DEV5' },
                { value: 'SITA', label: 'SITA' },
                { value: 'SITB', label: 'SITB' },
                { value: 'T1', label: 'T1' },
                { value: 'T2', label: 'T2' },
                { value: 'T3', label: 'T3' },
                { value: 'T4', label: 'T4' },
                { value: 'ET', label: 'ET' },
                { value: 'PREPROD', label: 'PREPROD' }
              ]}
              style={{ width: '220px' }}
            />
          </Form.Item>
          <Form.Item name="tester" label="测试人" style={{ marginBottom: '0', height: '64px' }}>
          <Input
              placeholder="请输入测试人"
              allowClear
              className={styles.historyInput}
              style={{ width: '307px' }}
            />
          </Form.Item>
          <Form.Item name="caseNo" label="案例编号" style={{ marginBottom: '0', height: '64px' }}>
            <Input
              placeholder="请输入案例编号"
              allowClear
              className={styles.historyInput}
              style={{ width: '350px' }}
            />
          </Form.Item>
          <Form.Item label=" " colon={false} style={{ marginBottom: '0', height: '64px', marginLeft: 'auto' }}>
            <div className={styles.modalButtonGroup}>
              <Button
                type="primary"
                icon={<SearchOutlined />}
                onClick={searchHistoryRecords}
                className={styles.historySearchButton}
              >
                查询
              </Button>
              <Button
                icon={<ReloadOutlined />}
                onClick={resetHistoryForm}
                className={styles.historyResetButton}
                style={{ marginLeft: '8px' }}
              >
                重置
              </Button>
            </div>
          </Form.Item>
        </Form>
      </div>

      <div className={styles.historyTableSection}>
        <Table
          columns={historyColumns}
          dataSource={historyRecords}
          rowKey="id"
          loading={historyLoading}
          pagination={{
            current: historyPagination.current,
            pageSize: historyPagination.pageSize,
            total: historyPagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
          onChange={handleTableChange}
          scroll={{ x: 1000, y: 400 }}
          className={styles.historyTable}
          size="middle"
          locale={{ emptyText: '暂无数据，请输入条件后点击查询' }}
        />
      </div>
    </Modal>
  );
};

export default HistoryMessageModal;