.container {
  height: calc(100vh - 84px);
  margin-top: 50px;
  display: flex;
  flex-direction: column;
  background-color: #f0f2f5;
  overflow: hidden;
}

.mainLayout {
  display: flex;
  height: 100%;
  width: 100%;
}

.contentArea {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.urlBar {
  display: flex;
  padding: 16px;
  background-color: #fff;
  border-bottom: 1px solid #e8e8e8;
  justify-content: space-between;
  align-items: center;
  .selectWrapper {
    display: flex;
    align-items: center;
    gap: 16px !important; // 添加!important确保间距不被覆盖
    width: 75%; // 占用父元素75%的宽度

    // 为标签添加右边距，作为额外保障
    .selectLabel {
      font-size: 14px;
      color: #333;
      font-weight: 500;
      white-space: nowrap;
      margin-right: 16px !important; // 添加明确的右边距
      padding-right: 4px; // 添加一些内边距
    }
  }

  .selectLabel {
    font-size: 14px;
    color: #333;
    font-weight: 500;
    white-space: nowrap;
  }

  .transactionSelect {
    width: 55% !important; // 修改为55%，缩短交易码输入框长度
    border-radius: 6px !important;

    :global {
      .ant-select-selector {
        border-radius: 6px !important;
        height: 32px !important; // 减小高度，从40px改为32px
        padding: 0 16px !important; // 调整内边距，从4px改为0px
        line-height: 32px !important; // 添加行高以保持文字垂直居中
      }

      .ant-select-selection-search {
        margin-left: 6px;
        line-height: 32px; // 添加行高以保持搜索框文字垂直居中
      }

      .ant-select-selection-item {
        line-height: 32px; // 添加行高以保持选中项文字垂直居中
      }

      .ant-select-clear {
        background-color: transparent;
        right: 16px;

        &:hover {
          color: rgba(0, 0, 0, 0.65);
        }
      }
    }
  }
}

.buttonGroup {
  display: flex;
  align-items: center;
  gap: 8px !important; // 从12px减小到8px
  margin-right: 0; // 确保右侧没有额外的margin
  flex-wrap: nowrap; /* 防止按钮换行 */

  // 确保每个按钮都有正确的margin和样式
  > button {
    margin-right: 0 !important; // 移除右侧margin，完全依赖gap控制间距
    border-radius: 8px !important; /* 保持圆角 */
    transition: all 0.3s ease; /* 添加过渡效果 */
    box-shadow: 0 2px 0 rgba(0, 0, 0, 0.015); /* 添加轻微阴影 */
    min-width: 80px; // 设置最小宽度
    height: 32px; // 保持高度一致

    &:last-child {
      margin-right: 0 !important; // 最后一个按钮不需要右侧margin
    }

    &:hover {
      transform: translateY(-1px); /* 悬停时轻微上浮 */
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); /* 增强阴影效果 */
    }
  }
}

.historyButton {
  background-color: #13c2c2;
  color: white;
  border-color: #13c2c2;
  border-radius: 8px !important;
  font-weight: 500;

  &:hover {
    background-color: #36cfc9;
    border-color: #36cfc9;
    color: white;
  }

  &:active {
    background-color: #08979c;
    border-color: #08979c;
  }
}

.referenceButton {
  background-color: #722ed1;
  color: white;
  border-color: #722ed1;
  border-radius: 8px !important;
  font-weight: 500;

  &:hover {
    background-color: #9254de;
    border-color: #9254de;
    color: white;
  }

  &:active {
    background-color: #531dab;
    border-color: #531dab;
  }
}

.clearButton {
  border-radius: 8px !important;
  background-color: #ff4d4f;
  color: white;
  border-color: #ff4d4f;
  font-weight: 500;

  &:hover {
    background-color: #ff7875;
    border-color: #ff7875;
    color: white;
  }

  &:active {
    background-color: #cf1322;
    border-color: #cf1322;
  }
}

.sendButton {
  border-radius: 8px !important;
  background-color: #1890ff;
  color: white;
  border-color: #1890ff;
  font-weight: 500;

  &:hover {
    background-color: #40a9ff;
    border-color: #40a9ff;
    color: white;
  }

  &:active {
    background-color: #096dd9;
    border-color: #096dd9;
  }
}

.requestResponseArea {
  display: flex;
  flex: 1;
  overflow: hidden;
  background-color: #f0f2f5;
  padding: 16px;
  gap: 16px;
  min-height: 0;
  flex-wrap: nowrap;
  justify-content: space-between;
}

.requestArea, .responseArea {
  width: 49%;
  height: 100%;
  min-width: 300px;
  max-width: 49%;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
  overflow: hidden;
  flex-shrink: 1;
  margin: 0;
  border: 1px solid #e8e8e8;
}

.requestArea {
  :global {
    .ant-tabs {
      height: 100%;
    }

    .ant-tabs-content {
      height: calc(100% - 46px);
      padding: 0;
    }

    .ant-tabs-tabpane {
      height: 100%;
      cursor: pointer;
    }

    .ant-tabs-nav {
      margin-bottom: 0;
      padding: 0 16px;
      background-color: #fafafa;
      border-bottom: 1px solid #f0f0f0;
    }

    .ant-tabs-tab {
      padding: 12px 16px;
      font-weight: 500;
    }

    .ant-tabs-tab-active {
      .ant-tabs-tab-btn {
        color: #1890ff;
      }
    }
  }
}

.responseArea {
  cursor: pointer;
}

.responseHeader {
  padding: 12px 16px;
  font-weight: 500;
  border-bottom: 1px solid #f0f0f0;
  background-color: #fafafa;
}

.paramsTabs {
  height: 100%;
}

.codeEditorWrapper {
  height: 100%;
  padding: 16px;
  background-color: #fff;
  border-radius: 0 0 8px 8px;
  overflow: auto;
  position: relative;
}

.editorContainer {
  position: relative;
  height: 100%;
}

.editorActions {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 10;
  padding: 4px;
}

.copyButton {
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 4px;
  color: #1890ff;
  font-size: 12px;
  padding: 2px 8px;
  display: flex;
  align-items: center;
  transition: all 0.3s;
  border: 1px solid #e8e8e8;

  &:hover {
    background-color: #1890ff;
    color: #fff;
    border-color: #1890ff;
  }
}

.codeEditor {
  border-radius: 4px;
  height: auto !important;

  :global {
    .cm-editor {
      border: 1px solid #f0f0f0;
      border-radius: 4px;
      height: auto !important;
    }

    .cm-gutters {
      background-color: #fafafa;
    }

    .cm-activeLineGutter {
      background-color: #e6f7ff;
    }

    .cm-activeLine {
      background-color: #e6f7ff;
    }
  }
}

.jsonTreeWrapper {
  animation: fadeInUp 0.3s ease;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.jsonTreeContainer {
  height: 100%;
  overflow: auto;
  padding: 16px;
  background-color: #0f2845;
}

.jsonTree {
  width: 100%;

  :global {
    .ant-tree-treenode {
      width: 100%;
      padding: 8px 0;
      transition: all 0.3s ease;
      border-radius: 6px;

      &:hover {
        background-color: rgba(24, 144, 255, 0.05);
      }
    }

    .ant-tree-node-content-wrapper {
      width: calc(100% - 24px);
      transition: all 0.3s;
      color: #ffffff;
    }

    .ant-tree-switcher {
      color: #ffffff;
      transition: transform 0.3s ease;

      &.ant-tree-switcher_open {
        transform: rotate(0deg);
      }

      &.ant-tree-switcher_close {
        transform: rotate(-90deg);
      }
    }

    .ant-tree-indent-unit {
      width: 20px;
    }

    .ant-tree-list-holder-inner {
      padding: 8px;
    }

    .ant-tree-icon_loading,
    .ant-tree-icon__open,
    .ant-tree-icon__close,
    .ant-tree-icon_docu {
      display: none !important;
    }

    .ant-tree-node-content-wrapper {
      padding-left: 0 !important;

      &::before {
        display: none !important;
      }
    }

    .ant-tree-switcher {
      background: transparent !important;

      .ant-tree-switcher-icon {
        color: rgba(255, 255, 255, 0.85) !important;
        font-size: 14px !important;
      }
    }
  }
}

.objectNodeContainer {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  padding: 4px 0;
  transition: all 0.3s;
  animation: fadeIn 0.5s ease;

  &:hover {
    transform: scale(1.01);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0.8;
  }
  to {
    opacity: 1;
  }
}

.objectNodeTitle {
  font-weight: 500;
  color: #40a9ff;
  font-size: 14px;
  position: relative;
  transition: all 0.3s;

  &:after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 1px;
    background-color: #1890ff;
    transition: width 0.3s ease;
  }

  &:hover:after {
    width: 100%;
  }
}

.chineseLabel {
  margin-left: 8px;
  color: #d9d9d9;
  font-size: 12px;
  font-weight: normal;
  background-color: rgba(255, 255, 255, 0.15);
  padding: 2px 6px;
  border-radius: 10px;
  transition: all 0.3s;
  animation: fadeIn 0.8s ease;

  &:hover {
    background-color: rgba(24, 144, 255, 0.3);
    color: #ffffff;
  }
}

.leafNodeKey {
  color: #ffffff;
  font-weight: normal;
  transition: color 0.3s;

  &:hover {
    color: #40a9ff;
  }
}

.leafNodeContainer {
  display: flex;
  align-items: flex-start;
  width: 100%;
  flex-wrap: wrap;
  padding: 6px 0;
  transition: all 0.3s;
  animation: fadeIn 0.5s ease;

  &:hover {
    background-color: rgba(24, 144, 255, 0.05);
    border-radius: 4px;
    transform: translateX(2px);
  }
}

.leafNodeLabelContainer {
  display: flex;
  align-items: center;
  min-width: 150px;
  margin-right: 12px;
  flex-wrap: wrap;
}

.leafNodeKey {
  color: #333;
  font-weight: normal;
  transition: color 0.3s;

  &:hover {
    color: #1890ff;
  }
}

.leafNodeInput {
  flex: 1;
  font-family: 'JetBrains Mono', 'Courier New', monospace;
  min-width: 200px;
  border-radius: 4px;
  transition: all 0.3s;

  &:hover, &:focus {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }
}

.noMappingIcon {
  margin-left: 4px;
  color: #bfbfbf;
  font-size: 12px;
  cursor: help;
  transition: all 0.3s;

  &:hover {
    color: #faad14;
    transform: scale(1.2);
  }
}

.fieldDrawer {
  :global {
    .ant-drawer-header {
      background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
      border-bottom: none;
      padding: 16px 24px;

      .ant-drawer-title {
        color: white;
        font-weight: 500;
        font-size: 16px;
      }

      .ant-drawer-close {
        color: white;
        transition: transform 0.3s;

        &:hover {
          transform: rotate(90deg);
          color: white;
        }
      }
    }

    .ant-drawer-body {
      padding: 0;
      background-color: #fafafa;
    }

    .ant-drawer-content {
      border-radius: 8px 0 0 8px;
      overflow: hidden;
      box-shadow: -8px 0 24px rgba(0, 0, 0, 0.15);
    }

    .ant-table-tbody > tr > td {
      padding: 12px 16px;
      border-bottom: 1px solid #f0f0f0;
      word-break: break-all;
      transition: all 0.3s;
    }

    .ant-input {
      font-family: 'JetBrains Mono', 'Courier New', monospace;
      transition: all 0.3s;
    }
  }
}

.objectField {
  font-weight: 500;
  color: #1890ff;
}

.objectValueInput {
  font-family: 'Courier New', monospace;
  background-color: #f5f8ff;
}

.codeEditorWrapper {
  height: 100%;
  padding: 16px;
  background-color: #fff;
  border-radius: 0 0 8px 8px;
  overflow: auto;
  position: relative;
}

.editorContainer {
  position: relative;
  height: 100%;
}

.editorActions {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 10;
  padding: 4px;
}

.copyButton {
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 4px;
  color: #1890ff;
  font-size: 12px;
  padding: 2px 8px;
  display: flex;
  align-items: center;
  transition: all 0.3s;
  border: 1px solid #e8e8e8;

  &:hover {
    background-color: #1890ff;
    color: #fff;
    border-color: #1890ff;
  }
}

.fieldDrawer {
  :global {
    .ant-drawer-header {
      background: #001529;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      padding: 16px 24px;

      .ant-drawer-title {
        color: #ffffff;
        font-weight: 500;
        font-size: 16px;
      }

      .ant-drawer-close {
        color: #ffffff;
        transition: all 0.3s;

        &:hover {
          transform: rotate(90deg);
          color: #40a9ff;
        }
      }
    }

    .ant-drawer-body {
      padding: 0;
      background-color: #0f2845;
    }

    .ant-drawer-content {
      background-color: #0f2845;
      border-radius: 8px 0 0 8px;
      overflow: hidden;
      box-shadow: -8px 0 24px rgba(0, 0, 0, 0.15);
    }
  }
}

.jsonTreeContainer {
  height: 100%;
  overflow: auto;
  padding: 16px;
  background-color: #0f2845;

  :global {
    .ant-tree {
      background: transparent;
    }
  }
}

.jsonTree {
  width: 100%;

  :global {
    .ant-tree-treenode {
      padding: 4px 0;
      transition: all 0.3s ease;
      border-radius: 4px;
      margin: 4px 0;

      &:hover {
        background-color: rgba(255, 255, 255, 0.08);
      }
    }

    .ant-tree-node-content-wrapper {
      color: #ffffff;
      transition: all 0.3s;

      &:hover {
        background-color: transparent;
      }
    }

    .ant-tree-switcher {
      color: rgba(255, 255, 255, 0.85);
      background: transparent;

      &.ant-tree-switcher_open {
        transform: rotate(0deg);
      }

      &.ant-tree-switcher_close {
        transform: rotate(-90deg);
      }
    }

    .ant-tree-switcher-icon {
      color: rgba(255, 255, 255, 0.85);
      background: transparent;
    }

    .ant-tree-icon__customize,
    .ant-tree-icon__open,
    .ant-tree-icon__close {
      color: rgba(255, 255, 255, 0.85);
      background: transparent;
    }
  }
}

.objectNodeContainer {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.3s;

  &:hover {
    background-color: rgba(255, 255, 255, 0.08);
  }
}

.objectNodeTitle {
  color: #40a9ff;
  font-weight: 500;
  font-size: 14px;
}

.chineseLabel {
  margin-left: 8px;
  color: rgba(255, 255, 255, 0.65);
  font-size: 12px;
  background-color: rgba(255, 255, 255, 0.1);
  padding: 2px 8px;
  border-radius: 4px;
  transition: all 0.3s;

  &:hover {
    background-color: rgba(255, 255, 255, 0.15);
    color: #ffffff;
  }
}

.leafNodeContainer {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.3s;

  &:hover {
    background-color: rgba(255, 255, 255, 0.08);
  }
}

.leafNodeLabelContainer {
  display: flex;
  align-items: center;
  min-width: 180px;
  margin-right: 16px;
}

.leafNodeKey {
  color: rgba(255, 255, 255, 0.85);
  margin-right: 8px;
}

.leafNodeInput {
  background-color: rgba(0, 0, 0, 0.25);
  border-color: rgba(255, 255, 255, 0.15);
  color: #ffffff;
  border-radius: 4px;
  font-family: 'JetBrains Mono', 'Courier New', monospace;
  font-size: 13px;
  letter-spacing: 0.3px;

  &:hover, &:focus {
    background-color: rgba(0, 0, 0, 0.35);
    border-color: #40a9ff;
  }

  &::placeholder {
    color: rgba(255, 255, 255, 0.25);
  }

  // 添加输入框内部样式
  :global {
    .ant-input {
      background-color: rgba(0, 0, 0, 0.25);
      color: #ffffff;
      font-family: 'JetBrains Mono', 'Courier New', monospace;
      font-size: 13px;
      letter-spacing: 0.3px;

      &:hover, &:focus {
        background-color: rgba(0, 0, 0, 0.35);
      }
    }

    .ant-input-prefix {
      color: #40a9ff;
    }

    .ant-input-clear-icon {
      color: rgba(255, 255, 255, 0.45);

      &:hover {
        color: rgba(255, 255, 255, 0.85);
      }
    }
  }
}

.noMappingIcon {
  color: rgba(255, 255, 255, 0.45);
  margin-left: 4px;
  font-size: 14px;

  &:hover {
    color: #40a9ff;
  }
}

.docContent {
  h3 {
    margin-bottom: 16px;
    color: #1890ff;
    font-size: 14px;
  }

  p {
    font-size: 12px;
    color: #666;
  }
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
  background-color: rgba(255, 255, 255, 0.8);

  :global {
    .ant-spin {
      .ant-spin-text {
        margin-top: 8px;
        color: #1890ff;
        font-size: 14px;
      }

      .anticon {
        color: #1890ff;
      }
    }
  }
}

// 添加加载动画效果
@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}

.loadingContainer {
  animation: pulse 1.5s infinite ease-in-out;
}

// 历史报文模态框样式
.historyModal {
  :global {
    .ant-modal-content {
      border-radius: 12px; // 增加圆角
      overflow: hidden;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .ant-modal-header {
      padding: 16px 24px;
      background: #f7f7f7;
      border-bottom: 1px solid #f0f0f0;
      border-radius: 12px 12px 0 0; // 增加圆角
    }

    .ant-modal-title {
      font-size: 18px;
      font-weight: 600;
      color: #1d1d1d;
    }

    .ant-modal-close {
      transition: all 0.3s;
      &:hover {
        background: rgba(0, 0, 0, 0.06);
      }
    }

    .ant-modal-body {
      padding: 24px;
    }

    // 添加滚动条样式
    ::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }

    ::-webkit-scrollbar-thumb {
      background: #d9d9d9;
      border-radius: 4px;

      &:hover {
        background: #bfbfbf;
      }
    }

    ::-webkit-scrollbar-track {
      background: #f0f0f0;
      border-radius: 4px;
    }
  }
}

// 历史报文查询区域样式
.historySearchSection {
  margin-bottom: 16px;
  padding: 20px;
  background: #f9f9f9;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

  :global {
    .ant-form {
      display: flex;
      flex-wrap: wrap;
      align-items: flex-start;
    }

    .ant-form-item {
      margin-right: 16px;
      display: flex;
      align-items: flex-start;

      // 响应式调整
      @media (max-width: 768px) {
        width: 100%;
        margin-right: 0;
      }
    }

    .ant-form-item-label {
      white-space: nowrap;
      height: 32px;
      line-height: 32px;
    }

    .ant-form-item-explain-error {
      position: absolute;
      white-space: nowrap;
      font-size: 12px;
    }

    .ant-form-item-with-help {
      margin-bottom: 0;
    }
  }
}

.historyTableSection {
  animation: slideUp 0.4s ease-in-out;
  overflow-x: auto; // 添加水平滚动
  margin-top: 12px; // 增加与搜索区域的间距
}

.historySearchButton, .historyResetButton {
  border-radius: 8px; // 增加按钮圆角
  transition: all 0.3s;
  padding: 0 16px;
  height: 32px;
  min-width: 80px; // 设置最小宽度

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  }

  &:active {
    transform: translateY(0);
  }
}

.historySearchButton {
  background-color: #13c2c2;
  color: white;
  border-color: #13c2c2;
  border-radius: 8px !important;
  font-weight: 500;

  &:hover {
    background-color: #36cfc9;
    border-color: #36cfc9;
    color: white;
  }

  &:active {
    background-color: #08979c;
    border-color: #08979c;
  }
}

.historyTable {
  :global {
    .ant-table {
      border-radius: 10px; // 增加表格圆角
      overflow: hidden;
    }

    .ant-table-thead > tr > th {
      background: #f7f7f7;
      font-weight: 600;
      text-align: center !important; // 列名居中
      padding: 12px 8px;
    }

    .ant-table-tbody > tr:hover > td {
      background: #e6f7ff;
    }

    .ant-pagination {
      margin-top: 16px;
      text-align: right;
    }

    .ant-table-cell {
      white-space: nowrap; // 防止内容换行
    }
  }
}

// 动画效果
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 添加输入框样式
.historyInput {
  border-radius: 8px !important; // 外边框圆角
  overflow: hidden;
  position: relative; // 添加相对定位

  :global {
    .ant-input {
      border-radius: 0 !important; // 内边框直角
      transition: all 0.3s;
      border: none !important; // 隐藏内部边框
      padding: 4px 11px; // 保持内边距

      &:hover, &:focus {
        box-shadow: none !important; // 移除内部阴影
        border: none !important; // 确保隐藏内部边框
      }
    }

    // 处理Input组件的容器样式
    .ant-input-affix-wrapper {
      border: none !important; // 移除容器边框
      padding: 0 !important; // 移除内边距

      &:hover, &:focus, &-focused {
        border: none !important; // 确保悬停时无边框
        box-shadow: none !important; // 移除阴影
      }
    }
  }

  // 添加自定义边框样式
  border: 1px solid #d9d9d9;

  &:hover {
    border-color: #40a9ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }

  &:focus-within {
    border-color: #40a9ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }
}

// 历史表单样式
.historyForm {
  width: 100%;

  :global {
    .ant-form-item-control-input {
      min-height: 32px;
    }

    // 调整按钮组样式
    .ant-space {
      display: flex;
      gap: 16px !important; // 强制设置间距
    }

    // 确保按钮有足够空间
    .ant-btn {
      min-width: 80px;
      margin-right: 0 !important; // 清除可能的右侧margin
    }
  }
}

// 修改buttonGroup样式，专门用于历史模态框中的按钮
.modalButtonGroup {
  display: flex;
  gap: 8px !important; // 从16px减小到8px
  margin-left: 0; // 移除左侧margin以确保靠左对齐
}

.historyInput {
  width: 220px;
  position: relative;

  :global {
    .ant-input {
      border: none !important;
      padding: 4px 11px;
    }

    .ant-input-affix-wrapper {
      border: none !important;
      padding: 0 11px;
    }
  }

  border: 1px solid #d9d9d9;
  border-radius: 6px;
  overflow: hidden;

  &:hover {
    border-color: #40a9ff;
  }

  &:focus-within {
    border-color: #40a9ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }
}

// 历史报文使用按钮样式
.useButton {
  background-color: #ffd54f; // 淡黄色
  color: #333;
  border-color: #ffd54f;
  font-weight: 500;

  &:hover {
    background-color: #ffca28; // 悬浮时颜色加深
    border-color: #ffca28;
    color: #fff;
  }

  &:active {
    background-color: #ffb300;
    border-color: #ffb300;
    color: #fff;
  }
}

.saveButton {
  background-color: #52c41a;
  color: white;
  border-color: #52c41a;
  border-radius: 8px !important;
  font-weight: 500;

  &:hover {
    background-color: #73d13d;
    border-color: #73d13d;
    color: white;
  }

  &:active {
    background-color: #389e0d;
    border-color: #389e0d;
  }
}

// 保存案例模态框样式
.saveCaseModal {
  :global {
    .ant-modal-content {
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .ant-modal-header {
      padding: 16px 24px;
      background: #f7f7f7;
      border-bottom: 1px solid #f0f0f0;
      border-radius: 12px 12px 0 0;
    }

    .ant-modal-title {
      font-size: 18px;
      font-weight: 600;
      color: #1d1d1d;
    }

    .ant-modal-body {
      padding: 24px;
    }

    // 让模态框更宽
    .ant-modal {
      width: 550px !important;
      max-width: 90%;
    }

    // 美化按钮
    .ant-modal-footer {
      border-top: 1px solid #f0f0f0;
      padding: 16px 24px;

      .ant-btn {
        border-radius: 6px;
        height: 36px;
        min-width: 80px;
        transition: all 0.3s;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
        }
      }

      .ant-btn-primary {
        background-color: #52c41a;
        border-color: #52c41a;

        &:hover {
          background-color: #73d13d;
          border-color: #73d13d;
        }
      }
    }
  }
}

// 案例输入框样式
.caseInput {
  width: 100%;
  height: 40px;
  border-radius: 8px !important;
  font-size: 14px;
  transition: all 0.3s;
  border: 1px solid #d9d9d9;

  &:hover {
    border-color: #40a9ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
  }

  &:focus {
    border-color: #40a9ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    outline: none;
  }

  :global {
    .ant-input {
      height: 40px;
      border-radius: 8px !important;
      padding: 0 12px;
      font-size: 14px;

      &::placeholder {
        color: #bfbfbf;
      }

      &:hover, &:focus {
        border-color: #40a9ff;
      }
    }
  }
}

// 为历史模态框中的下拉选择器添加样式
.historySelectInput {
  border-radius: 8px !important;
  overflow: hidden;

  :global {
    .ant-select-selector {
      border-radius: 8px !important;
      height: 32px !important;
      border: 1px solid #d9d9d9 !important;
    }
  }

  &:hover {
    :global {
      .ant-select-selector {
        border-color: #40a9ff !important;
      }
    }
  }

  &:focus-within {
    :global {
      .ant-select-selector {
        border-color: #40a9ff !important;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
      }
    }
  }
}

.importButton {
  background-color: #2f54eb;
  color: white;
  border-color: #2f54eb;
  border-radius: 8px !important;
  font-weight: 500;

  &:hover {
    background-color: #597ef7;
    border-color: #597ef7;
    color: white;
  }

  &:active {
    background-color: #1d39c4;
    border-color: #1d39c4;
  }
}

.importModal {
  :global {
    .ant-modal-content {
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .ant-modal-header {
      padding: 16px 24px;
      background: #f7f7f7;
      border-bottom: 1px solid #f0f0f0;
      border-radius: 12px 12px 0 0;
    }

    .ant-modal-title {
      font-size: 18px;
      font-weight: 600;
      color: #1d1d1d;
    }

    .ant-modal-body {
      padding: 24px;
    }

    // 美化按钮
    .ant-modal-footer {
      border-top: 1px solid #f0f0f0;
      padding: 16px 24px;

      .ant-btn {
        border-radius: 6px;
        height: 36px;
        min-width: 80px;
        transition: all 0.3s;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
        }
      }

      .ant-btn-primary {
        background-color: #2f54eb;
        border-color: #2f54eb;

        &:hover {
          background-color: #597ef7;
          border-color: #597ef7;
        }
      }
    }
  }
}

.importTextArea {
  width: 100%;
  border-radius: 8px !important;
  font-family: 'JetBrains Mono', 'Courier New', monospace;
  font-size: 14px;
  transition: all 0.3s;

  :global {
    .ant-input {
      border-radius: 8px !important;
      font-family: 'JetBrains Mono', 'Courier New', monospace;

      &:hover, &:focus {
        border-color: #2f54eb;
        box-shadow: 0 0 0 2px rgba(47, 84, 235, 0.2);
      }
    }
  }
}