import request from '@/utils/request';

// 导入FieldDescription接口
export interface FieldDescription {
  fieldName: string;
  fieldCode: string;
  fieldValue: string;
  isObject?: boolean; // 添加isObject可选属性
}

// 根据环境直接构建URL
export function getProxyUrls(env: string) {
  if (!env) {
    return {
      macUrl: '',
      requestUrl: ''
    };
  }

  return {
    macUrl: `/mac-service/${env}/requestGenMac`,
    requestUrl: `/transaction-service/${env}/`
  };
}

// 获取MAC值
export async function getMacValue(macUrl: string, requestData: any) {
  try {
    // 创建一个可以超时的 Promise
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error('获取mac地址超时')), 30000); // 30秒超时
    });

    const fetchPromise = fetch(macUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json;charset=UTF-8',
      },
      body: JSON.stringify(requestData),
    }).then(response => {
      if (!response.ok) {
        throw new Error(`MAC请求失败: ${response.status} ${response.statusText}`);
      }
      return response.text();
    });

    // 使用 Promise.race 实现超时控制
    return await Promise.race([fetchPromise, timeoutPromise]);
  } catch (error) {
    console.error('获取MAC值失败:', error);
    throw error;
  }
}

// 发送交易请求到外部地址
export async function sendExternalTransaction(requestUrl: string, requestData: any) {
  try {
    // 创建一个可以超时的 Promise
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error('交易请求超时')), 30000); // 30秒超时
    });

    const fetchPromise = fetch(requestUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json;charset=UTF-8',
      },
      body: JSON.stringify(requestData),
    }).then(response => {
      if (!response.ok) {
        throw new Error(`请求失败: ${response.status} ${response.statusText}`);
      }
      return response.json();
    });

    // 使用 Promise.race 实现超时控制
    return await Promise.race([fetchPromise, timeoutPromise]);
  } catch (error) {
    console.error('发送交易请求失败:', error);
    throw error;
  }
}

interface PcsMessageResponse {
  code: number;
  msg: string;
  data: string;
  error: boolean;
  success: boolean;
}

interface PcsMessage {
  pcsName: string;
  pcsNo: string;
}

// 获取交易列表
export async function getPcsMessage() {
  console.log('开始请求交易列表');
  const response = await request<PcsMessageResponse>('/message/getPcsList', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });


  if (response.data) {
    try {
      const pcsMessages: PcsMessage[] = JSON.parse(response.data);

      const result = pcsMessages.map(item => ({
        code: item.pcsNo,
        name: item.pcsName
      }));

      return result;
    } catch (error) {
      console.error('解析交易列表数据失败:', error);
      return [];
    }
  }

  console.log('交易列表请求未成功或无数据');
  return [];
}

// 递归解析JSON对象
export function parseJsonObject(obj: any, parentKey: string = ''): FieldDescription[] {
  let fields: FieldDescription[] = [];

  for (const [key, value] of Object.entries(obj)) {
    const currentKey = parentKey ? `${parentKey}.${key}` : key;

    if (value && typeof value === 'object') {
      // 如果是对象，递归解析
      fields.push({
        fieldName: `${currentKey}`,
        fieldCode: currentKey,
        fieldValue: JSON.stringify(value),
        isObject: true
      });
      fields = fields.concat(parseJsonObject(value, currentKey));
    } else {
      // 如果是基本类型，直接添加
      fields.push({
        fieldName: currentKey,
        fieldCode: currentKey,
        fieldValue: String(value),
        isObject: false
      });
    }
  }

  return fields;
}

// 定义字段映射接口
export interface FieldMapping {
  fieldCode: string;
  fieldName: string;
}

// 根据交易码获取字段映射关系
export async function getPcsMessageByNo(pcsNo: string) {
  const messageResponse = await request('/message/getPcsMessage', {
    method: 'GET',
    params: { pcsNo },
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });

  if (messageResponse && messageResponse.data) {
    try {
      // 解析返回的数据
      const parsedData = JSON.parse(messageResponse.data);

      if (Array.isArray(parsedData) && parsedData.length >= 2) {
        // 第一个元素包含jsonMessage
        let rawData = {};
        if (parsedData[0] && parsedData[0].jsonMessage) {
          // 处理可能存在的特殊字符，如\r、\n和多个反斜杠
          const cleanedJsonString = parsedData[0].jsonMessage
            .replace(/\\r/g, '')
            .replace(/\\n/g, '')
            .replace(/\\\\/g, '\\');

          try {
            rawData = JSON.parse(cleanedJsonString);
          } catch (e) {
            console.error('解析jsonMessage失败:', e);
            rawData = {};
          }
        }

        // 第二个元素包含字段映射
        const fieldMappings: FieldMapping[] = [];
        if (parsedData[1]) {
          for (const [fieldCode, fieldName] of Object.entries(parsedData[1])) {
            fieldMappings.push({
              fieldCode,
              fieldName: fieldName as string
            });
          }
        }

        return {
          rawData, // 原始数据用于显示在编辑器中
          fields: parseJsonObject(rawData), // 解析后的字段用于显示在抽屉中
          fieldMappings // 字段映射关系
        };
      } else {
        console.error('返回数据格式不符合预期:', parsedData);
        return {
          rawData: {},
          fields: [],
          fieldMappings: []
        };
      }
    } catch (error) {
      console.error('解析报文数据失败:', error);
      return {
        rawData: {},
        fields: [],
        fieldMappings: []
      };
    }
  }

  return {
    rawData: {},
    fields: [],
    fieldMappings: []
  };
}

// 获取历史报文
export async function getHistoryMessages() {
  return request('/message/getPcsSendRecord', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

// 获取参考报文
export async function getReferenceMessages() {
  return request('/message/referenceMessages', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

// 获取响应报文字段映射
export async function getResponseFieldMappings(pcsNo: string): Promise<FieldMapping[]> {
  try {
    const response = await request('/message/getPcsOutMapping', {
      method: 'GET',
      params: { pcsNo },
      headers: {
        'Content-Type': 'application/json;charset=UTF-8',
      },
    });

    if (response && response.data) {
      try {
        const mappings = JSON.parse(response.data);
        const fieldMappings: FieldMapping[] = Object.entries(mappings).map(([fieldCode, fieldName]) => ({
          fieldCode,
          fieldName: fieldName as string
        }));
        return fieldMappings;
      } catch (error) {
        console.error('解析响应字段映射失败:', error);
        return [];
      }
    }
    return [];
  } catch (error) {
    console.error('获取响应字段映射失败:', error);
    return [];
  }
}

// 获取历史报文分页查询接口
export async function getPcsSendRecords(params: {
  current: number;
  pageSize: number;
  pcsNo?: string;
  caseNo?: string;
  tester?: string;
  tenantId?: string;
}) {
  return request('/message/getPcsSendRecords', {
    method: 'POST',
    data: {
      current: params.current,
      pageSize: params.pageSize,
      pcsNo: params.pcsNo,
      caseNo: params.caseNo,
      tester: params.tester,
      tenantId: params.tenantId
    },
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
}

// 获取单条历史报文记录接口
export async function getPcsSendRecord(globalBusiTrackNo: string) {
  try {
    const response = await request('/message/getPcsSendRecord', {
      method: 'GET',
      params: { globalBusiTrackNo },
      headers: {
        'Content-Type': 'application/json;charset=UTF-8',
      },
    });

    if (response && response.data) {
      // 处理返回的数据
      const parsedData = typeof response.data === 'string'
        ? JSON.parse(response.data)
        : response.data;

      // 确保request字段是有效的JSON
      if (parsedData.request) {
        try {
          const requestJson = typeof parsedData.request === 'string'
            ? JSON.parse(parsedData.request)
            : parsedData.request;

          parsedData.request = typeof requestJson === 'object'
            ? JSON.stringify(requestJson, null, 2)
            : parsedData.request;
        } catch (e) {
          console.error('解析历史记录请求JSON失败:', e);
        }
      }

      return {
        code: response.code || 200,
        msg: response.msg || 'success',
        data: parsedData,
        error: false,
        success: true
      };
    }

    return {
      code: 404,
      msg: '未找到记录',
      data: null,
      error: true,
      success: false
    };
  } catch (error) {
    console.error('获取历史报文记录失败:', error);
    return {
      code: 500,
      msg: '获取历史报文记录失败',
      data: null,
      error: true,
      success: false
    };
  }
}

// 保存案例参数接口
export interface SaveCaseParams {
  caseNo: string;
  caseName: string;
  tenantId: string;
  pcsNo: string;
  request: string;
  response: string;
}

// 保存案例方法
export async function saveCase(params: SaveCaseParams) {
  return request('/message/savePcsSendRecord', {
    method: 'POST',
    data: params,
  });
}