import React, { useState, useEffect } from "react";
import {
  Select,
  Button,
  Tabs,
  Drawer,
  message,
  Spin,
  Modal,
  Input,
  Form,
} from "antd";
import {
  SendOutlined,
  HistoryOutlined,
  FileTextOutlined,
  ClearOutlined,
  LoadingOutlined,
  CopyOutlined,
  SaveOutlined,
  ImportOutlined,
} from "@ant-design/icons";
import CodeMirror from "@uiw/react-codemirror";
import { json } from "@codemirror/lang-json";
import styles from "./style.less";
import {
  getProxyUrls, // 替换 getBaseUrl 为 getProxyUrls
  getPcsMessage,
  getMacValue,
  sendExternalTransaction,
  getPcsMessageByNo,
  FieldDescription,
  FieldMapping,
  getResponseFieldMappings,
  getPcsSendRecord, // 新增导入
  saveCase, // 新增导入
  SaveCaseParams, // 新增导入
  parseJsonObject, // 新增导入
} from "./service";
import JsonFieldTree from "./components/JsonFieldTree";
import HistoryMessageModal from "./components/HistoryMessageModal";  // 导入新的历史报文模态框组件
import { KeepAlive, useActivate, useUnactivate } from "react-activation";
import { storage } from "@/utils/storageUtils";
import { debounce } from 'lodash';

const { TabPane } = Tabs;
const { TextArea } = Input;

interface TransactionCode {
  code: string;
  name: string;
}

const HttpTest: React.FC = () => {
  const [transactionCode, setTransactionCode] = useState<string>("");
  const [transactionList, setTransactionList] = useState<TransactionCode[]>([]);
  const [requestData, setRequestData] = useState("{}");
  const [responseData, setResponseData] = useState("");
  const [activeTab, setActiveTab] = useState("Request");
  const [docDrawerVisible, setDocDrawerVisible] = useState(false);
  const [isRequestDoc, setIsRequestDoc] = useState(true);
  const [fieldDescriptions, setFieldDescriptions] = useState<
    FieldDescription[]
  >([]);
  const [jsonData, setJsonData] = useState<any>({});
  const [fieldMappings, setFieldMappings] = useState<FieldMapping[]>([]); // 添加字段映射状态
  const [loading, setLoading] = useState<boolean>(false); // 添加loading状态变量
  const [responseLoading, setResponseLoading] = useState<boolean>(false); // 添加响应区域的加载状态
  const [requestCopied, setRequestCopied] = useState<boolean>(false); // 添加请求复制状态
  const [responseCopied, setResponseCopied] = useState<boolean>(false); // 添加响应复制状态
  const [responseFieldMappings, setResponseFieldMappings] = useState<FieldMapping[]>([]); // 添加响应字段映射状态

  // 添加历史报文模态框相关状态
  const [historyModalVisible, setHistoryModalVisible] = useState<boolean>(false);

  // 添加案例保存相关状态
  const [saveCaseModalVisible, setSaveCaseModalVisible] = useState<boolean>(false);
  const [saveCaseForm] = Form.useForm();

  // 添加导入报文相关状态
  const [importModalVisible, setImportModalVisible] = useState<boolean>(false);
  const [importMessageContent, setImportMessageContent] = useState<string>("");

  // 缓存时间设置（30分钟）
  const CACHE_EXPIRES_TIME = 30 * 60;

  // 缓存键名设置
  const CACHE_KEYS = {
    transactionCode: 'http_test_transaction_code',
    requestData: 'http_test_request_data',
    responseData: 'http_test_response_data'
  };

  // 添加防抖函数
  const debouncedSaveRequestData = debounce((data) => {
    if (data && data !== '{}') {
      storage.set(CACHE_KEYS.requestData, data, CACHE_EXPIRES_TIME);
    }
  }, 500);

  const debouncedSaveResponseData = debounce((data) => {
    if (data) {
      storage.set(CACHE_KEYS.responseData, data, CACHE_EXPIRES_TIME);
    }
  }, 500);

  // 初始化状态，尝试从缓存加载数据
  useEffect(() => {
    loadCachedData();
  }, []);

  // 监听数据变化，更新缓存
  useEffect(() => {
    if (transactionCode) {
      storage.set(CACHE_KEYS.transactionCode, transactionCode, CACHE_EXPIRES_TIME);
    }
  }, [transactionCode]);

  useEffect(() => {
    debouncedSaveRequestData(requestData);
    // 清理函数
    return () => {
      debouncedSaveRequestData.cancel();
    };
  }, [requestData]);

  useEffect(() => {
    debouncedSaveResponseData(responseData);
    // 清理函数
    return () => {
      debouncedSaveResponseData.cancel();
    };
  }, [responseData]);

  // 从缓存加载数据
  const loadCachedData = async () => {
    try {
      const cachedTransactionCode = storage.get(CACHE_KEYS.transactionCode);
      const cachedRequestData = storage.get(CACHE_KEYS.requestData);
      const cachedResponseData = storage.get(CACHE_KEYS.responseData);

      // 先加载响应数据，因为它不依赖其他数据
      if (cachedResponseData) {
        setResponseData(cachedResponseData);
      }

      // 先设置交易码，可能会触发请求模板获取
      if (cachedTransactionCode) {
        // 直接设置交易码但不触发模板获取
        setTransactionCode(cachedTransactionCode);

        // 如果没有请求数据缓存，则获取该交易码的请求模板
        if (!cachedRequestData) {
          try {
            setLoading(true);
            const { rawData, fields, fieldMappings } = await getPcsMessageByNo(cachedTransactionCode);
            if (rawData) {
              setRequestData(JSON.stringify(rawData, null, 2));
              setJsonData(rawData);
              setFieldDescriptions(fields);
              setFieldMappings(fieldMappings || []);
            }
          } catch (error) {
            console.error("获取交易模板失败:", error);
          } finally {
            setLoading(false);
          }
        }
      }

      // 最后加载请求数据，优先使用缓存的请求数据
      if (cachedRequestData) {
        setRequestData(cachedRequestData);
        // 尝试解析JSON以更新jsonData
        try {
          const jsonObj = JSON.parse(cachedRequestData);
          setJsonData(jsonObj);
        } catch (error) {
          console.error("解析缓存的请求报文JSON失败:", error);
        }
      }
    } catch (error) {
      console.error("加载缓存数据失败:", error);
    }
  };

  // 处理页面激活时重新加载数据
  useActivate(() => {
    setTimeout(() => {
      loadCachedData();
      // 不在这里调用handleTransactionCodeChange，因为loadCachedData中已经处理了获取模板逻辑
    }, 0);
  });

  // 处理页面失活时清理状态
  useUnactivate(() => {
    // 清理加载状态
    setLoading(false);
    setResponseLoading(false);
  });

  // 获取交易码列表
  useEffect(() => {
    fetchTransactionList();
  }, []);

  const fetchTransactionList = async () => {
    try {
      console.log("开始获取交易码列表");
      const data = await getPcsMessage();
      console.log("获取到的交易码列表:", data);
      setTransactionList(data);
      console.log("设置交易码列表后的状态:", transactionList); // 注意：这里可能看不到更新后的值，因为setState是异步的
    } catch (error) {
      console.error("获取交易码列表失败:", error);
      setTransactionList([]);
    }
  };

  // 处理字段值更新
  const handleFieldValueChange = (
    record: FieldDescription,
    newValue: string
  ) => {
    try {
      const jsonObj = JSON.parse(requestData);

      // 处理嵌套路径
      const pathParts = record.fieldCode.split(".");
      let current = jsonObj;

      // 遍历路径直到倒数第二层
      for (let i = 0; i < pathParts.length - 1; i++) {
        const part = pathParts[i];
        if (!current[part]) {
          current[part] = {};
        }
        current = current[part];
      }

      // 设置最后一层的值
      const lastPart = pathParts[pathParts.length - 1];

      // 如果是对象类型，尝试解析JSON
      if (record.isObject) {
        try {
          current[lastPart] = JSON.parse(newValue);
        } catch (e) {
          // 如果解析失败，保持原样
          current[lastPart] = newValue;
        }
      } else {
        // 尝试转换为数字或布尔值
        if (newValue === "true") {
          current[lastPart] = true;
        } else if (newValue === "false") {
          current[lastPart] = false;
        } else if (!isNaN(Number(newValue)) && newValue.trim() !== "") {
          current[lastPart] = Number(newValue);
        } else {
          current[lastPart] = newValue;
        }
      }

      setRequestData(JSON.stringify(jsonObj, null, 2));

      // 清空响应数据
      setResponseData("");

      // 更新字段描述列表中的值
      const updatedFields = fieldDescriptions.map((field) => {
        if (field.fieldCode === record.fieldCode) {
          return { ...field, fieldValue: newValue };
        }
        return field;
      });
      setFieldDescriptions(updatedFields);
    } catch (error) {
      console.error("更新字段值失败:", error);
      message.error("更新字段值失败");
    }
  };

  // 发送请求
  const handleSend = async () => {
    try {
      setResponseLoading(true);

      // 1. 获取当前环境和交易码
      const currentEnv = localStorage.getItem("currentEnv") || "";
      if (!currentEnv) {
        message.error("请先选择环境");
        setResponseLoading(false);
        return;
      }

      if (!transactionCode) {
        message.error("请先选择交易码");
        setResponseLoading(false);
        return;
      }

      if (currentEnv === "DEVS") {
        message.error("当前环境DEVS受保护,不允许发送请求");
        setResponseLoading(false);
        return;
      }

      // 2. 直接根据环境构建代理URL
      const urlInfo = getProxyUrls(currentEnv);
      if (!urlInfo.macUrl) {
        message.error("获取MAC地址失败");
        setResponseLoading(false);
        return;
      }

      if (!urlInfo.requestUrl) {
        message.error("获取请求地址失败");
        setResponseLoading(false);
        return;
      }

      // 3. 解析请求数据
      let requestObj;
      try {
        requestObj = JSON.parse(requestData);
      } catch (error) {
        message.error("请求报文格式错误");
        setResponseLoading(false);
        return;
      }

      // 4. 生成时间戳和交易跟踪号
      // 获取当前日期和时间
      const date = new Date();

      // 格式化日期为 YYYYMMDD
      const curYearMonDay =
        date.getFullYear() +
        (date.getMonth() + 1).toString().padStart(2, "0") +
        date.getDate().toString().padStart(2, "0");

      // 格式化时间为 HHMMSSNNN + 两位随机数 (共9位)
      const time1 =
        date.getHours().toString().padStart(2, "0") +
        date.getMinutes().toString().padStart(2, "0") +
        date.getSeconds().toString().padStart(2, "0") +
        date.getMilliseconds().toString().padStart(3, "0");

      // 生成格式化时间戳 YYYYMMDDHHMMSSNNN
      const generateTimestamp = (date: Date): string => {
        const year = date.getFullYear().toString();
        const month = (date.getMonth() + 1).toString().padStart(2, "0");
        const day = date.getDate().toString().padStart(2, "0");
        const hours = date.getHours().toString().padStart(2, "0");
        const minutes = date.getMinutes().toString().padStart(2, "0");
        const seconds = date.getSeconds().toString().padStart(2, "0");
        const milliseconds = date.getMilliseconds().toString().padStart(3, "0");

        return `${year}${month}${day}${hours}${minutes}${seconds}${milliseconds}`;
      };

      // 生成子交易号 (32位)
      // 固定前缀(23位) + 日期(8位) + 随机字符(1位)
      const randomChar = Math.floor(Math.random() * 10).toString(); // 生成0-9的随机数字
      const subtxNo = "10221990001111000000000" + randomChar + curYearMonDay;

      // 生成时间戳 (YYYYMMDDHHMMSSNNN 格式，17位)
      const timestamp1 = generateTimestamp(date);

      // 生成交易开始时间
      const txStartTime = curYearMonDay + time1;

      // 生成全局业务跟踪号 (32位)
      // 时间戳(14位) + 源系统(7位) + 发起交易实例序号(5位) + 序列号(6位)
      const globalBusiTrackNo =
        txStartTime +                   // 时间戳 (17位)
        "1022199" +                     // 源系统 (7位)
        "CK001" +                       // 发起交易实例序号 (5位)
        Math.floor(Math.random() * 1000)
          .toString()
          .padStart(3, "0");            // 序列号 (3位)

      // 修改请求头中的tenantId字段
      if (requestObj.txHeader.tenantId) {
        if (currentEnv != "TEST") {
          requestObj.txHeader.tenantId = currentEnv;
        } else {
          requestObj.txHeader.tenantId = "QHGD";
        }
      }

      // 5. 更新请求对象中的字段
      if (requestObj.txHeader) {
        requestObj.txHeader.subtxNo = subtxNo;
        requestObj.txHeader.txStartTime = timestamp1;
        requestObj.txHeader.txSendTime = timestamp1;
        requestObj.txHeader.globalBusiTrackNo = globalBusiTrackNo;
      }

      // 创建一个深拷贝的请求对象，避免重复请求
      const requestObjForMac = JSON.parse(JSON.stringify(requestObj));

      // 抽取处理数字类型的函数
      const processNumericFields = (obj: any) => {
        if (obj?.txBody?.txComn1) {
          // 处理 bgnIndexNo
          if (obj.txBody.txComn1.bgnIndexNo) {
            if (typeof obj.txBody.txComn1.bgnIndexNo === "string") {
              const numValue = parseInt(obj.txBody.txComn1.bgnIndexNo, 10);
              obj.txBody.txComn1.bgnIndexNo = !isNaN(numValue) ? numValue : 1;
            }
          }
          // 处理 curQryReqNum
          if (obj.txBody.txComn1.curQryReqNum) {
            if (typeof obj.txBody.txComn1.curQryReqNum === "string") {
              const numValue = parseInt(obj.txBody.txComn1.curQryReqNum, 10);
              obj.txBody.txComn1.curQryReqNum = !isNaN(numValue) ? numValue : 10;
            }
          }
          // 处理 execRetryTimes
          if (obj.txBody.txComn1.execRetryTimes) {
            if (typeof obj.txBody.txComn1.execRetryTimes === "string") {
              const numValue = parseInt(obj.txBody.txComn1.execRetryTimes, 10);
              obj.txBody.txComn1.execRetryTimes = !isNaN(numValue) ? numValue : 10;
            }
          }
        }
      };

      // 处理 MAC 请求对象中的数字类型
      processNumericFields(requestObjForMac);

      // 6. 获取MAC值
      try {
        let macValue = "";
        if (currentEnv === "DEV1" || currentEnv === "DEV2"
          || currentEnv === "TEST" || currentEnv === "DEV5"
          || currentEnv === "DEVS") {
          macValue = "068A29C6270CB954";  // 组装环境不校验mac，故写死一个固定值
        } else {
          macValue = await getMacValue(urlInfo.macUrl, requestObjForMac);
        }
        if (requestObj.txHeader) {
          requestObj.txHeader.msgrptMac = macValue;
        }
      } catch (error: any) {
        message.error("获取MAC值失败: " + (error.message || "未知错误"));
        setResponseLoading(false);
        return;
      }

      // 处理最终请求对象中的数字类型
      processNumericFields(requestObj);

      // 7. 发送最终请求
      try {
        const responseData = await sendExternalTransaction(
          urlInfo.requestUrl,
          requestObj
        );
        setResponseData(JSON.stringify(responseData, null, 2));

        // 8. 自动保存执行记录（无需用户感知）
        try {
          // 准备保存记录的参数
          const autoSaveParams: SaveCaseParams = {
            caseNo: '', // 案例编号留空
            caseName: '', // 案例名称留空
            tenantId: currentEnv,
            pcsNo: transactionCode,
            request: JSON.stringify(requestObj, null, 2),
            response: JSON.stringify(responseData, null, 2),
          };

          // 静默调用保存方法（不显示成功消息，不影响用户体验）
          await saveCase(autoSaveParams);
        } catch (saveError) {
          // 静默处理保存错误，不影响主流程
          console.error('自动保存执行记录失败:', saveError);
        }
      } catch (error: any) {
        setResponseData(
          JSON.stringify({ error: error.message || "未知错误" }, null, 2)
        );
      }
    } catch (error: any) {
      console.error("发送请求失败:", error);
      setResponseData(
        JSON.stringify({ error: error.message || "未知错误" }, null, 2)
      );
    } finally {
      setResponseLoading(false);
    }
  };

  // 修改清空方法，同时清除缓存
  const handleClear = () => {
    setRequestData("{}");
    setResponseData("");
    setTransactionCode(""); // 清空交易码

    // 清除缓存
    storage.remove(CACHE_KEYS.requestData);
    storage.remove(CACHE_KEYS.responseData);
    storage.remove(CACHE_KEYS.transactionCode);
  };

  // 复制文本到剪贴板
  const copyToClipboard = async (text: string, isRequest: boolean) => {
    try {
      // 尝试使用现代的Clipboard API
      if (navigator.clipboard && navigator.clipboard.writeText) {
        await navigator.clipboard.writeText(text);
      } else {
        // 如果Clipboard API不可用，使用传统方法
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        const successful = document.execCommand('copy');
        document.body.removeChild(textArea);
        if (!successful) {
          throw new Error('复制操作失败');
        }
      }

      // 更新状态并显示成功消息
      if (isRequest) {
        setRequestCopied(true);
        setTimeout(() => setRequestCopied(false), 2000); // 2秒后重置状态
        message.success('请求报文已复制到剪贴板');
      } else {
        setResponseCopied(true);
        setTimeout(() => setResponseCopied(false), 2000); // 2秒后重置状态
        message.success('响应报文已复制到剪贴板');
      }
    } catch (error) {
      console.error('复制失败:', error);
      message.error('复制失败，请重试');
    }
  };

  // 处理JSON字段值更新
  const handleJsonValueChange = (path: string, value: any) => {
    try {
      const jsonObj = JSON.parse(requestData);

      // 处理嵌套路径
      const pathParts = path.split(".");
      let current = jsonObj;

      // 遍历路径直到倒数第二层
      for (let i = 0; i < pathParts.length - 1; i++) {
        const part = pathParts[i];
        if (!current[part]) {
          current[part] = {};
        }
        current = current[part];
      }

      // 设置最后一层的值
      const lastPart = pathParts[pathParts.length - 1];

      // 检查是否为特殊处理的数字字段
      const isSpecialNumericField = path.endsWith('.bgnIndexNo') ||
                                   path.endsWith('.curQryReqNum') ||
                                   path.endsWith('.execRetryTimes');

      // 尝试转换为适当的类型
      if (value === "true") {
        current[lastPart] = true;
      } else if (value === "false") {
        current[lastPart] = false;
      } else if (value === "null" || value === "") {
        current[lastPart] = null;
      } else if (isSpecialNumericField && !isNaN(Number(value)) && value.trim() !== "") {
        // 只有特殊数字字段才转换为数字类型
        current[lastPart] = Number(value);
      } else {
        // 其他所有字段都保持字符串类型
        current[lastPart] = value;
      }

      // 更新请求数据
      const updatedJson = JSON.stringify(jsonObj, null, 2);
      setRequestData(updatedJson);
      setJsonData(jsonObj);

      // 清空响应数据
      setResponseData("");
    } catch (error) {
      console.error("更新字段值失败:", error);
      message.error("更新字段值失败");
    }
  };

  // 修改点击事件处理
  const handleEditorClick = () => {
    setIsRequestDoc(true);
    try {
      const jsonObj = JSON.parse(requestData);
      setJsonData(jsonObj);
      setDocDrawerVisible(true);
    } catch (error) {
      console.error("解析JSON失败:", error);
      message.error("解析JSON失败");
    }
  };

  // 处理交易码选择变更
  const handleTransactionCodeChange = async (value: string) => {
    setTransactionCode(value);

    // 清空响应区域
    setResponseData("");

    if (value) {
      try {
        setLoading(true); // 开始加载
        const { rawData, fields, fieldMappings } =
          await getPcsMessageByNo(value);
        if (rawData && typeof rawData === "object") {
          setRequestData(JSON.stringify(rawData, null, 2));
          setJsonData(rawData);
          setFieldDescriptions(fields);

          // 确保字段映射数据正确设置
          if (Array.isArray(fieldMappings) && fieldMappings.length > 0) {
            console.log("字段映射数据:", fieldMappings); // 添加日志以便调试
            setFieldMappings(fieldMappings);
          } else {
            console.warn("未获取到字段映射数据");
            setFieldMappings([]);
          }
        } else {
          setRequestData("{}");
          setJsonData({});
          setFieldDescriptions([]);
          setFieldMappings([]);
        }
      } catch (error) {
        console.error("获取报文数据失败:", error);
        message.error("获取报文数据失败");
        setRequestData("{}");
        setJsonData({});
        setFieldDescriptions([]);
        setFieldMappings([]);
      } finally {
        setLoading(false); // 结束加载
      }
    } else {
      setRequestData("{}");
      setJsonData({});
      setFieldDescriptions([]);
      setFieldMappings([]);
    }
  };

  // 获取响应报文字段映射
  const getResponseFieldMappingsData = async () => {
    try {
      const fieldMappings = await getResponseFieldMappings(transactionCode);
      setResponseFieldMappings(fieldMappings);
    } catch (error) {
      console.error('获取响应字段映射失败:', error);
      setResponseFieldMappings([]);
    }
  };

  // 修改响应区域点击事件
  const handleResponseClick = async () => {
    setIsRequestDoc(false);
    try {
      const jsonObj = JSON.parse(responseData);
      setJsonData(jsonObj);

      // 获取响应字段映射
      await getResponseFieldMappingsData();

      setDocDrawerVisible(true);
    } catch (error) {
      console.error('解析响应JSON失败:', error);
      message.error('解析响应JSON失败');
    }
  };

  // 打开历史报文模态框
  const handleHistoryClick = () => {
    setHistoryModalVisible(true);
  };

  // 使用历史报文 - 保留这个函数，将作为子组件的回调
  const useHistoryRecord = async (record: any) => {
    try {
      // 关闭模态框
      setHistoryModalVisible(false);
      // 清空响应区域
      setResponseData("");

      // 根据流水号获取详细信息
      setLoading(true);
      message.loading({ content: '加载历史报文中...', key: 'loadingMessage' });

      const response = await getPcsSendRecord(record.globalBusiTrackNo);

      if (response && response.data) {
        // 设置交易码
        setTransactionCode(record.pcsNo);

        // 解析data数据
        const parsedData = typeof response.data === 'string'
          ? JSON.parse(response.data)
          : response.data;

        if (Array.isArray(parsedData) && parsedData.length >= 2) {
          // 第一个元素包含jsonMessage
          let rawData = {};
          if (parsedData[0] && parsedData[0].jsonMessage) {
            // 处理可能存在的特殊字符，如\r、\n和多个反斜杠
            const cleanedJsonString = parsedData[0].jsonMessage
              .replace(/\\r/g, '')
              .replace(/\\n/g, '')
              .replace(/\\\\/g, '\\');

            try {
              rawData = JSON.parse(cleanedJsonString);

              // 设置请求数据
              setRequestData(JSON.stringify(rawData, null, 2));

              // 设置JSON数据供编辑器使用
              setJsonData(rawData);

              // 设置字段描述
              setFieldDescriptions(parseJsonObject(rawData));

              // 第二个元素包含字段映射
              const fieldMappings: FieldMapping[] = [];
              if (parsedData[1]) {
                for (const [fieldCode, fieldName] of Object.entries(parsedData[1])) {
                  fieldMappings.push({
                    fieldCode,
                    fieldName: fieldName as string
                  });
                }

                // 设置字段映射
                setFieldMappings(fieldMappings);
              }

              message.success({ content: '历史报文已加载', key: 'loadingMessage' });
            } catch (e) {
              console.error('解析jsonMessage失败:', e);
              message.error({ content: '解析历史请求报文失败', key: 'loadingMessage' });
            }
          } else {
            message.error({ content: '历史报文数据格式错误', key: 'loadingMessage' });
          }
        } else {
          message.error({ content: '历史报文数据格式错误', key: 'loadingMessage' });
        }
      } else {
        message.error({ content: '获取历史报文详情失败', key: 'loadingMessage' });
      }
    } catch (error) {
      console.error('使用历史报文失败:', error);
      message.error({ content: '使用历史报文失败', key: 'loadingMessage' });
    } finally {
      setLoading(false);
    }
  };

  // 添加保存案例处理函数
  const handleSaveCase = () => {
    if (!responseData) {
      message.error('请先发送请求获取响应报文后再保存');
      return;
    }
    setSaveCaseModalVisible(true);
  };

  // 保存案例提交
  const handleSaveCaseSubmit = async () => {
    try {
      const values = await saveCaseForm.validateFields();

      // 从localStorage获取当前环境
      const currentEnv = localStorage.getItem('currentEnv') || '';

      const params: SaveCaseParams = {
        caseNo: values.caseNo,
        caseName: values.caseName,
        tenantId: currentEnv,
        pcsNo: transactionCode,
        request: requestData,
        response: responseData,
      };

      await saveCase(params);
      message.success('案例保存成功');
      setSaveCaseModalVisible(false);
      saveCaseForm.resetFields();
    } catch (error) {
      console.error('保存案例失败:', error);
    }
  };

  // 取消保存案例
  const handleSaveCaseCancel = () => {
    setSaveCaseModalVisible(false);
    saveCaseForm.resetFields();
  };

  // 打开导入报文模态框
  const handleImportClick = () => {
    setImportModalVisible(true);
    setImportMessageContent("");
  };

  // 关闭导入报文模态框
  const handleImportCancel = () => {
    setImportModalVisible(false);
    setImportMessageContent("");
  };

  // 处理导入报文提交
  const handleImportSubmit = () => {
    try {
      // 检查输入是否为空
      if (!importMessageContent.trim()) {
        message.error('请输入报文内容');
        return;
      }

      // 尝试解析JSON，处理可能的JSON字符串格式
      let parsedMessage;
      try {
        // 首先尝试直接解析为JSON
        parsedMessage = JSON.parse(importMessageContent);
      } catch (e) {
        // 记录原始解析错误
        const originalError = e as Error;

        // 提取错误位置信息
        const positionMatch = originalError.message.match(/position (\d+)/);
        const lineColumnMatch = originalError.message.match(/at line (\d+) column (\d+)/);

        let errorPosition = '';
        if (positionMatch && positionMatch[1]) {
          const position = parseInt(positionMatch[1], 10);

          // 找出错误位置的行号和列号
          const lines = importMessageContent.substring(0, position).split('\n');
          const lineNumber = lines.length;
          const columnNumber = lines[lines.length - 1].length + 1;

          errorPosition = `错误位置: 第${lineNumber}行, 第${columnNumber}列`;

          // 找出问题行的内容
          const allLines = importMessageContent.split('\n');
          if (lineNumber <= allLines.length) {
            const problemLine = allLines[lineNumber - 1];
            errorPosition += `\n问题行内容: "${problemLine.trim()}"`;
          }
        } else if (lineColumnMatch && lineColumnMatch[1] && lineColumnMatch[2]) {
          const lineNumber = parseInt(lineColumnMatch[1], 10);
          const columnNumber = parseInt(lineColumnMatch[2], 10);

          errorPosition = `错误位置: 第${lineNumber}行, 第${columnNumber}列`;

          // 找出问题行的内容
          const allLines = importMessageContent.split('\n');
          if (lineNumber <= allLines.length) {
            const problemLine = allLines[lineNumber - 1];
            errorPosition += `\n问题行内容: "${problemLine.trim()}"`;
          }
        }

        // 如果直接解析失败，尝试将其当作转义的JSON字符串处理
        try {
          // 去除可能的引号并处理转义字符
          const cleanedContent = importMessageContent
            .trim()
            .replace(/^"(.*)"$/, '$1') // 去除首尾的引号
            .replace(/\\"/g, '"')      // 处理转义的引号
            .replace(/\\\\/g, '\\');   // 处理转义的反斜杠

          parsedMessage = JSON.parse(cleanedContent);
        } catch (err) {
          // 如果所有尝试都失败，则报文格式不符合要求
          // 结合原始错误和位置信息提供更有用的错误消息
          const errorMsg = `报文格式错误，不满足JSON格式要求\n${errorPosition}\n可能原因: ${(originalError.message || '').replace('JSON.parse: ', '').replace(' at ', '在 ')}`;
          console.error('JSON解析错误:', errorMsg);

          Modal.error({
            title: 'JSON解析错误',
            content: <div style={{ whiteSpace: 'pre-line' }}>{errorMsg}</div>,
            width: 500,
          });
          return;
        }
      }

      // 检查是否是有效的对象
      if (typeof parsedMessage !== 'object' || parsedMessage === null) {
        message.error('报文格式错误，不满足JSON格式要求');
        return;
      }

      // 提取交易码
      const servNo = parsedMessage?.txHeader?.servNo;
      if (!servNo) {
        message.error('报文中未找到交易码(txHeader.servNo)');
        return;
      }

      // 确保txHeader存在
      if (!parsedMessage.txHeader) {
        parsedMessage.txHeader = {};
      }

      // 替换或添加指定字段
      parsedMessage.txHeader.globalBusiTrackNo = "{{globalBusiTrackNo}}";
      parsedMessage.txHeader.subtxNo = "{{subtxNo}}";
      parsedMessage.txHeader.txStartTime = "{{txStartTime}}";
      parsedMessage.txHeader.txSendTime = "{{txSendTime}}";
      parsedMessage.txHeader.msgrptMac = "{{msgrptMac}}";

      // 设置交易码
      setTransactionCode(servNo);

      // 更新请求数据
      setRequestData(JSON.stringify(parsedMessage, null, 2));
      setJsonData(parsedMessage);

      // 清空响应数据
      setResponseData("");

      // 关闭模态框
      setImportModalVisible(false);

      // 显示成功消息
      message.success('报文导入成功');

      // 获取交易码对应的字段描述和映射
      fetchFieldsAndMappings(servNo);

    } catch (error) {
      console.error('处理导入报文失败:', error);
      message.error('处理导入报文失败');
    }
  };

  // 获取字段描述和映射的辅助函数
  const fetchFieldsAndMappings = async (code: string) => {
    if (!code) return;

    try {
      setLoading(true);
      const { fields, fieldMappings } = await getPcsMessageByNo(code);

      if (fields) {
        setFieldDescriptions(fields);
      }

      if (fieldMappings) {
        setFieldMappings(fieldMappings);
      }
    } catch (error) {
      console.error("获取字段描述和映射失败:", error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={styles.container}>
      {/* 添加一个调试信息 */}
      {console.log("渲染时的交易列表:", transactionList)}

      <div className={styles.mainLayout}>
        <div className={styles.contentArea}>
          <div className={styles.urlBar}>
            <div className={styles.selectWrapper}>
              <span className={styles.selectLabel}>交易码</span>
              <Select
                showSearch
                allowClear
                value={transactionCode}
                onChange={handleTransactionCodeChange}
                className={styles.transactionSelect}
                placeholder="请选择交易码"
                optionFilterProp="children"
                filterOption={(input, option) =>
                  (option?.label ?? "")
                    .toLowerCase()
                    .includes(input.toLowerCase())
                }
                options={transactionList.map((item) => ({
                  value: item.code,
                  label: `${item.code}-${item.name}`,
                }))}
                style={{ width: '55%' }}
              />
            </div>
            <div className={styles.buttonGroup}>
              <Button
                type="default"
                icon={<ImportOutlined />}
                onClick={handleImportClick}
                className={styles.importButton}
              >
                导入报文
              </Button>
              <Button
                type="default"
                icon={<SaveOutlined />}
                className={styles.saveButton}
                onClick={handleSaveCase}
                style={{ marginLeft: '8px' }}
              >
                保存案例
              </Button>
              <Button
                type="default"
                icon={<HistoryOutlined />}
                className={styles.historyButton}
                onClick={handleHistoryClick}
                style={{ marginLeft: '8px' }}
              >
                历史报文
              </Button>
              <Button
                type="default"
                icon={<FileTextOutlined />}
                className={styles.referenceButton}
                style={{ marginLeft: '8px' }}
              >
                参考报文
              </Button>
              <Button
                danger
                icon={<ClearOutlined />}
                onClick={handleClear}
                className={styles.clearButton}
                style={{ marginLeft: '8px' }}
              >
                清空
              </Button>
              <Button
                type="primary"
                icon={<SendOutlined />}
                onClick={handleSend}
                className={styles.sendButton}
                style={{ marginLeft: '8px' }}
              >
                发送
              </Button>
            </div>
          </div>

          <div className={styles.requestResponseArea}>
            <div className={styles.requestArea}>
              <Tabs
                activeKey={activeTab}
                onChange={setActiveTab}
                className={styles.paramsTabs}
              >
                <TabPane tab="Request" key="Request">
                  <div
                    className={styles.codeEditorWrapper}
                  >
                    {loading ? (
                      <div className={styles.loadingContainer}>
                        <Spin
                          indicator={
                            <LoadingOutlined style={{ fontSize: 24 }} spin />
                          }
                          tip="正在加载报文数据..."
                        />
                      </div>
                    ) : (
                      <div className={styles.editorContainer}>
                        <div className={styles.editorActions}>
                          <Button
                            type="text"
                            icon={<CopyOutlined />}
                            className={styles.copyButton}
                            onClick={(e) => {
                              e.stopPropagation(); // 阻止事件冒泡
                              copyToClipboard(requestData, true);
                            }}
                          >
                            {requestCopied ? '已复制' : '复制'}
                          </Button>
                        </div>
                        <div onClick={handleEditorClick}>
                          <CodeMirror
                            value={requestData}
                            height="100%"
                            extensions={[json()]}
                            theme="light"
                            onChange={(value) => {
                              setRequestData(value);
                              // 清空响应区域
                              setResponseData("");
                            }}
                            className={styles.codeEditor}
                          />
                        </div>
                      </div>
                    )}
                  </div>
                </TabPane>
              </Tabs>
            </div>
            {/* 响应区域部分 */}
            <div className={styles.responseArea}>
              <div className={styles.responseHeader}>Response</div>
              <div
                className={styles.codeEditorWrapper}
              >
                {responseLoading ? (
                  <div className={styles.loadingContainer}>
                    <Spin
                      indicator={
                        <LoadingOutlined style={{ fontSize: 24 }} spin />
                      }
                      tip="正在处理请求..."
                    />
                  </div>
                ) : (
                  <div className={styles.editorContainer}>
                    <div className={styles.editorActions}>
                      <Button
                        type="text"
                        icon={<CopyOutlined />}
                        className={styles.copyButton}
                        onClick={(e) => {
                          e.stopPropagation(); // 阻止事件冒泡
                          copyToClipboard(responseData, false);
                        }}
                      >
                        {responseCopied ? '已复制' : '复制'}
                      </Button>
                    </div>
                    <div onClick={handleResponseClick}>
                      <CodeMirror
                        value={responseData}
                        height="100%"
                        extensions={[json()]}
                        theme="light"
                        editable={false}
                        className={styles.codeEditor}
                      />
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      <Drawer
        title={isRequestDoc ? "请求报文字段说明" : "响应报文字段说明"}
        placement="right"
        width={600}
        onClose={() => setDocDrawerVisible(false)}
        visible={docDrawerVisible}
        className={styles.fieldDrawer}
        maskStyle={{ background: "rgba(0, 0, 0, 0.45)" }}
        headerStyle={{
          borderBottom: "1px solid rgba(255, 255, 255, 0.1)",
          padding: "16px 24px",
        }}
        bodyStyle={{ padding: "0" }}
        destroyOnClose={false}
      >
        {isRequestDoc ? (
          <div className={styles.jsonTreeContainer}>
            <JsonFieldTree
              data={jsonData}
              onValueChange={handleJsonValueChange}
              fieldMappings={fieldMappings}
            />
          </div>
        ) : (
          <div className={styles.jsonTreeContainer}>
            <JsonFieldTree
              data={jsonData}
              onValueChange={handleJsonValueChange}
              fieldMappings={responseFieldMappings}
            />
          </div>
        )}
      </Drawer>

      {/* 保存案例模态框 */}
      <Modal
        title="保存案例"
        open={saveCaseModalVisible}
        onOk={handleSaveCaseSubmit}
        onCancel={handleSaveCaseCancel}
        destroyOnClose
        className={styles.saveCaseModal}
        width={550}
      >
        <Form
          form={saveCaseForm}
          layout="vertical"
        >
          <Form.Item
            name="caseNo"
            label="案例编号"
            rules={[{ required: true, message: '请输入案例编号' }]}
          >
            <Input placeholder="请输入案例编号" className={styles.caseInput} />
          </Form.Item>
          <Form.Item
            name="caseName"
            label="案例名称"
            rules={[{ required: true, message: '请输入案例名称' }]}
          >
            <Input placeholder="请输入案例名称" className={styles.caseInput} />
          </Form.Item>
        </Form>
      </Modal>

      {/* 历史报文模态框 */}
      <HistoryMessageModal
        visible={historyModalVisible}
        onCancel={() => setHistoryModalVisible(false)}
        onUse={useHistoryRecord}
        copyToClipboard={copyToClipboard}
        transactionList={transactionList}
        currentTransactionCode={transactionCode}
      />

      {/* 导入报文模态框 */}
      <Modal
        title="导入报文"
        open={importModalVisible}
        onOk={handleImportSubmit}
        onCancel={handleImportCancel}
        destroyOnClose
        className={styles.importModal}
        width={800}
        okText="确认导入"
        cancelText="取消"
      >
        <div style={{ marginBottom: '12px' }}>
          请输入要导入的报文内容(JSON格式)：
        </div>
        <TextArea
          value={importMessageContent}
          onChange={(e) => setImportMessageContent(e.target.value)}
          placeholder="请粘贴JSON格式的报文内容"
          autoSize={{ minRows: 15, maxRows: 25 }}
          className={styles.importTextArea}
        />
      </Modal>
    </div>
  );
};

// 带有缓存的组件
const CachedHttpTest: React.FC = () => (
  <KeepAlive name="HttpTest" when={true} id="HttpTest">
    <HttpTest />
  </KeepAlive>
);

export default CachedHttpTest;
