import { useActivate, useUnactivate } from "react-activation";
import {
  message,
  Button,
  Modal,
  Input,
  Form,
  Select,
  Spin,
  Drawer,
  Tabs,
  Typography,
  Space,
  Tooltip,
  Layout,
  Slider,
  Upload,
} from "antd";
import { useCallback, useEffect, useState, useRef, useMemo, memo } from "react";
import {
  ClearOutlined,
  SendOutlined,
  CopyOutlined,
  SaveOutlined,
  LoadingOutlined,
  FormatPainterOutlined,
  CheckSquareOutlined,
  FileExcelOutlined,
  SettingOutlined,
  UploadOutlined,
  ReloadOutlined,
  BugOutlined,
} from "@ant-design/icons";
import { useLocation } from "umi";
import queryString from "query-string";
import { json } from "@codemirror/lang-json";
import CodeMirror from "@uiw/react-codemirror";
import { debounce, throttle } from "lodash";
import styles from "./style.less";
import {
  getPcsMessageByNo,
  getProxyUrls,
  getHistoryTransactionCodes,
  sendExternalTransaction,
  getMacValue,
  saveCase,
  importExcelData,
  getBatchTasks,
  selectTbCaseBookByUniqueKey,
} from "./service";
import { KeepAlive } from "react-activation";
import { storage } from "@/utils/storageUtils";
import TaskMenu from "./components/Menu";
import CaseTitle from "./components/CaseTitle";
import CodeEditor from "./components/CodeEditor";
import type { UploadProps } from "antd";
import pageDataService from "../services/pageDataService"; // 引入 pageDataService
import { EditorView } from '@codemirror/view';
import { indentUnit } from '@codemirror/language';
// 在import部分添加ApiDataViewer导入
import ApiDataViewer from './ApiDataViewer';
// 引入新的工具函数
import { parseNestedJsonString } from './utils';

// Define TableListItem here to avoid import issues
interface TableListItem {
  testCircle?: string;
  tester?: string;
  testTaskNm?: string;
  testTaskCode: string;
  pcsPost?: string | null;
  pcsId?: string;
  uniqueKey?: string;
  autoFlag: number | string;
  assertFlag?: number | string;
  selected?: boolean;
}

const { TabPane } = Tabs;
const { TextArea } = Input;
const { Text, Title } = Typography;
const { Content, Sider } = Layout;

// 添加开发环境检测
const isDev = process.env.NODE_ENV === 'development';

// 使用debounce限制状态更新，特别是在开发模式下
const useDebounceState = <T,>(initialState: T, delay: number = isDev ? 200 : 0): [T, (value: T) => void] => {
  const [state, setState] = useState<T>(initialState);
  const lastValueRef = useRef<T>(initialState);
  
  const debouncedSetState = useCallback(
    debounce((value: T) => {
      setState(value);
    }, delay),
    []
  );
  
  // 优化的setState函数，会先比较新旧值是否相同
  const optimizedSetState = useCallback((value: T) => {
    // 检查是否相同值
    if (JSON.stringify(value) === JSON.stringify(lastValueRef.current)) {
      return;
    }
    
    // 更新引用值
    lastValueRef.current = value;
    
    // 使用防抖函数更新状态
    debouncedSetState(value);
  }, [debouncedSetState]);
  
  return [state, optimizedSetState];
};

interface TransactionCode {
  code: string;
  name: string;
}

// 定义 location.state 的类型
interface LocationState {
  pcsPost?: string;
  testTaskCode?: string;
  testTaskNm?: string;
  tester?: string;
  testCircle?: string;
  pcsId?: string;
  request?: string;
  rowData?: any;
  selectedTasks?: TableListItem[];
  autoFlag?: number | string;
  assertFlag?: number | string;
  uniqueKey?: string;
}

// 修改查询参数接口，为了兼容性保留旧字段
interface QueryParams {
  dataId?: string;
  batchMode?: string;
  // 为了兼容性保留的旧参数
  pcsId?: string;
  taskId?: string;
  uniqueKey?: string;
  testTaskName?: string;
  testTaskCode?: string;
  testCircle?: string;
}

// 先添加一个数据接口，表示从pageDataService接收的任务数据结构
interface TaskData {
  txNo: string;
  taskId: string;
  uniqueKey: string;
  completed: boolean;
  testTaskName: string;
  caseNo: string;
  pcsPost: string;
  record?: any;
  selectedTasks?: TableListItem[];
}

// 将CodeMirror组件包装成memo组件以减少不必要的重新渲染
const MemoizedCodeMirror = memo(CodeMirror);

/**
 * HTTP测试组件
 */
const HttpTest: React.FC = () => {
  // 将原来组件外部的状态变量移到这里
  const [debugMode, setDebugMode] = useState<boolean>(false);
  const [rawResponse, setRawResponse] = useState<string>("");
  const [showApiDataModal, setShowApiDataModal] = useState<boolean>(false);
  const [apiRawData, setApiRawData] = useState<string>("");
  
  const location = useLocation<LocationState>();
  const [transactionCode, setTransactionCode] = useState<string>("");
  const [transactionList, setTransactionList] = useState<TransactionCode[]>([]);
  const [requestData, setRequestDataRaw] = useDebounceState<string>("{}");
  const [responseData, setResponseDataRaw] = useDebounceState<string>("");
  const [activeTab, setActiveTab] = useState("Request");
  const [loading, setLoading] = useState<boolean>(false);
  const [responseLoading, setResponseLoading] = useState<boolean>(false);
  const [requestCopied, setRequestCopied] = useState<boolean>(false);
  const [responseCopied, setResponseCopied] = useState<boolean>(false);
  const [pcsPostProcessed, setPcsPostProcessed] = useState<boolean>(false);
  const [selectedTasks, setSelectedTasks] = useState<TableListItem[]>([]);
  const [fontSize, setFontSize] = useState<number>(14);
  const [isSettingsModalVisible, setIsSettingsModalVisible] =
    useState<boolean>(false);
  const [importLoading, setImportLoading] = useState<boolean>(false);
  // 记录是否已从pageDataService加载过数据
  const [dataLoaded, setDataLoaded] = useState<boolean>(false);
  // 添加一个状态变量，跟踪数据是否已从API加载
  const [dataFromApi, setDataFromApi] = useState<boolean>(false);
  // 添加状态标记，防止重复调用handleTaskSelect
  const [autoSelectDone, setAutoSelectDone] = useState<boolean>(false);
  // 添加一个状态记录当前的dataId，用于检测是否是新的请求
  const [currentDataId, setCurrentDataId] = useState<string | null>(null);
  // 添加标记，跟踪初始加载是否已完成，防止重复请求
  const [initialLoadComplete, setInitialLoadComplete] = useState<boolean>(false);
  // 添加ref来跟踪当前选中的任务的uniqueKey，用于防止重复请求
  const lastRequestedUniqueKeyRef = useRef<string | null>(null);
  // 添加请求锁标志，防止多个useEffect同时触发请求
  const [isRequestInProgress, setIsRequestInProgress] = useState<boolean>(false);
  // 在组件顶部添加请求跟踪ref
  const requestSentRef = useRef(false);
  // 在组件顶部定义请求参数引用
  const lastRequestParamsRef = useRef<{uniqueKey?: string, dataId?: string}>({});

  // 处理显示API原始数据的函数
  const showApiRawDataModal = useCallback((data: any) => {
    try {
      // 格式化数据用于显示
      const formattedData = typeof data === 'string' 
        ? data 
        : JSON.stringify(data, null, 2);
      
      // 设置API原始数据
      setApiRawData(formattedData);
      
      // 显示弹窗
      setShowApiDataModal(true);
    } catch (error) {
      console.error("格式化API数据失败:", error);
      message.error("无法显示API原始数据");
    }
  }, []);

  // 改进 useActivate 钩子来强制刷新数据，清除缓存
  useActivate(() => {
    console.log("PostPcs组件被激活");
    
    // 重置请求标记，允许重新发送请求
    requestSentRef.current = false;
    console.log("重置请求标记，允许发送新请求");
    
    // 清除可能的缓存数据
    localStorage.removeItem('http_test_request_data');
    sessionStorage.removeItem('http_test_request_data');
    Object.keys(CACHE_KEYS).forEach(key => {
      storage.remove(key);
    });
    console.log("已清除所有缓存数据");
    
    // 重置所有状态
    setRequestData("{}");
    setResponseData("");
    setDataFromApi(false);
    setIsRequestInProgress(false);
    lastRequestedUniqueKeyRef.current = null;
    
    // 获取当前URL的query参数
    const currentQuery = queryString.parse(location.search.substr(1)) as unknown as QueryParams;
    console.log("当前URL查询参数:", currentQuery);
    
    // 检查是否有新的dataId，或者是否与之前的dataId不同
    if (currentQuery.dataId) {
      console.log("检测到dataId，重置组件状态:", currentQuery.dataId);
      
      // 更新当前dataId
      setCurrentDataId(currentQuery.dataId);
      
      // 重置加载状态，允许重新加载数据
      setDataLoaded(false);
      setDataFromApi(false);
      setAutoSelectDone(false);
      setIsRequestInProgress(false); // 重置请求锁
      lastRequestedUniqueKeyRef.current = null; // 重置上一次请求的key
      
      // 清空已选中的任务
      setSelectedTasks([]);
      
      // 触发数据加载流程
      loadCachedData();
      fetchTransactionList();
    } else {
      console.log("激活时未检测到dataId:", currentDataId);
    }
  });
  
  // 组件停用时的处理
  useUnactivate(() => {
    console.log("PostPcs组件被停用");
    // 可以在这里添加一些清理逻辑，但不要重置dataLoaded或currentDataId
  });

  // 缓存时间设置（30分钟）
  const CACHE_EXPIRES_TIME = 30 * 60;

  // 缓存键名设置
  const CACHE_KEYS = {
    transactionCode: "http_test_transaction_code",
    requestData: "http_test_request_data",
    responseData: "http_test_response_data",
  };
  
  // 添加引用以追踪编辑器容器大小
  const requestEditorRef = useRef<HTMLDivElement>(null);
  const responseEditorRef = useRef<HTMLDivElement>(null);
  
  // 添加性能优化的wrapper函数，需要在使用前定义
  const setRequestData = useCallback((value: string) => {
    // 如果值相同，不触发更新
    if (value === requestData) return;
    setRequestDataRaw(value);
  }, [requestData, setRequestDataRaw]);

  const setResponseData = useCallback((value: string) => {
    // 如果值相同，不触发更新
    if (value === responseData) return;
    setResponseDataRaw(value);
  }, [responseData, setResponseDataRaw]);
  
  // 修复 basicExtensions，使用useMemo作为自定义hook
  const basicExtensions = useMemo(() => [
    json(),
    EditorView.lineWrapping,
    indentUnit.of('  '),
    EditorView.theme({
      "&": {
        height: "100%",
        maxHeight: "100%"
      },
      ".cm-scroller": {
        overflow: "auto"
      }
    })
  ], []);

  // 解析URL查询参数
  const query = queryString.parse(
    location.search.substr(1),
  ) as unknown as QueryParams;

  // 使用useMemo来存储taskData
  const taskData = useMemo(() => {
    if (!query.dataId) return null;
    return pageDataService.getData(query.dataId);
  }, [query.dataId]);

  // 同步编辑器尺寸的函数
  const syncEditorDimensions = useCallback(() => {
    if (requestEditorRef.current && responseEditorRef.current) {
      // 获取当前窗口可见高度
      const windowHeight = window.innerHeight;
      // 考虑编辑器上方元素高度，调整为可用空间的最大高度
      const availableHeight = windowHeight - 200; // 200px是顶部元素和padding的大致高度

      // 确保最小高度为500px，以容纳约20行代码
      const editorHeight = Math.max(500, availableHeight);

      // 设置相同高度
      requestEditorRef.current.style.height = `${editorHeight}px`;
      responseEditorRef.current.style.height = `${editorHeight}px`;
    }
  }, []);
  
  // 使用throttle来优化滚动和窗口调整事件
  const throttledSyncDimensions = useMemo(() => 
    throttle(syncEditorDimensions, isDev ? 200 : 100), 
  [syncEditorDimensions]);

  // 优化loadDataFromService，减少函数调用和状态更新
  const loadDataFromService = useCallback(() => {
    if (!query.dataId || dataLoaded) return false;

    try {
      // 从pageDataService获取数据
      const data = taskData;
      
      if (!data) {
        console.error("无法获取任务数据，可能数据已过期");
        return false;
      }
      
      console.log("从pageDataService获取数据:", query.dataId);
      
      // 批量处理所有状态更新，只触发一次渲染
      let hasUpdates = false;
      
      // 设置交易编号
      if (data.txNo && data.txNo !== transactionCode) {
        setTransactionCode(data.txNo);
        hasUpdates = true;
      }
      
      // 处理批量任务数据
      if (data.selectedTasks && Array.isArray(data.selectedTasks) && 
          (selectedTasks.length === 0 || JSON.stringify(selectedTasks) !== JSON.stringify(data.selectedTasks))) {
        setSelectedTasks(data.selectedTasks);
        hasUpdates = true;
      }
      
      // 设置请求数据
      if (data.pcsPost) {
        try {
          const pcsPost = data.pcsPost;
          const pcsPostStr =
            typeof pcsPost === "string" ? pcsPost : JSON.stringify(pcsPost);
          const jsonData = JSON.parse(pcsPostStr);
          const formattedJson = JSON.stringify(jsonData, null, 2);
          
          if (formattedJson !== requestData) {
            setRequestData(formattedJson);
            hasUpdates = true;
          }
        } catch (error) {
          // 如果JSON解析失败，直接使用原始字符串
          const rawData = typeof data.pcsPost === "string" 
            ? data.pcsPost 
            : JSON.stringify(data.pcsPost);
            
          if (rawData !== requestData) {
            setRequestData(rawData);
            hasUpdates = true;
          }
        }
      }
      
      // 只有在确实有更新时才标记数据已加载
      if (hasUpdates) {
        // 标记已加载数据，防止重复加载
        setDataLoaded(true);
        return true;
      }
      
      return false;
    } catch (error) {
      console.error("从pageDataService加载数据失败:", error);
      return false;
    }
  }, [query.dataId, dataLoaded, taskData, transactionCode, selectedTasks, requestData, setRequestData]);

  // 从缓存加载数据
  const loadCachedData = useCallback(async () => {
    try {
      // 首先尝试从pageDataService加载数据
      const dataLoaded = loadDataFromService();
      if (dataLoaded) return;
      
      const locationState = location.state || {};

      // 清空所有缓存数据
      storage.remove(CACHE_KEYS.transactionCode);
      storage.remove(CACHE_KEYS.requestData);
      storage.remove(CACHE_KEYS.responseData);

      // 处理批量任务数据
      if (
        locationState.selectedTasks &&
        Array.isArray(locationState.selectedTasks)
      ) {
        setSelectedTasks(locationState.selectedTasks);
      }

      // 如果 pcsPost 有值，加载并显示该值
      if (locationState.pcsPost) {
        try {
          const pcsPost = locationState.pcsPost;
          const pcsPostStr =
            typeof pcsPost === "string" ? pcsPost : JSON.stringify(pcsPost);
          const jsonData = JSON.parse(pcsPostStr);
          const formattedJson = JSON.stringify(jsonData, null, 2);
          setRequestData(formattedJson);
          // 标记数据来自页面缓存，防止被模拟数据覆盖
          setDataFromApi(true);
          return;
        } catch (error) {
          console.error("解析 pcsPost 数据失败:", error);
        }
      }

      // 如果 pcsPost 为 null 或不存在，清空所有内容
      setTransactionCode("");
      setRequestData("{}");
      setResponseData("");
    } catch (error) {
      console.error("加载数据失败:", error);
    }
  }, [
    CACHE_KEYS.transactionCode,
    CACHE_KEYS.requestData,
    CACHE_KEYS.responseData,
    location.state,
    loadDataFromService,
    setRequestData,
    setResponseData,
  ]);

  // 添加复制到剪贴板的功能
  const copyToClipboard = useCallback((text: string, isRequest: boolean) => {
    if (!text) {
      message.warning("无内容可复制");
      return;
    }
    
    navigator.clipboard.writeText(text)
      .then(() => {
        if (isRequest) {
          setRequestCopied(true);
          setTimeout(() => setRequestCopied(false), 2000);
        } else {
          setResponseCopied(true);
          setTimeout(() => setResponseCopied(false), 2000);
        }
        message.success("已复制到剪贴板");
      })
      .catch((error) => {
        console.error("复制失败:", error);
        message.error("复制失败，请重试");
      });
  }, []);
  
  // 添加发送请求的功能
  const handleSend = useCallback(async () => {
    if (!transactionCode) {
      message.error("请选择交易码");
      return;
    }

    try {
      setResponseLoading(true);
      
      // 解析请求数据
      let requestJson;
      try {
        requestJson = JSON.parse(requestData);
      } catch (error) {
        message.error("请求数据格式错误，请检查JSON语法");
        setResponseLoading(false);
        return;
      }
      
      // 发送请求
      const response = await sendExternalTransaction(
        "/api/transaction", // 添加API请求URL路径
        {
          txNo: transactionCode,
          data: requestJson
        }
      );
      
      if (response) {
        // 格式化响应数据
        try {
          const formattedResponse = JSON.stringify(response, null, 2);
          setResponseData(formattedResponse);
          setActiveTab("Response"); // 自动切换到响应标签
        } catch (error) {
          console.error("格式化响应数据失败:", error);
          setResponseData(JSON.stringify(response));
        }
      } else {
        setResponseData("{}");
        message.warning("未获取到响应数据");
      }
    } catch (error) {
      console.error("发送请求失败:", error);
      setResponseData(JSON.stringify({ error: "请求失败，请检查网络连接" }));
      message.error("发送请求失败");
    } finally {
      setResponseLoading(false);
    }
  }, [transactionCode, requestData, setResponseData]);

  // 获取交易码列表
  const fetchTransactionList = useCallback(async () => {
    try {
      console.log("使用静态交易码列表");
      // 使用静态数据代替getPcsMessage()
      const data = [
        { code: "P0001", name: "用户查询" },
        { code: "P0002", name: "账户查询" },
        { code: "P0003", name: "交易查询" },
        { code: "P0004", name: "系统管理" },
      ];
      console.log("静态交易码列表:", data);
      setTransactionList(data);
    } catch (error) {
      console.error("设置交易码列表失败:", error);
      setTransactionList([]);
    }
  }, []);

  // 添加防抖函数
  const debouncedSaveRequestData = debounce((data) => {
    if (data && data !== "{}") {
      storage.set(CACHE_KEYS.requestData, data, CACHE_EXPIRES_TIME);
    }
  }, 500);

  const debouncedSaveResponseData = debounce((data) => {
    if (data) {
      storage.set(CACHE_KEYS.responseData, data, CACHE_EXPIRES_TIME);
    }
  }, 500);
  
  // 对大型状态更新进行批量处理
  const updateMultipleStates = useCallback((updates: {
    requestData?: string;
    responseData?: string;
    transactionCode?: string;
    selectedTasks?: TableListItem[];
  }) => {
    // 批量更新状态，减少渲染次数
    Object.entries(updates).forEach(([key, value]) => {
      if (value !== undefined) {
        switch (key) {
          case 'requestData':
            if (value !== requestData) setRequestData(value as string);
            break;
          case 'responseData':
            if (value !== responseData) setResponseData(value as string);
            break;
          case 'transactionCode':
            if (value !== transactionCode) setTransactionCode(value as string);
            break;
          case 'selectedTasks':
            setSelectedTasks(value as TableListItem[]);
            break;
        }
      }
    });
  }, [requestData, responseData, transactionCode, setRequestData, setResponseData, setTransactionCode]);

  // 获取字段和字段映射数据 - 完全禁用
  const fetchFieldsAndMappings = useCallback(async (code: string) => {
    console.log("fetchFieldsAndMappings已被禁用，不会获取模拟数据");
    return;
  }, []);

  // 获取字段和字段映射数据的useEffect - 完全禁用模拟数据请求
  useEffect(() => {
    console.log("模拟数据加载已被禁用");
    // 不执行任何操作，确保不会加载模拟数据
  }, [transactionCode]);

  // 修改handleTransactionCodeChange函数，避免加载模拟数据
  const handleTransactionCodeChange = async (value: string) => {
    // 保存之前的dataFromApi状态
    const wasFromApi = dataFromApi;
    
    setTransactionCode(value);

    // 清空响应区域
    setResponseData("");

    // 不加载任何模拟数据，无论什么情况
    console.log("交易码已更改，但不会加载模拟数据");
    
    // 如果清空了交易码，则重置请求数据
    if (!value) {
      setRequestData("{}");
    }
  };

  // 获取交易码列表
  useEffect(() => {
    fetchTransactionList();
  }, [fetchTransactionList]);

  // 获取选中的任务数量
  const getSelectedTaskCount = useCallback(() => {
    return selectedTasks.filter((task) => task.selected).length;
  }, [selectedTasks]);

  // 修改handleSaveCase方法，确保使用正确的字段名
  const handleSaveCase = async () => {
    try {
      setLoading(true);

      // 如果未选择交易码，提示错误
      if (!transactionCode) {
        message.error("请选择交易码");
        setLoading(false);
        return;
      }

      // 从URL参数或pageDataService获取任务信息
      let taskData: TaskData | null = null;
      
      // 尝试从pageDataService获取数据
      if (query.dataId) {
        taskData = pageDataService.getData(query.dataId);
      }
      
      if (!taskData) {
        message.error("无法获取任务数据，请重新从任务列表进入");
        setLoading(false);
        return;
      }

      // 构建保存参数，使用新的字段名
      const params = {
        uniqueKey: taskData.uniqueKey || "",
        caseGroup: taskData.record?.caseGroup || "",
        caseAuthor: taskData.record?.caseAuthor || "",
        caseName: taskData.testTaskName || "",
        caseNo: taskData.caseNo || "",
        pcsPost: requestData,
        txNo: transactionCode,
        autoDataFlag: "1", // 表示已完成
        assertFlag: "1", // 默认断言标志为1
      };

      // 调用保存接口
      const response = await saveCase(params);

      if (response && response.code === 200) {
        message.success("保存成功");
      } else {
        message.error(`保存失败: ${response?.msg || "未知错误"}`);
      }
    } catch (error) {
      console.error("保存案例失败:", error);
      message.error("保存案例失败，请检查网络连接或联系管理员");
    } finally {
      setLoading(false);
    }
  };

  // 完全重写解析函数，直接保留原始响应便于调试
  const parseCaseResponseData = useCallback((responseData: any) => {
    try {
      // 保存原始响应内容，无论格式如何
      const originalResponseStr = typeof responseData === 'string' 
        ? responseData 
        : JSON.stringify(responseData, null, 2);
      
      console.log("原始响应数据:", originalResponseStr);
      setRawResponse(originalResponseStr);
      
      // 开始解析
      console.log("开始解析，原始数据类型:", typeof responseData);
      
      // 第一步：使用parseNestedJsonString尝试解析任何复杂的嵌套JSON
      let caseData: any;
      
      if (typeof responseData === 'string') {
        console.log("输入是字符串，使用parseNestedJsonString处理");
        caseData = parseNestedJsonString(responseData);
      } else {
        caseData = responseData;
      }
      
      // 确保数据是对象类型
      if (typeof caseData !== 'object' || caseData === null) {
        console.error("解析后的数据不是有效对象");
        return {
          parsedPostPcs: typeof caseData === 'string' ? caseData : JSON.stringify(caseData),
          rawResponse: originalResponseStr
        };
      }
      
      console.log("第一步解析后的数据:", caseData);
      
      // 定位postPcs或pcsPost字段
      let targetField = null;
      
      // 检查存在哪些字段
      const hasPostPcs = 'postPcs' in caseData;
      const hasPcsPost = 'pcsPost' in caseData;
      
      console.log("检测到的字段:", {hasPostPcs, hasPcsPost});
      
      // 优先使用postPcs，如果不存在则使用pcsPost
      if (hasPostPcs) {
        targetField = caseData.postPcs;
        console.log("使用postPcs字段");
      } else if (hasPcsPost) {
        targetField = caseData.pcsPost;
        console.log("使用pcsPost字段");
      } else {
        console.warn("未找到postPcs或pcsPost字段");
        // 尝试在响应中查找所有可能的JSON对象字段
        for (const key in caseData) {
          if (caseData[key] && 
              (typeof caseData[key] === 'object' || 
               typeof caseData[key] === 'string')) {
            console.log(`尝试使用字段 ${key}`);
            targetField = caseData[key];
            break;
          }
        }
        
        // 如果仍未找到有效字段，使用整个响应
        if (targetField === null) {
          console.log("未找到任何有效字段，使用整个响应");
          targetField = caseData;
        }
      }
      
      console.log("提取的目标字段:", typeof targetField);
      
      // 处理目标字段
      let finalContent = "{}";
      
      // 如果字段是字符串，使用parseNestedJsonString函数进行处理
      if (typeof targetField === 'string') {
        console.log("目标字段是字符串，使用parseNestedJsonString处理");
        const processedField = parseNestedJsonString(targetField);
        
        // 如果处理后是对象，转为格式化的JSON字符串
        if (typeof processedField === 'object' && processedField !== null) {
          finalContent = JSON.stringify(processedField, null, 2);
          console.log("成功解析为对象并格式化");
        } else if (typeof processedField === 'string') {
          finalContent = processedField;
          console.log("解析后仍为字符串");
        } else {
          finalContent = String(processedField);
        }
      } else if (typeof targetField === 'object' && targetField !== null) {
        // 如果字段已经是对象，直接格式化
        try {
          finalContent = JSON.stringify(targetField, null, 2);
          console.log("成功将对象字段格式化为JSON");
        } catch (e) {
          console.error("格式化对象字段失败:", e);
          finalContent = "{}";
        }
      }
      
      console.log("最终解析的内容:", finalContent);
      
      // 返回完整结果对象，包含原始响应和解析结果
      return {
        ...caseData,
        parsedPostPcs: finalContent,
        rawResponse: originalResponseStr
      };
    } catch (error) {
      console.error("解析案例数据失败:", error);
      // 即使解析失败，也返回一个包含原始响应的对象
      return {
        parsedPostPcs: typeof responseData === 'string' ? responseData : JSON.stringify(responseData),
        rawResponse: typeof responseData === 'string' ? responseData : JSON.stringify(responseData)
      };
    }
  }, [setRawResponse]);

  // 修改useEffect，添加参数比较，避免重复请求
  useEffect(() => {
    // 记录当前查询参数，方便调试
    console.log("URL参数变化，当前参数:", query, "上次请求参数:", lastRequestParamsRef.current);
    
    // 如果有请求正在进行中，跳过
    if (isRequestInProgress) {
      console.log("已有请求正在进行中，跳过重复请求");
      return;
    }
    
    // 获取当前参数
    const uniqueKey = query.uniqueKey;
    const dataId = query.dataId;
    
    // 比较当前参数与上次请求参数，如果相同则跳过
    if (
      uniqueKey === lastRequestParamsRef.current.uniqueKey && 
      dataId === lastRequestParamsRef.current.dataId
    ) {
      console.log("参数未变化，跳过重复请求");
      return;
    }
    
    // 如果没有请求相关参数，退出
    if (!uniqueKey && !dataId) {
      console.log("没有请求参数，跳过API请求");
      return;
    }
    
    // 更新最后请求参数
    lastRequestParamsRef.current = { uniqueKey, dataId };
    
    // 清除所有缓存和模拟数据
    localStorage.removeItem('http_test_request_data');
    sessionStorage.removeItem('http_test_request_data');
    Object.keys(CACHE_KEYS).forEach(key => {
      storage.remove(key);
    });
    
    // 重置数据状态
    setRequestData("{}");
    setDataFromApi(false);
    
    // 如果有uniqueKey，直接获取数据
    if (uniqueKey) {
      console.log("使用uniqueKey获取数据:", uniqueKey);
      
      // 显示加载状态
      setLoading(true);
      
      // 标记请求正在进行中
      setIsRequestInProgress(true);
      
      // 立即清空当前数据，避免显示旧数据
      setRequestData("{}");
      
      // 调用API获取案例详情
      selectTbCaseBookByUniqueKey(uniqueKey)
        .then(apiResponse => {
          console.log("API调用返回结果:", apiResponse);
          
          // 显示API原始数据弹窗，用于检查问题
          showApiRawDataModal(apiResponse);
          
          // 直接记录原始响应，无论成功失败
          if (apiResponse) {
            const responseStr = typeof apiResponse === 'string' 
              ? apiResponse 
              : JSON.stringify(apiResponse, null, 2);
            setRawResponse(responseStr);
          }
          
          if (apiResponse && apiResponse.code === 200 && apiResponse.data) {
            // 使用定义在组件内的函数解析数据
            const caseData = parseCaseResponseData(apiResponse.data);
            
            if (!caseData) {
              console.error("解析案例数据失败");
              message.error("解析案例数据失败");
              setIsRequestInProgress(false);
              return;
            }
            
            console.log("解析后的案例数据:", caseData);
            
            // 设置交易编号
            if (caseData.txNo) {
              setTransactionCode(caseData.txNo);
            }
            
            // 强制标记为API数据
            setDataFromApi(true);
            
            // 使用定时器分离UI更新，确保状态变化能被React检测到
            setTimeout(() => {
              // 获取解析后的数据内容
              const contentToShow = caseData.parsedPostPcs || "{}";
              console.log(`设置请求数据 (${typeof contentToShow}, 长度: ${contentToShow.length})`);
              
              // 如果内容为空或只有空格，使用空对象
              if (!contentToShow.trim()) {
                setRequestData("{}");
              } else {
                setRequestData(contentToShow);
              }
              
              // 调用格式化函数，确保JSON格式化正确
              try {
                const jsonObj = JSON.parse(contentToShow);
                const formattedJson = JSON.stringify(jsonObj, null, 2);
                if (formattedJson !== contentToShow) {
                  console.log("自动格式化JSON");
                  setRequestData(formattedJson);
                }
              } catch (e) {
                // 不是有效JSON，保持原样
                console.log("内容不是有效JSON，保持原样");
              }
              
              // 标记请求完成和数据已加载
              setIsRequestInProgress(false);
              setDataLoaded(true);
              setInitialLoadComplete(true);
              
              // 创建任务项
              const singleTask = {
                testTaskCode: caseData.caseNo || "",
                testTaskNm: caseData.caseName || "",
                testCircle: caseData.caseGroup || "",
                tester: caseData.caseAuthor || "",
                pcsId: caseData.txNo || "",
                pcsPost: caseData.parsedPostPcs || null,
                uniqueKey: uniqueKey,
                autoFlag: caseData.autoDataFlag || "0",
                assertFlag: caseData.assertFlag || "0",
                selected: true
              };
              
              // 更新任务列表
              setSelectedTasks([singleTask]);
              
              // 通知成功
              message.success("成功加载案例数据");
              
              // 强制触发编辑器更新
              if (requestEditorRef.current) {
                const element = requestEditorRef.current;
                const event = new MouseEvent('click', {
                  view: window,
                  bubbles: true,
                  cancelable: true
                });
                element.dispatchEvent(event);
                
                // 添加一个类然后移除，强制触发重新渲染
                element.classList.add('force-update');
                setTimeout(() => {
                  element.classList.remove('force-update');
                }, 10);
              }
            }, 0);
          } else {
            console.error("获取案例详情失败:", apiResponse?.msg || "未知错误");
            message.error(`获取案例详情失败: ${apiResponse?.msg || "未知错误"}`);
            setIsRequestInProgress(false);
            setLoading(false);
          }
        })
        .catch(error => {
          console.error("API请求错误:", error);
          message.error("获取案例数据失败，请重试");
          setIsRequestInProgress(false);
          setLoading(false);
        });
    } 
    // 如果有dataId但没有uniqueKey，尝试从pageDataService获取
    else if (dataId) {
      const data = pageDataService.getData(dataId);
      console.log("从pageDataService获取的数据:", data);
      
      if (data?.uniqueKey) {
        console.log("使用pageDataService获取的uniqueKey调用API:", data.uniqueKey);
        
        // 显示加载状态
        setLoading(true);
        
        // 标记请求正在进行中
        setIsRequestInProgress(true);
        
        // 立即清空当前数据，避免显示旧数据
        setRequestData("{}");
        
        // 调用API获取案例详情
        selectTbCaseBookByUniqueKey(data.uniqueKey)
          .then(apiResponse => {
            console.log("API调用返回结果:", apiResponse);
            
            // 显示API原始数据弹窗，用于检查问题
            showApiRawDataModal(apiResponse);
            
            // 处理响应数据...
            if (apiResponse && apiResponse.code === 200 && apiResponse.data) {
              console.log("获取案例详情成功，原始返回:", typeof apiResponse.data, apiResponse.data);
              
              // 临时增加：输出详细的API返回信息
              try {
                const dataStr = JSON.stringify(apiResponse.data, null, 2);
                console.log('%c API原始数据: ', 'background: #222; color: #bada55', dataStr);
                
                // 显示确认框，便于查看API返回信息
                const showDataStr = dataStr.length > 1000 ? dataStr.substring(0, 1000) + '...(数据过长已截断)' : dataStr;
                setTimeout(() => {
                  alert(`API返回数据:\n${showDataStr}`);
                }, 500);
              } catch (err) {
                console.error('无法序列化API数据:', err);
              }
              
              // 使用新的解析函数解析嵌套的JSON数据
              const caseData = parseCaseResponseData(apiResponse.data);
              
              if (!caseData) {
                console.error("解析案例数据失败");
                message.error("解析案例数据失败");
                setIsRequestInProgress(false);
                setLoading(false);
                return;
              }
              
              // 处理响应数据...
              console.log("测试：获取到了API数据，详细信息查看弹窗");
              setLoading(false);
              setIsRequestInProgress(false);
              setDataLoaded(true);
            } else {
              console.error("获取案例详情失败:", apiResponse?.msg || "未知错误");
              message.error(`获取案例详情失败: ${apiResponse?.msg || "未知错误"}`);
              setIsRequestInProgress(false);
              setLoading(false);
            }
          })
          .catch(error => {
            console.error("API请求错误:", error);
            message.error("获取案例数据失败，请重试");
            setIsRequestInProgress(false);
            setLoading(false);
          });
      } else {
        console.log("无法从pageDataService获取uniqueKey");
        message.warning("无法获取唯一标识");
        setIsRequestInProgress(false);
      }
    }
  }, [query.uniqueKey, query.dataId, isRequestInProgress, CACHE_KEYS, setRequestData, setDataFromApi, setLoading, setIsRequestInProgress, setRawResponse, setTransactionCode, setDataLoaded, setInitialLoadComplete, setSelectedTasks, parseCaseResponseData, showApiRawDataModal]);

  // 修改handleTaskSelect函数，使用新的解析函数
  const handleTaskSelect = useCallback(async (task: TableListItem) => {
    if (!task) return;

    console.log("处理任务选择:", task.testTaskCode);
    
    // 检查是否有请求正在进行中
    if (isRequestInProgress) {
      console.log("已有请求正在进行中，跳过任务选择");
      return;
    }
    
    // 检查是否是重复请求
    if (task.uniqueKey && task.uniqueKey === lastRequestedUniqueKeyRef.current) {
      console.log("跳过重复的任务选择请求, uniqueKey:", task.uniqueKey);
      return;
    }
    
    // 记录当前请求的uniqueKey
    if (task.uniqueKey) {
      lastRequestedUniqueKeyRef.current = task.uniqueKey;
    }
    
    // 更新选中任务状态
    if (selectedTasks && selectedTasks.length > 0) {
      // 检查任务是否已选中
      const isAlreadySelected = selectedTasks.some(t => 
        (t.uniqueKey === task.uniqueKey || t.testTaskCode === task.testTaskCode) && t.selected);
      
      // 如果已经选中且数据已加载，则不需要重新加载数据
      if (isAlreadySelected) {
        console.log("任务已经选中，不重新加载数据");
        return;
      }
      
      // 更新所有任务的选中状态
      const updatedTasks = selectedTasks.map(t => ({
        ...t,
        selected: t.uniqueKey === task.uniqueKey || t.testTaskCode === task.testTaskCode
      }));
      setSelectedTasks(updatedTasks);
    }

    // 获取当前任务的uniqueKey
    const uniqueKey = task.uniqueKey || `${task.testTaskCode}_${task.testCircle}_${task.tester}`;
    
    console.log("准备调用selectTbCaseBookByUniqueKey，uniqueKey =", uniqueKey);
    console.log("完整的task对象:", JSON.stringify(task));
    
    try {
      // 显示加载状态
      setLoading(true);
      
      // 标记请求正在进行中
      setIsRequestInProgress(true);
      
      // 清空当前请求数据，强制刷新
      setRequestData("{}");
      
      // 确保uniqueKey是一个字符串
      const uniqueKeyString = String(uniqueKey);
      console.log("发送请求前的uniqueKey:", uniqueKeyString);
      
      // 调用后端接口获取案例详情
      const response = await selectTbCaseBookByUniqueKey(uniqueKeyString);
      
      // 显示API原始数据弹窗，用于检查问题
      showApiRawDataModal(response);
      
      // 处理响应数据
      if (response && response.code === 200 && response.data) {
        console.log("获取案例详情成功，原始返回:", typeof response.data, response.data);
        
        // 临时增加：输出详细的API返回信息
        try {
          const dataStr = JSON.stringify(response.data, null, 2);
          console.log('%c API原始数据: ', 'background: #222; color: #bada55', dataStr);
          
          // 显示确认框，便于查看API返回信息
          const showDataStr = dataStr.length > 1000 ? dataStr.substring(0, 1000) + '...(数据过长已截断)' : dataStr;
          setTimeout(() => {
            alert(`API返回数据:\n${showDataStr}`);
          }, 500);
        } catch (err) {
          console.error('无法序列化API数据:', err);
        }
        
        // 使用新的解析函数解析嵌套的JSON数据
        const caseData = parseCaseResponseData(response.data);
        
        if (!caseData) {
          console.error("解析案例数据失败");
          message.error("解析案例数据失败");
          setLoading(false);
          return;
        }
        
        console.log("解析后的案例数据:", caseData);
        
        // 更新交易编号（如果存在）
        if (caseData.txNo) {
          console.log("设置交易编号:", caseData.txNo);
          setTransactionCode(caseData.txNo);
        }
        
        // 强制设置dataFromApi=true，防止模拟数据覆盖
        setDataFromApi(true);
        
        // 使用解析后的postPcs字段更新请求数据 - 使用setTimeout确保UI更新
        if (caseData.parsedPostPcs) {
          console.log("设置请求数据为解析后的postPcs:", caseData.parsedPostPcs);
          
          // 强制使用setTimeout进行异步更新，确保状态更新并触发渲染
          setTimeout(() => {
            setRequestData(caseData.parsedPostPcs);
            console.log("已异步更新请求数据");
          }, 0);
        } else {
          console.log("无postPcs数据，设置空对象");
          // 如果没有postPcs数据，显示空对象
          setTimeout(() => {
            setRequestData("{}");
          }, 0);
        }
        
        // 清空响应数据区域
        setResponseData("");
        
        message.success("成功加载案例数据");
      } else {
        // 接口返回错误
        console.error("获取案例详情失败:", response?.msg || "未知错误");
        message.error(`获取案例详情失败: ${response?.msg || "未知错误"}`);
      }
    } catch (error) {
      console.error("请求案例详情时发生错误:", error);
      message.error("加载案例详情失败，请重试");
    } finally {
      // 无论成功失败都关闭加载状态
      setLoading(false);
      // 重置自动选择状态
      setAutoSelectDone(false);
      // 释放请求锁
      setIsRequestInProgress(false);
    }
  }, [selectedTasks, setSelectedTasks, setLoading, setTransactionCode, setRequestData, setResponseData, isRequestInProgress, parseCaseResponseData]);

  // 查找组件中的任务，并调用handleTaskSelect，但只执行一次
  useEffect(() => {
    // 如果正在进行初始加载，则跳过自动选择任务
    if (!initialLoadComplete) {
      console.log("初始加载未完成，跳过自动选择任务");
      return;
    }
    
    // 如果有请求正在进行中，跳过本次执行
    if (isRequestInProgress) {
      console.log("已有请求正在进行中，跳过自动选择任务");
      return;
    }
    
    // 强制在任务列表变化时尝试选择任务
    if (selectedTasks && selectedTasks.length > 0) {
      // 找到选中的任务
      const selectedTask = selectedTasks.find(task => task.selected);
      if (selectedTask) {
        // 添加任务跟踪信息
        console.log("选中的任务:", selectedTask);
        console.log("选中任务的pcsPost数据类型:", typeof selectedTask.pcsPost);
        console.log("选中任务的pcsPost内容:", selectedTask.pcsPost);
        
        // 检查是否为重复请求 - 允许一次强制刷新
        if (selectedTask.uniqueKey && selectedTask.uniqueKey === lastRequestedUniqueKeyRef.current) {
          console.log("跳过重复请求, uniqueKey:", selectedTask.uniqueKey);
          return;
        }
        
        // 打印详细的调试信息
        console.log("准备自动选择任务:", selectedTask);
        console.log("任务uniqueKey:", selectedTask.uniqueKey);
        
        // 记录请求的 uniqueKey
        if (selectedTask.uniqueKey) {
          lastRequestedUniqueKeyRef.current = selectedTask.uniqueKey;
        }
        
        // 自动调用handleTaskSelect处理选中的任务
        handleTaskSelect(selectedTask);
      } else {
        console.log("没有找到选中的任务，将第一个任务设为选中");
        // 如果没有找到选中的任务，强制选择第一个任务
        const firstTask = selectedTasks[0];
        if (firstTask) {
          const updatedTasks = selectedTasks.map((t, idx) => ({
            ...t,
            selected: idx === 0
          }));
          setSelectedTasks(updatedTasks);
          // 不立即调用handleTaskSelect，让下次更新时触发
        }
      }
    }
  }, [selectedTasks, handleTaskSelect, initialLoadComplete, isRequestInProgress]);

  // 添加重新加载数据的函数，可在界面上使用
  const reloadCaseData = useCallback(async () => {
    // 如果没有选中任务，不执行
    const selectedTask = selectedTasks.find(t => t.selected);
    if (!selectedTask) {
      message.warning("请先选择一个任务");
      return;
    }
    
    // 清除请求锁和上次请求记录
    setIsRequestInProgress(false);
    lastRequestedUniqueKeyRef.current = null;
    
    // 重置数据源标记，允许重新加载
    setDataFromApi(false);
    
    // 执行任务选择，触发数据加载
    console.log("强制重新加载任务数据:", selectedTask);
    handleTaskSelect(selectedTask);
  }, [selectedTasks, handleTaskSelect]);

  // 在应用初始化时清除会话存储中可能存在的模拟数据
  useEffect(() => {
    // 清除所有可能的模拟数据缓存
    localStorage.removeItem('http_test_request_data');
    sessionStorage.removeItem('http_test_request_data');
    
    // 清除storage工具类中的缓存
    Object.keys(CACHE_KEYS).forEach(key => {
      storage.remove(key);
    });
    
    console.log("已清除所有可能的模拟数据缓存");
  }, []);

  // 修复最后一次改动引入的错误，通过保留之前删除的关键函数和组件
  // 优化格式化JSON操作，添加try-catch避免错误和不必要的状态更新
  const formatJsonData = useCallback(() => {
    try {
      const jsonObj = JSON.parse(requestData);
      const formattedJson = JSON.stringify(jsonObj, null, 2);
      // 只在实际有变化时更新状态
      if (formattedJson !== requestData) {
        setRequestData(formattedJson);
        // 避免在开发模式下使用message，减少渲染
        if (!isDev) {
          message.success("请求JSON格式化成功");
        }
      }
    } catch (error) {
      message.error("请求JSON格式化失败，请检查JSON语法");
    }
  }, [requestData, setRequestData]);

  // 优化代码编辑器处理函数
  const handleRequestDataChange = useCallback((value: string) => {
    // 避免不必要的状态更新
    if (value !== requestData) {
      setRequestData(value);
    }
  }, [requestData, setRequestData]);

  // 处理设置按钮点击
  const handleSettingsClick = () => {
    setIsSettingsModalVisible(true);
  };

  // 处理字体大小变更
  const handleFontSizeChange = (value: number) => {
    setFontSize(value);
    // 更新编辑器样式
    const editorElements = document.querySelectorAll(".cm-editor");
    editorElements.forEach((editor) => {
      (editor as HTMLElement).style.fontSize = `${value}px`;
    });
  };

  // 处理设置确认
  const handleSettingsConfirm = () => {
    setIsSettingsModalVisible(false);
    message.success("设置已保存");
  };

  // 使用React.lazy懒加载不立即需要的组件
  const SettingsModal = useMemo(() => memo(() => (
    <Modal
      title="编辑器设置"
      open={isSettingsModalVisible}
      onOk={handleSettingsConfirm}
      onCancel={() => setIsSettingsModalVisible(false)}
      okText="确定"
      cancelText="取消"
    >
      <div style={{ padding: "20px 0" }}>
        <div style={{ marginBottom: "20px" }}>
          <div style={{ marginBottom: "10px" }}>
            <Text strong>字体大小：{fontSize}px</Text>
          </div>
          <Slider
            min={12}
            max={24}
            value={fontSize}
            onChange={handleFontSizeChange}
            marks={{
              12: "12px",
              16: "16px",
              20: "20px",
              24: "24px",
            }}
          />
        </div>
      </div>
    </Modal>
  )), [isSettingsModalVisible, fontSize, handleSettingsConfirm, handleFontSizeChange]);

  // 使用条件渲染减少不必要的组件渲染
  const renderSettingsModal = isSettingsModalVisible ? <SettingsModal /> : null;

  // 处理Excel导入
  const handleExcelImport = async (file: File) => {
    try {
      setImportLoading(true);

      // 创建FormData对象
      const formData = new FormData();
      formData.append("file", file);

      // 添加当前选中的任务数据
      if (selectedTasks.length > 0) {
        const selectedTaskData = selectedTasks.map((task) => ({
          testTaskCode: task.testTaskCode,
          testTaskNm: task.testTaskNm,
          pcsId: task.pcsId,
          uniqueKey: task.uniqueKey,
          autoFlag: task.autoFlag,
          assertFlag: task.assertFlag,
        }));
        formData.append("taskData", JSON.stringify(selectedTaskData));
      }

      // 调用导入接口
      const response = await importExcelData(formData);

      if (response && response.code === 200) {
        message.success("Excel导入成功");
        // 可以在这里处理导入成功后的逻辑，比如刷新数据等
      } else {
        message.error(response?.message || "导入失败");
      }
    } catch (error: any) {
      console.error("导入Excel失败:", error);
      message.error(error?.message || "导入Excel失败，请重试");
    } finally {
      setImportLoading(false);
    }
  };

  // 配置Upload组件的属性
  const uploadProps: UploadProps = {
    name: "file",
    accept: ".xlsx,.xls",
    showUploadList: false,
    beforeUpload: (file) => {
      const isExcel =
        file.type ===
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" ||
        file.type === "application/vnd.ms-excel";
      if (!isExcel) {
        message.error("只能上传Excel文件！");
        return false;
      }
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        message.error("文件大小不能超过10MB！");
        return false;
      }
      handleExcelImport(file);
      return false; // 阻止自动上传
    },
  };

  // 替换ResizeObserver的使用
  useEffect(() => {
    // 初始化同步
    syncEditorDimensions();
    
    // 使用ResizeObserver替代window resize事件
    const resizeObserver = new ResizeObserver(() => {
      syncEditorDimensions();
    });
    
    // 观察容器变化
    if (requestEditorRef.current) {
      resizeObserver.observe(requestEditorRef.current);
    }
    
    // 注册窗口调整事件，但使用throttle避免过多触发
    window.addEventListener('resize', syncEditorDimensions);
    
    // 清理函数
    return () => {
      resizeObserver.disconnect();
      window.removeEventListener('resize', syncEditorDimensions);
    };
  }, [syncEditorDimensions]);

  // 添加一个完全重置的函数，确保状态干净
  const forceResetAndRefresh = useCallback(() => {
    console.log("强制重置并刷新数据");
    
    // 重置所有与请求相关的状态
    setIsRequestInProgress(false);
    lastRequestParamsRef.current = {};
    requestSentRef.current = false;
    
    // 清空数据和缓存
    setRequestData("{}");
    setResponseData("");
    setDataFromApi(false);
    setDataLoaded(false);
    
    // 清除本地存储和会话存储中的数据
    localStorage.removeItem('http_test_request_data');
    sessionStorage.removeItem('http_test_request_data');
    Object.keys(CACHE_KEYS).forEach(key => {
      storage.remove(key);
    });
    
    // 获取当前选中的任务
    const selectedTask = selectedTasks.find(t => t.selected);
    
    if (selectedTask?.uniqueKey) {
      console.log("强制刷新数据，重新请求:", selectedTask.uniqueKey);
      
      // 修复类型错误：确保uniqueKey是string类型
      const safeUniqueKey = String(selectedTask.uniqueKey);
      
      // 延迟10ms再发送请求，确保状态更新已完成
      setTimeout(() => {
        // 不使用fetchCaseDetail函数，直接调用API
        // 显示加载状态
        setLoading(true);
        
        // 标记请求正在进行中
        setIsRequestInProgress(true);
        
        // 直接调用API获取数据
        selectTbCaseBookByUniqueKey(safeUniqueKey)
          .then(apiResponse => {
            console.log("API调用返回结果:", apiResponse);
            
            // 显示API原始数据弹窗，用于检查问题
            showApiRawDataModal(apiResponse);
            
            // 临时增加：输出详细的API返回信息
            try {
              const dataStr = JSON.stringify(apiResponse, null, 2);
              console.log('%c API原始数据: ', 'background: #222; color: #bada55', dataStr);
            } catch (err) {
              console.error('无法序列化API数据:', err);
            }
            
            if (apiResponse && apiResponse.code === 200 && apiResponse.data) {
              // 使用定义在组件内的函数解析数据
              const caseData = parseCaseResponseData(apiResponse.data);
              
              if (caseData && caseData.parsedPostPcs) {
                // 查看解析后的数据，以便调试
                console.log("解析后的数据:", caseData.parsedPostPcs);
                
                // 使用timeout确保UI状态更新
                setTimeout(() => {
                  // 设置交易编号
                  if (caseData.txNo) {
                    setTransactionCode(caseData.txNo);
                  }
                  
                  // 设置请求数据
                  setRequestData(caseData.parsedPostPcs);
                  
                  // 重置标记，完成加载
                  setIsRequestInProgress(false);
                  setLoading(false);
                  setDataFromApi(true);
                  setDataLoaded(true);
                  
                  message.success("数据刷新成功");
                  
                  // 强制触发编辑器刷新
                  if (requestEditorRef.current) {
                    // 添加自定义事件触发DOM更新
                    requestEditorRef.current.dispatchEvent(new CustomEvent('editor-refresh'));
                  }
                }, 100);
              } else {
                setIsRequestInProgress(false);
                setLoading(false);
                message.error("解析数据失败");
              }
            } else {
              setIsRequestInProgress(false);
              setLoading(false);
              message.error("获取数据失败");
            }
          })
          .catch(err => {
            console.error("刷新数据出错:", err);
            setIsRequestInProgress(false);
            setLoading(false);
            message.error("请求失败");
          });
      }, 10);
    } else if (query.uniqueKey) {
      console.log("使用URL参数中的uniqueKey刷新数据:", query.uniqueKey);
      
      // 修复类型错误：确保uniqueKey是string类型
      const safeUniqueKey = String(query.uniqueKey);
      
      // 延迟10ms再发送请求，确保状态更新已完成
      setTimeout(() => {
        // 同样直接调用API
        setLoading(true);
        setIsRequestInProgress(true);
        
        selectTbCaseBookByUniqueKey(safeUniqueKey)
          .then(apiResponse => {
            // 处理响应...
            setIsRequestInProgress(false);
            setLoading(false);
            message.success("数据刷新成功");
          })
          .catch(err => {
            console.error("刷新数据出错:", err);
            setIsRequestInProgress(false);
            setLoading(false);
            message.error("请求失败");
          });
      }, 10);
    } else {
      message.warning("无法获取唯一标识，请选择一个任务");
    }
  }, [selectedTasks, query.uniqueKey, setIsRequestInProgress, setRequestData, setResponseData, setDataFromApi, setDataLoaded, CACHE_KEYS, setLoading, parseCaseResponseData, showApiRawDataModal, setTransactionCode]);

  // 修改刷新数据按钮处理函数，使用强制重置函数
  const handleRefreshData = useCallback(() => {
    // 调用强制重置函数来刷新数据
    forceResetAndRefresh();
  }, [forceResetAndRefresh]);

  // 添加切换调试模式的函数
  const toggleDebugMode = useCallback(() => {
    setDebugMode(prevMode => !prevMode);
    console.log("调试模式:", !debugMode ? "开启" : "关闭");
  }, [debugMode]);

  // 复制API数据
  const copyApiRawData = useCallback(() => {
    if (!apiRawData) {
      message.warning("没有数据可复制");
      return;
    }
    
    navigator.clipboard.writeText(apiRawData)
      .then(() => {
        message.success("已复制原始API数据");
      })
      .catch((error) => {
        console.error("复制失败:", error);
        message.error("复制失败，请重试");
      });
  }, [apiRawData]);

  return (
    <Layout
      style={{
        height: "calc(100vh - 60px)",
        paddingTop: "30px",
        margin: "20px",
        background: "#f0f2f5",
        borderRadius: "8px",
        boxShadow: "0 4px 12px rgba(0, 0, 0, 0.05)",
      }}
    >
      {selectedTasks && selectedTasks.length > 0 && (
        <Sider
          width={250}
          theme="light"
          style={{
            marginTop: "10px",
            overflow: "auto",
            background: "transparent",
            paddingLeft: "20px",
          }}
          className={styles.taskNavigator}
        >
          <TaskMenu
            onTaskSelect={handleTaskSelect}
            selectedTasks={selectedTasks}
            onTaskSelectionChange={setSelectedTasks}
          />
        </Sider>
      )}
      <Content
        style={{
          padding: "10px 20px 20px 20px",
          overflow: "auto",
        }}
      >
        <div className={styles.container}>
          <div className={styles.mainLayout}>
            <div className={styles.contentArea}>
              <div className={styles.urlBar}>
                <CaseTitle taskData={taskData} />
                <div className={styles.buttonGroup}>
                  {selectedTasks.length > 0 && (
                    <Upload {...uploadProps}>
                      <Button
                        type="default"
                        icon={<FileExcelOutlined />}
                        className={styles.selectAllButton}
                        style={{ marginRight: "8px" }}
                        loading={importLoading}
                      >
                        导入excel
                      </Button>
                    </Upload>
                  )}
                  <Button
                    type="default"
                    icon={<SaveOutlined />}
                    className={styles.saveButton}
                    onClick={handleSaveCase}
                  >
                    保存案例
                  </Button>
                  <Button
                    type="default"
                    icon={<ReloadOutlined />}
                    onClick={handleRefreshData}
                    className={styles.reloadButton}
                    style={{ marginLeft: "8px" }}
                  >
                    刷新数据
                  </Button>
                  <Button
                    type="default"
                    icon={<SettingOutlined />}
                    onClick={handleSettingsClick}
                    className={styles.settingsButton}
                    style={{ marginLeft: "8px" }}
                  >
                    设置
                  </Button>
                  <Button
                    type="primary"
                    icon={<SendOutlined />}
                    onClick={handleSend}
                    className={styles.sendButton}
                    style={{ marginLeft: "8px" }}
                  >
                    发送
                  </Button>
                  <Button
                    type="default"
                    icon={<BugOutlined />}
                    onClick={toggleDebugMode}
                    style={{ marginLeft: "8px" }}
                  >
                    {debugMode ? "关闭调试" : "调试模式"}
                  </Button>
                  
                  <Button
                    type="default"
                    onClick={() => setShowApiDataModal(true)}
                    style={{ marginLeft: "8px" }}
                    disabled={!apiRawData}
                  >
                    查看API数据
                  </Button>
                </div>
              </div>

              <div className={styles.requestResponseArea}>
                <div className={styles.requestArea}>
                  <Tabs
                    activeKey={activeTab}
                    onChange={setActiveTab}
                    className={styles.paramsTabs}
                    items={[
                      {
                        key: "Request",
                        label: <span data-key="Request">Request</span>,
                        children: (
                          <div className={styles.codeEditorWrapper}>
                            {loading ? (
                              <div className={styles.loadingContainer}>
                                <Spin
                                  indicator={
                                    <LoadingOutlined
                                      style={{ fontSize: 24 }}
                                      spin
                                    />
                                  }
                                  tip="正在加载报文数据..."
                                />
                              </div>
                            ) : (
                              <>
                                {/* 新增调试信息 */}
                                {isDev && console.log("渲染编辑器，数据类型:", typeof requestData, "长度:", requestData?.length, "前50字符:", requestData?.substring(0, 50))}
                                <CodeEditor
                                  key={`request-editor-${Date.now()}`}
                                  value={requestData || "{}"}
                                  onChange={handleRequestDataChange}
                                  extensions={basicExtensions}
                                  elementRef={requestEditorRef}
                                  actions={
                                    <Space size={8}>
                                      <Tooltip
                                        title="格式化JSON"
                                        placement="bottom"
                                      >
                                        <Button
                                          type="text"
                                          icon={<FormatPainterOutlined />}
                                          className="formatButton"
                                          onClick={(e) => {
                                            e.stopPropagation();
                                            formatJsonData();
                                          }}
                                        >
                                          格式化
                                        </Button>
                                      </Tooltip>
                                      <Tooltip
                                        title="复制到剪贴板"
                                        placement="bottom"
                                      >
                                        <Button
                                          type="text"
                                          icon={<CopyOutlined />}
                                          className="copyButton"
                                          onClick={(e) => {
                                            e.stopPropagation();
                                            copyToClipboard(requestData, true);
                                          }}
                                        >
                                          {requestCopied ? "已复制" : "复制"}
                                        </Button>
                                      </Tooltip>
                                    </Space>
                                  }
                                />
                              </>
                            )}
                          </div>
                        ),
                      },
                    ]}
                  />
                </div>
                <div className={styles.responseArea}>
                  <Tabs
                    activeKey="Response"
                    className={styles.paramsTabs}
                    items={[
                      {
                        key: "Response",
                        label: <span data-key="Response">Response</span>,
                        children: (
                          <div className={styles.codeEditorWrapper}>
                            {responseLoading ? (
                              <div className={styles.loadingContainer}>
                                <Spin
                                  indicator={
                                    <LoadingOutlined
                                      style={{ fontSize: 24 }}
                                      spin
                                    />
                                  }
                                  tip="正在处理请求..."
                                />
                              </div>
                            ) : (
                              <CodeEditor
                                value={responseData}
                                editable={false}
                                extensions={basicExtensions}
                                elementRef={responseEditorRef}
                                actions={
                                  <Space size={8}>
                                    <Tooltip
                                      title="格式化JSON"
                                      placement="bottom"
                                    >
                                      <Button
                                        type="text"
                                        icon={<FormatPainterOutlined />}
                                        className="formatButton"
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          try {
                                            const jsonObj = JSON.parse(responseData);
                                            const formattedJson = JSON.stringify(jsonObj, null, 2);
                                            setResponseData(formattedJson);
                                            message.success("响应JSON格式化成功");
                                          } catch (error) {
                                            message.error("响应JSON格式化失败，请检查JSON语法");
                                          }
                                        }}
                                      >
                                        格式化
                                      </Button>
                                    </Tooltip>
                                    <Tooltip
                                      title="复制到剪贴板"
                                      placement="bottom"
                                    >
                                      <Button
                                        type="text"
                                        icon={<CopyOutlined />}
                                        className="copyButton"
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          copyToClipboard(responseData, false);
                                        }}
                                      >
                                        {responseCopied ? "已复制" : "复制"}
                                      </Button>
                                    </Tooltip>
                                  </Space>
                                }
                              />
                            )}
                          </div>
                        ),
                      },
                    ]}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </Content>

      {renderSettingsModal}

      {/* 在请求响应区域后添加调试区域 */}
      {debugMode && (
        <div className={styles.debugSection} style={{ marginTop: "20px", padding: "10px", border: "1px dashed #ccc" }}>
          <div style={{ marginBottom: "10px", display: "flex", justifyContent: "space-between", alignItems: "center" }}>
            <h3 style={{ margin: 0 }}>调试模式 - 原始响应</h3>
            <Button 
              size="small" 
              onClick={() => copyToClipboard(rawResponse, false)}
              icon={<CopyOutlined />}
            >
              复制
            </Button>
          </div>
          <div style={{ 
            maxHeight: "300px", 
            overflow: "auto", 
            padding: "10px", 
            backgroundColor: "#f5f5f5", 
            borderRadius: "4px", 
            fontFamily: "monospace" 
          }}>
            <pre>{rawResponse}</pre>
          </div>
        </div>
      )}

      {/* API数据查看弹窗 */}
      <Modal
        title="API原始返回数据"
        open={showApiDataModal}
        onCancel={() => setShowApiDataModal(false)}
        width={800}
        footer={[
          <Button key="close" onClick={() => setShowApiDataModal(false)}>
            关闭
          </Button>,
          <Button key="copy" type="primary" onClick={copyApiRawData}>
            复制数据
          </Button>
        ]}
      >
        <div style={{ position: 'relative' }}>
          <div style={{ marginBottom: "10px", fontSize: "12px", color: "#999" }}>
            这是API返回的原始数据，用于诊断postPcs字段解析问题
          </div>
          <div
            style={{
              maxHeight: "calc(80vh - 200px)",
              overflow: "auto",
              padding: "10px",
              backgroundColor: "#f5f5f5",
              borderRadius: "4px",
              fontFamily: "monospace",
              fontSize: "12px",
              whiteSpace: "pre-wrap",
              wordBreak: "break-word"
            }}
          >
            <pre>{apiRawData}</pre>
          </div>
          
          {/* 添加查看API原始数据的按钮在顶部操作栏中 */}
          {apiRawData && (
            <Button
              type="primary"
              size="small"
              icon={<CopyOutlined />}
              style={{
                position: 'absolute',
                right: 10,
                top: 5,
              }}
              onClick={copyApiRawData}
            >
              复制
            </Button>
          )}
        </div>
      </Modal>
      {/* 调试工具 */}
      <div style={{ position: 'fixed', right: 20, bottom: 20, zIndex: 1000 }}>
        <ApiDataViewer />
      </div>
    </Layout>
  );
};

// 带有缓存的组件
const CachedHttpTest: React.FC = () => (
  <KeepAlive name="HttpTest" when={true} id="HttpTest">
    <HttpTest />
  </KeepAlive>
);

export default CachedHttpTest;
