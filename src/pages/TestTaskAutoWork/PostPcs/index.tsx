import React, { useState, useEffect, useRef, useCallback } from "react";
import {
  Layout,
  Menu as AntdMenu,
  Button,
  Form,
  Input,
  Select,
  Badge,
  Tabs,
  message,
  Typography,
  Spin,
  Tooltip,
  Modal,
  InputNumber,
  Radio,
  Upload,
  Space,
  Divider,
  Progress,
  Result,
} from "antd";
import {
  SendOutlined,
  SaveOutlined,
  LoadingOutlined,
  FileExcelOutlined,
  ReloadOutlined,
  SettingOutlined,
  InboxOutlined,
} from "@ant-design/icons";
// Use CodeMirror instead of Monaco
import CodeMirror from "@uiw/react-codemirror";
import { json } from "@codemirror/lang-json";
// 添加EditorView导入
import { EditorView } from "@codemirror/view";
import queryString from "query-string";
import {
  saveCase,
  selectTbCaseBookByUniqueKey,
  getBatchTasks,
  SaveCaseParams,
  sendExternalTransaction,
  importExcelData,
  getProxyUrls,
  getMacValue,
  downloadProcessedExcel,
  ApiResponse,
  ExcelProcessResult,
} from "./service";
import TaskMenu from "./components/Menu";
import CaseTitle from "./components/CaseTitle";
import CodeEditor from "./components/CodeEditor";
import { history } from "umi";
import { debounce, throttle } from "lodash";
import storage from "store2";
import { useActivate, useUnactivate, KeepAlive } from "react-activation";
import pageDataService from "../services/pageDataService";
import {
  parseNestedJsonString,
  handleEscapedJson,
  processSelectTbCaseBookResponse,
} from "./utils";
import styles from "./style.less";
import type { UploadFile, RcFile, UploadProps } from "antd/es/upload/interface";

interface TableListItem {
  caseGroup?: string; // 测试周期 (原 testCircle)
  caseAuthor?: string; // 测试者 (原 tester)
  caseName?: string; // 测试案例名称 (原 testTaskNm)
  caseNo: string; // 测试案例编号 (原 testTaskCode)
  pcsPost?: string | null; // 接口请求信息
  txNo?: string; // 接口编号 (原 pcsId)
  uniqueKey?: string; // 唯一标识（用于列表去重和作为rowKey）
  autoDataFlag: number | string; // 完成状态：1-已完成，0-未完成（必须字段，原 autoFlag）
  assertFlag?: number | string; // 断言标志
  selected?: boolean; // 是否被选中
}

const { Content, Sider } = Layout;

// Fix for baseReturnObj reference
const baseReturnObj = {
  txNo: "",
  caseNo: "",
  caseName: "",
  caseGroup: "",
  caseAuthor: "",
  autoDataFlag: "0",
  assertFlag: "0",
  pcsPost: "{}",
  dataSource: "",
};

// 添加开发环境检查
const isDev = process.env.NODE_ENV === "development";

// 使用debounce限制状态更新，特别是在开发模式下
const useDebounceState = <T,>(
  initialState: T,
  delay: number = isDev ? 200 : 0,
): [T, (value: T) => void] => {
  const [state, setState] = useState<T>(initialState);
  const lastValueRef = useRef<T>(initialState);

  const debouncedSetState = useCallback(
    debounce((value: T) => {
      setState(value);
    }, delay),
    [],
  );

  // 优化的setState函数，会先比较新旧值是否相同
  const optimizedSetState = useCallback(
    (value: T) => {
      // 检查是否相同
      if (JSON.stringify(value) === JSON.stringify(lastValueRef.current)) {
        return;
      }

      // 更新引用
      lastValueRef.current = value;

      // 使用防抖函数更新状态
      debouncedSetState(value);
    },
    [debouncedSetState],
  );

  return [state, optimizedSetState];
};

interface TransactionCode {
  code: string;
  name: string;
}

// 定义 location.state 的类
interface LocationState {
  pcsPost?: string;
  caseNo?: string; // 原 testTaskCode
  caseName?: string; // 原 testTaskNm
  caseAuthor?: string; // 原 tester
  caseGroup?: string; // 原 testCircle
  txNo?: string; // 原 pcsId
  request?: string;
  rowData?: any;
  selectedTasks?: TableListItem[];
  autoDataFlag?: number | string; // 原 autoFlag
  assertFlag?: number | string;
  uniqueKey?: string;
}

// 修改查询参数接口
interface QueryParams {
  dataId?: string;
  batchMode?: string;
  // 仅包含新字段
  txNo?: string;
  taskId?: string;
  uniqueKey?: string;
  caseName?: string;
  caseNo?: string;
  caseGroup?: string;
}

// 添加一个数据接口，表示从pageDataService接收的任务数据结果
interface TaskData {
  txNo: string;
  taskId: string;
  uniqueKey: string;
  completed: boolean;
  caseName: string; // 原 testTaskName
  caseNo: string;
  pcsPost: string;
  record?: any;
  selectedTasks?: TableListItem[];
}

// 添加一个自定义滚动条EditorView配置 - 使用更极端的设置
const scrollbarTheme = EditorView.theme({
  "&": {
    height: "100%",
    minHeight: "600px", // 增加最小高度
  },
  ".cm-content": {
    minHeight: "1000px", // 减少高度以避免多余的滚动条
    paddingBottom: "100px", // 减少底部填充
  },
  ".cm-gutters": {
    minHeight: "600px", // 保持与内容一致
  },
});

// 深色主题配置
const darkTheme = EditorView.theme({
  "&": {
    backgroundColor: "#000",
    color: "#39ff14",
    height: "100%",
    minHeight: "600px",
  },
  ".cm-content": {
    backgroundColor: "#000",
    color: "#39ff14",
    minHeight: "1000px",
    paddingBottom: "100px",
  },
  ".cm-gutters": {
    backgroundColor: "#000",
    color: "#858585",
    border: "none",
    minHeight: "600px",
  },
  ".cm-line": {
    color: "#39ff14",
    backgroundColor: "#000",
  },
  // 强制所有语法元素使用相同的绿色
  ".cm-string, .cm-string-2": { color: "#39ff14 !important" },
  ".cm-number": { color: "#39ff14 !important" },
  ".cm-keyword": { color: "#39ff14 !important" },
  ".cm-operator": { color: "#39ff14 !important" },
  ".cm-property": { color: "#39ff14 !important" },
  ".cm-property.cm-definition": { color: "#39ff14 !important" },
  ".cm-variable": { color: "#39ff14 !important" },
  ".cm-atom": { color: "#39ff14 !important" },
  ".cm-meta": { color: "#39ff14 !important" },
  ".cm-tag": { color: "#39ff14 !important" },
  ".cm-attribute": { color: "#39ff14 !important" },
  ".cm-qualifier": { color: "#39ff14 !important" },
  ".cm-builtin": { color: "#39ff14 !important" },
  ".cm-bracket": { color: "#39ff14 !important" },
  ".cm-comment": { color: "#6a9955 !important", fontStyle: "italic" },
  ".cm-special": { color: "#39ff14 !important" },
  ".ͼb": {
    color: "#39ff14 !important",
  } /* CodeMirror内部类，通常用于属性名/键 */,
  ".ͼc": { color: "#39ff14 !important" } /* CodeMirror内部类，通常用于字符串 */,
  ".ͼd": { color: "#39ff14 !important" } /* CodeMirror内部类，通常用于数字 */,
  ".cm-activeLine": {
    backgroundColor: "transparent !important",
  },
  ".cm-activeLineGutter": {
    backgroundColor: "transparent !important",
  },
  ".cm-selectionBackground, .cm-focused .cm-selectionBackground, .cm-selectionLayer .cm-selectionBackground":
    {
      background: "#222 !important",
      color: "#ff9900 !important",
    },
  ".cm-selectionMatch": {
    backgroundColor: "#222",
    color: "#ff9900 !important",
  },
  ".cm-cursor": {
    borderLeft: "1.5px solid #39ff14",
  },
  "::selection": {
    background: "#222",
    color: "#ff9900",
  },
});

// 扩展配置，使用深色主题
const darkModeExtensions = [json(), darkTheme];

const { Dragger } = Upload;

/**
 * HTTP测试组件
 */
const HttpTest: React.FC = () => {
  // 将原来组件外部的状态变量移到这里

  // 使用history.location替代useLocation
  const location = history.location as any;
  const [transactionCode, setTransactionCode] = useState<string>("");
  const [transactionList, setTransactionList] = useState<TransactionCode[]>([]);
  const [requestData, setRequestDataRaw] = useDebounceState<string>("{}");
  const [responseData, setResponseDataRaw] = useDebounceState<string>("");
  const [activeTab, setActiveTab] = useState("Request");
  const [loading, setLoading] = useState<boolean>(false);
  const [responseLoading, setResponseLoading] = useState<boolean>(false);
  const [requestCopied, setRequestCopied] = useState<boolean>(false);
  const [responseCopied, setResponseCopied] = useState<boolean>(false);
  const [pcsPostProcessed, setPcsPostProcessed] = useState<boolean>(false);
  const [selectedTasks, setSelectedTasks] = useState<TableListItem[]>([]);
  const [fontSize, setFontSize] = useState<number>(14);
  const [isSettingsModalVisible, setIsSettingsModalVisible] =
    useState<boolean>(false);
  const [importLoading, setImportLoading] = useState<boolean>(false);
  // 记录是否已从pageDataService加载过数据
  const [dataLoaded, setDataLoaded] = useState<boolean>(false);
  // 添加一个状态变量，跟踪数据是否已从API加载
  const [dataFromApi, setDataFromApi] = useState<boolean>(false);
  // 添加状态标记，防止重复调用handleTaskSelect
  const [autoSelectDone, setAutoSelectDone] = useState<boolean>(false);
  // 添加一个状态记录当前的dataId，用于检测是否是新的请求
  const [currentDataId, setCurrentDataId] = useState<string | null>(null);
  // 添加标记，跟踪初始加载是否已完成，防止重复请求
  const [initialLoadComplete, setInitialLoadComplete] =
    useState<boolean>(false);
  // 添加ref来跟踪当前选中的任务的uniqueKey，用于防止重复请求
  const lastRequestedUniqueKeyRef = useRef<string | null>(null);
  // 添加请求锁标志，防止多个useEffect同时触发请求
  const [isRequestInProgress, setIsRequestInProgress] =
    useState<boolean>(false);
  // 在组件顶部添加请求跟踪ref
  const requestSentRef = useRef(false);
  // 在组件顶部定义请求参数引用
  const lastRequestParamsRef = useRef<{ uniqueKey?: string; dataId?: string }>(
    {},
  );
  // 在组件顶部添加handleTaskSelectRef
  const handleTaskSelectRef = useRef<
    ((task: TableListItem) => Promise<void>) | null
  >(null);
  // 添加编辑器DOM引用
  const requestEditorRef = useRef<HTMLDivElement>(null);
  const responseEditorRef = useRef<HTMLDivElement>(null);
  // 组件状态

  // 在组件顶部状态声明区添加taskData状态
  const [taskData, setTaskData] = useState<TaskData | null>({
    txNo: "",
    taskId: "",
    uniqueKey: "",
    completed: false,
    caseName: "",
    caseNo: "",
    pcsPost: "{}",
  });

  // Excel处理相关状态
  const [excelModalVisible, setExcelModalVisible] = useState<boolean>(false);
  const [uploadedFile, setUploadedFile] = useState<RcFile | null>(null);
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [fileId, setFileId] = useState<string>("");
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [processingStatus, setProcessingStatus] = useState<
    "idle" | "uploading" | "processing" | "completed" | "failed"
  >("idle");
  const [originalFileName, setOriginalFileName] = useState<string>("");

  // 改进 useActivate 钩子来强制刷新数据，清除缓存
  useActivate(() => {
    // 从缓存恢复状态的逻辑保持不变...
    console.log("组件激活");

    // 其余代码保持不变...
  });

  // 当组件被卸载时保存状态
  useUnactivate(() => {
    // 保存状态到缓存的逻辑保持不变...
    console.log("组件缓存");

    // 其余代码保持不变...
  });

  // 从URL参数中初始化数据
  useEffect(() => {
    // 解析URL参数
    const urlParams = queryString.parse(location.search);
    console.log("解析到的URL参数:", urlParams);

    // 获取dataId、uniqueKey和batchMode
    const { dataId, uniqueKey, batchMode } = urlParams as QueryParams;

    // 检查是否是批量模式
    if (
      batchMode === "single" &&
      location.state &&
      Array.isArray(location.state.selectedTasks) &&
      location.state.selectedTasks.length > 0
    ) {
      console.log("单任务模式，有任务列表:", location.state.selectedTasks);

      // 转换任务格式
      const convertedTasks = location.state.selectedTasks.map(
        (task: any, index: number) => ({
          caseGroup: task.caseGroup || task.testCircle || "",
          caseAuthor: task.caseAuthor || task.tester || "",
          caseName: task.caseName || task.testTaskNm || "",
          caseNo: task.caseNo || task.testTaskCode || "",
          pcsPost: task.pcsPost || null,
          txNo: task.txNo || task.pcsId || "",
          uniqueKey:
            task.uniqueKey ||
            `${task.caseNo || task.testTaskCode}_${task.caseGroup || task.testCircle}_${task.caseAuthor || task.tester}`,
          autoDataFlag: task.autoDataFlag || task.autoFlag || "0",
          assertFlag: task.assertFlag || "0",
          selected: index === 0, // 默认选中第一个
        }),
      );

      console.log("转换后的任务列表:", convertedTasks);

      // 更新选中任务状态
      setSelectedTasks(convertedTasks);

      // 如果有任务，自动选择第一个
      if (convertedTasks.length > 0 && handleTaskSelectRef.current) {
        // 延迟执行，确保组件完全挂载
        setTimeout(() => {
          console.log("自动选择第一个任务");
          handleTaskSelectRef.current?.(convertedTasks[0]);
        }, 100);
      }
    }
    // 检查是否是批量跳转模式
    else if (batchMode === "multiple" && dataId) {
      console.log("批量跳转模式，从pageDataService获取数据:", dataId);
      const data = pageDataService.getData(dataId as string);

      if (data && data.selectedTasks && Array.isArray(data.selectedTasks)) {
        console.log("获取到批量任务数据:", data.selectedTasks);

        // 更新选中任务状态，将所有任务添加到左侧菜单
        setSelectedTasks(data.selectedTasks);

        // 如果有任务，自动选择第一个
        if (data.selectedTasks.length > 0 && handleTaskSelectRef.current) {
          // 延迟执行，确保组件完全挂载
          setTimeout(() => {
            console.log("自动选择批量任务中的第一个任务");
            handleTaskSelectRef.current?.(data.selectedTasks[0]);
          }, 100);
        }
      } else {
        console.error("无法获取批量任务数据或数据格式不正确");
        message.error("加载批量任务数据失败");
      }
    }
    // 如果不是批量模式，但有uniqueKey，单独处理该案例
    else if (uniqueKey) {
      console.log("URL中包含uniqueKey，准备通过API获取案例详情:", uniqueKey);
      setLoading(true);

      // 调用后端接口获取案例详情
      selectTbCaseBookByUniqueKey(uniqueKey as string)
        .then((response) => {
          console.log("获取案例详情成功:", response);

          // 处理API响应数据
          handleApiResponse(response);

          // 构建TableListItem对象用于菜单选择
          if (response && response.code === 200) {
            try {
              // 处理API响应
              const processedData = processSelectTbCaseBookResponse(response);

              // 构建TableListItem对象用于菜单选择
              const tableItem: TableListItem = {
                caseGroup: processedData.caseGroup || "",
                caseAuthor: processedData.caseAuthor || "",
                caseName: processedData.caseName || "",
                caseNo: processedData.caseNo || "",
                pcsPost: processedData.postPcsFormatted || null,
                txNo: processedData.txNo || "",
                uniqueKey: uniqueKey as string,
                autoDataFlag: processedData.autoDataFlag || "0",
                assertFlag: processedData.assertFlag || "0",
                selected: true,
              };

              // 更新选中任务
              setSelectedTasks([tableItem]);
            } catch (e) {
              console.error("处理案例数据失败:", e);
              message.error("处理案例数据失败");
            }
          }
        })
        .catch((error) => {
          console.error("API请求错误:", error);
          message.error("获取案例数据失败，请重试");
        })
        .finally(() => {
          setLoading(false);
        });
    }
    // 如果有dataId但不是批量模式，从pageDataService获取单个任务数据
    else if (dataId) {
      console.log("URL中包含dataId，从pageDataService获取数据:", dataId);
      const data = pageDataService.getData(dataId as string);

      if (data) {
        console.log("从pageDataService获取的数据:", data);

        // 如果pageDataService中有uniqueKey，调用API获取完整数据
        if (data.uniqueKey) {
          console.log(
            "使用pageDataService中的uniqueKey调用API:",
            data.uniqueKey,
          );
          setLoading(true);

          // 调用后端接口获取案例详情
          selectTbCaseBookByUniqueKey(data.uniqueKey)
            .then((response) => {
              console.log("获取案例详情成功:", response);

              // 处理API响应数据
              handleApiResponse(response);

              if (response && response.code === 200) {
                try {
                  // 处理API响应
                  const processedData =
                    processSelectTbCaseBookResponse(response);

                  // 构建TableListItem对象并更新选中任务
                  const tableItem: TableListItem = {
                    caseGroup: processedData.caseGroup || "",
                    caseAuthor: processedData.caseAuthor || "",
                    caseName: processedData.caseName || "",
                    caseNo: processedData.caseNo || "",
                    pcsPost: processedData.postPcsFormatted || null,
                    txNo: processedData.txNo || "",
                    uniqueKey: data.uniqueKey,
                    autoDataFlag: processedData.autoDataFlag || "0",
                    assertFlag: processedData.assertFlag || "0",
                    selected: true,
                  };

                  setSelectedTasks([tableItem]);
                } catch (e) {
                  console.error("处理案例数据失败:", e);
                  message.error("处理案例数据失败");
                }
              }
            })
            .catch((error) => {
              console.error("API请求错误:", error);
              message.error("获取案例数据失败，请重试");
            })
            .finally(() => {
              setLoading(false);
            });
        }
        // 如果pageDataService中没有uniqueKey但有其他数据
        else if (data.pcsPost) {
          console.log("pageDataService中没有uniqueKey，但有pcsPost数据");
          setRequestDataRaw(data.pcsPost);
        }
      }
    }
  }, [location.search, location.state, setRequestDataRaw]);

  // 处理请求数据变化
  const handleRequestDataChange = (value: string) => {
    // 逻辑保持不变
    setRequestDataRaw(value);
  };

  // 发送请求
  const handleSend = async () => {
    try {
      setResponseLoading(true);

      // 1. 获取当前环境和交易码
      const currentEnv = localStorage.getItem("currentEnv") || "";
      if (!currentEnv) {
        message.error("请先选择环境");
        setResponseLoading(false);
        return;
      }

      if (!transactionCode) {
        message.error("请先选择交易码");
        setResponseLoading(false);
        return;
      }

      // 2. 根据环境构建代理URL
      const urlInfo = getProxyUrls(currentEnv);
      if (!urlInfo.macUrl) {
        message.error("获取MAC地址失败");
        setResponseLoading(false);
        return;
      }

      if (!urlInfo.requestUrl) {
        message.error("获取请求地址失败");
        setResponseLoading(false);
        return;
      }

      // 3. 解析请求数据
      let requestObj;
      try {
        requestObj = JSON.parse(requestData);
      } catch (error) {
        message.error("请求报文格式错误");
        setResponseLoading(false);
        return;
      }

      // 4. 生成时间戳和交易跟踪号
      // 获取当前日期和时间
      const date = new Date();

      // 格式化日期为 YYYYMMDD
      const curYearMonDay =
        date.getFullYear() +
        (date.getMonth() + 1).toString().padStart(2, "0") +
        date.getDate().toString().padStart(2, "0");

      // 格式化时间 HHMMSS + 毫秒
      const time1 =
        date.getHours().toString().padStart(2, "0") +
        date.getMinutes().toString().padStart(2, "0") +
        date.getSeconds().toString().padStart(2, "0") +
        date.getMilliseconds().toString().padStart(3, "0");

      // 生成格式化时间戳 YYYYMMDDHHMMSSNNN
      const generateTimestamp = (date: Date): string => {
        const year = date.getFullYear().toString();
        const month = (date.getMonth() + 1).toString().padStart(2, "0");
        const day = date.getDate().toString().padStart(2, "0");
        const hours = date.getHours().toString().padStart(2, "0");
        const minutes = date.getMinutes().toString().padStart(2, "0");
        const seconds = date.getSeconds().toString().padStart(2, "0");
        const milliseconds = date.getMilliseconds().toString().padStart(3, "0");

        return `${year}${month}${day}${hours}${minutes}${seconds}${milliseconds}`;
      };

      // 生成子交易号 (32位)
      const randomChar = Math.floor(Math.random() * 10).toString(); // 生成0-9的随机数字
      const subtxNo = "10221990001111000000000" + randomChar + curYearMonDay;

      // 生成时间戳
      const timestamp1 = generateTimestamp(date);

      // 生成交易开始时间
      const txStartTime = curYearMonDay + time1;

      // 生成全局业务跟踪号 (32位)
      const globalBusiTrackNo =
        txStartTime + // 时间戳 (17位)
        "1022199" + // 源系统 (7位)
        "CK001" + // 发起交易实例序号 (5位)
        Math.floor(Math.random() * 1000)
          .toString()
          .padStart(3, "0"); // 序列号 (3位)

      // 修改请求头中的tenantId字段
      if (requestObj.txHeader && requestObj.txHeader.tenantId) {
        if (currentEnv != "TEST") {
          requestObj.txHeader.tenantId = currentEnv;
        } else {
          requestObj.txHeader.tenantId = "QHGD";
        }
      }

      // 5. 更新请求对象中的字段
      if (requestObj.txHeader) {
        requestObj.txHeader.subtxNo = subtxNo;
        requestObj.txHeader.txStartTime = timestamp1;
        requestObj.txHeader.txSendTime = timestamp1;
        requestObj.txHeader.globalBusiTrackNo = globalBusiTrackNo;
      }

      // 处理可能的数字类型字段
      const processNumericFields = (obj: any) => {
        if (obj?.txBody?.txComn1) {
          // 处理 bgnIndexNo
          if (obj.txBody.txComn1.bgnIndexNo) {
            if (typeof obj.txBody.txComn1.bgnIndexNo === "string") {
              const numValue = parseInt(obj.txBody.txComn1.bgnIndexNo, 10);
              obj.txBody.txComn1.bgnIndexNo = !isNaN(numValue) ? numValue : 1;
            }
          }
          // 处理 curQryReqNum
          if (obj.txBody.txComn1.curQryReqNum) {
            if (typeof obj.txBody.txComn1.curQryReqNum === "string") {
              const numValue = parseInt(obj.txBody.txComn1.curQryReqNum, 10);
              obj.txBody.txComn1.curQryReqNum = !isNaN(numValue)
                ? numValue
                : 10;
            }
          }
        }
      };

      // 创建一个深拷贝的请求对象，避免重复请求
      const requestObjForMac = JSON.parse(JSON.stringify(requestObj));

      // 处理 MAC 请求对象中的数字类型
      processNumericFields(requestObjForMac);

      // 6. 获取MAC值
      try {
        let macValue = "";
        // 某些环境不校验mac，使用固定值
        if (
          currentEnv === "DEV1" ||
          currentEnv === "DEV2" ||
          currentEnv === "TEST" ||
          currentEnv === "DEV5" ||
          currentEnv === "DEVS"
        ) {
          macValue = "068A29C6270CB954"; // 组装环境不校验mac，故写死一个固定值
        } else {
          macValue = await getMacValue(urlInfo.macUrl, requestObjForMac);
        }
        if (requestObj.txHeader) {
          requestObj.txHeader.msgrptMac = macValue;
        }
      } catch (error: any) {
        message.error("获取MAC值失败: " + (error.message || "未知错误"));
        setResponseLoading(false);
        return;
      }

      // 处理最终请求对象中的数字类型
      processNumericFields(requestObj);

      // 7. 发送最终请求
      try {
        const responseData = await sendExternalTransaction(
          urlInfo.requestUrl,
          requestObj,
        );
        setResponseDataRaw(JSON.stringify(responseData, null, 2));

        // 更新成功状态
        if (taskData && taskData.uniqueKey && selectedTasks.length > 0) {
          const updatedTasks = selectedTasks.map((task) => {
            if (task.uniqueKey === taskData.uniqueKey) {
              return { ...task, autoDataFlag: "1" };
            }
            return task;
          });

          // 更新任务状态
          setSelectedTasks(updatedTasks);

          // 更新当前任务状态
          setTaskData({
            ...taskData,
            completed: true,
          });
        }

        // 8. 自动保存案例（无需用户感知）
        try {
          if (taskData && taskData.uniqueKey) {
            // 准备保存案例参数
            const saveParams = {
              uniqueKey: taskData.uniqueKey,
              caseGroup: "",
              caseAuthor: "",
              caseName: taskData.caseName || "",
              caseNo: taskData.caseNo || "",
              pcsPost: JSON.stringify(requestObj),
              txNo: transactionCode,
              autoDataFlag: "1", // 标记为已完成
              assertFlag: "0",
            };

            // 静默调用保存方法
            await saveCase(saveParams);
            console.log("案例保存成功");
          }
        } catch (saveError) {
          // 静默处理保存错误
          console.error("自动保存案例失败:", saveError);
        }
      } catch (error: any) {
        console.error("发送请求失败:", error);
        setResponseDataRaw(
          JSON.stringify({ error: error.message || "未知错误" }, null, 2),
        );
      }
    } catch (error: any) {
      console.error("发送请求失败:", error);
      setResponseDataRaw(
        JSON.stringify({ error: error.message || "未知错误" }, null, 2),
      );
    } finally {
      setResponseLoading(false);
    }
  };

  // 处理API响应数据
  const handleApiResponse = (apiResponse: any) => {
    console.log("API响应:", apiResponse);

    if (apiResponse && apiResponse.code === 200 && apiResponse.data) {
      try {
        // 使用专门的处理函数解析API响应
        const processedData = processSelectTbCaseBookResponse(apiResponse);
        console.log("处理后的数据:", processedData);

        // 检查处理是否成功
        if (processedData.error) {
          console.warn("数据处理过程中出现警告:", processedData.error);
        }

        // 更新请求数据编辑器内容
        if (processedData.postPcsFormatted) {
          setRequestDataRaw(processedData.postPcsFormatted);
        } else {
          console.warn("未获取到格式化的postPcs数据");
          setRequestDataRaw("{}");
        }

        // 更新taskData
        setTaskData({
          txNo: processedData.txNo || "",
          taskId: processedData.caseNo || "",
          uniqueKey: processedData.uniqueKey?.toString() || "",
          completed:
            processedData.autoDataFlag === "1" ||
            processedData.autoDataFlag === 1,
          caseName: processedData.caseName || "",
          caseNo: processedData.caseNo || "",
          pcsPost: processedData.postPcsFormatted || "{}",
        });

        // 更新交易编号
        if (processedData.txNo) {
          setTransactionCode(processedData.txNo);
        }
      } catch (e) {
        console.error("解析API响应数据失败:", e);
        message.error("解析案例数据失败");
      }
    }
  };

  // 更新handleTaskSelectRef的引用和实现
  handleTaskSelectRef.current = async (task: TableListItem) => {
    try {
      console.log("选择任务:", task);
      // 如果已经选中该任务，不需要重新加载
      if (task.selected && task.pcsPost) {
        console.log("任务已选中，使用现有数据");
        return;
      }

      setLoading(true);

      // 确保有uniqueKey
      if (!task.uniqueKey) {
        message.error("缺少唯一标识，无法获取案例详情");
        setLoading(false);
        return;
      }

      // 调用后端API获取案例详情
      const response = await selectTbCaseBookByUniqueKey(task.uniqueKey);

      // 处理API响应数据
      handleApiResponse(response);

      // 更新选中任务状态
      const updatedTasks = selectedTasks.map((t) => ({
        ...t,
        selected: t.uniqueKey === task.uniqueKey,
      }));

      setSelectedTasks(updatedTasks);
    } catch (err) {
      console.error("选择任务失败:", err);
      message.error("处理任务选择时出错");
    } finally {
      setLoading(false);
    }
  };

  // 强制添加滚动条 - 使用更多的DOM操作和填充内容
  const forceScrollbars = useCallback((element: HTMLElement) => {
    if (!element) return;

    setTimeout(() => {
      try {
        // 查找编辑器内容区域
        const content = element.querySelector(".cm-content") as HTMLElement;

        if (content) {
          // 添加一个隐形的高度元素来确保滚动条出现
          const existingSpacer = content.querySelector(
            ".fake-content-for-scroll",
          );
          if (existingSpacer) {
            existingSpacer.remove();
          }

          const spacer = document.createElement("div");
          spacer.className = "fake-content-for-scroll";
          spacer.style.height = "1000px";
          spacer.style.width = "1px";
          spacer.style.opacity = "0";
          content.appendChild(spacer);
        }
      } catch (e) {
        console.error("在应用DOM样式时发生错误:", e);
      }
    }, 100);
  }, []);

  const requestEditorMountRef = useCallback(
    (node: HTMLDivElement) => {
      if (node) {
        // 直接使用node而不是更新ref
        setTimeout(() => forceScrollbars(node), 100);
      }
    },
    [forceScrollbars],
  );

  const responseEditorMountRef = useCallback(
    (node: HTMLDivElement) => {
      if (node) {
        // 直接使用node而不是更新ref
        setTimeout(() => forceScrollbars(node), 100);
      }
    },
    [forceScrollbars],
  );

  // 在组件渲染后直接应用全局样式并强制应用滚动条
  useEffect(() => {
    // 创建全局样式，确保CodeMirror滚动条不重叠
    const styleEl = document.createElement("style");
    styleEl.id = "codemirror-scroll-fix";
    styleEl.innerHTML = `
      /* 确保滚动区域只有一个滚动条 */
      .cm-scroller {
        overflow: auto !important;
      }
      
      /* 确保内容区域有适当高度 */
      .cm-content {
        min-height: 1000px !important;
      }
      
      /* 确保容器不产生额外滚动条 */
      .${styles.codeEditorWrapper} {
        overflow: hidden !important;
      }
    `;

    // 如果不存在则添加到文档
    if (!document.getElementById("codemirror-scroll-fix")) {
      document.head.appendChild(styleEl);
    }

    // 在组件渲染后查找所有编辑器容器并应用滚动条
    const applyScrollbarsToAll = () => {
      const editors = document.querySelectorAll(`.${styles.codeEditorWrapper}`);
      editors.forEach((editor) => {
        forceScrollbars(editor as HTMLElement);
      });
    };

    // 初始应用
    setTimeout(applyScrollbarsToAll, 500);

    // 组件卸载时清理
    return () => {
      const existingStyle = document.getElementById("codemirror-scroll-fix");
      if (existingStyle) {
        existingStyle.remove();
      }
    };
  }, [forceScrollbars, styles.codeEditorWrapper]);

  // 添加全局样式强制所有文本为绿色
  useEffect(() => {
    // 创建一个样式标签
    const styleElement = document.createElement("style");
    styleElement.id = "green-theme-override";

    // 包含所有可能的CodeMirror类
    styleElement.innerHTML = `
      /* 强制所有CodeMirror编辑器内的文本为绿色 */
      .cm-editor [class*="cm-"], 
      .cm-editor .cm-line,
      .cm-editor .cm-content,
      .cm-editor .ͼ1, .cm-editor .ͼ2, .cm-editor .ͼ3, .cm-editor .ͼ4, 
      .cm-editor .ͼ5, .cm-editor .ͼ6, .cm-editor .ͼ7, .cm-editor .ͼ8, 
      .cm-editor .ͼ9, .cm-editor .ͼa, .cm-editor .ͼb, .cm-editor .ͼc, 
      .cm-editor .ͼd, .cm-editor .ͼe, .cm-editor .ͼf, .cm-editor .ͼg,
      .cm-editor [class^="ͼ"] {
        color: #39ff14 !important;
        background-color: transparent !important;
      }
      
      /* 只有注释颜色略暗 */
      .cm-editor .cm-comment {
        color: #6a9955 !important;
        font-style: italic !important;
      }
      
      /* 背景黑色 */
      .cm-editor, .cm-editor .cm-scroller, .cm-editor .cm-content {
        background-color: #000 !important;
      }
      
      /* 选中文本橙色 */
      .cm-editor .cm-selectionBackground,
      .cm-editor .cm-focused .cm-selectionBackground,
      .cm-editor .cm-selectionLayer .cm-selectionBackground {
        color: #ff9900 !important;
        background-color: #222 !important;
      }
      
      /* 强制选中元素橙色，即使在选区内 */
      .cm-editor .cm-selectionBackground [class*="cm-"],
      .cm-editor .cm-selectionBackground [class^="ͼ"] {
        color: #ff9900 !important;
      }
      
      /* 光标颜色 */
      .cm-editor .cm-cursor {
        border-left: 1.5px solid #39ff14 !important;
      }
    `;

    // 添加到文档头部
    document.head.appendChild(styleElement);

    // 清理函数
    return () => {
      const existingStyle = document.getElementById("green-theme-override");
      if (existingStyle) {
        existingStyle.remove();
      }
    };
  }, []); // 空依赖数组，仅在组件挂载时执行一次

  // 文件上传前检查
  const beforeUpload = (file: RcFile) => {
    const isExcel =
      file.type ===
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" ||
      file.name.endsWith(".xlsx") ||
      file.name.endsWith(".xls");

    if (!isExcel) {
      message.error("只能上传Excel文件！");
      return Upload.LIST_IGNORE;
    }

    const isLt50M = file.size / 1024 / 1024 < 50;

    if (!isLt50M) {
      message.error("文件大小不能超过50MB！");
      return Upload.LIST_IGNORE;
    }

    // 保存文件到状态
    setUploadedFile(file);
    // 保存原始文件名
    setOriginalFileName(file.name);

    // 创建符合UploadFile接口的对象
    const uploadFile: UploadFile = {
      uid: file.uid,
      name: file.name,
      size: file.size,
      type: file.type,
      percent: 0,
      originFileObj: file,
    };

    setFileList([uploadFile]);
    return false; // 阻止自动上传
  };

  // 处理上传和处理Excel
  const handleProcessExcel = async () => {
    if (!uploadedFile) {
      message.error("请先选择Excel文件");
      return;
    }

    try {
      setImportLoading(true);
      setProcessingStatus("uploading");

      // 模拟上传进度
      const progressInterval = setInterval(() => {
        setUploadProgress((prev) => {
          if (prev >= 99) {
            clearInterval(progressInterval);
            return 99;
          }
          return prev + 1;
        });

        // 同时更新文件列表中的进度
        setFileList((prev) =>
          prev.map((file) => ({
            ...file,
            percent: uploadProgress,
            status: "uploading" as const,
          })),
        );
      }, 100);

      // 根据需求准备三个数据
      // 1. Excel文件
      // 2. 案例的uniqueKey值
      // 3. request编辑框里的数据
      const currentUniqueKey =
        selectedTasks.find((t) => t.selected)?.uniqueKey || "";

      // 检查是否有唯一标识符
      if (!currentUniqueKey) {
        console.warn("当前没有选中的任务或唯一标识符为空");
      }

      // 准备FormData对象，并确保数据的正确性
      const formData = new FormData();
      formData.append("file", uploadedFile);
      formData.append("uniqueKey", currentUniqueKey);

      // 确保请求数据是有效的JSON
      try {
        // 尝试解析请求数据确保它是有效的JSON
        JSON.parse(requestData);
        formData.append("requestData", requestData);
      } catch (jsonError) {
        console.error("请求数据不是有效的JSON格式:", jsonError);
        formData.append("requestData", "{}"); // 使用空对象作为后备
      }

      console.log("准备发送表单数据，文件名:", uploadedFile.name);

      // 发送请求，添加错误处理和重试机制
      let response;
      try {
        response = await importExcelData(formData);
      } catch (uploadError) {
        console.error("Excel上传失败，尝试重试:", uploadError);
        message.warning("上传失败，正在重试...");

        // 等待短暂时间后重试
        await new Promise((resolve) => setTimeout(resolve, 1000));
        response = await importExcelData(formData);
      }

      clearInterval(progressInterval);
      setUploadProgress(100);

      // 更新文件状态为处理中
      setFileList((prev) =>
        prev.map((file) => ({
          ...file,
          percent: 100,
          status: "done" as const,
        })),
      );

      setProcessingStatus("processing");

      // 验证响应数据
      console.log("接收到的响应:", response);

      // 处理响应
      if (response && response.code === 200) {
        // 检查响应中是否有data对象以及fielId字段
        if (
          response.data &&
          typeof response.data === "object" &&
          "fielId" in response.data
        ) {
          // 获取fielId值
          const fielId = response.data.fielId;

          if (fielId) {
            // 保存fielId作为fileId使用
            setFileId(fielId);
            setProcessingStatus("completed");
            message.success("Excel处理成功");
          } else {
            throw new Error("返回的fielId为空");
          }
        } else {
          // 检查字符串形式的响应
          if (typeof response.data === "string") {
            try {
              const parsedData = JSON.parse(response.data);
              if (parsedData && parsedData.fielId) {
                setFileId(parsedData.fielId);
                setProcessingStatus("completed");
                message.success("Excel处理成功");
                return;
              }
            } catch (e) {
              console.error("解析响应字符串失败:", e);
            }
          }

          throw new Error("返回数据格式不正确");
        }
      } else {
        setProcessingStatus("failed");
        message.error(response?.msg || "处理失败");

        // 更新文件状态为失败
        setFileList((prev) =>
          prev.map((file) => ({
            ...file,
            status: "error" as const,
          })),
        );
      }
    } catch (error: any) {
      console.error("Excel处理出错:", error);
      setProcessingStatus("failed");
      message.error(`Excel处理出错: ${error?.message || "未知错误"}`);

      // 更新文件状态为失败
      setFileList((prev) =>
        prev.map((file) => ({
          ...file,
          status: "error" as const,
        })),
      );
    } finally {
      setImportLoading(false);
    }
  };

  // 处理下载Excel
  const handleDownloadExcel = async () => {
    if (!fileId) {
      message.error("没有可下载的文件");
      return;
    }

    try {
      setImportLoading(true);
      console.log("调用下载接口，fileId:", fileId);

      // 添加错误处理和重试机制
      let response;
      try {
        response = await downloadProcessedExcel(fileId);
      } catch (requestError) {
        console.error("下载请求失败，尝试重试:", requestError);
        message.warning("下载请求失败，正在重试...");
        // 等待短暂时间后重试
        await new Promise((resolve) => setTimeout(resolve, 1000));
        response = await downloadProcessedExcel(fileId);
      }

      console.log("下载接口响应:", response);

      // 确保响应正确
      if (!response || !response.data) {
        throw new Error("响应数据无效");
      }

      // 检查数据大小
      const dataSize = response.data.size || 0;
      console.log("下载的文件大小:", dataSize);
      if (dataSize === 0) {
        throw new Error("服务器返回了空文件");
      }

      // 使用更健壮的方式创建Blob
      let blob: Blob;
      try {
        blob = new Blob([response.data], {
          type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        });
      } catch (blobError) {
        console.error("创建Blob对象失败:", blobError);
        // 尝试使用另一种方式创建Blob
        const fileReader = new FileReader();
        const promise = new Promise<ArrayBuffer>((resolve, reject) => {
          fileReader.onload = () => resolve(fileReader.result as ArrayBuffer);
          fileReader.onerror = () => reject(new Error("读取文件数据失败"));
          fileReader.readAsArrayBuffer(response.data);
        });
        const arrayBuffer = await promise;
        blob = new Blob([arrayBuffer], {
          type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        });
      }

      if (blob.size === 0) {
        throw new Error("创建的Blob为空");
      }

      console.log("Blob创建成功，大小:", blob.size);

      const downloadFileName = originalFileName || "excel-data.xlsx";

      // 使用更安全的下载方法
      try {
        if (window.navigator && "msSaveOrOpenBlob" in window.navigator) {
          // IE浏览器处理方式
          (window.navigator as any).msSaveOrOpenBlob(blob, downloadFileName);
          console.log("使用IE下载方式");
        } else {
          // 现代浏览器 - 创建一个新的Blob URL
          const url = window.URL.createObjectURL(
            new Blob([blob], {
              type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            }),
          );
          console.log("创建URL:", url);

          const link = document.createElement("a");
          link.href = url;
          link.setAttribute("download", downloadFileName);
          // 不使用target属性，避免某些浏览器的安全限制
          link.style.display = "none";

          // 附加到DOM、触发点击并立即移除
          document.body.appendChild(link);
          link.click();
          console.log("触发下载操作");

          // 立即清理
          document.body.removeChild(link);
          window.URL.revokeObjectURL(url);
          console.log("清理下载资源");
        }

        message.success("下载成功");
      } catch (downloadError: any) {
        console.error("执行下载操作失败:", downloadError);

        // 尝试备用下载方法
        try {
          console.log("尝试使用备用下载方法...");
          const dataUrl = window.URL.createObjectURL(blob);
          window.open(dataUrl, "_blank");
          setTimeout(() => window.URL.revokeObjectURL(dataUrl), 100);
          message.success("下载已在新窗口中打开");
        } catch (backupError: any) {
          throw new Error(
            `下载操作失败: ${downloadError.message}, 备用方法也失败: ${backupError.message}`,
          );
        }
      }
    } catch (error) {
      console.error("下载文件详细错误:", error);
      // 显示更友好的错误信息
      let errorMessage = "下载失败";
      if (error instanceof Error) {
        errorMessage += ": " + error.message;
      }
      message.error(errorMessage);
    } finally {
      setImportLoading(false);
    }
  };

  // 重置Excel导入状态
  const resetExcelStatus = () => {
    setUploadedFile(null);
    setFileList([]);
    setFileId("");
    setUploadProgress(0);
    setProcessingStatus("idle");
    setOriginalFileName("");
  };

  // 关闭Excel导入模态框
  const closeExcelModal = () => {
    setExcelModalVisible(false);
    // 如果处理未完成，重置状态
    if (processingStatus !== "completed") {
      resetExcelStatus();
    }
  };

  // 渲染Excel处理模态框的内容
  const renderExcelModalContent = () => {
    switch (processingStatus) {
      case "idle":
        return (
          <>
            <Dragger
              name="file"
              multiple={false}
              showUploadList={false}
              beforeUpload={beforeUpload}
              fileList={fileList}
            >
              <p className="ant-upload-drag-icon">
                <InboxOutlined style={{ fontSize: "48px", color: "#1890ff" }} />
              </p>
              <p className="ant-upload-text">点击或拖拽Excel文件到此区域</p>
              <p className="ant-upload-hint">
                仅支持.xlsx和.xls格式，文件大小不超过50MB
              </p>
            </Dragger>

            {uploadedFile && (
              <div style={{ marginTop: "16px" }}>
                <Typography.Text strong>已选择文件: </Typography.Text>
                <Typography.Text type="secondary">
                  {uploadedFile.name}
                </Typography.Text>
              </div>
            )}

            <div style={{ marginTop: "20px", textAlign: "right" }}>
              <Button onClick={closeExcelModal} style={{ marginRight: 8 }}>
                取消
              </Button>
              <Button
                type="primary"
                onClick={handleProcessExcel}
                disabled={!uploadedFile}
              >
                开始处理
              </Button>
            </div>
          </>
        );

      case "uploading":
      case "processing":
        return (
          <div style={{ textAlign: "center", padding: "20px 0" }}>
            <Spin
              tip={
                processingStatus === "uploading"
                  ? "正在上传文件..."
                  : "正在处理Excel数据..."
              }
              size="large"
            />
            <div style={{ marginTop: "20px" }}>
              <Progress
                percent={uploadProgress}
                status="active"
                strokeColor={{
                  "0%": "#108ee9",
                  "100%": "#87d068",
                }}
              />
            </div>
            <Typography.Text
              type="secondary"
              style={{ display: "block", marginTop: "10px" }}
            >
              {processingStatus === "uploading"
                ? "正在上传Excel文件，请稍候..."
                : "正在将测试案例数据插入到Excel中，这可能需要几分钟时间..."}
            </Typography.Text>
          </div>
        );

      case "completed":
        return (
          <Result
            status="success"
            title="Excel处理成功！"
            subTitle="测试案例数据已成功插入到Excel文件中，您可以下载处理后的文件。"
            extra={[
              <Button
                type="primary"
                key="download"
                onClick={handleDownloadExcel}
                icon={<FileExcelOutlined />}
              >
                下载Excel
              </Button>,
              <Button key="reset" onClick={resetExcelStatus}>
                重新上传
              </Button>,
            ]}
          />
        );

      case "failed":
        return (
          <Result
            status="error"
            title="处理失败"
            subTitle="Excel文件处理过程中出现错误，请重试或联系管理员。"
            extra={[
              <Button key="retry" type="primary" onClick={resetExcelStatus}>
                重新上传
              </Button>,
            ]}
          />
        );

      default:
        return null;
    }
  };

  // 添加handleSaveCase函数实现
  const handleSaveCase = async () => {
    try {
      setLoading(true);

      // 如果未选择交易码，提示错误
      if (!transactionCode) {
        message.error("请选择交易码");
        setLoading(false);
        return;
      }

      // 从URL参数或pageDataService获取任务信息
      let taskData: TaskData | null = null;

      // 获取URL参数
      const urlParams = queryString.parse(location.search);
      const paramDataId = urlParams.dataId as string;

      // 尝试从pageDataService获取数据
      if (paramDataId) {
        taskData = pageDataService.getData(paramDataId);
      }

      if (!taskData) {
        message.error("无法获取任务数据，请重新从任务列表进入");
        setLoading(false);
        return;
      }

      // 构建保存参数，使用新的字段名
      const params = {
        uniqueKey: taskData.uniqueKey || "",
        caseGroup: taskData.record?.caseGroup || "",
        caseAuthor: taskData.record?.caseAuthor || "",
        caseName: taskData.caseName || "",
        caseNo: taskData.caseNo || "",
        pcsPost: requestData,
        txNo: transactionCode,
        autoDataFlag: "1", // 表示已完成
        assertFlag: "1", // 默认断言标志为1
      };

      // 调用保存接口
      const response = await saveCase(params);

      if (response && response.code === 200) {
        message.success("保存成功");
      } else {
        message.error(`保存失败: ${response?.msg || "未知错误"}`);
      }
    } catch (error) {
      console.error("保存案例失败:", error);
      message.error("保存案例失败，请检查网络连接或联系管理员");
    } finally {
      setLoading(false);
    }
  };

  // 添加刷新数据的处理函数
  const handleRefreshData = async () => {
    try {
      // 先获取当前选中的任务
      const currentTask = selectedTasks.find((task) => task.selected);

      if (!currentTask || !currentTask.uniqueKey) {
        message.error("无法获取当前任务信息，请选择一个有效的任务");
        return;
      }

      // 开始加载
      setLoading(true);

      // 调用后端API获取最新的案例数据
      const response = await selectTbCaseBookByUniqueKey(currentTask.uniqueKey);

      if (response && response.code === 200) {
        // 使用handleApiResponse函数处理API响应，更新编辑框内容
        handleApiResponse(response);
        message.success("数据已刷新");
      } else {
        message.error(response?.msg || "获取数据失败");
      }
    } catch (error) {
      console.error("刷新数据失败:", error);
      message.error("刷新数据失败，请检查网络连接或联系管理员");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Layout className={styles.layout}>
      <Sider
        width={250}
        theme="light"
        className={styles.sider}
        breakpoint="lg"
        collapsedWidth={0}
        zeroWidthTriggerStyle={{ top: "10px" }}
      >
        <TaskMenu
          onTaskSelect={(task: TableListItem) => {
            // 任务选择逻辑
            if (handleTaskSelectRef.current) {
              handleTaskSelectRef.current(task);
            }
          }}
          selectedTasks={selectedTasks}
          onTaskSelectionChange={setSelectedTasks}
        />
      </Sider>

      <Content className={styles.content}>
        <div className={styles.mainContent}>
          {/* 标题和按钮 */}
          <div className={styles.headerArea}>
            <CaseTitle taskData={taskData} />

            <div className={styles.buttons}>
              <Space className={styles.buttonSpace}>
                {selectedTasks.length > 0 && (
                  <Button
                    type="default"
                    icon={<FileExcelOutlined />}
                    loading={importLoading}
                    onClick={() => {
                      setExcelModalVisible(true);
                      resetExcelStatus();
                    }}
                    className={`${styles.headerButtonHighlight} ${styles["excel-button"]}`}
                  >
                    导入excel
                  </Button>
                )}

                <Button
                  type="primary"
                  icon={<SendOutlined />}
                  onClick={() => {
                    // 发送请求逻辑
                    handleSend();
                  }}
                  className={`${styles.headerButtonHighlight} ${styles["send-button"]}`}
                >
                  发送
                </Button>

                <Button
                  type="default"
                  icon={<SaveOutlined />}
                  onClick={handleSaveCase}
                  className={`${styles.headerButtonHighlight} ${styles["save-button"]}`}
                >
                  保存
                </Button>

                <Button
                  type="default"
                  icon={<ReloadOutlined />}
                  onClick={handleRefreshData}
                  className={`${styles.headerButtonHighlight} ${styles["refresh-button"]}`}
                >
                  刷新数据
                </Button>

                <Button
                  type="default"
                  icon={<SettingOutlined />}
                  onClick={() => setIsSettingsModalVisible(true)}
                  className={`${styles.headerButtonHighlight} ${styles["settings-button"]}`}
                >
                  设置
                </Button>
              </Space>
            </div>
          </div>

          {/* 编辑器区域 */}
          <div className={styles.editorArea}>
            <div className={styles.editorContainer}>
              <div className={styles.editorsPanelContainer}>
                <div className={styles.requestArea}>
                  <Tabs
                    activeKey="Request"
                    className={styles.paramsTabs}
                    items={[
                      {
                        key: "Request",
                        label: <span data-key="Request">Request</span>,
                        children: (
                          <div
                            className={styles.codeEditorWrapper}
                            ref={requestEditorMountRef}
                            style={{
                              border: "1px solid #ddd",
                              height: "500px",
                              maxHeight: "500px",
                              overflow: "hidden",
                            }}
                          >
                            {loading ? (
                              <div className={styles.loadingContainer}>
                                <Spin
                                  indicator={
                                    <LoadingOutlined
                                      style={{ fontSize: "1.5vh" }}
                                      spin
                                    />
                                  }
                                  tip="正在加载报文数据..."
                                />
                              </div>
                            ) : (
                              <CodeMirror
                                value={requestData || "{}"}
                                height="500px"
                                extensions={darkModeExtensions}
                                onChange={(value) =>
                                  handleRequestDataChange(value)
                                }
                                style={{
                                  fontSize: `${fontSize}px`,
                                  border: "1px solid #222",
                                  maxHeight: "500px",
                                  overflow: "hidden",
                                }}
                                className="single-scrollbar-editor"
                                theme={darkTheme}
                              />
                            )}
                          </div>
                        ),
                      },
                    ]}
                  />
                </div>
                <div className={styles.responseArea}>
                  <Tabs
                    activeKey="Response"
                    className={styles.paramsTabs}
                    items={[
                      {
                        key: "Response",
                        label: <span data-key="Response">Response</span>,
                        children: (
                          <div
                            className={styles.codeEditorWrapper}
                            ref={responseEditorMountRef}
                            style={{
                              border: "1px solid #ddd",
                              height: "500px",
                              maxHeight: "500px",
                              overflow: "hidden",
                            }}
                          >
                            {responseLoading ? (
                              <div className={styles.loadingContainer}>
                                <Spin
                                  indicator={
                                    <LoadingOutlined
                                      style={{ fontSize: "1.5vh" }}
                                      spin
                                    />
                                  }
                                  tip="正在处理请求..."
                                />
                              </div>
                            ) : (
                              <CodeMirror
                                value={responseData || ""}
                                height="500px"
                                extensions={darkModeExtensions}
                                onChange={(value) => setResponseDataRaw(value)}
                                style={{
                                  fontSize: `${fontSize}px`,
                                  border: "1px solid #222",
                                  maxHeight: "500px",
                                  overflow: "hidden",
                                }}
                                readOnly={true}
                                className="single-scrollbar-editor"
                                theme={darkTheme}
                              />
                            )}
                          </div>
                        ),
                      },
                    ]}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </Content>

      {/* Excel导入模态框 */}
      <Modal
        title="Excel数据处理"
        open={excelModalVisible}
        onCancel={closeExcelModal}
        footer={null}
        width={700}
        maskClosable={false}
        destroyOnClose={true}
      >
        {renderExcelModalContent()}
      </Modal>

      {/* 设置模态框 */}
      <Modal
        title="编辑器设置"
        open={isSettingsModalVisible}
        onOk={() => setIsSettingsModalVisible(false)}
        onCancel={() => setIsSettingsModalVisible(false)}
      >
        <div>
          <div style={{ marginBottom: 16 }}>
            <div>字体大小:</div>
            <InputNumber
              min={10}
              max={24}
              value={fontSize}
              onChange={(value) => setFontSize(value || 14)}
            />
          </div>
        </div>
      </Modal>
    </Layout>
  );
};

// 带有缓存的组件
const CachedHttpTest: React.FC = () => (
  <KeepAlive name="HttpTest" when={true} id="HttpTest">
    <HttpTest />
  </KeepAlive>
);

export default CachedHttpTest;
