/* eslint-disable @typescript-eslint/no-unused-expressions */
import request from "@/utils/request";

// 导入FieldDescription接口
export interface FieldDescription {
  fieldName: string;
  fieldCode: string;
  fieldValue: string;
  isObject?: boolean; // 添加isObject可选属性
}

// 根据环境直接构建URL
export function getProxyUrls(env: string) {
  if (!env) {
    return {
      macUrl: "",
      requestUrl: "",
    };
  }

  return {
    macUrl: `/mac-service/${env}/requestGenMac`,
    requestUrl: `/transaction-service/${env}/`,
  };
}

// 获取MAC值
export async function getMacValue(macUrl: string, requestData: any) {
  try {
    // 创建一个可以超时的 Promise
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error("获取mac地址超时")), 30000); // 30秒超时
    });

    const fetchPromise = fetch(macUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json;charset=UTF-8",
      },
      body: JSON.stringify(requestData),
    }).then((response) => {
      if (!response.ok) {
        throw new Error(
          `MAC请求失败: ${response.status} ${response.statusText}`,
        );
      }
      return response.text();
    });

    // 使用 Promise.race 实现超时控制
    return await Promise.race([fetchPromise, timeoutPromise]);
  } catch (error) {
    void console.error("获取MAC值失败:", error);
    throw error;
  }
}

// 发送交易请求到外部地址
export async function sendExternalTransaction(
  requestUrl: string,
  requestData: any,
) {
  try {
    // 创建一个可以超时的 Promise
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error("交易请求超时")), 30000); // 30秒超时
    });

    const fetchPromise = fetch(requestUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json;charset=UTF-8",
      },
      body: JSON.stringify(requestData),
    }).then((response) => {
      if (!response.ok) {
        throw new Error(`请求失败: ${response.status} ${response.statusText}`);
      }
      return response.json();
    });

    // 使用 Promise.race 实现超时控制
    return await Promise.race([fetchPromise, timeoutPromise]);
  } catch (error) {
    void console.error("发送交易请求失败:", error);
    throw error;
  }
}

// 递归解析JSON对象
export function parseJsonObject(
  obj: any,
  parentKey: string = "",
): FieldDescription[] {
  let fields: FieldDescription[] = [];

  for (const [key, value] of Object.entries(obj)) {
    const currentKey = parentKey ? `${parentKey}.${key}` : key;

    if (value && typeof value === "object") {
      // 如果是对象，递归解析
      fields.push({
        fieldName: `${currentKey}`,
        fieldCode: currentKey,
        fieldValue: JSON.stringify(value),
        isObject: true,
      });
      fields = fields.concat(parseJsonObject(value, currentKey));
    } else {
      // 如果是基本类型，直接添加
      fields.push({
        fieldName: currentKey,
        fieldCode: currentKey,
        fieldValue: String(value),
        isObject: false,
      });
    }
  }

  return fields;
}

// 定义字段映射接口
export interface FieldMapping {
  fieldCode: string;
  fieldName: string;
}

// 根据交易码获取字段映射关系 - 完全禁用模拟数据
export async function getPcsMessageByNo(pcsNo: string) {
  console.log('getPcsMessageByNo被调用，但已禁用模拟数据');
  
  // 返回空数据而不是模拟数据
  return {
    rawData: null,
    fields: [],
    fieldMappings: [],
  };
}

// 获取响应报文字段映射 - 使用本地模拟数据替代API请求
export async function getResponseFieldMappings(
  pcsNo: string,
): Promise<FieldMapping[]> {
  console.log('使用本地模拟数据: getResponseFieldMappings', pcsNo);
  
  // 模拟的响应字段映射
  return [
    { fieldCode: "header.responseCode", fieldName: "响应码" },
    { fieldCode: "header.responseMessage", fieldName: "响应消息" },
    { fieldCode: "header.responseTime", fieldName: "响应时间" },
    { fieldCode: "body.transactionId", fieldName: "交易ID" },
    { fieldCode: "body.status", fieldName: "交易状态" },
    { fieldCode: "body.resultDetail", fieldName: "结果详情" }
  ];
}

// 保存案例参数接口
export interface SaveCaseParams {
  uniqueKey: string;
  caseGroup: string;
  caseAuthor: string;
  caseName: string;
  caseNo: string;
  pcsPost: string;
  txNo: string;
  autoDataFlag: string | number;
  assertFlag: string | number;
}

// 获取历史交易码列表 - 使用本地模拟数据替代API请求
export async function getHistoryTransactionCodes(uniqueKey: string) {
  console.log('使用本地模拟数据: getHistoryTransactionCodes', uniqueKey);
  
  if (!uniqueKey) return [];

  // 生成一些模拟的历史交易码
  return [
    "TX001", 
    "TX002", 
    "TX003", 
    "TX004", 
    "TX005"
  ];
}

// 保存案例方法
export async function saveCase(params: SaveCaseParams) {
  return request("/testtool/savePcsSendRecord", {
    method: "POST",
    data: params,
  });
}

/**
 * 导入Excel数据
 * @param formData 包含Excel文件和任务数据的FormData对象
 */
export async function importExcelData(formData: FormData) {
  return request('/testtool/importExcel', {
    method: 'POST',
    data: formData,
    requestType: 'form',
  });
}

/**
 * 根据uniqueKey列表批量获取任务
 * @param uniqueKeyList 唯一键列表
 */
export async function getBatchTasks(uniqueKeyList: string[]) {
  console.log('调用后端API: /testtool/getBatchTasks, uniqueKeyList =', uniqueKeyList);
  
  // 使用request向实际后端API发送请求
  return request('/testtool/getBatchTasks', {
    method: 'POST',
    data: { uniqueKeyList },
  });
}

/**
 * 根据uniqueKey获取案例详情
 * @param uniqueKey 唯一键
 */
export async function selectTbCaseBookByUniqueKey(uniqueKey: string) {
  console.log('调用后端API: /testtool/selectTbCaseBookByUniqueKey, uniqueKey =', uniqueKey);
  
  // 使用request向实际后端API发送GET请求
  return request('/testtool/selectTbCaseBookByUniqueKey', {
    method: 'GET',
    params: { uniqueKey },
  });
} 