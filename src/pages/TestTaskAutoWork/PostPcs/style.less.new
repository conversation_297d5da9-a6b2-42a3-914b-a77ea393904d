.container {
  height: 100%;
  background: #fff;
  padding: 1.5vh;
  margin: 0;
  display: flex;
  flex-direction: column;
  border-radius: 0.5vh;
  box-shadow: 0 0.2vh 0.8vh rgba(0, 0, 0, 0.1);
}

.mainLayout {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.contentArea {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: auto;
  padding: 1vh;
}

.urlBar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1vh 1.5vh;
  margin-bottom: 1vh;
  background: #fff;
  border-radius: 0.5vh;
  box-shadow: 0 0.2vh 0.8vh rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, #1890ff, #36cfc9, #52c41a, #fadb14, #fa8c16, #fa541c);
    opacity: 0.7;
  }

  .selectWrapper {
    display: flex;
    align-items: center;
    gap: 16px; // 间距
    width: 75%; // 占用父元素75%的宽度

    // 为标签添加右边距，作为额外保障
    .selectLabel {
      font-size: 14px;
      color: #333;
      font-weight: 500;
      white-space: nowrap;
      margin-right: 16px; // 右边距
      padding-right: 4px; // 添加一些内边距
    }
  }

  .selectLabel {
    font-size: 14px;
    color: #333;
    font-weight: 500;
    white-space: nowrap;
  }

  .transactionSelect {
    width: 55%; // 修改为55%，缩短交易码输入框长度
    border-radius: 6px;

    :global {
      .ant-select-selector {
        border-radius: 6px;
        height: 32px; // 减小高度，从40px改为32px
        padding: 0 16px; // 调整内边距，从4px改为0px
        line-height: 32px; // 添加行高以保持文字垂直居中
      }

      .ant-select-selection-search {
        margin-left: 6px;
        line-height: 32px; // 添加行高以保持搜索框文字垂直居中
      }

      .ant-select-selection-item {
        line-height: 32px; // 添加行高以保持选中项文字垂直居中
      }

      .ant-select-clear {
        background-color: transparent;
        right: 16px;

        &:hover {
          color: rgba(0, 0, 0, 0.65);
        }
      }
    }
  }
}

.buttonGroup {
  display: flex;
  align-items: center;
  gap: 0.6vh;

  button {
    height: 3.2vh;
    padding: 0 1.2vh;
    font-size: 1.4vh;
    border-radius: 0.4vh;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-0.2vh);
      box-shadow: 0 0.4vh 0.8vh rgba(0, 0, 0, 0.1);
    }

    &.saveButton {
      background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
      border-color: #52c41a;
      color: white;

      &:hover {
        background: linear-gradient(135deg, #389e0d 0%, #52c41a 100%);
        border-color: #389e0d;
      }
    }

    &.sendButton {
      background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
      border-color: #1890ff;

      &:hover {
        background: linear-gradient(135deg, #096dd9 0%, #1890ff 100%);
        border-color: #096dd9;
      }
    }

    &.clearButton {
      background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
      border-color: #ff4d4f;
      color: white;

      &:hover {
        background: linear-gradient(135deg, #cf1322 0%, #ff4d4f 100%);
        border-color: #cf1322;
      }
    }
  }
}

.requestResponseArea {
  flex: 1;
  display: flex;
  flex-direction: row; // 改为水平排列
  gap: 1vh;
  min-height: 0;
  background-color: #fafafa;
  padding: 1vh;
  overflow: hidden;
  height: calc(100vh - 15vh); // 设置固定高度，减去顶部和底部空间

  .requestArea,
  .responseArea {
    flex: 1;
    background: #fff;
    border-radius: 0.5vh;
    box-shadow: 0 0.2vh 0.8vh rgba(0, 0, 0, 0.06);
    border: 1px solid #f0f0f0;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    min-height: 0;
    width: 50%; // 确保两个编辑器占用相等的宽度
    height: 100%; // 使用100%高度
  }
}

.paramsTabs {
  height: 100%;
  display: flex;
  flex-direction: column;

  :global {
    .ant-tabs-content {
      flex: 1;
      height: 100%;
      overflow: hidden;
    }

    .ant-tabs-tabpane {
      height: 100%;
    }

    .ant-tabs-nav {
      margin: 0;
      padding: 0 1vh;
      background: #fff;
      border-bottom: 1px solid #f0f0f0;

      &::before {
        display: none;
      }

      .ant-tabs-tab {
        padding: 0.8vh 0;
        margin: 0 1vh 0 0;
        transition: all 0.3s;

        &:hover {
          color: #1890ff;
        }

        &.ant-tabs-tab-active {
          .ant-tabs-tab-btn {
            color: #1890ff;
            font-weight: 500;
          }
        }
      }
    }
  }
}

.editorContainer {
  position: relative;
  height: 100%;
}

.codeEditorWrapper {
  height: 100%;
  padding: 16px;
  background-color: #fff;
  border-radius: 0 0 8px 8px;
  overflow: auto;
  position: relative;
}

.editorActions {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 10;
  padding: 4px 8px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 6px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  display: flex;
  gap: 8px;
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }
}

.copyButton {
  color: #1890ff;
  border-color: #1890ff;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 4px;
  font-size: 12px;
  padding: 2px 8px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
  border: 1px solid #e8e8e8;
  
  &:hover {
    background-color: #1890ff;
    color: #fff;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}

.formatButton {
  color: #52c41a;
  border-color: #52c41a;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 4px;
  font-size: 12px;
  padding: 2px 8px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
  border: 1px solid #e8e8e8;
  
  &:hover {
    background-color: #52c41a;
    color: #fff;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}

/* 按钮出现动画 */
@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.editorActions {
  animation: fadeInDown 0.3s ease-out;
}

/* 在第一次有内容时显示按钮提示 */
.editorContainer:hover .editorActions {
  opacity: 1;
}

/* 全局样式 */
:global {
  /* 强制滚动条始终显示 */
  html {
    scrollbar-width: thin; /* Firefox */
    -ms-overflow-style: scrollbar; /* IE and Edge */
  }
  
  /* IE和Edge特定的滚动条设置 */
  body {
    -ms-overflow-style: scrollbar;
  }
  
  /* 确保CodeMirror编辑器内的滚动条始终可见 */
  .cm-scroller {
    scrollbar-width: thin !important; /* Firefox */
    -ms-overflow-style: scrollbar !important; /* IE and Edge */
    overflow: scroll !important; /* 所有浏览器 */
  }

  /* 滚动条样式 */
  *::-webkit-scrollbar {
    width: 10px;
    height: 10px;
  }
  
  *::-webkit-scrollbar-track {
    background: #f5f5f5;
    border-radius: 4px;
  }
  
  *::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
    border: 2px solid #f5f5f5;
    
    &:hover {
      background: #a0a0a0;
    }
  }
  
  * {
    scrollbar-width: thin;
    scrollbar-color: #c1c1c1 #f5f5f5;
  }
}

// CodeMirror styles
.codeEditor {
  border-radius: 4px;
  height: auto !important;

  :global {
    .cm-editor {
      border: 1px solid #f0f0f0;
      border-radius: 4px;
      height: auto !important;
    }

    .cm-gutters {
      background-color: #fafafa;
    }

    .cm-activeLineGutter {
      background-color: #e6f7ff;
    }

    .cm-activeLine {
      background-color: #e6f7ff;
    }
  }
} 