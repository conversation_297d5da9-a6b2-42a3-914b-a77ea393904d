.container {
  height: 100%;
  background: #fff;
  padding: 1.5vh;
  margin: 0;
  display: flex;
  flex-direction: column;
  border-radius: 0.5vh;
  box-shadow: 0 0.2vh 0.8vh rgba(0, 0, 0, 0.1);
}

.mainLayout {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.contentArea {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: auto;
  padding: 1vh;
}

.urlBar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1vh 1.5vh;
  margin-bottom: 1vh;
  background: #fff;
  border-radius: 0.5vh;
  box-shadow: 0 0.2vh 0.8vh rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;
}

.selectWrapper {
  display: flex;
  align-items: center;
  gap: 16px;
  width: 75%;
}

.selectLabel {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  white-space: nowrap;
  margin-right: 16px;
  padding-right: 4px;
}

.transactionSelect {
  width: 55%;
  border-radius: 6px;
}

.buttonGroup {
  display: flex;
  align-items: center;
  gap: 0.6vh;
}

.saveButton {
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
  border-color: #52c41a;
  color: white;
}

.sendButton {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  border-color: #1890ff;
}

.clearButton {
  background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
  border-color: #ff4d4f;
  color: white;
}

.requestResponseArea {
  flex: 1;
  display: flex;
  flex-direction: row;
  gap: 1.5vh;
  min-height: 0;
  background-color: #fafafa;
  padding: 1vh;
  overflow: hidden;
  height: calc(100vh - 15vh);
}

.requestArea,
.responseArea {
  flex: 1;
  background: #fff;
  border-radius: 0.5vh;
  box-shadow: 0 0.2vh 0.8vh rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-height: 0;
  width: 50%;
  height: 100%;
}

.paramsTabs {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.codeEditorWrapper {
  flex: 1;
  height: 100%;
  min-height: 0;
  position: relative;
  background: #fff;
  overflow: hidden !important;
  display: flex;
  flex-direction: column;
}

.editorContainer {
  position: relative;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  height: 100%;
  min-height: 500px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.editorActions {
  position: absolute;
  top: 10px;
  right: 20px;
  z-index: 10;
  padding: 6px 8px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  gap: 10px;
  transition: all 0.3s ease;
}

.copyButton {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 4px 10px;
  color: #1890ff;
  transition: all 0.3s;
  border-radius: 3px;
  background: rgba(24, 144, 255, 0.1);
  cursor: pointer;
  margin-right: 8px;
  border: 1px solid #1890ff;
}

.formatButton {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 4px 10px;
  color: #52c41a;
  transition: all 0.3s;
  border-radius: 3px;
  background: rgba(82, 196, 26, 0.1);
  cursor: pointer;
  margin-right: 8px;
  border: 1px solid #52c41a;
}

.codeEditor {
  border-radius: 4px;
  height: auto;
}

.loadingContainer {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(255, 255, 255, 0.9);
  z-index: 1;
}

.layout {
  height: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: row;
  background-color: #f5f5f5;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px;
  height: 56px;
  background-color: #fff;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
  position: sticky;
  top: 0;
  z-index: 100;
}

.headerTitle {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

.sider {
  background-color: #fff;
  border-right: 1px solid #f0f0f0;
  overflow-y: auto;
  overflow-x: hidden;
  height: 100vh;
  position: sticky;
  left: 0;
  top: 0;
  z-index: 10;
  padding: 0;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  background-color: #f5f5f5;
  height: 100vh;
}

.mainContent {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  padding: 24px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.headerArea {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  flex-wrap: wrap;
  gap: 12px;
}

.buttons {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;

  .buttonSpace {
    display: flex;
    gap: 12px;
  }

  .headerButtonHighlight {
    transition: all 0.3s;
    border-radius: 6px;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
    min-width: 100px;
    padding: 6px 16px;
    height: 40px;

    &:before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(255, 255, 255, 0.15);
      opacity: 0;
      transition: opacity 0.3s;
    }

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);

      &:before {
        opacity: 1;
      }
    }

    &:active {
      transform: translateY(0);
      box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);
    }

    &.send-button {
      background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
      border-color: #1890ff;
      color: white;
    }

    &.save-button {
      background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
      border-color: #52c41a;
      color: white;
    }

    &.excel-button {
      background: linear-gradient(135deg, #722ed1 0%, #9254de 100%);
      border-color: #722ed1;
      color: white;
    }

    &.refresh-button {
      background: linear-gradient(135deg, #fa8c16 0%, #ffa940 100%);
      border-color: #fa8c16;
      color: white;
    }

    &.settings-button {
      background: linear-gradient(135deg, #13c2c2 0%, #36cfc9 100%);
      border-color: #13c2c2;
      color: white;
    }
  }
}

.editorArea {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-height: 0;
}

.editorsPanelContainer {
  flex: 1;
  display: flex;
  gap: 24px;
  min-height: 0;
  height: 100%;
}

.requestArea,
.responseArea {
  flex: 1;
  display: flex;
  flex-direction: column;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  overflow: hidden;
  background-color: #fff;
  height: 100%;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  gap: 16px;
  background-color: rgba(255, 255, 255, 0.7);
}

// Mobile responsive styles
@media screen and (max-width: 768px) {
  .layout {
    flex-direction: column;
  }

  .sider {
    width: 100% !important;
    max-width: 100% !important;
    flex: none;
    height: auto;
    max-height: 300px;
    overflow-y: auto;
    margin-bottom: 16px;
  }

  .content {
    padding: 8px;
  }

  .mainContent {
    padding: 12px;
  }

  .headerArea {
    flex-direction: column;
    align-items: flex-start;
  }

  .editorsPanelContainer {
    flex-direction: column;
  }

  .buttons {
    width: 100%;
    justify-content: space-between;
  }
}

// Customize scrollbar for sider
.sider::-webkit-scrollbar {
  width: 3px;
}

.sider::-webkit-scrollbar-track {
  background: transparent;
}

.sider::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 3px;

  &:hover {
    background: #bfbfbf;
  }
}

:global {
  .single-scrollbar-editor {
    &.cm-editor,
    .cm-editor {
      overflow: hidden !important;
    }

    .cm-scroller {
      overflow: auto !important;

      &::-webkit-scrollbar {
        width: 16px !important;
        height: 14px !important;
        display: block !important;
      }

      &::-webkit-scrollbar-track {
        background: #e0e0e0 !important;
        border-radius: 0 !important;
      }

      &::-webkit-scrollbar-thumb {
        background: #888888 !important;
        border-radius: 0 !important;
        border: 0 !important;
        min-height: 40px !important;
      }

      &::-webkit-scrollbar-thumb:hover {
        background: #666666 !important;
      }
    }

    .cm-content {
      overflow: visible !important;
    }
  }

  .force-scrollbar-visible {
    .cm-scroller {
      overflow: auto !important;
      scrollbar-width: auto !important;
      -ms-overflow-style: scrollbar !important;
    }

    .cm-scroller::-webkit-scrollbar {
      width: 16px !important;
      height: 14px !important;
      display: block !important;
      visibility: visible !important;
    }

    .cm-scroller::-webkit-scrollbar-track {
      background: #e0e0e0 !important;
      border-radius: 0 !important;
      visibility: visible !important;
    }

    .cm-scroller::-webkit-scrollbar-thumb {
      background: #888888 !important;
      border-radius: 0 !important;
      visibility: visible !important;
      border: 0 !important;
      min-height: 40px !important;
    }

    .cm-scroller::-webkit-scrollbar-thumb:hover {
      background: #666666 !important;
    }
  }

  .cm-editor {
    background-color: #fff !important;
    color: #333 !important;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    height: 100% !important;
    overflow: hidden !important;
  }

  .cm-scroller {
    height: 100% !important;
    overflow: auto !important;
  }

  .cm-scroller::-webkit-scrollbar {
    width: 16px !important;
    height: 14px !important;
    display: block !important;
  }

  .cm-scroller::-webkit-scrollbar-track {
    background: #e0e0e0 !important;
    border-radius: 0 !important;
  }

  .cm-scroller::-webkit-scrollbar-thumb {
    background: #888888 !important;
    border-radius: 0 !important;
    border: 0 !important;
    min-height: 40px !important;

    &:hover {
      background: #666666 !important;
    }
  }

  .cm-scroller::-webkit-scrollbar-corner {
    background: #e0e0e0 !important;
  }

  .cm-gutters {
    background-color: #f5f5f5 !important;
    border-right: 1px solid #e8e8e8 !important;
  }

  .cm-line {
    color: #333 !important;
  }

  .cm-activeLineGutter {
    background-color: #f0f7ff !important;
  }

  .cm-activeLine {
    background-color: #f0f7ff !important;
  }

  .cm-string {
    color: #0b8235 !important;
  }

  .cm-number {
    color: #1677ff !important;
  }

  .cm-property {
    color: #d32f2f !important;
  }

  .cm-keyword {
    color: #9c27b0 !important;
  }

  .cm-content {
    padding: 10px !important;
    font-family: "Consolas", "Monaco", monospace !important;
    height: auto !important;
    min-height: 1000px !important;
    padding-bottom: 200px !important;
    overflow: visible !important;
  }

  .w-tc-editor {
    height: 100% !important;
    overflow: hidden !important;
  }

  .w-tc-editor-wapper {
    height: 100% !important;
    overflow: hidden !important;
  }

  body {
    .cm-scroller {
      overflow: auto !important;

      &::-webkit-scrollbar {
        width: 16px !important;
        height: 14px !important;
        display: block !important;
        visibility: visible !important;
      }

      &::-webkit-scrollbar-track {
        background: #e0e0e0 !important;
        border-radius: 0 !important;
        visibility: visible !important;
      }

      &::-webkit-scrollbar-thumb {
        background: #888888 !important;
        border-radius: 0 !important;
        visibility: visible !important;
        border: 0 !important;
        min-height: 40px !important;

        &:hover {
          background: #666666 !important;
        }
      }
    }
  }

  .w-tc-editor,
  .cm-editor {
    overflow: hidden !important;
  }

  .cm-scroller {
    overflow: auto !important;
  }
}

.taskNavigator {
  background: #fff;
  border-radius: 8px;
  height: 100%;
  overflow: auto;
}

.customCodeEditor {
  width: 100%;
  height: 100% !important;
  overflow: hidden !important;

  :global {
    .cm-editor {
      height: 100% !important;
      overflow: hidden !important;
    }

    .cm-scroller {
      overflow: auto !important;
    }

    .cm-scroller::-webkit-scrollbar {
      width: 16px !important;
      height: 14px !important;
      display: block !important;
      visibility: visible !important;
    }

    .cm-scroller::-webkit-scrollbar-track {
      background: #e0e0e0 !important;
      border-radius: 0 !important;
    }

    .cm-scroller::-webkit-scrollbar-thumb {
      background: #888888 !important;
      border-radius: 0 !important;
      border: 0 !important;
      min-height: 40px !important;

      &:hover {
        background: #666666 !important;
      }
    }
  }
}
