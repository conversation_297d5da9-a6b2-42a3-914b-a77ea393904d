/* eslint-disable @typescript-eslint/no-unused-expressions */
import request from "@/utils/request";

// 导入FieldDescription接口
export interface FieldDescription {
  fieldName: string;
  fieldCode: string;
  fieldValue: string;
  isObject?: boolean; // 添加isObject可选属性
}

// 根据环境直接构建URL
export function getProxyUrls(env: string) {
  if (!env) {
    return {
      macUrl: "",
      requestUrl: "",
    };
  }

  return {
    macUrl: `/mac-service/${env}/requestGenMac`,
    requestUrl: `/transaction-service/${env}/`,
  };
}

// 获取MAC值
export async function getMacValue(macUrl: string, requestData: any) {
  try {
    // 创建一个可以超时的 Promise
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error("获取mac地址超时")), 30000); // 30秒超时
    });

    const fetchPromise = fetch(macUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json;charset=UTF-8",
      },
      body: JSON.stringify(requestData),
    }).then((response) => {
      if (!response.ok) {
        throw new Error(
          `MAC请求失败: ${response.status} ${response.statusText}`,
        );
      }
      return response.text();
    });

    // 使用 Promise.race 实现超时控制
    return await Promise.race([fetchPromise, timeoutPromise]);
  } catch (error) {
    void console.error("获取MAC值失败:", error);
    throw error;
  }
}

// 发送交易请求到外部地址
export async function sendExternalTransaction(
  requestUrl: string,
  requestData: any,
) {
  try {
    // 创建一个可以超时的 Promise
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error("交易请求超时")), 30000); // 30秒超时
    });

    const fetchPromise = fetch(requestUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json;charset=UTF-8",
      },
      body: JSON.stringify(requestData),
    }).then((response) => {
      if (!response.ok) {
        throw new Error(`请求失败: ${response.status} ${response.statusText}`);
      }
      return response.json();
    });

    // 使用 Promise.race 实现超时控制
    return await Promise.race([fetchPromise, timeoutPromise]);
  } catch (error) {
    void console.error("发送交易请求失败:", error);
    throw error;
  }
}

// 递归解析JSON对象
export function parseJsonObject(
  obj: any,
  parentKey: string = "",
): FieldDescription[] {
  let fields: FieldDescription[] = [];

  for (const [key, value] of Object.entries(obj)) {
    const currentKey = parentKey ? `${parentKey}.${key}` : key;

    if (value && typeof value === "object") {
      // 如果是对象，递归解析
      fields.push({
        fieldName: `${currentKey}`,
        fieldCode: currentKey,
        fieldValue: JSON.stringify(value),
        isObject: true,
      });
      fields = fields.concat(parseJsonObject(value, currentKey));
    } else {
      // 如果是基本类型，直接添加
      fields.push({
        fieldName: currentKey,
        fieldCode: currentKey,
        fieldValue: String(value),
        isObject: false,
      });
    }
  }

  return fields;
}

// 定义字段映射接口
export interface FieldMapping {
  fieldCode: string;
  fieldName: string;
}

// 根据交易码获取字段映射关系 - 完全禁用模拟数据
export async function getPcsMessageByNo(pcsNo: string) {
  console.log("getPcsMessageByNo被调用，但已禁用模拟数据");

  // 返回空数据而不是模拟数据
  return {
    rawData: null,
    fields: [],
    fieldMappings: [],
  };
}

// 获取响应报文字段映射 - 使用本地模拟数据替代API请求
export async function getResponseFieldMappings(
  pcsNo: string,
): Promise<FieldMapping[]> {
  console.log("使用本地模拟数据: getResponseFieldMappings", pcsNo);

  // 模拟的响应字段映射
  return [
    { fieldCode: "header.responseCode", fieldName: "响应码" },
    { fieldCode: "header.responseMessage", fieldName: "响应消息" },
    { fieldCode: "header.responseTime", fieldName: "响应时间" },
    { fieldCode: "body.transactionId", fieldName: "交易ID" },
    { fieldCode: "body.status", fieldName: "交易状态" },
    { fieldCode: "body.resultDetail", fieldName: "结果详情" },
  ];
}

// 保存案例参数接口
export interface SaveCaseParams {
  uniqueKey: string;
  caseGroup: string;
  caseAuthor: string;
  caseName: string;
  caseNo: string;
  pcsPost: string;
  txNo: string;
  autoDataFlag: string | number;
  assertFlag: string | number;
}

// 获取历史交易码列表 - 使用本地模拟数据替代API请求
export async function getHistoryTransactionCodes(uniqueKey: string) {
  console.log("使用本地模拟数据: getHistoryTransactionCodes", uniqueKey);

  if (!uniqueKey) return [];

  // 生成一些模拟的历史交易码
  return ["TX001", "TX002", "TX003", "TX004", "TX005"];
}

// 保存案例方法
export async function saveCase(params: SaveCaseParams) {
  // 添加调试日志
  console.log("调用saveCase API: /testtool/savePcsSendRecord, 参数:", params);

  try {
    // 首先尝试使用request工具发送请求
    const response = await request("/testtool/savePcsSendRecord", {
      method: "POST",
      data: params,
    });

    console.log("saveCase API成功响应:", response);
    return response;
  } catch (error) {
    console.error("使用request工具调用saveCase API失败:", error);

    // 如果request工具失败，尝试使用原生fetch作为备选方案
    console.log("使用原生fetch作为备选方案发送请求");

    try {
      // 获取API基础路径
      const apiBasePath = (window as any).SERVER_API_URL || "";
      const url = `${apiBasePath}/api/testtool/savePcsSendRecord`;

      console.log("备选方案请求URL:", url);

      const fetchResponse = await fetch(url, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(params),
        credentials: "include",
      });

      if (!fetchResponse.ok) {
        throw new Error(
          `备选方案请求失败: ${fetchResponse.status} ${fetchResponse.statusText}`,
        );
      }

      const result = await fetchResponse.json();
      console.log("备选方案请求成功:", result);
      return result;
    } catch (fetchError) {
      console.error("使用备选方案也失败:", fetchError);
      throw fetchError;
    }
  }
}

// API响应接口
export interface ApiResponse<T = any> {
  code: number;
  msg: string;
  data?: T | string; // data可能是对象T或JSON字符串
}

// Excel处理结果接口
export interface ExcelProcessResult {
  fielId: string; // 注意这里是fielId而不是fileId，与后端保持一致
}

/**
 * 导入Excel数据
 * @param formData 包含Excel文件、uniqueKey和request数据的FormData对象
 * @returns 处理结果
 */
export async function importExcelData(
  formData: FormData,
): Promise<ApiResponse<ExcelProcessResult>> {
  console.log("开始导入Excel数据处理");

  // 创建可取消的请求
  const controller = new AbortController();
  const timeoutId = setTimeout(() => {
    controller.abort();
    console.log("请求超时，已中止");
  }, 60000); // 60秒超时

  try {
    const response = await request<ApiResponse<ExcelProcessResult>>(
      "/exceltool/excel/importExcel",
      {
        method: "POST",
        data: formData,
        requestType: "form",
        getResponse: true,
        timeout: 60000, // 60秒超时
        errorHandler: (error) => {
          console.error("Excel导入请求错误:", error);
          return Promise.reject(error);
        },
        signal: controller.signal,
      },
    );

    // 请求成功，清除超时定时器
    clearTimeout(timeoutId);

    console.log("Excel数据导入响应:", response);
    return response.data;
  } catch (error) {
    // 请求失败，清除超时定时器
    clearTimeout(timeoutId);
    console.error("Excel导入失败:", error);
    throw error;
  }
}

/**
 * 下载处理后的Excel文件
 * @param fielId 文件ID标识 (后端使用fielId作为参数名)
 * @returns Blob响应数据
 */
export async function downloadProcessedExcel(fielId: string) {
  console.log("开始下载Excel文件，fielId:", fielId);
  return request(`/exceltool/excel/downloadProcessedExcel?fielId=${fielId}`, {
    method: "GET",
    responseType: "blob",
    headers: {
      Accept:
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      "Content-Type": "application/json",
    },
    getResponse: true,
    errorHandler: (error) => {
      console.error("文件下载请求错误:", error);
      return Promise.reject(error);
    },
  });
}

/**
 * 根据uniqueKey列表批量获取任务
 * @param uniqueKeyList 唯一键列表
 */
export async function getBatchTasks(uniqueKeyList: string[]) {
  console.log(
    "调用后端API: /testtool/getBatchTasks, uniqueKeyList =",
    uniqueKeyList,
  );

  // 使用request向实际后端API发送请求
  return request("/testtool/getBatchTasks", {
    method: "POST",
    data: { uniqueKeyList },
  });
}

/**
 * 根据uniqueKey获取案例详情
 * @param uniqueKey 唯一键
 */
export async function selectTbCaseBookByUniqueKey(uniqueKey: string) {
  console.log(
    "调用后端API: /exceltool/excel/selectTbCaseBookByUniqueKey, uniqueKey =",
    uniqueKey,
  );

  // 使用request向实际后端API发送GET请求
  return request("/exceltool/excel/selectTbCaseBookByUniqueKey", {
    method: "GET",
    params: { uniqueKey },
  });
}
