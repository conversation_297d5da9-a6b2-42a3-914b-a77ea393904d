import React from "react";
import { Typography, Tag, Space } from "antd";
import styles from "./style.less";

const { Title, Text } = Typography;

interface TaskData {
  txNo: string;
  taskId: string;
  uniqueKey: string;
  completed: boolean;
  caseName: string;
  caseNo: string;
  pcsPost: string;
}

interface CaseTitleProps {
  taskData: TaskData | null;
}

const CaseTitle: React.FC<CaseTitleProps> = ({ taskData }) => {
  if (!taskData) {
    return (
      <div className={styles.caseTitleContainer}>
        <Title level={4}>未选择测试案例</Title>
      </div>
    );
  }

  return (
    <div className={styles.caseTitleContainer}>
      <Space direction="vertical" size={0}>
        <Title level={4}>{taskData.caseName || "未命名案例"}</Title>
        <Space size={8}>
          {taskData.caseNo && (
            <Text type="secondary">案例编号: {taskData.caseNo}</Text>
          )}
          {taskData.txNo && (
            <Text type="secondary">交易编号: {taskData.txNo}</Text>
          )}
          {taskData.completed && <Tag color="success">已完成</Tag>}
          {!taskData.completed && <Tag color="warning">未完成</Tag>}
        </Space>
      </Space>
    </div>
  );
};

export default CaseTitle;
