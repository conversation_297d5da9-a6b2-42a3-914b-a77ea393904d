.menuContainer {
  background: #fff;
  border-radius: 8px;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 12px;
}

.menuHeader {
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
  background: linear-gradient(to right, #fafafa, #f5f5f5);

  .menuTitle {
    font-size: 16px;
    font-weight: 500;
    color: #262626;
    margin-bottom: 4px;
  }

  .taskCount {
    font-size: 12px;
    color: #8c8c8c;
  }
}

.menuContent {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
}

.menuItem {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  margin-bottom: 6px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  background-color: #fff;
  border: 1px solid transparent;
  border-left: 2px solid transparent;

  &:hover {
    background-color: #f5f5f5;
    transform: translateX(2px);
  }

  &.menu-item-active {
    &.completed {
      background-color: #f6ffed;
      border-left: 3px solid #52c41a;
      padding-left: 11px;
      box-shadow: 0 2px 4px rgba(82, 196, 26, 0.1);

      &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(82, 196, 26, 0.05);
        pointer-events: none;
        border-radius: 6px;
      }

      .taskName {
        color: #52c41a;
      }
    }

    &.pending {
      background-color: #fffbe6;
      border-left: 3px solid #faad14;
      padding-left: 11px;
      box-shadow: 0 2px 4px rgba(250, 173, 20, 0.1);

      &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(250, 173, 20, 0.05);
        pointer-events: none;
        border-radius: 6px;
      }

      .taskName {
        color: #faad14;
      }
    }
  }
}

.taskInfo {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.taskName {
  font-size: 13px;
  font-weight: 500;
  color: #262626;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  transition: color 0.3s ease;
}

.taskCode {
  font-size: 11px;
  color: #8c8c8c;
  font-family: monospace;
}

.completedIcon {
  color: #52c41a;
  font-size: 14px;
  margin-left: 6px;
  transition: all 0.3s ease;
}

.pendingIcon {
  color: #faad14;
  font-size: 14px;
  margin-left: 6px;
  transition: all 0.3s ease;
}

// Custom scrollbar styles
.menuContent {
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: #f5f5f5;
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb {
    background: #d9d9d9;
    border-radius: 2px;

    &:hover {
      background: #bfbfbf;
    }
  }
}

.taskInfoSection {
  display: flex;
  flex-direction: column;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 12px;
  background-color: #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.03);
}

.taskInfoHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.taskInfoContent {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.taskInfoItem {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.emptyTaskInfo {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px 0;
  border: 1px dashed #f0f0f0;
  border-radius: 6px;
  background-color: #fafafa;
}

.taskList {
  margin-top: 12px;
}

.tasksContainer {
  max-height: calc(100vh - 260px);
  overflow-y: auto;
  padding: 2px 0;
}

// Custom scrollbar for tasks container
.tasksContainer::-webkit-scrollbar {
  width: 3px;
}

.tasksContainer::-webkit-scrollbar-track {
  background: transparent;
}

.tasksContainer::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 2px;

  &:hover {
    background: #bfbfbf;
  }
}

// Mobile responsive styles
@media screen and (max-width: 768px) {
  .menuContainer {
    padding: 8px;
  }

  .taskInfoSection {
    padding: 8px;
  }

  .tasksContainer {
    max-height: 200px;
  }
}
