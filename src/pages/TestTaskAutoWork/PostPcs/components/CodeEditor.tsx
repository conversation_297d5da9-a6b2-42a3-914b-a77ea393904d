import React, { useEffect, useRef, ReactNode, memo, useState } from "react";
import CodeMirror from "@uiw/react-codemirror";
import { json } from "@codemirror/lang-json";
// @ts-ignore
import { EditorView } from "@codemirror/view";
// @ts-ignore
import { indentUnit } from "@codemirror/language";
import styles from "../style.less";

interface CodeEditorProps {
  value: string;
  onChange?: (value: string) => void;
  editable?: boolean;
  elementRef?: React.RefObject<HTMLDivElement>;
  extensions?: any[];
  actions?: ReactNode;
  height?: string | number;
  width?: string;
  readOnly?: boolean;
  fontSize?: number;
  className?: string;
}

// 修改为正确的memo用法
const MemoizedCodeMirror = memo(CodeMirror);

// Optimized CodeEditor component that wraps CodeMirror
const CodeEditor: React.FC<CodeEditorProps> = ({
  value,
  onChange,
  editable = true,
  elementRef,
  extensions = [],
  actions,
  height = "100%",
  width = "100%",
  readOnly = false,
  fontSize = 14,
  className,
}) => {
  const localRef = useRef<HTMLDivElement>(null);
  const ref = elementRef || localRef;
  const previousValueRef = useRef<string>(value);
  const [forceUpdate, setForceUpdate] = useState<number>(0);

  // Detect value changes and force rerender when necessary
  useEffect(() => {
    if (value !== previousValueRef.current) {
      console.log("CodeEditor value changed, forcing rerender");
      console.log(
        "New value:",
        value
          ? value.substring(0, 100) + (value.length > 100 ? "..." : "")
          : "empty",
      );
      console.log("Value length:", value?.length || 0);
      console.log("Value type:", typeof value);
      previousValueRef.current = value;
      // Force component to rerender
      setForceUpdate((prev) => prev + 1);
    }
  }, [value]);

  // Only update when value actually changes
  const handleChange = (newValue: string) => {
    if (newValue !== previousValueRef.current && onChange) {
      previousValueRef.current = newValue;
      onChange(newValue);
    }
  };

  // Default extensions with optimized performance settings
  const defaultExtensions = [
    json(),
    EditorView.lineWrapping,
    indentUnit.of("  "),
    EditorView.theme({
      "&": {
        height: "100%",
        maxHeight: "100%",
        overflow: "hidden",
      },
      ".cm-scroller": {
        overflow: "auto",
        height: "100%",
        maxHeight: "100%",
      },
      ".cm-content": {
        minHeight: "600px",
        padding: "8px 12px",
        overflow: "visible",
      },
      ".cm-gutters": {
        minHeight: "600px",
      },
    }),
  ];

  // Merge default extensions with any custom extensions
  const mergedExtensions = [...defaultExtensions, ...extensions];

  // Update editor font size from global settings
  useEffect(() => {
    console.log(
      "CodeEditor mounting/updating with value:",
      value?.substring(0, 50) + "...",
    );
    if (ref.current) {
      const editorElement = ref.current.querySelector(
        ".cm-editor",
      ) as HTMLElement;
      if (editorElement) {
        // Apply font size from the parent component's settings
        editorElement.style.fontSize = `${fontSize}px`;
        // Ensure full height
        editorElement.style.height =
          typeof height === "number" ? `${height}px` : String(height);
        // 确保编辑器本身不显示滚动条
        editorElement.style.overflow = "hidden";
      }

      // 确保滚动区域有滚动条
      const scrollerElement = ref.current.querySelector(
        ".cm-scroller",
      ) as HTMLElement;
      if (scrollerElement) {
        scrollerElement.style.overflow = "auto";
      }

      // 确保内容区域没有滚动条
      const contentElement = ref.current.querySelector(
        ".cm-content",
      ) as HTMLElement;
      if (contentElement) {
        contentElement.style.overflow = "visible";
        // Add fake content for scrollbar to appear if needed
        const extraDiv = document.createElement("div");
        extraDiv.style.height = "1000px";
        extraDiv.style.width = "1px";
        extraDiv.style.opacity = "0";
        contentElement.appendChild(extraDiv);
      }
    }
  }, [value, ref, forceUpdate, height, fontSize]);

  return (
    <div
      ref={ref}
      className={`${styles.codeEditor} ${className || ""}`}
      style={{
        height,
        width,
        overflow: "hidden",
        display: "flex",
        flexDirection: "column",
      }}
    >
      {actions && <div className={styles.editorHeader}>{actions}</div>}
      <MemoizedCodeMirror
        key={`editor-${forceUpdate}`}
        value={value}
        height={typeof height === "number" ? `${height}px` : String(height)}
        width={typeof width === "string" ? width : "100%"}
        basicSetup={{
          lineNumbers: true,
          foldGutter: true,
          highlightActiveLine: true,
          tabSize: 2,
          allowMultipleSelections: true,
          indentOnInput: true,
          bracketMatching: true,
          closeBrackets: true,
          autocompletion: true,
          highlightSelectionMatches: true,
          syntaxHighlighting: true,
        }}
        onChange={handleChange}
        readOnly={!editable || readOnly}
        extensions={mergedExtensions}
        style={
          {
            fontSize: `${fontSize}px`,
            height: "100%",
            overflow: "hidden",
          } as React.CSSProperties
        }
        className="force-scrollbar-visible"
      />
    </div>
  );
};

// Export a memoized version of the component to prevent unnecessary re-renders
export default memo(CodeEditor);
