import React, { useState, useEffect, useMemo, CSSProperties } from "react";
import {
  Typography,
  Badge,
  message,
  Tooltip,
  Menu as AntMenu,
  Space,
  Divider,
  Tag,
} from "antd";
import { history } from "umi";
import {
  FileTextOutlined,
  ApiOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  AppstoreOutlined,
  CheckSquareOutlined,
  InfoCircleOutlined,
} from "@ant-design/icons";
import { getHistoryTransactionCodes } from "../service";
import styles from "./style.less";

// Define the TableListItem interface here to avoid import issues
interface TableListItem {
  caseGroup?: string; // 测试周期
  caseAuthor?: string; // 测试者
  caseName?: string; // 测试案例名称
  caseNo: string; // 测试案例编号（必须字段）
  pcsPost?: string | null; // 接口请求信息
  txNo?: string; // 接口编号
  uniqueKey?: string; // 唯一标识（用于列表去重和作为rowKey）
  autoDataFlag: number | string; // 完成状态：1-已完成，0-未完成（必须字段）
  assertFlag?: number | string; // 断言标志
  selected?: boolean; // 是否被选中
  caseDesc?: string; // 案例描述
  caseType?: string; // 案例类型
}

const { Title, Text, Paragraph } = Typography;

interface MenuProps {
  onTaskSelect: (task: TableListItem) => void;
  selectedTasks?: TableListItem[]; // 添加批量跳转的任务列表
  onTaskSelectionChange?: (tasks: TableListItem[]) => void; // 添加任务选择变更回调
  onHistoryDataLoaded?: (data: any) => void; // 添加历史数据加载回调
}

// 现代主题色常量
const THEME_COLORS = {
  primary: "#1890ff",
  primaryLight: "#e6f7ff",
  primaryLighter: "#f0f7ff",
  text: {
    primary: "#262626",
    secondary: "#595959",
    disabled: "#8c8c8c",
  },
  background: {
    light: "#ffffff",
    hover: "#f5f5f5",
    selected: "#e6f7ff",
  },
  border: "#f0f0f0",
  status: {
    success: "#52c41a",
    successLight: "rgba(82, 196, 26, 0.15)",
    successBorder: "rgba(82, 196, 26, 0.6)",
    successText: "#389e0d",
    pending: "#faad14",
    pendingLight: "rgba(250, 173, 20, 0.15)",
    pendingBorder: "rgba(250, 173, 20, 0.6)",
    pendingText: "#d46b08",
    error: "#ff4d4f",
  },
};

/**
 * 任务菜单组件
 */
const TaskMenu: React.FC<MenuProps> = ({
  onTaskSelect,
  selectedTasks = [],
  onTaskSelectionChange,
  onHistoryDataLoaded,
}) => {
  const [activeTask, setActiveTask] = useState<{
    code: string;
    index: number;
  } | null>(null);

  // 自定义滚动条样式的类名，使用 useMemo 确保不会在每次渲染时创建新值
  const scrollBarClassName = useMemo(() => "custom-scrollbar-menu", []);

  // 渲染前清理任何可能的重复选中状态
  const taskListToRender = useMemo(() => {
    if (!selectedTasks || selectedTasks.length === 0) return [];
    return selectedTasks;
  }, [selectedTasks]);

  // 在组件挂载时添加全局CSS以支持滚动条样式和菜单高亮
  useEffect(() => {
    const styleId = "custom-scrollbar-styles";
    // 检查是否已存在该样式
    if (!document.getElementById(styleId)) {
      // 添加全局CSS样式
      const styleElement = document.createElement("style");
      styleElement.id = styleId;
      styleElement.innerHTML = `
        .${scrollBarClassName}::-webkit-scrollbar {
          width: 4px;
        }
        .${scrollBarClassName}::-webkit-scrollbar-track {
          background: transparent;
        }
        .${scrollBarClassName}::-webkit-scrollbar-thumb {
          background: ${THEME_COLORS.border};
          border-radius: 4px;
        }
        
        .menu-item-active {
          position: relative;
          padding-left: 11px !important;
          border: 1px solid fade(@primary-color, 30%);
          box-shadow: 0 0 5px rgba(24, 144, 255, 0.1);
        }
        
        .menu-item-active.completed {
          background-color: ${THEME_COLORS.status.successLight} !important;
          border-left: 3px solid ${THEME_COLORS.status.success} !important;
        }
        
        .menu-item-active.completed::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(90deg, rgba(82, 196, 26, 0.1), rgba(82, 196, 26, 0.05), rgba(82, 196, 26, 0.02));
          pointer-events: none;
          border-radius: 8px;
        }
        
        .menu-item-active.pending {
          background-color: ${THEME_COLORS.status.pendingLight} !important;
          border-left: 3px solid ${THEME_COLORS.status.pending} !important;
        }
        
        .menu-item-active.pending::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(90deg, rgba(250, 173, 20, 0.1), rgba(250, 173, 20, 0.05), rgba(250, 173, 20, 0.02));
          pointer-events: none;
          border-radius: 8px;
        }
        
        @keyframes ripple-effect {
          0% {
            opacity: 1;
            transform: scale(0) translate(-50%, -50%);
          }
          100% {
            opacity: 0;
            transform: scale(2) translate(-25%, -25%);
          }
        }
        
        .checkbox-ripple-effect {
          position: absolute;
          border-radius: 50%;
          pointer-events: none;
          opacity: 0.6;
          transform-origin: 0 0;
        }

        .menuItem {
          &:hover {
            background-color: #f5f5f5;
          }

          &.completed:hover {
            background-color: rgba(82, 196, 26, 0.15);
          }

          &.pending:hover {
            background-color: rgba(250, 173, 20, 0.15);
          }

          &.menu-item-active {
            background-color: #e6f7ff !important;
          }
        }
      `;
      document.head.appendChild(styleElement);
    }

    // 组件卸载时不移除样式，因为其他实例可能仍需要它
  }, [scrollBarClassName]);

  // 初始化时，设置默认激活项
  useEffect(() => {
    // 如果有任务列表但还没有设置激活索引
    if (selectedTasks && selectedTasks.length > 0 && !activeTask) {
      // 默认激活第一个任务
      setActiveTask({ code: selectedTasks[0].caseNo, index: 0 });

      // 可选：如果URL中有taskCode参数，可以尝试找到匹配项设为激活，但不必须
      try {
        const urlParams = new URLSearchParams(window.location.search);
        const taskCode = urlParams.get("caseNo");

        if (taskCode) {
          const matchIndex = selectedTasks.findIndex(
            (task) => task.caseNo === taskCode,
          );
          if (matchIndex >= 0) {
            setActiveTask({
              code: selectedTasks[matchIndex].caseNo,
              index: matchIndex,
            });
            // 不自动调用handleTaskSelect，让用户点击来触发
          }
        }
      } catch (err) {
        console.error("解析URL参数失败:", err);
      }
    }
  }, [selectedTasks, activeTask]);

  /**
   * 处理任务选择
   * @param task 被选择的任务
   * @param index 任务在列表中的索引
   */
  const handleTaskSelect = async (task: TableListItem, index: number) => {
    // 任务选择逻辑不变
    if (task.selected) return; // 如果已经选中则不进行处理

    setActiveTask({
      code: task.caseNo,
      index,
    });

    console.log("Task selected:", task);

    if (onTaskSelect) {
      onTaskSelect(task);
    }

    // 更新selectedTasks中的selected状态
    if (selectedTasks && selectedTasks.length > 0 && onTaskSelectionChange) {
      const updatedTasks = selectedTasks.map((t, i) => ({
        ...t,
        selected: i === index,
      }));
      onTaskSelectionChange(updatedTasks);
    }

    try {
      // 获取历史数据
      const historyData = await getHistoryTransactionCodes(
        task.uniqueKey || "",
      );

      // 如果获取数据成功，通知父组件
      if (historyData && onHistoryDataLoaded) {
        onHistoryDataLoaded(historyData);
      }
    } catch (error) {
      console.error("获取历史数据失败:", error);
      message.error("获取历史数据失败，请重试");
    }
  };

  /**
   * 判断任务是否已完成
   */
  const isTaskCompleted = (task: TableListItem): boolean => {
    return task.autoDataFlag === "1" || task.autoDataFlag === 1;
  };

  /**
   * 获取任务状态对应的颜色
   */
  const getStatusColor = (task: TableListItem): string => {
    return isTaskCompleted(task)
      ? THEME_COLORS.status.success
      : THEME_COLORS.status.pending;
  };

  /**
   * 获取菜单项样式
   */
  const getMenuItemStyle = (task: TableListItem): React.CSSProperties => {
    const isCompleted = isTaskCompleted(task);
    const isActive = task.selected === true;

    // 基础样式
    const baseStyle: React.CSSProperties = {
      padding: "8px 12px", // 减小内边距
      marginBottom: "6px", // 减小底部边距
      borderRadius: "6px",
      cursor: "pointer",
      transition: "all 0.3s ease",
      position: "relative",
      borderLeft: "2px solid transparent",
      display: "flex",
      alignItems: "center",
      justifyContent: "space-between",
    };

    // 已完成任务的样式
    if (isCompleted) {
      return {
        ...baseStyle,
        borderLeftColor: isActive ? THEME_COLORS.status.success : "transparent",
        backgroundColor: isActive
          ? THEME_COLORS.status.successLight
          : "transparent",
      };
    }

    // 未完成任务的样式
    return {
      ...baseStyle,
      borderLeftColor: isActive ? THEME_COLORS.status.pending : "transparent",
      backgroundColor: isActive
        ? THEME_COLORS.status.pendingLight
        : "transparent",
    };
  };

  /**
   * 获取任务状态颜色
   */
  const getTaskStatusColor = (
    task: TableListItem,
  ): { color: string; backgroundColor: string; border: string } => {
    const isCompleted = isTaskCompleted(task);

    if (isCompleted) {
      return {
        color: THEME_COLORS.status.successText,
        backgroundColor: THEME_COLORS.status.successLight,
        border: THEME_COLORS.status.successBorder,
      };
    }

    return {
      color: THEME_COLORS.status.pendingText,
      backgroundColor: THEME_COLORS.status.pendingLight,
      border: THEME_COLORS.status.pendingBorder,
    };
  };

  // 渲染单个任务项
  const renderTaskItem = (task: TableListItem, index: number) => {
    const isCompleted = isTaskCompleted(task);
    const taskName = task.caseName || "未命名任务";
    const taskCode = task.caseNo;
    const isActive = task.selected === true;

    // 简化显示的内容
    return (
      <Tooltip
        title={
          <div>
            <div>
              <strong>名称:</strong> {taskName}
            </div>
            <div>
              <strong>编号:</strong> {taskCode}
            </div>
            {task.txNo && (
              <div>
                <strong>接口:</strong> {task.txNo}
              </div>
            )}
            {task.caseDesc && (
              <div>
                <strong>描述:</strong> {task.caseDesc}
              </div>
            )}
            {task.caseType && (
              <div>
                <strong>类型:</strong> {task.caseType}
              </div>
            )}
          </div>
        }
        placement="right"
        mouseEnterDelay={0.5}
      >
        <div
          key={task.caseNo}
          className={`${styles.menuItem} ${isActive ? styles["menu-item-active"] : ""} ${isCompleted ? styles.completed : styles.pending}`}
          onClick={() => handleTaskSelect(task, index)}
          style={getMenuItemStyle(task)}
        >
          <div className={styles.taskInfo}>
            {/* 显示简洁的名称 - 只显示前15个字符 */}
            <span
              className={styles.taskName}
              style={{
                color: isCompleted
                  ? THEME_COLORS.status.successText
                  : THEME_COLORS.status.pendingText,
                fontWeight: 500,
              }}
            >
              {taskName.length > 15
                ? `${taskName.substring(0, 15)}...`
                : taskName}
            </span>
          </div>
          {isCompleted ? (
            <CheckCircleOutlined
              className={styles.completedIcon}
              style={{
                color: THEME_COLORS.status.successText,
                fontSize: "14px",
              }}
            />
          ) : (
            <ClockCircleOutlined
              className={styles.pendingIcon}
              style={{
                color: THEME_COLORS.status.pendingText,
                fontSize: "14px",
              }}
            />
          )}
        </div>
      </Tooltip>
    );
  };

  // 渲染任务信息区域 - 简化显示内容
  const renderTaskInfoSection = () => {
    if (!selectedTasks || selectedTasks.length === 0) {
      return (
        <div className={styles.emptyTaskInfo}>
          <InfoCircleOutlined style={{ fontSize: "24px", color: "#bfbfbf" }} />
          <Text type="secondary" style={{ marginTop: "8px" }}>
            未选择任务
          </Text>
        </div>
      );
    }

    // 找到当前选中的任务
    const selectedTask =
      selectedTasks.find((task) => task.selected) || selectedTasks[0];

    return (
      <div className={styles.taskInfoSection}>
        <div className={styles.taskInfoHeader}>
          <Title level={5} style={{ margin: 0 }}>
            当前案例
          </Title>
          <Tag
            color={
              selectedTask.autoDataFlag === "1" ||
              selectedTask.autoDataFlag === 1
                ? "success"
                : "warning"
            }
          >
            {selectedTask.autoDataFlag === "1" ||
            selectedTask.autoDataFlag === 1
              ? "已完成"
              : "未完成"}
          </Tag>
        </div>

        <Divider style={{ margin: "8px 0" }} />

        <div className={styles.taskInfoContent}>
          <div className={styles.taskInfoItem}>
            <Text type="secondary">名称:</Text>
            <Text strong>{selectedTask.caseName || "未命名"}</Text>
          </div>

          <div className={styles.taskInfoItem}>
            <Text type="secondary">编号:</Text>
            <Text copyable={{ text: selectedTask.caseNo }}>
              {selectedTask.caseNo || "无"}
            </Text>
          </div>

          {selectedTask.txNo && (
            <div className={styles.taskInfoItem}>
              <Text type="secondary">交易:</Text>
              <Text copyable={{ text: selectedTask.txNo }}>
                {selectedTask.txNo}
              </Text>
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className={styles.menuContainer}>
      {/* 任务信息区域 - 显示当前选中任务 */}
      {renderTaskInfoSection()}

      <Divider style={{ margin: "12px 0" }} />

      {/* 任务列表标题 - 显示任务总数 */}
      {selectedTasks.length > 0 && (
        <div className={styles.taskList}>
          <Title level={5} style={{ margin: "0 0 8px 0" }}>
            任务列表 ({selectedTasks.length})
          </Title>

          <div className={`${styles.tasksContainer} ${scrollBarClassName}`}>
            {taskListToRender.map((task, index) => renderTaskItem(task, index))}
          </div>
        </div>
      )}
    </div>
  );
};

export default React.memo(TaskMenu);
