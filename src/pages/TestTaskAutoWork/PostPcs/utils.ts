/**
 * PcsPost组件数据处理工具函数
 */

/**
 * 尝试多次解析JSON字符串，处理嵌套的JSON字符串情况
 * @param jsonStr 需要解析的JSON字符串
 * @param maxIterations 最大尝试次数
 * @returns 解析后的对象或原始字符串
 */
export const parseNestedJson = (
  jsonStr: string,
  maxIterations: number = 5,
): any => {
  // 如果不是字符串，直接返回
  if (typeof jsonStr !== "string") {
    return jsonStr;
  }

  // 如果字符串为空或仅包含空白字符，返回空对象
  if (!jsonStr.trim()) {
    return {};
  }

  let currentData = jsonStr;
  let iterations = 0;

  // 多次尝试解析JSON，处理嵌套JSON字符串
  while (typeof currentData === "string" && iterations < maxIterations) {
    try {
      const parsed = JSON.parse(currentData);
      console.log(`第${iterations + 1}次解析成功，结果类型:`, typeof parsed);

      // 如果解析后不是对象或数组，终止解析
      if (typeof parsed !== "object") {
        console.log("解析结果不是对象，终止解析");
        return parsed;
      }

      // 继续迭代
      currentData = parsed;
    } catch (e) {
      console.log(
        `第${iterations + 1}次解析失败，保持当前形式: ${e instanceof Error ? e.message : String(e)}`,
      );
      break;
    }

    iterations++;
  }

  return currentData;
};

/**
 * 增强版的案例数据解析函数
 * @param responseData API响应数据
 * @returns 解析后的数据对象，包含formatted字段
 */
export const enhancedParseCaseResponseData = (responseData: any): any => {
  try {
    if (!responseData) {
      console.error("API响应数据为空");
      return {
        formatted: "{}",
        raw: "{}",
      };
    }

    // 保存原始响应内容，用于调试
    const originalResponseStr =
      typeof responseData === "string"
        ? responseData
        : JSON.stringify(responseData, null, 2);

    console.log("原始响应数据类型:", typeof responseData);

    // 确保数据是对象
    let caseData = responseData;

    // 如果是字符串，尝试解析
    if (typeof responseData === "string") {
      try {
        caseData = JSON.parse(responseData);
      } catch (e) {
        console.error("无法解析响应字符串:", e);
        // 返回原始内容
        return {
          formatted: responseData,
          raw: responseData,
        };
      }
    }

    // 如果不是对象，转换为对象形式
    if (typeof caseData !== "object" || caseData === null) {
      console.warn("API响应不是对象类型");
      return {
        formatted: String(caseData),
        raw: originalResponseStr,
      };
    }

    // 查找postPcs或pcsPost字段
    let targetField = null;
    let targetFieldName = null;

    // 先检查直接字段
    if ("postPcs" in caseData) {
      targetField = caseData.postPcs;
      targetFieldName = "postPcs";
    } else if ("pcsPost" in caseData) {
      targetField = caseData.pcsPost;
      targetFieldName = "pcsPost";
    }
    // 如果是在data字段内
    else if (caseData.data) {
      if ("postPcs" in caseData.data) {
        targetField = caseData.data.postPcs;
        targetFieldName = "data.postPcs";
      } else if ("pcsPost" in caseData.data) {
        targetField = caseData.data.pcsPost;
        targetFieldName = "data.pcsPost";
      }
    }

    // 如果仍未找到，查找可能的JSON对象字段
    if (targetField === null) {
      console.warn("未找到标准字段名，尝试查找其他可能的JSON字段");

      // 查找顶层字段
      for (const key in caseData) {
        if (
          caseData[key] &&
          (typeof caseData[key] === "object" ||
            typeof caseData[key] === "string")
        ) {
          console.log(`尝试使用顶层字段 ${key}`);
          targetField = caseData[key];
          targetFieldName = key;
          break;
        }
      }

      // 如果顶层没找到，查找data内部
      if (targetField === null && caseData.data) {
        for (const key in caseData.data) {
          if (
            caseData.data[key] &&
            (typeof caseData.data[key] === "object" ||
              typeof caseData.data[key] === "string")
          ) {
            console.log(`尝试使用data内部字段 ${key}`);
            targetField = caseData.data[key];
            targetFieldName = `data.${key}`;
            break;
          }
        }
      }

      // 如果仍未找到，使用整个响应
      if (targetField === null) {
        console.log("未找到任何有效字段，使用整个响应");
        targetField = caseData;
        targetFieldName = "(整个响应)";
      }
    }

    console.log(`使用字段 ${targetFieldName}，字段类型:`, typeof targetField);

    // 处理字段内容
    let formattedContent = "{}";

    // 如果字段是字符串，可能需要解析
    if (typeof targetField === "string") {
      // 尝试多次解析嵌套的JSON字符串
      const parsedData = parseNestedJson(targetField);

      // 将解析结果格式化为JSON字符串
      if (typeof parsedData === "object" && parsedData !== null) {
        try {
          formattedContent = JSON.stringify(parsedData, null, 2);
        } catch (e) {
          console.error("格式化解析结果失败:", e);
          formattedContent = String(parsedData);
        }
      } else {
        formattedContent = String(parsedData);
      }
    }
    // 如果字段是对象，直接格式化
    else if (typeof targetField === "object" && targetField !== null) {
      try {
        formattedContent = JSON.stringify(targetField, null, 2);
      } catch (e) {
        console.error("格式化对象失败:", e);
        formattedContent = "{}";
      }
    } else {
      // 其他类型直接转字符串
      formattedContent = String(targetField);
    }

    console.log("最终格式化的内容长度:", formattedContent.length);

    // 返回完整结果对象
    return {
      ...caseData,
      formatted: formattedContent,
      raw: originalResponseStr,
      sourceField: targetFieldName,
    };
  } catch (error) {
    console.error("解析案例数据失败:", error);
    // 即使解析失败，也返回一个包含原始响应的对象
    return {
      formatted:
        typeof responseData === "string"
          ? responseData
          : JSON.stringify(responseData),
      raw:
        typeof responseData === "string"
          ? responseData
          : JSON.stringify(responseData),
      error: error instanceof Error ? error.message : String(error),
    };
  }
};

/**
 * 特别处理带有\r\n转义字符的JSON字符串
 * 专门针对格式如 {"postPcs":"{\\r\\n    \\\"txBody\\\": {...}" 的情况
 */
export function handleEscapedJson(jsonStr: string): string {
  if (typeof jsonStr !== "string") {
    return jsonStr;
  }

  console.log("处理带有转义字符的JSON, 长度:", jsonStr.length);

  // 1. 处理\\r\\n -> \n (这是最常见的问题)
  let processed = jsonStr.replace(/\\r\\n/g, "\n");

  // 2. 处理多重转义的引号 \\\" -> \"
  processed = processed.replace(/\\\\"/g, '\\"');

  // 3. 处理多重转义的反斜杠 \\\\ -> \\
  processed = processed.replace(/\\\\/g, "\\");

  // 4. 处理特殊字符，如花括号等
  processed = processed
    .replace(/\\{/g, "{")
    .replace(/\\}/g, "}")
    .replace(/\\:/g, ":")
    .replace(/\\,/g, ",");

  // 5. 移除可能存在的开头和结尾多余的引号
  if (processed.startsWith('"') && processed.endsWith('"')) {
    const content = processed.substring(1, processed.length - 1);
    if (content.includes("{") && content.includes("}")) {
      processed = content;
    }
  }

  // 6. 处理连续多次转义的情况 (已删除 - 可能导致语法错误)
  // 有时可能会遇到引号嵌套的情况，但直接替换可能导致问题

  // 7. 处理多重转义的花括号 (新增)
  processed = processed.replace(/\\\\{/g, "{").replace(/\\\\}/g, "}");

  // 8. 处理特殊转义格式 (新增 - 针对特定后端格式)
  if (processed.includes('"txBody"') || processed.includes("txBody")) {
    // 针对txBody格式的特殊处理，确保其内部JSON结构正确
    console.log("发现txBody字段，应用特殊规则");

    // 替换可能的转义序列
    processed = processed
      .replace(/\\\\\\\\/g, "\\") // 处理四重转义的反斜杠
      .replace(/\\\\\\"/g, '"') // 处理三重转义的引号
      .replace(/\\\\n/g, "\n"); // 处理双重转义的换行
  }

  console.log("处理后的字符串长度:", processed.length);
  return processed;
}

/**
 * 处理多重嵌套和转义的JSON字符串
 *
 * 专门处理类似 "{\"autoDataFlag\":\"0\",\"caseAuthor\":\"武灿灿\",...}" 这样的
 * 多重转义的JSON字符串，确保它们能被正确解析为JSON对象
 */
export function parseNestedJsonString(jsonStr: string): any {
  console.log("开始解析嵌套JSON字符串:", jsonStr.substring(0, 100) + "...");

  try {
    // 第一次解析
    let result = JSON.parse(jsonStr);
    console.log("第一次解析结果类型:", typeof result);

    // 如果结果是字符串，可能还需要再次解析
    if (typeof result === "string") {
      try {
        console.log("尝试第二次解析...");
        result = JSON.parse(result);
        console.log("第二次解析结果类型:", typeof result);
      } catch (e) {
        console.warn("第二次解析失败，保持第一次解析结果:", e);
      }
    }

    // 如果是对象且有特定字段，检查这些字段是否也是字符串形式的JSON
    if (result && typeof result === "object") {
      // 检查特定字段如postPcs, pcsPost等
      const jsonFields = ["postPcs", "pcsPost"];

      for (const field of jsonFields) {
        if (result[field] && typeof result[field] === "string") {
          try {
            // 处理转义字符
            const unescaped = result[field]
              .replace(/\\r\\n/g, "\n")
              .replace(/\\\\/g, "\\")
              .replace(/\\"/g, '"');

            // 尝试解析
            result[field] = JSON.parse(unescaped);
            console.log(`成功解析${field}字段为对象`);
          } catch (e) {
            console.warn(`解析${field}字段失败:`, e);
          }
        }
      }
    }

    return result;
  } catch (e) {
    console.error("解析嵌套JSON字符串失败:", e);
    return null;
  }
}

/**
 * 专门处理selectTbCaseBookByUniqueKey接口返回的数据格式
 * 该函数处理嵌套JSON和特殊的字符转义格式
 * @param response API响应数据
 * @returns 处理后的数据，包含postPcs字段的JSON格式内容
 */
export const processSelectTbCaseBookResponse = (response: any): any => {
  try {
    // 检查响应状态
    if (!response || response.code !== 200 || !response.data) {
      console.error("API响应无效或无数据");
      return { error: "无效响应" };
    }

    // 检查data是否为字符串，若是则解析
    let caseData;
    if (typeof response.data === "string") {
      try {
        caseData = JSON.parse(response.data);
        console.log("解析响应data字段成功:", caseData);
      } catch (e) {
        console.error("解析data字段失败:", e);
        return { error: "数据解析失败" };
      }
    } else {
      caseData = response.data;
    }

    // 检查是否有postPcs字段
    if (caseData.postPcs) {
      // 处理可能包含转义字符的JSON字符串
      let postPcsContent = caseData.postPcs;

      try {
        // 针对特殊转义处理，如\\r\\n -> \r\n
        postPcsContent = postPcsContent
          .replace(/\\r\\n/g, "\n")
          .replace(/\\\\/g, "\\")
          .replace(/\\"/g, '"');

        // 尝试解析处理后的字符串
        const postPcsJson = JSON.parse(postPcsContent);

        // 将结果重新格式化为美观的JSON字符串
        const formattedPostPcs = JSON.stringify(postPcsJson, null, 2);

        // 返回处理后的完整数据
        return {
          ...caseData,
          postPcsFormatted: formattedPostPcs,
          postPcsObject: postPcsJson,
        };
      } catch (e) {
        console.error("处理postPcs字段失败:", e);
        // 返回原始数据
        return {
          ...caseData,
          postPcsFormatted: postPcsContent,
          error: "无法解析postPcs字段",
        };
      }
    } else {
      console.warn("未找到postPcs字段");
      return {
        ...caseData,
        postPcsFormatted: "{}",
        error: "未找到postPcs字段",
      };
    }
  } catch (e) {
    console.error("处理API响应出错:", e);
    return { error: "处理失败" };
  }
};
