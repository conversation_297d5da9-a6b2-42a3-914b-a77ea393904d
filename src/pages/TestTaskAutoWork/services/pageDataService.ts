/**
 * 页面数据传输服务
 * 用于页面间传递大量数据，避免URL参数限制
 */

// 存储页面数据的缓存
const pageDataCache: Record<string, any> = {};

// 存储页面数据的监听器
const pageDataListeners: Record<string, ((data: any) => void)[]> = {};

/**
 * 页面数据服务
 */
export const pageDataService = {
  /**
   * 发送数据到指定页面
   * @param targetPage 目标页面路径
   * @param data 要发送的数据
   */
  sendData: (targetPage: string, data: any): string => {
    // 生成唯一ID
    const dataId = `${targetPage}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // 存储数据
    pageDataCache[dataId] = {
      data,
      timestamp: Date.now(),
      targetPage
    };
    
    // 通知监听器
    if (pageDataListeners[targetPage]) {
      pageDataListeners[targetPage].forEach(listener => {
        listener({ dataId, data });
      });
    }
    
    // 设置过期时间 (10分钟后自动清除)
    setTimeout(() => {
      delete pageDataCache[dataId];
    }, 10 * 60 * 1000);
    
    return dataId;
  },
  
  /**
   * 获取指定ID的数据
   * @param dataId 数据ID
   */
  getData: (dataId: string): any | null => {
    const item = pageDataCache[dataId];
    if (item) {
      // 获取后不删除，允许重复访问
      return item.data;
    }
    return null;
  },
  
  /**
   * 监听指定页面的数据
   * @param targetPage 目标页面路径
   * @param listener 监听器函数
   */
  listenData: (targetPage: string, listener: (data: any) => void): () => void => {
    if (!pageDataListeners[targetPage]) {
      pageDataListeners[targetPage] = [];
    }
    
    pageDataListeners[targetPage].push(listener);
    
    // 返回取消监听的函数
    return () => {
      if (pageDataListeners[targetPage]) {
        const index = pageDataListeners[targetPage].indexOf(listener);
        if (index !== -1) {
          pageDataListeners[targetPage].splice(index, 1);
        }
      }
    };
  },
  
  /**
   * 清除指定ID的数据
   * @param dataId 数据ID
   */
  clearData: (dataId: string): void => {
    delete pageDataCache[dataId];
  }
};

// 导出默认模块
export default pageDataService; 