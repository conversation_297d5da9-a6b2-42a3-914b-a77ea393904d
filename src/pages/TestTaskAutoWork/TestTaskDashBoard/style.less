.dashboard-container {
  background-color: #f0f2f5;
  padding: 24px;

  .dashboard-header {
    margin-bottom: 24px;

    .header-title {
      font-size: 20px;
      font-weight: 600;
      color: #1a1a1a;
    }

    .header-subtitle {
      color: #8c8c8c;
      font-size: 14px;
      margin-top: 8px;
    }
  }

  .filter-bar {
    margin-bottom: 24px;
    padding: 16px 24px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

    .filter-item {
      margin-right: 16px;
      margin-bottom: 8px;
    }

    .filter-buttons {
      display: flex;
      gap: 8px;
    }
  }

  .stat-card {
    height: 100%;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    transition: all 0.3s;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
      transform: translateY(-2px);
    }

    .ant-card-head {
      border-bottom: 1px solid #f0f0f0;
    }

    .stat-value {
      font-size: 32px;
      font-weight: 600;
      line-height: 38px;
      margin-bottom: 8px;
      color: #262626;
    }

    .stat-subtitle {
      font-size: 14px;
      color: #8c8c8c;
    }

    .stat-trend {
      margin-top: 8px;
      font-size: 12px;

      &.up {
        color: #52c41a;
      }

      &.down {
        color: #ff4d4f;
      }
    }
  }

  .card-success {
    .stat-value {
      color: #52c41a;
    }
    .ant-card-head-title {
      color: #52c41a;
    }
  }

  .card-warning {
    .stat-value {
      color: #faad14;
    }
    .ant-card-head-title {
      color: #faad14;
    }
  }

  .card-error {
    .stat-value {
      color: #ff4d4f;
    }
    .ant-card-head-title {
      color: #ff4d4f;
    }
  }

  .chart-card {
    margin-bottom: 24px;
    background-color: #fff;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

    .chart-header {
      padding: 16px 24px;
      border-bottom: 1px solid #f0f0f0;

      .chart-title {
        font-size: 16px;
        font-weight: 500;
        color: #262626;
      }
    }

    .chart-content {
      padding: 24px;
      height: 380px;
    }
  }

  .table-card {
    margin-bottom: 24px;

    .ant-table-thead > tr > th {
      background-color: #fafafa;
      font-weight: 500;
    }

    .status-tag {
      padding: 4px 8px;
      border-radius: 2px;
      font-size: 12px;

      &.passed {
        background-color: rgba(82, 196, 26, 0.15);
        color: #52c41a;
        border: 1px solid rgba(82, 196, 26, 0.5);
      }

      &.failed {
        background-color: rgba(255, 77, 79, 0.15);
        color: #ff4d4f;
        border: 1px solid rgba(255, 77, 79, 0.5);
      }

      &.pending {
        background-color: rgba(250, 173, 20, 0.15);
        color: #faad14;
        border: 1px solid rgba(250, 173, 20, 0.5);
      }
    }
  }

  .progress-column {
    width: 200px;
  }

  .rate-column {
    width: 80px;
  }

  // 进度条颜色定制
  .progress-success .ant-progress-bg {
    background-color: #52c41a;
  }

  .progress-warning .ant-progress-bg {
    background-color: #faad14;
  }

  .progress-error .ant-progress-bg {
    background-color: #ff4d4f;
  }

  // 响应式调整
  @media (max-width: 768px) {
    .stat-card .stat-value {
      font-size: 24px;
    }

    .chart-card .chart-content {
      height: 300px;
    }
  }
}
