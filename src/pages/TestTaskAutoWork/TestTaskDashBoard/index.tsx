import React, { useState, useEffect, useCallback } from "react";
import { PageContainer } from "@ant-design/pro-layout";
import {
  Card,
  Row,
  Col,
  Statistic,
  DatePicker,
  Select,
  Button,
  Spin,
  Table,
  Progress,
  Tag,
  Divider,
  Space,
  Typography,
  message,
  Dropdown,
  Menu,
} from "antd";
import {
  ReloadOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  WarningOutlined,
  CodepenOutlined,
  FileTextOutlined,
  DownOutlined,
  FilterOutlined,
} from "@ant-design/icons";
import { Line, Column, Pie } from "@ant-design/charts";
import type { Moment } from "moment";
import moment from "moment";
import { getDashboardData, getTestCycles, getTesters } from "./service";
import type {
  DashboardData,
  TestCycleStat,
  TesterStat,
  TestTypeDistribution,
  QueryParams,
} from "./data.d";
import styles from "./style.less";

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;

const TestTaskDashBoard: React.FC = () => {
  const [loading, setLoading] = useState<boolean>(true);
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(
    null,
  );
  const [testCycles, setTestCycles] = useState<string[]>([]);
  const [testers, setTesters] = useState<string[]>([]);
  const [dateRange, setDateRange] = useState<[Moment, Moment] | null>(null);
  const [selectedCycle, setSelectedCycle] = useState<string | undefined>(
    undefined,
  );
  const [selectedTester, setSelectedTester] = useState<string | undefined>(
    undefined,
  );
  const [lastUpdateTime, setLastUpdateTime] = useState<string>("");
  const [selectedTrendCycle, setSelectedTrendCycle] = useState<string | null>(
    null,
  );

  // Fetch dashboard data
  const fetchDashboardData = useCallback(
    async (showSuccessMessage: boolean = false) => {
      setLoading(true);
      try {
        const params: QueryParams = {};

        if (dateRange) {
          params.startDate = dateRange[0].format("YYYY-MM-DD");
          params.endDate = dateRange[1].format("YYYY-MM-DD");
        }

        if (selectedCycle) {
          params.testCycle = selectedCycle;
        }

        if (selectedTester) {
          params.tester = selectedTester;
        }

        const response = await getDashboardData(params);
        if (response.success) {
          setDashboardData(response.data);
          // 更新最后刷新时间
          setLastUpdateTime(moment().format("YYYY-MM-DD HH:mm:ss"));
          // 重置趋势周期选择
          setSelectedTrendCycle(null);

          if (showSuccessMessage) {
            message.success("数据已更新");
          }
        } else {
          message.error("获取仪表盘数据失败");
        }
      } catch (error) {
        console.error("Failed to fetch dashboard data:", error);
        message.error("获取仪表盘数据失败");
      } finally {
        setLoading(false);
      }
    },
    [dateRange, selectedCycle, selectedTester],
  );

  // Fetch options data
  const fetchOptionsData = useCallback(async () => {
    try {
      const testCyclesRes = await getTestCycles();
      if (testCyclesRes.success) {
        setTestCycles(testCyclesRes.data);
      }

      const testersRes = await getTesters();
      if (testersRes.success) {
        setTesters(testersRes.data);
      }
    } catch (error) {
      console.error("Failed to fetch options data:", error);
    }
  }, []);

  // Initial load
  useEffect(() => {
    fetchOptionsData();
    fetchDashboardData();

    // 设置定时刷新，每60秒刷新一次数据，确保与任务列表同步
    const refreshInterval = setInterval(() => {
      fetchDashboardData(false);
    }, 60000);

    // 组件卸载时清除定时器
    return () => {
      clearInterval(refreshInterval);
    };
  }, []);

  // Reset filters
  const handleReset = () => {
    setDateRange(null);
    setSelectedCycle(undefined);
    setSelectedTester(undefined);
    fetchDashboardData(true);
  };

  // Handle filter changes
  const handleDateChange = (dates: [Moment, Moment] | null) => {
    setDateRange(dates);
  };

  const handleCycleChange = (value: string | undefined) => {
    setSelectedCycle(value);
  };

  const handleTesterChange = (value: string | undefined) => {
    setSelectedTester(value);
  };

  const handleSearch = () => {
    fetchDashboardData(true);
  };

  // 强制刷新数据 - 与任务列表同步
  const handleForceRefresh = () => {
    fetchOptionsData();
    fetchDashboardData(true);
  };

  // 处理趋势图周期选择
  const handleTrendCycleChange = (cycle: string | null) => {
    setSelectedTrendCycle(cycle);
  };

  // Chart configurations
  const testCycleConfig = {
    data: selectedTrendCycle
      ? ([
          dashboardData?.testCycleStats.find(
            (stat) => stat.testCycle === selectedTrendCycle,
          ),
        ].filter(Boolean) as any[])
      : dashboardData?.testCycleStats || ([] as any[]),
    title: {
      visible: true,
      text: selectedTrendCycle
        ? `${selectedTrendCycle}周期通过率`
        : "本周期测试通过率",
    },
    xField: "testCycle",
    yField: "passRate",
    point: {
      visible: true,
      size: 5,
      shape: "diamond",
    },
    label: {
      visible: true,
      type: "point",
    },
    color: "#1890ff",
    xAxis: {
      title: {
        visible: false,
      },
    },
    yAxis: {
      title: {
        visible: false,
      },
      min: 0,
      max: 100,
    },
  } as any;

  const testTypeDistributionConfig = {
    data: dashboardData?.testTypeDistribution || ([] as any[]),
    angleField: "count",
    colorField: "type",
    radius: 0.8,
    label: {
      visible: true,
      type: "outer",
      formatter: (val: any) => {
        return `${val.type}: ${val.count}`;
      },
    },
    legend: {
      visible: true,
      position: "right-top",
    },
  } as any;

  // 趋势图周期选择菜单
  const trendCycleMenu = (
    <Menu onClick={({ key }) => handleTrendCycleChange(key as string)}>
      <Menu.Item key="null">显示所有周期</Menu.Item>
      <Menu.Divider />
      {dashboardData?.testCycleStats.map((stat) => (
        <Menu.Item key={stat.testCycle}>{stat.testCycle}</Menu.Item>
      ))}
    </Menu>
  );

  // Table columns for test cycle stats
  const testCycleColumns = [
    {
      title: "测试周期",
      dataIndex: "testCycle",
      key: "testCycle",
    },
    {
      title: "总测试数",
      dataIndex: "totalTests",
      key: "totalTests",
      sorter: (a: TestCycleStat, b: TestCycleStat) =>
        a.totalTests - b.totalTests,
    },
    {
      title: "通过测试数",
      dataIndex: "passedTests",
      key: "passedTests",
      sorter: (a: TestCycleStat, b: TestCycleStat) =>
        a.passedTests - b.passedTests,
    },
    {
      title: "失败测试数",
      dataIndex: "failedTests",
      key: "failedTests",
      sorter: (a: TestCycleStat, b: TestCycleStat) =>
        a.failedTests - b.failedTests,
    },
    {
      title: "待处理测试数",
      dataIndex: "pendingTests",
      key: "pendingTests",
      sorter: (a: TestCycleStat, b: TestCycleStat) =>
        a.pendingTests - b.pendingTests,
    },
    {
      title: "通过率",
      dataIndex: "passRate",
      key: "passRate",
      sorter: (a: TestCycleStat, b: TestCycleStat) => a.passRate - b.passRate,
      render: (text: number) => (
        <div className={styles.progressColumn}>
          <Progress
            percent={text}
            size="small"
            status={
              text >= 90 ? "success" : text >= 80 ? "normal" : "exception"
            }
            className={
              text >= 90
                ? styles.progressSuccess
                : text >= 80
                  ? styles.progressWarning
                  : styles.progressError
            }
          />
        </div>
      ),
    },
  ];

  // Table columns for tester stats
  const testerColumns = [
    {
      title: "测试人员",
      dataIndex: "caseAuthor",
      key: "caseAuthor",
      align: "center" as const,
    },
    {
      title: "总测试数",
      dataIndex: "totalTests",
      key: "totalTests",
      sorter: (a: TesterStat, b: TesterStat) => a.totalTests - b.totalTests,
    },
    {
      title: "通过测试数",
      dataIndex: "passedTests",
      key: "passedTests",
      sorter: (a: TesterStat, b: TesterStat) => a.passedTests - b.passedTests,
    },
    {
      title: "失败测试数",
      dataIndex: "failedTests",
      key: "failedTests",
      sorter: (a: TesterStat, b: TesterStat) => a.failedTests - b.failedTests,
    },
    {
      title: "待处理测试数",
      dataIndex: "pendingTests",
      key: "pendingTests",
      sorter: (a: TesterStat, b: TesterStat) => a.pendingTests - b.pendingTests,
    },
    {
      title: "通过率",
      dataIndex: "passRate",
      key: "passRate",
      sorter: (a: TesterStat, b: TesterStat) => a.passRate - b.passRate,
      render: (text: number) => (
        <div className={styles.progressColumn}>
          <Progress
            percent={text}
            size="small"
            status={
              text >= 90 ? "success" : text >= 80 ? "normal" : "exception"
            }
            className={
              text >= 90
                ? styles.progressSuccess
                : text >= 80
                  ? styles.progressWarning
                  : styles.progressError
            }
          />
        </div>
      ),
    },
  ];

  return (
    <PageContainer>
      <div className={styles.dashboardContainer}>
        <div className={styles.dashboardHeader}>
          <div className={styles.headerTitle}>测试任务仪表盘</div>
          <div className={styles.headerSubtitle}>
            实时监控测试执行情况和质量指标
            {lastUpdateTime && (
              <span
                style={{
                  marginLeft: "10px",
                  fontSize: "12px",
                  color: "#8c8c8c",
                }}
              >
                (最后更新: {lastUpdateTime})
              </span>
            )}
          </div>
        </div>

        {/* Filters */}
        <Card className={styles.filterBar}>
          <Row gutter={[16, 16]} align="middle">
            <Col xs={24} sm={12} md={7} lg={7} xl={7}>
              <RangePicker
                value={dateRange}
                onChange={handleDateChange as any}
                style={{ width: "100%" }}
                placeholder={["开始日期", "结束日期"]}
              />
            </Col>
            <Col xs={24} sm={12} md={5} lg={5} xl={5}>
              <Select
                placeholder="选择测试周期"
                style={{ width: "100%" }}
                value={selectedCycle}
                onChange={handleCycleChange}
                allowClear
              >
                {testCycles.map((cycle) => (
                  <Option key={cycle} value={cycle}>
                    {cycle}
                  </Option>
                ))}
              </Select>
            </Col>
            <Col xs={24} sm={12} md={5} lg={5} xl={5}>
              <Select
                placeholder="选择测试人员"
                style={{ width: "100%" }}
                value={selectedTester}
                onChange={handleTesterChange}
                allowClear
              >
                {testers.map((tester) => (
                  <Option key={tester} value={tester}>
                    {tester}
                  </Option>
                ))}
              </Select>
            </Col>
            <Col xs={24} sm={12} md={7} lg={7} xl={7}>
              <div className={styles.filterButtons}>
                <Button type="primary" onClick={handleSearch}>
                  查询
                </Button>
                <Button onClick={handleReset}>重置</Button>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={handleForceRefresh}
                  title="从任务列表刷新最新数据"
                >
                  同步刷新
                </Button>
              </div>
            </Col>
          </Row>
        </Card>

        {/* Stats cards */}
        <Spin spinning={loading}>
          <Row gutter={[16, 16]} style={{ marginBottom: "24px" }}>
            <Col xs={24} sm={12} md={6} lg={6} xl={6}>
              <Card className={styles.statCard}>
                <Statistic
                  title="总测试周期数"
                  value={dashboardData?.summary.totalTestCycles || 0}
                  prefix={<CodepenOutlined />}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} md={6} lg={6} xl={6}>
              <Card className={`${styles.statCard} ${styles.cardSuccess}`}>
                <Statistic
                  title="总体通过率"
                  value={dashboardData?.summary.overallPassRate || 0}
                  precision={2}
                  suffix="%"
                  prefix={<CheckCircleOutlined />}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} md={6} lg={6} xl={6}>
              <Card className={styles.statCard}>
                <Statistic
                  title="总测试数"
                  value={dashboardData?.summary.totalTests || 0}
                  prefix={<FileTextOutlined />}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} md={6} lg={6} xl={6}>
              <Card className={`${styles.statCard} ${styles.cardError}`}>
                <Statistic
                  title="总失败测试数"
                  value={dashboardData?.summary.failedTestsCount || 0}
                  prefix={<CloseCircleOutlined />}
                />
              </Card>
            </Col>
          </Row>

          {/* Charts */}
          <Row gutter={[16, 16]}>
            <Col xs={24} md={24} lg={12} xl={12}>
              <Card
                title={
                  <div
                    style={{
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                    }}
                  >
                    <span>本周期测试通过率</span>
                    <Dropdown
                      overlay={trendCycleMenu}
                      disabled={!dashboardData?.testCycleStats?.length}
                    >
                      <Button size="small">
                        {selectedTrendCycle
                          ? selectedTrendCycle
                          : "显示所有周期"}{" "}
                        <DownOutlined />
                      </Button>
                    </Dropdown>
                  </div>
                }
                className={styles.chartCard}
                loading={loading}
              >
                <div className={styles.chartContent}>
                  {dashboardData && dashboardData.testCycleStats.length > 0 ? (
                    selectedTrendCycle ? (
                      // 显示单个周期的通过率
                      <div
                        style={{
                          textAlign: "center",
                          paddingTop: "50px",
                          height: "250px",
                        }}
                      >
                        {(() => {
                          const cycleData = dashboardData.testCycleStats.find(
                            (stat) => stat.testCycle === selectedTrendCycle,
                          );
                          if (cycleData) {
                            return (
                              <div>
                                <Statistic
                                  title={`${selectedTrendCycle} 测试周期通过率`}
                                  value={cycleData.passRate}
                                  precision={2}
                                  suffix="%"
                                  valueStyle={{
                                    color: "#1890ff",
                                    fontSize: "36px",
                                  }}
                                />
                                <div style={{ marginTop: "20px" }}>
                                  <Progress
                                    percent={cycleData.passRate}
                                    status={
                                      cycleData.passRate >= 90
                                        ? "success"
                                        : cycleData.passRate >= 80
                                          ? "normal"
                                          : "exception"
                                    }
                                    strokeWidth={15}
                                  />
                                </div>
                                <Row style={{ marginTop: "30px" }}>
                                  <Col span={8}>
                                    <Statistic
                                      title="总测试数"
                                      value={cycleData.totalTests}
                                      valueStyle={{ fontSize: "24px" }}
                                    />
                                  </Col>
                                  <Col span={8}>
                                    <Statistic
                                      title="通过测试数"
                                      value={cycleData.passedTests}
                                      valueStyle={{
                                        color: "#52c41a",
                                        fontSize: "24px",
                                      }}
                                    />
                                  </Col>
                                  <Col span={8}>
                                    <Statistic
                                      title="失败/待处理"
                                      value={
                                        cycleData.failedTests +
                                        cycleData.pendingTests
                                      }
                                      valueStyle={{
                                        color: "#ff4d4f",
                                        fontSize: "24px",
                                      }}
                                    />
                                  </Col>
                                </Row>
                              </div>
                            );
                          }
                          return <Text type="secondary">没有该周期的数据</Text>;
                        })()}
                      </div>
                    ) : (
                      // 显示所有周期的趋势图
                      <Line {...testCycleConfig} />
                    )
                  ) : (
                    <div style={{ textAlign: "center", paddingTop: "100px" }}>
                      <Text type="secondary">暂无数据</Text>
                    </div>
                  )}
                </div>
              </Card>
            </Col>
            <Col xs={24} md={24} lg={12} xl={12}>
              <Card
                title="测试类型分布"
                className={styles.chartCard}
                loading={loading}
              >
                <div className={styles.chartContent}>
                  {dashboardData &&
                  dashboardData.testTypeDistribution.length > 0 ? (
                    <Pie {...testTypeDistributionConfig} />
                  ) : (
                    <div style={{ textAlign: "center", paddingTop: "100px" }}>
                      <Text type="secondary">暂无数据</Text>
                    </div>
                  )}
                </div>
              </Card>
            </Col>
          </Row>

          {/* Tables */}
          <Row gutter={[16, 16]} style={{ marginTop: "24px" }}>
            <Col span={24}>
              <Card
                title="测试周期统计数据"
                className={styles.tableCard}
                loading={loading}
              >
                <Table
                  dataSource={dashboardData?.testCycleStats || []}
                  columns={testCycleColumns}
                  rowKey="testCycle"
                  pagination={false}
                  size="middle"
                />
              </Card>
            </Col>
          </Row>

          <Row gutter={[16, 16]} style={{ marginTop: "24px" }}>
            <Col span={24}>
              <Card
                title="测试人员统计数据"
                className={styles.tableCard}
                loading={loading}
              >
                <Table
                  dataSource={dashboardData?.testerStats || []}
                  columns={testerColumns}
                  rowKey="caseAuthor"
                  pagination={false}
                  size="middle"
                />
              </Card>
            </Col>
          </Row>
        </Spin>
      </div>
    </PageContainer>
  );
};

export default TestTaskDashBoard;
