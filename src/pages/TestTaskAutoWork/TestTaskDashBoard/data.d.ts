export interface TestCycleStat {
  testCycle: string; // 测试周期
  totalTests: number; // 总测试数
  passedTests: number; // 通过测试数
  failedTests: number; // 失败测试数
  pendingTests: number; // 待处理测试数
  passRate: number; // 通过率 (百分比)
}

export interface TesterStat {
  caseAuthor: string; // 测试者（原tester）
  totalTests: number; // 总测试数
  passedTests: number; // 通过测试数
  failedTests: number; // 失败测试数
  pendingTests: number; // 待处理测试数
  passRate: number; // 通过率 (百分比)
}

export interface TestTypeDistribution {
  type: string; // 测试类型
  count: number; // 数量
}

export interface DashboardData {
  testCycleStats: TestCycleStat[]; // 测试周期统计数据
  testerStats: TesterStat[]; // 测试者统计数据
  testTypeDistribution: TestTypeDistribution[]; // 测试类型分布
  summary: {
    totalTestCycles: number; // 总测试周期数
    totalTests: number; // 总测试数
    overallPassRate: number; // 总体通过率
    failedTestsCount: number; // 失败测试数
  };
}

export interface ResponseData<T> {
  code: number;
  data: T;
  message?: string;
  success: boolean;
}

export interface QueryParams {
  startDate?: string;
  endDate?: string;
  testCycle?: string;
  tester?: string;
  [key: string]: any;
}
