import React, {
  useState,
  useEffect,
  useRef,
  useMemo,
  useCallback,
} from "react";
import type { CSSProperties } from "react";
import {
  Table,
  Card,
  Button,
  Input,
  Space,
  Tag,
  Tooltip,
  Typography,
  Spin,
  message,
  Modal,
  Slider,
  Dropdown,
  Menu,
  Col,
  Row,
  Popover,
} from "antd";
import {
  ReloadOutlined,
  SearchOutlined,
  InfoCircleOutlined,
  FilterOutlined,
  StarFilled,
  EyeOutlined,
  CopyOutlined,
  SettingOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ClearOutlined,
  CodepenOutlined,
  FileTextOutlined,
  EditOutlined,
  CaretDownOutlined,
  DownOutlined,
  FontSizeOutlined,
  SaveOutlined,
} from "@ant-design/icons";
import { history, Link } from "umi";
import type { TableListParams } from "./service";
import { getTableList, saveCase, SaveCaseParams } from "./service";
import type { AlignType } from "rc-table/lib/interface";
import type { ColumnsType } from "antd/es/table";
import type {
  TablePaginationConfig,
  FilterValue,
  SorterResult,
  TableCurrentDataSource,
} from "antd/es/table/interface";
import CodeMirror from "@uiw/react-codemirror";
import { json } from "@codemirror/lang-json";
import "./style.less"; // Import the custom styles
import { PageContainer } from "@ant-design/pro-layout";
import { request } from "umi";
import queryString from "query-string";
import copy from "copy-to-clipboard";
import pageDataService from "../services/pageDataService"; // 引入pageDataService

const { Title, Text } = Typography;

// 自定义颜色常量 - 用于一致的UI配色
const THEME_COLORS = {
  success: {
    light: "rgba(82, 196, 26, 0.15)",
    medium: "rgba(82, 196, 26, 0.25)",
    border: "rgba(82, 196, 26, 0.6)",
    text: "#389e0d", // 更深的绿色
  },
  warning: {
    light: "rgba(250, 173, 20, 0.15)",
    medium: "rgba(250, 173, 20, 0.25)",
    border: "rgba(250, 173, 20, 0.6)",
    text: "#d46b08", // 更深的橙色
  },
  background: "#f8f9fa",
  border: "#e8e8e8",
  highlight: "#1890ff",
  tableHeader: "#345678", // 表头颜色
  tableText: "#456789", // 表格文本颜色
};

interface LocalTableListItem {
  caseGroup?: string; // 测试周期
  caseAuthor?: string; // 测试者
  caseName?: string; // 测试案例名称
  caseNo: string; // 测试案例编号（必须字段）
  pcsPost?: string | null; // 接口请求信息
  txNo?: string; // 接口编号
  uniqueKey?: string; // 唯一标识（用于列表去重和作为rowKey）
  autoDataFlag: number | string; // 完成状态：1-已完成，0-未完成（必须字段）
  assertFlag?: number | string; // 断言标志
  selected?: boolean; // 是否被选中
  caseDesc?: string; // 案例描述
  caseType?: string; // 案例类型
}

const TaskList: React.FC = () => {
  // 状态管理
  const [loading, setLoading] = useState<boolean>(false);
  const [data, setData] = useState<LocalTableListItem[]>([]);
  const [searchText, setSearchText] = useState<string>("");
  const [filteredData, setFilteredData] = useState<LocalTableListItem[]>([]);
  const [hoveredRow, setHoveredRow] = useState<string | null>(null);
  const [statusFilter, setStatusFilter] = useState<string | null>(null);
  const [activeFilters, setActiveFilters] = useState<Record<string, string[]>>(
    {},
  );
  // 添加选中行管理状态
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  // 添加总记录数状态
  const [totalRecords, setTotalRecords] = useState<number>(0);
  // 添加总页数状态
  const [totalPages, setTotalPages] = useState<number>(1);
  // 添加当前页码状态
  const [currentPage, setCurrentPage] = useState<number>(1);
  // 添加每页条数状态
  const [pageSize, setPageSize] = useState<number>(10);

  // 表格设置相关状态
  const [settingsVisible, setSettingsVisible] = useState<boolean>(false);
  const [headerFontSize, setHeaderFontSize] = useState<number>(14);
  const [tableFontSize, setTableFontSize] = useState<number>(13);
  const [statusFontSize, setStatusFontSize] = useState<number>(14);
  const [actionFontSize, setActionFontSize] = useState<number>(13);

  // JSON预览状态
  const [jsonPreviewVisible, setJsonPreviewVisible] = useState<boolean>(false);
  const [jsonPreviewContent, setJsonPreviewContent] = useState<string>("{}");
  // 使用useRef代替useState存储当前记录，避免React队列问题
  const currentRecordRef = useRef<LocalTableListItem | null>(null);
  const [jsonCopied, setJsonCopied] = useState<boolean>(false);

  // 表格引用
  const tableRef = useRef(null);

  // 使用useRef保存最新的数据，避免异步操作中的闭包问题
  const latestDataRef = useRef<LocalTableListItem[]>([]);

  // 计算是否有选中行 - 使用useMemo而不是额外的state
  const hasSelected = useMemo(
    () => selectedRowKeys.length > 0,
    [selectedRowKeys],
  );

  // 计算当前页面上的所有数据的key
  const currentPageKeys = useMemo(() => {
    return filteredData.map(
      (item) =>
        item.uniqueKey || `${item.caseNo}_${item.caseGroup}_${item.caseAuthor}`,
    );
  }, [filteredData]);

  // 检查当前是否所有项都被选中
  const isAllSelected = useMemo(() => {
    return (
      currentPageKeys.length > 0 &&
      currentPageKeys.every((key) => selectedRowKeys.includes(key))
    );
  }, [currentPageKeys, selectedRowKeys]);

  // 选择当前页面所有数据
  const selectAllCurrent = useCallback(() => {
    if (isAllSelected) {
      // 如果所有项已经被选中，则取消所有选择
      setSelectedRowKeys([]);
    } else {
      // 否则全选当前页数据
      setSelectedRowKeys(currentPageKeys);
    }
  }, [currentPageKeys, isAllSelected]);

  // 更新ref
  useEffect(() => {
    latestDataRef.current = data;
  }, [data]);

  // 添加保存状态
  const [isSaving, setIsSaving] = useState<boolean>(false);

  // 统一定义常用样式，避免重复创建对象
  const STYLES: Record<string, CSSProperties> = {
    tagItem: {
      fontSize: "12px",
      padding: "4px 8px",
      borderRadius: "3px",
      display: "flex",
      alignItems: "center",
      gap: "4px",
    },
    filterArea: {
      display: "flex",
      flexWrap: "wrap" as const,
      gap: "8px",
      marginTop: "8px",
      alignItems: "center",
    },
    resetButton: {
      backgroundColor: "#f0f7ff",
      borderColor: "#91caff",
      color: "#1677ff",
    },
    cardTitle: {
      padding: "8px 0",
    },
    tableContainer: {
      margin: "24px",
    },
  };

  /**
   * 调试函数 - 输出组件当前状态信息
   * 仅在开发环境中使用，方便调试问题
   */
  const debugData = useCallback(() => {
    if (process.env.NODE_ENV === "development") {
      console.log(
        `[TaskList] 数据统计 - 总条数:${data.length}, 筛选后:${filteredData.length}, 筛选条件:${Object.keys(activeFilters).length}项`,
      );
    }
  }, [data, filteredData, activeFilters]);

  /**
   * 确保数据正确显示
   * 处理可能的状态不同步问题，当过滤后数据为空但原始数据不为空时恢复显示
   */
  const ensureDataDisplay = useCallback(() => {
    if (filteredData.length === 0 && data.length > 0) {
      // 防止筛选条件导致显示为空的情况
      setFilteredData(data);
    }
  }, [filteredData, data]);

  // 行选择变化处理函数
  const onSelectChange = useCallback((newSelectedRowKeys: React.Key[]) => {
    setSelectedRowKeys(newSelectedRowKeys);
    if (process.env.NODE_ENV === "development") {
      console.log("已选择行:", newSelectedRowKeys);
    }
  }, []);

  // 清除所有选择
  const clearAllSelection = useCallback(() => {
    setSelectedRowKeys([]);
  }, []);

  // 行选择配置 - 使用useMemo确保稳定引用
  const rowSelection = useMemo(
    () => ({
      selectedRowKeys,
      onChange: onSelectChange,
      columnTitle: (
        <span
          className="table-header"
          style={{ fontSize: `${headerFontSize}px` }}
        >
          跳转编辑
        </span>
      ),
      selections: [
        Table.SELECTION_ALL,
        Table.SELECTION_INVERT,
        Table.SELECTION_NONE,
      ],
    }),
    [selectedRowKeys, onSelectChange, headerFontSize],
  );

  /**
   * 统一判断状态是否为已完成
   */
  const isCompleted = useCallback(
    (status: number | string | undefined | null): boolean => {
      // 明确排除 null/undefined
      if (status == null) return false;
      return Number(status) === 1;
    },
    [],
  );

  /**
   * 处理API响应数据
   */
  const processApiResponse = useCallback((response: any) => {
    let rawData: any[] = [];
    try {
      // 检查 response.data 是否为字符串，如果是则尝试解析为 JSON
      if (typeof response.data === "string") {
        const parsedData = JSON.parse(response.data) as {
          records?: any[];
          total?: number;
          pages?: number;
        };
        // 从解析后的对象中获取 records 数组
        rawData = parsedData.records || [];
        // 从解析后的对象中获取总记录数
        if (parsedData.total !== undefined) {
          setTotalRecords(parsedData.total);
        }
        // 从解析后的对象中获取总页数
        if (parsedData.pages !== undefined) {
          setTotalPages(parsedData.pages);
        }
      } else if (response.data && Array.isArray(response.data)) {
        // 如果已经是数组，直接使用
        rawData = response.data;
      } else if (
        response.data &&
        Array.isArray((response.data as any).records)
      ) {
        // 如果是包含 records 数组的对象
        rawData = (response.data as any).records;
        // 从对象中获取总记录数
        if ((response.data as any).total !== undefined) {
          setTotalRecords((response.data as any).total);
        }
        // 从对象中获取总页数
        if ((response.data as any).pages !== undefined) {
          setTotalPages((response.data as any).pages);
        }
      } else {
        // 默认情况下，使用空数组
        rawData = [];
        console.warn("[DEBUG] 无法识别的数据格式:", response.data);
      }
    } catch (parseError) {
      console.error("[DEBUG] 解析数据出错:", parseError);
      rawData = [];
    }

    console.log("[DEBUG] 原始数据条数:", rawData.length);

    // 将原始数据映射为表格数据格式
    const mappedData: LocalTableListItem[] = rawData.map((item: any) => ({
      caseName: item.caseName || "", // 案例名称 - 使用案例名称(caseName)
      caseGroup: item.caseGroup || "", // 测试周期 - 使用测试周期(caseGroup)
      caseAuthor: item.caseAuthor || "", // 测试者 - 使用测试者(caseAuthor)
      txNo: item.txNo || "", // 接口编号 - 使用接口编号(txNo)
      autoDataFlag: item.autoDataFlag || "0", // 状态 - 使用状态(autoDataFlag)
      caseNo: item.caseNo || "", // 案例编号 - 使用案例编号(caseNo)
      uniqueKey: item.uniqueKey
        ? String(item.uniqueKey)
        : `${item.caseName}_${item.caseGroup}_${item.caseAuthor}`,
      pcsPost: item.postPcs || "",
    }));

    // 对数据进行去重处理
    const uniqueData = Array.from(
      new Map(
        mappedData.map((item: LocalTableListItem) => {
          // 生成组合键或使用已有的唯一键
          const key =
            item.uniqueKey ||
            `${item.caseNo}_${item.caseGroup}_${item.caseAuthor}`;
          return [key, item];
        }),
      ).values(),
    );

    console.log("[DEBUG] 去重后数据条数:", uniqueData.length);
    console.log("[DEBUG] 数据示例:", uniqueData.slice(0, 2));

    // 更新状态
    setData(uniqueData);
    setFilteredData(uniqueData);

    // 显示成功消息
    const successMsg = `成功加载了 ${uniqueData.length} 条数据`;
    message.success(successMsg);
  }, []);

  /**
   * 获取表格数据
   * @param forceClearCache 是否强制清除缓存
   */
  const fetchData = useCallback(
    async (forceClearCache: boolean = false) => {
      try {
        setLoading(true);
        console.log("[DEBUG] 开始获取数据...");

        // 准备请求参数
        const params: TableListParams = {
          current: currentPage,
          pageSize: pageSize,
        };

        // 清除缓存模式下添加随机参数
        if (forceClearCache) {
          params._t = Date.now();
          params._r = Math.random().toString(36).substring(2, 15);
        }

        console.log("[DEBUG] 请求参数:", params);
        const response = await getTableList(params);
        console.log("[DEBUG] API响应:", response);

        if (response?.code === 200) {
          processApiResponse(response);
        } else {
          console.error("[DEBUG] 获取数据失败:", response);
          message.error("获取数据失败");
        }
      } catch (error) {
        console.error("[DEBUG] 获取数据异常:", error);
        message.error("获取数据失败");
      } finally {
        // 清除缓存模式下添加短暂延迟以提升用户体验
        if (forceClearCache) {
          setTimeout(() => setLoading(false), 300);
        } else {
          setLoading(false);
        }
        console.log("[DEBUG] 数据获取完成, loading状态:", loading);
      }
    },
    [currentPage, pageSize, processApiResponse],
  );

  // 初始加载数据
  useEffect(() => {
    console.log("[DEBUG] 组件初始化，开始加载数据");
    fetchData();
  }, [fetchData]);

  // 应用列筛选条件
  const applyColumnFilters = useCallback(
    (filters: Record<string, string[]>) => {
      // 筛选相关代码
      const result = data.filter((item: LocalTableListItem) => {
        // 如果没有筛选条件，返回所有数据
        if (!filters || Object.keys(filters).length === 0) {
          return true;
        }

        // 遍历所有筛选条件
        for (const [key, values] of Object.entries(filters)) {
          // 如果有筛选值，且当前项不符合任何一个筛选值，则排除
          if (values && values.length > 0) {
            // 处理状态筛选
            if (key === "autoDataFlag") {
              const itemCompleted = isCompleted(item.autoDataFlag);
              const wantCompleted = values.includes("1");
              const wantIncomplete = values.includes("0");

              // 如果筛选条件包含已完成，但项目未完成，则排除
              // 如果筛选条件包含未完成，但项目已完成，则排除
              if (
                (wantCompleted && !itemCompleted) ||
                (wantIncomplete && itemCompleted)
              ) {
                return false;
              }
            } else if (key === "caseGroup") {
              if (!values.includes(item.caseGroup || "")) return false;
            } else if (key === "caseAuthor") {
              if (!values.includes(item.caseAuthor || "")) return false;
            } else if (key === "caseName") {
              if (
                !values.some((value) =>
                  item.caseName?.toLowerCase().includes(value.toLowerCase()),
                )
              )
                return false;
            } else if (key === "caseNo") {
              if (!values.includes(item.caseNo || "")) return false;
            } else if (key === "txNo") {
              if (!values.includes(item.txNo || "")) return false;
            }
            // 可以添加其他字段的筛选逻辑
          }
        }
        return true;
      });

      // 更新过滤后的数据状态
      setFilteredData(result);

      if (process.env.NODE_ENV === "development") {
        console.log("[ApplyFilters]", { filters, dataLength: result.length });
      }

      // 返回过滤后的数据，以便在其他函数中使用
      return result;
    },
    [data, isCompleted],
  );

  /**
   * 应用筛选条件
   */
  const applyFilters = useCallback(
    (
      activeFiltersToApply: Record<string, string[]>,
      statusFilterToApply: string | null,
    ) => {
      // 检查是否有活跃的筛选条件
      const hasActiveFilters =
        Object.keys(activeFiltersToApply).length > 0 ||
        statusFilterToApply !== null;

      // 无筛选条件时显示所有数据
      if (!hasActiveFilters) {
        setFilteredData([...data]);
        return;
      }

      // 构建合并后的过滤条件
      const mergedFilters = { ...activeFiltersToApply };

      // 如果有状态过滤器，将其添加到过滤条件中
      if (statusFilterToApply) {
        mergedFilters.autoDataFlag = [statusFilterToApply];
      }

      // 应用合并后的过滤条件（只需调用一次applyColumnFilters）
      applyColumnFilters(mergedFilters);

      if (process.env.NODE_ENV === "development") {
        console.log(
          `[ApplyFilters] 应用筛选: ${Object.keys(mergedFilters).length}个条件`,
        );
      }
    },
    [data, applyColumnFilters],
  );

  /**
   * 应用当前筛选条件
   */
  const applyCurrentFilters = useCallback(() => {
    applyFilters(activeFilters, statusFilter);
  }, [activeFilters, statusFilter, applyFilters]);

  /**
   * 处理搜索框值变化
   */
  const handleSearchChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const newSearchText = e.target.value;
      setSearchText(newSearchText);

      // 直接执行搜索
      if (!newSearchText) {
        // 如果清除了搜索，应用当前筛选条件
        applyCurrentFilters();
      } else {
        const searchLower = newSearchText.toLowerCase();
        // 应用搜索条件
        const filtered = [...data].filter(
          (item) =>
            (item.caseName &&
              item.caseName.toLowerCase().includes(searchLower)) ||
            (item.caseNo && item.caseNo.toLowerCase().includes(searchLower)) ||
            (item.caseAuthor &&
              item.caseAuthor.toLowerCase().includes(searchLower)) ||
            (item.caseGroup &&
              item.caseGroup.toLowerCase().includes(searchLower)) ||
            (item.txNo && item.txNo.toLowerCase().includes(searchLower)),
        );

        setFilteredData(filtered);
      }
    },
    [data, applyCurrentFilters],
  );

  /**
   * 处理刷新按钮点击
   */
  const handleRefresh = useCallback(async () => {
    try {
      // 重置搜索，但保留筛选状态
      setSearchText("");

      // 获取新数据（清除缓存模式）
      await fetchData(true);

      // 如果有活跃的筛选条件，重新应用筛选
      if (Object.keys(activeFilters).length > 0 || statusFilter) {
        setTimeout(() => applyCurrentFilters(), 100);
      }
    } catch (error) {
      console.error("刷新失败:", error);
      message.error("刷新出错");
    }
  }, [activeFilters, statusFilter, applyCurrentFilters, fetchData]);

  /**
   * 强制刷新数据
   */
  const forceRefreshData = useCallback(() => {
    // 先重置加载状态，提供视觉反馈
    setLoading(true);

    // 重置所有筛选状态
    setStatusFilter(null);
    setActiveFilters({});
    setSearchText("");
    setCurrentPage(1); // 重置到第一页

    // 重新获取数据
    fetchData(true)
      .then(() => message.success("已重新获取数据并刷新显示"))
      .catch(() => {
        message.error("获取数据失败，请稍后重试");
        setLoading(false);
      });
  }, [fetchData]);

  /**
   * 处理重置筛选
   */
  const handleResetFilters = useCallback(() => {
    // 清空所有筛选条件
    setActiveFilters({});
    setStatusFilter(null);
    setCurrentPage(1); // 重置到第一页

    // 重置筛选后刷新数据
    fetchData(true);
  }, [fetchData]);

  /**
   * 处理移除状态筛选
   */
  const handleRemoveStatusFilter = useCallback(() => {
    setStatusFilter(null);
    // 保留其他筛选条件
    applyFilters(activeFilters, null);
  }, [activeFilters, applyFilters]);

  // 处理复制JSON内容到剪贴板
  const copyJsonToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(jsonPreviewContent);
      setJsonCopied(true);
      message.success("已复制到剪贴板");
      setTimeout(() => setJsonCopied(false), 3000);
    } catch (error) {
      console.error("复制失败:", error);
      message.error("复制失败");
    }
  };

  // 处理查看按钮点击 - 打开JSON预览
  const handleViewClick = useCallback(
    (record: LocalTableListItem, event: React.MouseEvent) => {
      event.stopPropagation();

      // 保存当前记录到ref中
      currentRecordRef.current = record;

      // 获取并格式化JSON内容
      let formattedContent = "{}";
      let parseError = false;

      if (record.pcsPost && typeof record.pcsPost === "string") {
        try {
          // 尝试解析为JSON并格式化
          const jsonObj = JSON.parse(record.pcsPost);
          formattedContent = JSON.stringify(jsonObj, null, 2);
        } catch (e) {
          // 如果解析失败，直接显示原始内容
          formattedContent = record.pcsPost;
          parseError = true;
          console.error("JSON解析失败:", e);

          // 显示友好的错误提示
          message.warning("JSON格式错误，显示原始内容");
        }
      } else if (!record.pcsPost) {
        // 如果没有JSON内容，显示友好的提示
        formattedContent = "// 该记录没有JSON内容";
        message.info("该记录没有JSON内容");
      }

      setJsonPreviewContent(formattedContent);
      setJsonPreviewVisible(true);

      if (parseError) {
        // 如果解析出错，显示一个更详细的提示
        setTimeout(() => {
          message.error({
            content: "数据格式不是有效的JSON，请检查原始数据",
            duration: 3,
          });
        }, 500);
      }
    },
    [],
  );

  // 行点击事件
  const handleRowClick = useCallback(
    (record: LocalTableListItem) => {
      // 获取当前记录的uniqueKey
      const uniqueKey =
        record.uniqueKey ||
        `${record.caseNo}_${record.caseGroup}_${record.caseAuthor}`;

      // 创建单个任务对象，确保包含所有必要字段
      const singleTask = {
        caseNo: record.caseNo || "",
        caseName: record.caseName || "",
        caseGroup: record.caseGroup || "",
        caseAuthor: record.caseAuthor || "",
        txNo: record.txNo || "",
        pcsPost: record.pcsPost || null,
        uniqueKey: uniqueKey,
        autoDataFlag: record.autoDataFlag,
        assertFlag: record.assertFlag || "0",
        selected: true,
        caseDesc: record.caseDesc || "",
        caseType: record.caseType || "",
      };

      console.log("Creating singleTask object:", singleTask);

      // 构建要传递的任务数据
      const taskData: any = {
        txNo: record.txNo || "",
        taskId: record.caseNo || "",
        testTaskName: record.caseName || "",
        uniqueKey: uniqueKey,
        uniqueKeyList: [uniqueKey],
        batchMode: "single",
        pcsPost: record.pcsPost || "",
        autoDataFlag: record.autoDataFlag,
        selectedTasks: [singleTask],
        caseDesc: record.caseDesc || "",
        caseType: record.caseType || "",
      };

      console.log("Creating taskData object:", taskData);

      // 使用pageDataService发送数据
      const dataId = pageDataService.sendData(
        "/TestTaskAutoWork/PostPcs",
        taskData,
      );

      console.log("Data sent to pageDataService with dataId:", dataId);

      // 跳转到PostPcs页面，传递必要的查询参数
      history.push({
        pathname: "/TestTaskAutoWork/PostPcs",
        search: `?dataId=${dataId}&batchMode=single`,
      });

      console.log(
        `跳转到PostPcs页面，uniqueKey=${uniqueKey}，autoDataFlag=${record.autoDataFlag}，通过pageDataService传递`,
      );
    },
    [isCompleted],
  );

  // 创建表头标题组件，用于显示筛选状态
  const renderColumnTitle = useCallback(
    (title: string, key: string) => {
      // 检查当前列是否处于筛选状态
      const isFiltered = activeFilters[key] && activeFilters[key].length > 0;
      // 对于状态列特殊处理
      const isStatusFiltered = key === "autoDataFlag" && statusFilter !== null;

      return (
        <div
          className="table-header"
          style={{
            fontSize: `${headerFontSize}px`,
            color: THEME_COLORS.tableHeader,
            fontWeight: 600,
            whiteSpace: "nowrap",
            display: "flex",
            alignItems: "center",
            justifyContent: "center", // 确保水平居中
            width: "100%", // 占满宽度
          }}
        >
          {isFiltered || isStatusFiltered ? (
            <>
              <StarFilled style={{ color: "#1890ff", marginRight: 4 }} />
              <span>{title}</span>
              <span style={{ color: "#1890ff", marginLeft: 4 }}>*</span>
            </>
          ) : (
            <span>{title}</span>
          )}
        </div>
      );
    },
    [activeFilters, statusFilter, headerFontSize],
  );

  // 表格设置功能
  const openSettings = useCallback(() => {
    setSettingsVisible(true);
  }, []);

  const saveSettings = useCallback(() => {
    // 保存设置到localStorage以便下次打开页面时恢复
    localStorage.setItem("taskList.headerFontSize", headerFontSize.toString());
    localStorage.setItem("taskList.tableFontSize", tableFontSize.toString());
    localStorage.setItem("taskList.statusFontSize", statusFontSize.toString());
    localStorage.setItem("taskList.actionFontSize", actionFontSize.toString());
    setSettingsVisible(false);
    message.success("设置已保存");
  }, [headerFontSize, tableFontSize, statusFontSize, actionFontSize]);

  // 加载保存的设置
  useEffect(() => {
    const savedHeaderFontSize = localStorage.getItem("taskList.headerFontSize");
    const savedTableFontSize = localStorage.getItem("taskList.tableFontSize");
    const savedStatusFontSize = localStorage.getItem("taskList.statusFontSize");
    const savedActionFontSize = localStorage.getItem("taskList.actionFontSize");

    if (savedHeaderFontSize) {
      setHeaderFontSize(Number(savedHeaderFontSize));
    }

    if (savedTableFontSize) {
      setTableFontSize(Number(savedTableFontSize));
    }

    if (savedStatusFontSize) {
      setStatusFontSize(Number(savedStatusFontSize));
    }

    if (savedActionFontSize) {
      setActionFontSize(Number(savedActionFontSize));
    }
  }, []);

  /**
   * 表格行列配置
   */
  const tableConfig = useMemo(
    () => ({
      pagination: {
        pageSize: 10,
        showTotal: () => `共 ${totalRecords} 条记录，共 ${totalPages} 页`,
        showQuickJumper: true,
        showSizeChanger: true,
        pageSizeOptions: ["10", "20", "50", "100"],
        total: totalRecords, // 添加总记录数
      },
      scroll: { x: "max-content" },
      locale: {
        emptyText: (
          <div style={{ padding: "24px 0" }}>
            <InfoCircleOutlined style={{ fontSize: 24, marginBottom: 16 }} />
            <p>暂无数据</p>
          </div>
        ),
        filterConfirm: "确定",
        filterReset: "重置",
        filterEmptyText: "暂无筛选项",
      },
      rowClassNameFn: (record: LocalTableListItem) =>
        isCompleted(record.autoDataFlag) ? "completed-row" : "incomplete-row",
    }),
    [isCompleted], // 只需要引用 isCompleted，totalRecords 和 totalPages 会通过直接引用闭包中的值
  );

  /**
   * 处理表格行事件
   */
  const getRowProps = useCallback(
    (record: LocalTableListItem) => ({
      onMouseEnter: () => setHoveredRow(record.caseNo),
      onMouseLeave: () => setHoveredRow(null),
    }),
    [],
  );

  /**
   * 生成表格唯一key
   */
  const tableKey = useMemo(() => {
    const filtersKey = Object.keys(activeFilters).join("-");
    return `table-${filteredData.length}-${filtersKey}-${statusFilter || "all"}`;
  }, [filteredData.length, activeFilters, statusFilter]);

  /**
   * 表格列定义
   */
  const columns = useMemo<ColumnsType<LocalTableListItem>>(() => {
    // 获取状态类型 - 根据完成状态返回对应的类型名称
    const getStatusType = (status: number | string) => {
      return isCompleted(status) ? "success" : "warning";
    };

    // 生成筛选项 - 从数据集合中提取不重复的选项值
    const getFilterOptions = (
      sourceData: LocalTableListItem[],
      field: keyof LocalTableListItem,
    ) => {
      return Array.from(new Set(sourceData.map((item) => item[field])))
        .filter(Boolean)
        .map((value) => ({
          text: String(value),
          value: String(value),
        }));
    };

    return [
      {
        title: renderColumnTitle("测试周期", "caseGroup"),
        dataIndex: "caseGroup",
        key: "caseGroup",
        width: 100,
        ellipsis: true,
        align: "center" as AlignType,
        filters: getFilterOptions(data, "caseGroup"),
        filteredValue: activeFilters.caseGroup || null,
        onFilter: (
          value: string | number | boolean,
          record: LocalTableListItem,
        ) => record.caseGroup === value.toString(),
        render: (text: string, record: LocalTableListItem) => (
          <Tooltip title={text}>
            <span
              className={
                isCompleted(record.autoDataFlag)
                  ? "completed-text"
                  : "incomplete-text"
              }
              style={{
                fontSize: `${tableFontSize}px`,
                fontWeight: 500,
                display: "inline-block",
                width: "100%",
              }}
            >
              {text}
            </span>
          </Tooltip>
        ),
      },
      {
        title: renderColumnTitle("测试者", "caseAuthor"),
        dataIndex: "caseAuthor",
        key: "caseAuthor",
        width: 90,
        ellipsis: true,
        align: "center" as AlignType,
        filters: getFilterOptions(data, "caseAuthor"),
        filteredValue: activeFilters.caseAuthor || null,
        onFilter: (
          value: string | number | boolean,
          record: LocalTableListItem,
        ) => record.caseAuthor === value.toString(),
        render: (text: string, record: LocalTableListItem) => (
          <span
            className={
              isCompleted(record.autoDataFlag)
                ? "completed-text"
                : "incomplete-text"
            }
            style={{
              fontSize: `${tableFontSize}px`,
              fontWeight: 500,
              display: "inline-block",
              width: "100%",
            }}
          >
            {text}
          </span>
        ),
      },
      {
        title: renderColumnTitle("案例名称", "caseName"),
        dataIndex: "caseName",
        key: "caseName",
        width: 200,
        ellipsis: true,
        align: "center" as AlignType,
        filters: getFilterOptions(data, "caseName"),
        filteredValue: activeFilters.caseName || null,
        onFilter: (
          value: string | number | boolean,
          record: LocalTableListItem,
        ) => Boolean(record.caseName?.includes(value.toString())),
        render: (text: string, record: LocalTableListItem) => (
          <Tooltip title={text}>
            <span
              className={`table-cell-link ${isCompleted(record.autoDataFlag) ? "completed-text" : "incomplete-text"}`}
              onClick={() => handleRowClick(record)}
              style={{
                cursor: "pointer",
                textDecoration: "underline",
                display: "inline-block",
                width: "100%",
                textAlign: "center",
                fontSize: `${tableFontSize}px`,
                fontWeight: 500,
              }}
            >
              {text}
            </span>
          </Tooltip>
        ),
      },
      {
        title: renderColumnTitle("案例编号", "caseNo"),
        dataIndex: "caseNo",
        key: "caseNo",
        width: 140,
        ellipsis: true,
        align: "center" as AlignType,
        filters: getFilterOptions(data, "caseNo"),
        filteredValue: activeFilters.caseNo || null,
        onFilter: (
          value: string | number | boolean,
          record: LocalTableListItem,
        ) => record.caseNo === value.toString(),
        render: (text: string, record: LocalTableListItem) => (
          <span
            className={
              isCompleted(record.autoDataFlag)
                ? "completed-text"
                : "incomplete-text"
            }
            style={{
              fontSize: `${tableFontSize}px`,
              fontWeight: 500,
              display: "inline-block",
              width: "100%",
            }}
          >
            {text}
          </span>
        ),
      },
      {
        title: renderColumnTitle("接口编号", "txNo"),
        dataIndex: "txNo",
        key: "txNo",
        width: 100,
        ellipsis: true,
        align: "center" as AlignType,
        filters: getFilterOptions(data, "txNo"),
        filteredValue: activeFilters.txNo || null,
        onFilter: (
          value: string | number | boolean,
          record: LocalTableListItem,
        ) => record.txNo === value.toString(),
        render: (text: string, record: LocalTableListItem) => (
          <span
            className={
              isCompleted(record.autoDataFlag)
                ? "completed-text"
                : "incomplete-text"
            }
            style={{
              fontSize: `${tableFontSize}px`,
              fontWeight: 500,
              display: "inline-block",
              width: "100%",
            }}
          >
            {text}
          </span>
        ),
      },
      {
        title: renderColumnTitle("状态", "autoDataFlag"),
        dataIndex: "autoDataFlag",
        key: "autoDataFlag",
        width: 100,
        align: "center" as AlignType,
        filteredValue: statusFilter ? [statusFilter] : null,
        filters: [
          { text: "已完成", value: "1" },
          { text: "未完成", value: "0" },
        ],
        onFilter: (
          value: string | number | boolean,
          record: LocalTableListItem,
        ) => {
          const recordIsCompleted = isCompleted(record.autoDataFlag);
          const filterIsCompleted = value === "1" || value === 1;
          return recordIsCompleted === filterIsCompleted;
        },
        render: (_: any, record: LocalTableListItem) => {
          const completed = isCompleted(record.autoDataFlag);
          const statusText = completed ? "已完成" : "未完成";
          const TagIcon = completed ? CheckCircleOutlined : CloseCircleOutlined;
          const statusColor = completed ? "#389e0d" : "#d46b08";
          const bgColor = completed
            ? "rgba(82, 196, 26, 0.1)"
            : "rgba(250, 173, 20, 0.1)";
          const borderColor = completed
            ? "rgba(82, 196, 26, 0.3)"
            : "rgba(250, 173, 20, 0.3)";

          // 使用div替代Tag以确保样式完全可控
          return (
            <div
              className={`custom-status-tag ${completed ? "success" : "error"}`}
              data-font-size={statusFontSize}
              style={{
                display: "inline-flex",
                alignItems: "center",
                justifyContent: "center",
                padding: "4px 8px",
                borderRadius: "4px",
                background: bgColor,
                color: statusColor,
                border: `1px solid ${borderColor}`,
                fontSize: statusFontSize,
                fontWeight: 500,
                cursor: "default",
                lineHeight: "1.5",
                boxSizing: "border-box",
                margin: "0 auto",
              }}
            >
              <TagIcon
                style={{ marginRight: "4px", fontSize: `${statusFontSize}px` }}
              />
              <span style={{ fontSize: `${statusFontSize}px` }}>
                {statusText}
              </span>
            </div>
          );
        },
      },
      {
        title: renderColumnTitle("操作", "action"),
        key: "action",
        width: 80,
        align: "center" as const,
        render: (_: any, record: LocalTableListItem) => {
          const statusType = getStatusType(record.autoDataFlag);
          const isHovered = hoveredRow === record.caseNo;
          const buttonColor = isHovered
            ? THEME_COLORS[statusType].text
            : "#1890ff";

          return (
            <Tooltip title="查看详情">
              <div
                className="custom-action-button"
                data-font-size={actionFontSize}
                onClick={(event) => handleViewClick(record, event)}
                style={{
                  display: "inline-flex",
                  alignItems: "center",
                  justifyContent: "center",
                  color: buttonColor,
                  fontSize: actionFontSize,
                  fontWeight: 500,
                  cursor: "pointer",
                  transition: "all 0.3s",
                }}
              >
                <EyeOutlined
                  style={{ marginRight: "4px", fontSize: actionFontSize }}
                />
                <span style={{ fontSize: actionFontSize }}>查看</span>
              </div>
            </Tooltip>
          );
        },
      },
    ];
  }, [
    data,
    activeFilters,
    statusFilter,
    hoveredRow,
    renderColumnTitle,
    isCompleted,
    handleViewClick,
    actionFontSize,
    statusFontSize,
    handleRowClick,
    tableFontSize,
  ]);

  /**
   * 渲染筛选标签
   */
  const renderFilterTags = useCallback(() => {
    if (!(Object.keys(activeFilters).length > 0 || statusFilter)) {
      return null;
    }

    return (
      <div style={STYLES.filterArea}>
        <Text strong style={{ fontSize: 13, color: "#666" }}>
          筛选条件：
        </Text>

        {/* 状态筛选标签 */}
        {statusFilter && (
          <Tag
            color={statusFilter === "1" ? "success" : "warning"}
            closable
            onClose={(evt) => {
              evt.preventDefault();
              handleRemoveStatusFilter();
            }}
            style={STYLES.tagItem}
          >
            <span style={{ fontWeight: "bold" }}>状态</span>:{" "}
            {statusFilter === "1" ? "已完成" : "未完成"}
          </Tag>
        )}

        {/* 其他筛选标签 */}
        {Object.entries(activeFilters).map(([key, values]) => {
          // 跳过状态筛选，因为已单独处理
          if (key === "autoDataFlag" || values.length === 0) return null;

          // 提取纯文本标题
          let cleanTitle = key;
          if (key === "caseGroup") cleanTitle = "测试周期";
          else if (key === "caseAuthor") cleanTitle = "测试者";
          else if (key === "caseName") cleanTitle = "测试案例名称";
          else if (key === "caseNo") cleanTitle = "测试案例编号";
          else if (key === "txNo") cleanTitle = "接口编号";

          // 将多个值合并成一个字符串显示
          let valuesDisplay = values.join("、");
          if (values.length > 2) {
            valuesDisplay = `${values[0]}、${values[1]} 等${values.length}项`;
          }

          // 根据列类型选择颜色
          let tagColor = "blue";
          if (key === "caseAuthor") tagColor = "purple";
          else if (key === "caseName") tagColor = "geekblue";
          else if (key === "caseNo") tagColor = "cyan";
          else if (key === "txNo") tagColor = "volcano";

          return (
            <Tag
              key={key}
              color={tagColor}
              closable
              onClose={(evt) => {
                evt.preventDefault();
                // 移除该列的所有筛选条件
                const newActiveFilters = { ...activeFilters };
                delete newActiveFilters[key];
                setActiveFilters(newActiveFilters);
                // 应用现有筛选条件
                applyColumnFilters(newActiveFilters);
              }}
              style={STYLES.tagItem}
            >
              <span style={{ fontWeight: "bold" }}>{cleanTitle}</span>:{" "}
              {valuesDisplay}
            </Tag>
          );
        })}
      </div>
    );
  }, [
    activeFilters,
    statusFilter,
    STYLES.filterArea,
    STYLES.tagItem,
    handleRemoveStatusFilter,
    applyColumnFilters,
  ]);

  /**
   * 表格筛选和分页变化时的处理函数
   */
  const handleTableChange = useCallback(
    (
      pagination: TablePaginationConfig,
      filters: Record<string, FilterValue | null>,
      sorter:
        | SorterResult<LocalTableListItem>
        | SorterResult<LocalTableListItem>[],
      extra: TableCurrentDataSource<LocalTableListItem>,
    ) => {
      // 处理分页变化
      let shouldFetchData = false;
      let newPage = currentPage;
      let newPageSize = pageSize;

      if (pagination.current && pagination.current !== currentPage) {
        newPage = pagination.current;
        shouldFetchData = true;
        console.log("[PageChange] 页码变更为:", pagination.current);
      }

      if (pagination.pageSize && pagination.pageSize !== pageSize) {
        newPageSize = pagination.pageSize;
        shouldFetchData = true;
        console.log("[PageSizeChange] 每页条数变更为:", pagination.pageSize);
      }

      // 如果分页发生变化，重新加载数据
      if (shouldFetchData) {
        // 更新状态
        setCurrentPage(newPage);
        setPageSize(newPageSize);

        // 延迟执行，确保状态更新
        setTimeout(() => {
          fetchData(false);
        }, 0);

        return; // 分页变化时，不处理筛选条件
      }

      // 处理所有筛选条件
      const newFilters: Record<string, string[]> = {};

      // 将所有筛选条件转换为统一格式
      Object.entries(filters).forEach(([key, value]) => {
        if (value) {
          // 确保所有值都转换为字符串数组
          newFilters[key] = value.map((item) => String(item));
        }
      });

      // 更新状态过滤器
      if (filters.autoDataFlag && filters.autoDataFlag.length > 0) {
        setStatusFilter(String(filters.autoDataFlag[0]));
      } else {
        setStatusFilter(null);
      }

      // 更新活动的筛选条件
      setActiveFilters(newFilters);

      // 应用筛选条件
      applyColumnFilters(newFilters);

      // 开发模式下记录
      if (process.env.NODE_ENV === "development") {
        console.log("[TableChange]", {
          filters: newFilters,
          action: extra.action,
          pagination,
        });
      }
    },
    [applyColumnFilters, currentPage, pageSize, fetchData],
  );

  const clearStatusFilter = useCallback(() => {
    const newFilters = { ...activeFilters };
    delete newFilters.autoDataFlag;
    setActiveFilters(newFilters);
    setFilteredData(applyColumnFilters(newFilters));
  }, [activeFilters, applyColumnFilters]);

  const resetAllFilters = useCallback(() => {
    setActiveFilters({});
    setFilteredData(data);
  }, [data]);

  // 处理批量跳转功能
  const handleBatchJump = useCallback(() => {
    if (selectedRowKeys.length === 0) {
      message.warning("请至少选择一个任务");
      return;
    }

    // 获取选中的行数据
    const selectedRecords = filteredData.filter((item) =>
      selectedRowKeys.includes(
        item.uniqueKey || `${item.caseNo}_${item.caseGroup}_${item.caseAuthor}`,
      ),
    );

    if (selectedRecords.length === 0) {
      message.error("无法获取选中任务数据");
      return;
    }

    // 转换选中记录为TableListItem格式的数组
    const selectedTasks: LocalTableListItem[] = selectedRecords.map(
      (record, index) => ({
        caseNo: record.caseNo || "",
        caseName: record.caseName || "",
        caseGroup: record.caseGroup || "",
        caseAuthor: record.caseAuthor || "",
        txNo: record.txNo || "",
        pcsPost: record.pcsPost || null,
        uniqueKey:
          record.uniqueKey ||
          `${record.caseNo}_${record.caseGroup}_${record.caseAuthor}`,
        autoDataFlag: record.autoDataFlag,
        assertFlag: record.assertFlag || "0",
        selected: index === 0,
        // 新增：补充所有你需要的字段
        caseDesc: record.caseDesc || "",
        caseType: record.caseType || "",
      }),
    );

    // 构建要传递的任务数据
    const taskData: any = {
      // 保留基本信息用于页面初始化
      txNo: selectedRecords[0].txNo || "",
      taskId: selectedRecords[0].caseNo || "",
      testTaskName: selectedRecords[0].caseName || "",
      record: selectedRecords[0],
      // 传递完整的选中任务数组
      selectedTasks: selectedTasks,
      // 添加第一个记录的自动完成标志，用于PostPcs页面初始状态
      autoDataFlag: selectedRecords[0].autoDataFlag,
    };

    // 使用pageDataService发送数据
    const dataId = pageDataService.sendData(
      "/TestTaskAutoWork/PostPcs",
      taskData,
    );

    // 跳转到PostPcs页面，传递必要的查询参数
    history.push({
      pathname: "/TestTaskAutoWork/PostPcs",
      search: `?dataId=${dataId}&batchMode=multiple`,
    });

    message.success(`已选择 ${selectedRecords.length} 个任务进行批量处理`);
  }, [selectedRowKeys, filteredData]);

  // 添加处理保存按钮点击的函数
  const handleSaveClick = useCallback(async () => {
    if (!currentRecordRef.current || !currentRecordRef.current.uniqueKey) {
      message.error("无法获取当前记录信息");
      return;
    }

    try {
      setIsSaving(true);

      // 准备保存的数据
      const saveParams: SaveCaseParams = {
        uniqueKey: currentRecordRef.current.uniqueKey,
        pcsPost: jsonPreviewContent, // 使用当前编辑框中的JSON内容
        caseNo: currentRecordRef.current.caseNo,
        caseName: currentRecordRef.current.caseName,
        caseGroup: currentRecordRef.current.caseGroup,
        caseAuthor: currentRecordRef.current.caseAuthor,
        txNo: currentRecordRef.current.txNo,
        autoDataFlag: currentRecordRef.current.autoDataFlag,
        assertFlag: currentRecordRef.current.assertFlag || "0",
      };

      // 调用保存API
      const response = await saveCase(saveParams);

      if (response && response.code === 200) {
        // 如果保存成功
        message.success("JSON数据保存成功");

        // 更新当前行的数据
        if (currentRecordRef.current) {
          currentRecordRef.current.pcsPost = jsonPreviewContent;

          // 更新表格数据中对应行的数据
          const updatedData = data.map((item) => {
            if (item.uniqueKey === currentRecordRef.current?.uniqueKey) {
              return {
                ...item,
                pcsPost: jsonPreviewContent,
              };
            }
            return item;
          });

          // 更新状态
          setData(updatedData);

          // 应用当前的筛选条件
          if (searchText) {
            // 如果有搜索文本，需要应用搜索
            const searchLower = searchText.toLowerCase();
            const filtered = updatedData.filter(
              (item) =>
                (item.caseName &&
                  item.caseName.toLowerCase().includes(searchLower)) ||
                (item.caseNo &&
                  item.caseNo.toLowerCase().includes(searchLower)) ||
                (item.caseAuthor &&
                  item.caseAuthor.toLowerCase().includes(searchLower)) ||
                (item.caseGroup &&
                  item.caseGroup.toLowerCase().includes(searchLower)) ||
                (item.txNo && item.txNo.toLowerCase().includes(searchLower)),
            );
            setFilteredData(filtered);
          } else {
            // 否则直接应用筛选
            applyFilters(activeFilters, statusFilter);
          }
        }
      } else {
        // 保存失败
        message.error(response?.msg || "保存失败，请重试");
      }
    } catch (error) {
      console.error("保存JSON数据出错:", error);
      message.error("保存失败，请重试");
    } finally {
      setIsSaving(false);
    }
  }, [
    jsonPreviewContent,
    data,
    searchText,
    activeFilters,
    statusFilter,
    applyFilters,
  ]);

  return (
    <PageContainer
      title={
        <span style={{ color: "#2c4b76", fontWeight: 600 }}>测试任务</span>
      }
      content={
        <div style={{ color: "#5b8ac2" }}>
          <span>案例测试任务列表，根据测试任务计划派生执行。</span>
          <Button
            type="link"
            icon={<SettingOutlined style={{ color: "#1890ff" }} />}
            onClick={() => setSettingsVisible(true)}
            style={{ color: "#1890ff" }}
          >
            表格设置
          </Button>
        </div>
      }
    >
      {/* 搜索和操作区域 */}
      <div className="operations-area">
        <Row gutter={16} align="middle" justify="space-between">
          <Col xs={24} sm={12} md={8} lg={6} xl={6}>
            <Input
              className="search-input"
              placeholder="搜索案例名称、编号、测试者..."
              value={searchText}
              onChange={handleSearchChange}
              prefix={
                <span style={{ display: "flex", alignItems: "center" }}>
                  <SearchOutlined
                    style={{ color: "#1890ff", marginRight: 4 }}
                  />
                  <span
                    style={{
                      color: "#1890ff",
                      fontWeight: 500,
                      fontSize: "13px",
                    }}
                  >
                    智能搜索
                  </span>
                </span>
              }
              allowClear
              style={{ width: "100%" }}
            />
          </Col>
          <Col>
            <div className="actions-wrapper">
              <Button
                type="primary"
                icon={<ReloadOutlined />}
                loading={loading}
                onClick={() => {
                  setActiveFilters({});
                  fetchData(true);
                }}
                className="action-button"
              >
                刷新数据
              </Button>
              {/* 添加全选按钮 */}
              <Button
                type="default"
                onClick={selectAllCurrent}
                style={{
                  marginLeft: 8,
                  borderColor: "#1890ff",
                  color: "#1890ff",
                  fontWeight: 500,
                }}
              >
                {isAllSelected ? "取消全选" : "全选当页"}
              </Button>
              {/* 添加批量跳转按钮 */}
              <Button
                type="primary"
                disabled={!hasSelected}
                onClick={handleBatchJump}
                style={{
                  marginLeft: 8,
                  background: "#8c6cb5",
                  borderColor: "#8c6cb5",
                  color: "#ffffff",
                  fontWeight: 500,
                  boxShadow: "0 2px 6px rgba(140, 108, 181, 0.3)",
                }}
              >
                批量跳转 {hasSelected ? `(${selectedRowKeys.length})` : ""}
              </Button>
              {Object.keys(activeFilters).length > 0 && (
                <Button
                  danger
                  icon={<FilterOutlined />}
                  onClick={resetAllFilters}
                  className="filter-clear-button"
                >
                  清除筛选
                </Button>
              )}
            </div>
          </Col>
        </Row>

        {/* 筛选标签区域 */}
        {(Object.keys(activeFilters).length > 0 || statusFilter) && (
          <div style={{ marginTop: 16 }}>{renderFilterTags()}</div>
        )}
      </div>

      <Card
        className="task-list-card"
        bordered={false}
        bodyStyle={{ padding: "0 0 16px 0" }}
      >
        <div className="table-wrapper">
          {console.log(
            "[DEBUG] 表格渲染 - 数据条数:",
            filteredData.length,
            "筛选条件:",
            activeFilters,
          )}

          {/* 添加错误提示区域 */}
          {filteredData.length === 0 && !loading && (
            <div className="empty-data-alert">
              <InfoCircleOutlined
                style={{
                  fontSize: "32px",
                  color: "#1890ff",
                  marginBottom: "16px",
                }}
              />
              <p style={{ fontSize: "16px", marginBottom: "12px" }}>
                暂无数据，可能原因：
              </p>
              <ul>
                <li>1. 没有满足筛选条件的数据</li>
                <li>2. 服务器连接失败</li>
                <li>3. 数据加载错误</li>
              </ul>
              <Button
                type="primary"
                onClick={forceRefreshData}
                style={{ marginTop: "16px" }}
                icon={<ReloadOutlined />}
              >
                强制刷新
              </Button>
            </div>
          )}

          <Table<LocalTableListItem>
            className="custom-table ant-table-wrapper"
            rowClassName={(record) =>
              isCompleted(record.autoDataFlag)
                ? "completed-row"
                : "incomplete-row"
            }
            rowKey={(record) => {
              const key =
                record.uniqueKey ||
                `${record.caseNo}_${record.caseGroup}_${record.caseAuthor}`;
              return key;
            }}
            loading={loading}
            columns={columns}
            dataSource={filteredData}
            pagination={{
              showSizeChanger: true,
              showQuickJumper: true,
              defaultPageSize: 10,
              pageSize: pageSize,
              current: currentPage,
              hideOnSinglePage: false,
              showTotal: () => `共 ${totalRecords} 条记录，共 ${totalPages} 页`,
              itemRender: (page, type) => {
                if (type === "page") {
                  return (
                    <span style={{ color: THEME_COLORS.tableText }}>
                      {page}
                    </span>
                  );
                }
                return null;
              },
              total: totalRecords > 0 ? totalRecords : 50,
            }}
            rowSelection={rowSelection}
            onChange={handleTableChange}
            scroll={{ x: 1300 }}
            size="middle"
            tableLayout="fixed"
            locale={{
              emptyText: (
                <div
                  style={{ padding: "32px 0", color: THEME_COLORS.tableText }}
                >
                  <InfoCircleOutlined
                    style={{
                      fontSize: 28,
                      marginBottom: 20,
                      color: THEME_COLORS.highlight,
                    }}
                  />
                  <p style={{ fontSize: "15px", margin: "12px 0" }}>暂无数据</p>
                  <Button
                    size="small"
                    onClick={forceRefreshData}
                    className="action-button"
                  >
                    刷新数据
                  </Button>
                </div>
              ),
            }}
          />
        </div>
      </Card>

      {/* JSON预览弹窗 */}
      <Modal
        title={
          <Space>
            <CodepenOutlined style={{ color: "#1890ff" }} />
            <span>JSON预览</span>
            {currentRecordRef.current && (
              <Tooltip
                title={`${currentRecordRef.current.caseName || "未命名"} (${currentRecordRef.current.caseNo || "无编号"})`}
              >
                <Tag color="blue">
                  {currentRecordRef.current.caseName || "未命名任务"}
                </Tag>
              </Tooltip>
            )}
          </Space>
        }
        visible={jsonPreviewVisible}
        onCancel={() => setJsonPreviewVisible(false)}
        width={800}
        footer={[
          <Button
            key="save"
            type="primary"
            icon={<SaveOutlined />}
            onClick={handleSaveClick}
            loading={isSaving}
            className="modal-action-button"
          >
            保存
          </Button>,
          <Button
            key="copy"
            type="default"
            icon={<CopyOutlined />}
            onClick={copyJsonToClipboard}
            className="modal-action-button"
          >
            {jsonCopied ? "已复制" : "复制JSON"}
          </Button>,
          <Button
            key="edit"
            type="default"
            icon={<EditOutlined />}
            onClick={() => {
              if (currentRecordRef.current) {
                handleRowClick(currentRecordRef.current);
                setJsonPreviewVisible(false);
              } else {
                message.info("无法获取当前记录信息");
              }
            }}
            className="modal-action-button"
          >
            编辑记录
          </Button>,
          <Button
            key="close"
            onClick={() => setJsonPreviewVisible(false)}
            className="modal-action-button"
          >
            关闭
          </Button>,
        ]}
        bodyStyle={{
          padding: "12px",
          maxHeight: "70vh",
          overflow: "auto",
          border: "1px solid #f0f0f0",
          borderRadius: "4px",
          margin: "0 12px",
        }}
        className="preview-modal"
      >
        <CodeMirror
          value={jsonPreviewContent}
          height="100%"
          extensions={[json()]}
          theme="light"
          editable={false}
          style={{ fontSize: "14px" }}
        />
      </Modal>

      {/* 表格设置弹窗 */}
      <Modal
        title={
          <Space>
            <SettingOutlined style={{ color: "#1890ff" }} />
            <span>表格设置</span>
          </Space>
        }
        visible={settingsVisible}
        onCancel={() => setSettingsVisible(false)}
        onOk={saveSettings}
        className="preview-modal"
        width={500}
        okText="保存设置"
        cancelText="取消"
      >
        <div style={{ padding: "10px 20px" }}>
          <Typography.Title level={5}>
            <Space>
              <FontSizeOutlined />
              <span>表头字体大小</span>
            </Space>
          </Typography.Title>
          <Slider
            min={12}
            max={18}
            value={headerFontSize}
            onChange={(value) => setHeaderFontSize(value)}
            marks={{
              12: "12px",
              14: "14px",
              16: "16px",
              18: "18px",
            }}
          />

          <Typography.Title level={5} style={{ marginTop: 30 }}>
            <Space>
              <FontSizeOutlined />
              <span>表格内容字体大小</span>
            </Space>
          </Typography.Title>
          <Slider
            min={12}
            max={16}
            value={tableFontSize}
            onChange={(value) => setTableFontSize(value)}
            marks={{
              12: "12px",
              14: "14px",
              16: "16px",
            }}
          />
        </div>
      </Modal>
    </PageContainer>
  );
};

export default TaskList;
