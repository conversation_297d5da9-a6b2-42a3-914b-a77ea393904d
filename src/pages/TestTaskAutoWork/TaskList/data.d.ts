/**
 * 测试任务自动化模块类型定义
 */

// 表格数据项类型定义
export interface TableListItem {
  caseGroup?: string; // 测试周期（原testCircle）
  caseAuthor?: string; // 测试者（原tester）
  caseName?: string; // 测试案例名称（原testTaskNm）
  caseNo: string; // 测试案例编号（必须字段，原testTaskCode）
  pcsPost?: string | null; // 接口请求信息
  txNo?: string; // 接口编号（原pcsId）
  uniqueKey?: string; // 唯一标识（用于列表去重和作为rowKey）
  autoDataFlag: number | string; // 完成状态：1-已完成，0-未完成（必须字段，原autoFlag）
  assertFlag?: number | string; // 断言标志
  key?: string; // 表格行key
  [key: string]: any; // 其他可能的字段
}

// 表格请求参数
export interface TableListParams {
  current?: number; // 当前页码
  pageSize?: number; // 每页数量
  autoDataFlag?: number | null; // 完成状态筛选（原autoFlag）
  caseAuthor?: string; // 测试者筛选（原tester）
  caseGroup?: string; // 测试周期筛选（原testCircle）
  keyword?: string; // 关键词搜索
  txNo?: string; // 接口编号筛选（原pcsId）
  [key: string]: any; // 其他可能的筛选参数
}

// API响应数据结构
export interface ResponseData<T = any> {
  code: number; // 响应状态码，200表示成功
  total: number; // 总数据条数
  pageSize?: number; // 每页数量
  data: T; // 响应数据
  msg?: string; // 响应消息
}

// 筛选选项类型
export interface FilterOption {
  text: string; // 显示文本
  value: string; // 选项值
}

// 测试任务自动化命名空间
export namespace TestTaskAutoWork {
  export type { TableListItem, TableListParams, ResponseData, FilterOption };
}
