// 自定义样式文件 - 增强测试任务列表页面视觉效果

// 标题动画效果
@keyframes borderPulse {
  0% {
    box-shadow: 0 0 0 0 rgba(24, 144, 255, 0.4);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(24, 144, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(24, 144, 255, 0);
  }
}

// 颜色变量 - 更新为更柔和的色调
@primary-color: #1890ff;
@success-color: #52c41a;
@warning-color: #faad14;
@error-color: #f5222d;
@heading-color: #345678; // 更柔和的蓝色代替黑色
@text-color: #456789; // 更柔和的蓝灰色代替黑色
@text-color-secondary: #78909c;
@table-header-bg: #f0f5ff; // 更柔和的蓝色背景
@table-header-color: #345678; // 表头文字颜色

// 操作区域样式增强
.operations-area {
  background: linear-gradient(135deg, #f9fcff 0%, #f5f8ff 100%);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(24, 144, 255, 0.1);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  }

  .search-input {
    width: 100%;
    max-width: 360px;

    .ant-input-affix-wrapper {
      border-radius: 6px;
      border: 1px solid #e8f1ff;
      padding-left: 8px;

      &:hover,
      &:focus {
        border-color: @primary-color;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
      }

      .ant-input-prefix {
        margin-right: 8px;
        padding-right: 8px;
        border-right: 1px solid rgba(24, 144, 255, 0.15);
      }

      .ant-input {
        font-size: 13px;

        &::placeholder {
          color: #aab7c4;
        }
      }

      .ant-input-clear-icon {
        color: #aab7c4;

        &:hover {
          color: #1890ff;
        }
      }
    }
  }

  .actions-wrapper {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
    align-items: center;
    justify-content: flex-end;
  }
}

// 卡片和标题增强
.task-list-card {
  margin-bottom: 24px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
  transition: box-shadow 0.3s ease;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
  }

  .ant-card-head {
    background: linear-gradient(135deg, #f0f5ff 0%, #e6f7ff 100%);
    border-bottom: 1px solid #e8f1ff;

    .ant-card-head-title {
      color: #2c4b76;
      font-weight: 600;
      text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5);

      .ant-typography {
        color: #2c4b76;
        font-weight: 600;

        &.ant-typography-secondary {
          color: #5b8ac2;
          font-weight: normal;
          margin-left: 8px;
        }
      }
    }

    .ant-card-extra {
      .action-button {
        border-radius: 4px;
      }
    }
  }

  .ant-card-body {
    padding: 0;
    background-color: #ffffff;
  }
}

.table-wrapper {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

// 添加表格行文本样式
.completed-text {
  color: #389e0d; // 更深一点的绿色，提高可读性
  font-weight: 500;
}

.incomplete-text {
  color: #d46b08; // 更深一点的橙色，提高可读性
  font-weight: 500;
}

// 表格行hover美化
.ant-table-tbody {
  > tr {
    transition: all 0.3s ease;

    &:hover {
      > td {
        position: relative;
      }
    }

    // 已完成和未完成行的交替色彩
    &.completed-row {
      &:nth-child(odd) td {
        background-color: rgba(82, 196, 26, 0.05);
      }
      &:nth-child(even) td {
        background-color: rgba(82, 196, 26, 0.1);
      }

      &:hover td {
        background-color: rgba(82, 196, 26, 0.15) !important;
        transform: translateY(-1px);
      }
    }

    &.incomplete-row {
      &:nth-child(odd) td {
        background-color: rgba(250, 173, 20, 0.05);
      }
      &:nth-child(even) td {
        background-color: rgba(250, 173, 20, 0.1);
      }

      &:hover td {
        background-color: rgba(250, 173, 20, 0.15) !important;
        transform: translateY(-1px);
      }
    }
  }
}

// 状态标签增强
.status-tag {
  position: relative;
  overflow: hidden;
  font-size: 13px;
  padding: 4px 8px;
  border-radius: 4px;

  &.success,
  &.error {
    &::after {
      content: "";
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background-color: rgba(255, 255, 255, 0.3);
      transition: all 1s ease;
    }

    &:hover::after {
      left: 100%;
    }
  }
}

// 按钮悬停效果增强
.ant-btn {
  position: relative;
  overflow: hidden;
  border-radius: 6px;

  &::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(255, 255, 255, 0.5);
    opacity: 0;
    border-radius: 100%;
    transform: scale(1, 1) translate(-50%, -50%);
    transform-origin: 50% 50%;
  }

  &:hover::after {
    animation: ripple 1s ease-out;
  }
}

// 特殊按钮样式
.action-button {
  transition: all 0.3s ease;
  box-shadow: 0 2px 0 rgba(0, 0, 0, 0.02);
  border-radius: 6px;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }

  &.ant-btn-primary {
    background-color: @primary-color;
    border-color: @primary-color;

    &:hover {
      background-color: lighten(@primary-color, 5%);
      border-color: lighten(@primary-color, 5%);
    }
  }
}

// 筛选清除按钮样式优化
.filter-clear-button {
  transition: all 0.3s ease;
  border-radius: 6px;
  border: 1px solid #ff4d4f;
  color: #ff4d4f;

  &:hover {
    background-color: lighten(#ff4d4f, 35%);
    transform: translateY(-2px);
    box-shadow: 0 2px 6px rgba(255, 77, 79, 0.2);
  }
}

@keyframes ripple {
  0% {
    transform: scale(0, 0);
    opacity: 0.5;
  }
  100% {
    transform: scale(20, 20);
    opacity: 0;
  }
}

// 筛选标签增强
.ant-tag {
  position: relative;
  overflow: hidden;
  border-radius: 4px;
  padding: 4px 8px;
  margin: 4px;
  font-size: 13px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  border-width: 1px;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 3px;
    height: 100%;
    background: rgba(255, 255, 255, 0.5);
  }

  // 关闭图标样式优化
  .anticon-close {
    color: inherit;
    opacity: 0.6;
    transition: all 0.3s;

    &:hover {
      opacity: 1;
      color: inherit;
    }
  }
}

// 搜索框聚焦效果
.ant-input-affix-wrapper {
  &:focus,
  &-focused {
    &::before {
      content: "";
      position: absolute;
      bottom: -2px;
      left: 0;
      width: 100%;
      height: 2px;
      background: linear-gradient(to right, @primary-color, @success-color);
      animation: inputFocus 2s infinite;
    }
  }
}

@keyframes inputFocus {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}

// 表格页码美化
.ant-pagination-item-active {
  position: relative;
  overflow: hidden;

  &::after {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.3);
    animation: paginationShine 2s ease-in-out infinite;
  }
}

@keyframes paginationShine {
  0% {
    left: -100%;
  }
  30% {
    left: 100%;
  }
  100% {
    left: 100%;
  }
}

// 加载动画美化
.ant-spin {
  .ant-spin-dot-item {
    background-color: @primary-color;

    &:nth-child(1) {
      animation-delay: 0s;
    }
    &:nth-child(2) {
      animation-delay: 0.2s;
    }
    &:nth-child(3) {
      animation-delay: 0.4s;
    }
    &:nth-child(4) {
      animation-delay: 0.6s;
    }
  }
}

// 空数据状态样式优化
.empty-data-alert {
  border: 1px dashed rgba(0, 0, 0, 0.1);
  background: linear-gradient(to bottom, #fafcff, #f5f8ff);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }

  ul {
    border-left: 2px solid rgba(24, 144, 255, 0.3);
    padding-left: 16px;
  }

  .ant-btn {
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 2px 6px rgba(24, 144, 255, 0.2);
    }
  }
}

.modal-action-button {
  transition: all 0.2s ease;
  border-radius: 4px;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}

.preview-modal {
  .ant-modal-header {
    background: linear-gradient(135deg, #f0f2f5 0%, #e6f7ff 100%);
    border-bottom: 1px solid #e8e8e8;
    padding: 16px 24px;
  }

  .ant-modal-content {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }

  .ant-modal-body {
    padding: 20px;
  }

  .ant-modal-footer {
    border-top: 1px solid #f0f0f0;
    padding: 12px 24px;
  }
}

// 确保表格行可见
.custom-table {
  // 所有表格内容居中
  .ant-table-thead > tr > th,
  .ant-table-tbody > tr > td {
    text-align: center !important;
  }

  .ant-table-thead > tr > th {
    background: @table-header-bg;
    color: @table-header-color;
    font-weight: 600; // 从500改为600，加粗表头文字
    padding: 12px 8px; // 减少左右内边距，避免挤压导致换行
    font-size: 14px;
    transition: background-color 0.3s;
    position: relative;
    border-bottom: 1px solid #e8f1ff;
    white-space: nowrap; // 确保不换行

    &:hover {
      background: #e6f7ff;
    }

    // 表头文字
    .table-header {
      color: @heading-color;
      font-weight: 600; // 从500改为600，加粗表头文字
      letter-spacing: 0.2px; // 减少字间距，避免文字占用太多空间
      white-space: nowrap; // 确保不换行
      display: flex; // 使用flex布局使图标和文字在一行
      align-items: center; // 垂直居中对齐
      justify-content: center; // 水平居中对齐
    }
  }

  .ant-table-tbody > tr {
    transition: all 0.3s ease;

    &:hover {
      transform: translateX(2px);
      box-shadow: -2px 0 0 #1890ff;
    }

    td {
      color: @text-color;
      padding: 12px 8px;
      font-size: 13px;
      text-align: center; // 确保单元格文字居中
    }

    &.completed-row {
      td {
        color: #447766; // 已完成行的文字使用柔和的绿灰色
      }

      &:hover {
        background-color: rgba(82, 196, 26, 0.15) !important;
      }
    }

    &.incomplete-row {
      td {
        color: #775544; // 未完成行的文字使用柔和的棕灰色
      }

      &:hover {
        background-color: rgba(250, 173, 20, 0.15) !important;
      }
    }

    // 表格单元格链接样式
    .table-cell-link {
      text-align: center;
      display: inline-block;
      width: 100%;
      color: #1890ff;
      transition: color 0.3s;

      &:hover {
        color: #40a9ff;
      }

      &.completed-text {
        color: #52c41a;

        &:hover {
          color: #73d13d;
        }
      }

      &.incomplete-text {
        color: #fa8c16;

        &:hover {
          color: #ffa940;
        }
      }
    }
  }

  .ant-table-placeholder {
    .ant-empty-normal {
      margin: 32px 0;
    }
  }
}

// 表格字体样式修复 - 提高优先级
.custom-table.ant-table-wrapper {
  // 确保表格内容字体生效
  .ant-table-tbody > tr > td {
    font-size: 13px !important;
    color: @text-color !important;
  }

  // 确保表头字体生效
  .ant-table-thead > tr > th {
    font-size: 14px !important;
    font-weight: 600 !important;
  }

  // 确保表格行文本样式生效
  .ant-table-tbody .completed-text {
    color: #389e0d !important;
    font-weight: 500 !important;
  }

  .ant-table-tbody .incomplete-text {
    color: #d46b08 !important;
    font-weight: 500 !important;
  }

  // 确保状态标签样式生效
  .status-tag {
    &.success {
      color: #389e0d !important;
      background: rgba(82, 196, 26, 0.1) !important;
      border: 1px solid rgba(82, 196, 26, 0.3) !important;
    }

    &.error {
      color: #d46b08 !important;
      background: rgba(250, 173, 20, 0.1) !important;
      border: 1px solid rgba(250, 173, 20, 0.3) !important;
    }
  }

  // 确保操作按钮样式生效
  .action-button {
    font-size: 13px !important;
    &.success {
      color: #389e0d !important;
    }
    &.warning {
      color: #d46b08 !important;
    }
  }
}

// 独立定义状态标签样式，确保不被覆盖
.ant-tag.status-tag {
  &.success {
    color: #389e0d !important;
    background: rgba(82, 196, 26, 0.1) !important;
    border-color: rgba(82, 196, 26, 0.3) !important;
  }

  &.error {
    color: #d46b08 !important;
    background: rgba(250, 173, 20, 0.1) !important;
    border-color: rgba(250, 173, 20, 0.3) !important;
  }

  .anticon {
    margin-right: 4px;
  }
}

// 自定义状态标签样式
.custom-status-tag {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 4px 8px !important;
  border-radius: 4px !important;
  font-weight: 500 !important;
  line-height: 1.5 !important;

  &.success {
    background-color: rgba(82, 196, 26, 0.1) !important;
    color: #389e0d !important;
    border: 1px solid rgba(82, 196, 26, 0.3) !important;
  }

  &.error {
    background-color: rgba(250, 173, 20, 0.1) !important;
    color: #d46b08 !important;
    border: 1px solid rgba(250, 173, 20, 0.3) !important;
  }

  .anticon {
    margin-right: 4px !important;
  }
}

// 自定义操作按钮样式
.custom-action-button {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  color: #1890ff !important;
  cursor: pointer !important;
  transition: all 0.3s !important;
  font-weight: 500 !important;

  &:hover {
    color: #40a9ff !important;
    text-decoration: underline !important;
  }

  .anticon {
    margin-right: 4px !important;
  }
}

// 当使用特定字体大小时，确保全局应用
body {
  // 状态标签和操作按钮的字体大小控制
  [data-font-size="12"] {
    font-size: 12px !important;
  }
  [data-font-size="13"] {
    font-size: 13px !important;
  }
  [data-font-size="14"] {
    font-size: 14px !important;
  }
  [data-font-size="15"] {
    font-size: 15px !important;
  }
  [data-font-size="16"] {
    font-size: 16px !important;
  }
}
