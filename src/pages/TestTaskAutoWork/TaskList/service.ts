import { request } from "umi";

export interface TableListItem {
  caseGroup?: string;
  caseAuthor?: string;
  caseName?: string;
  caseNo: string;
  pcsPost?: string | null;
  txNo?: string;
  uniqueKey?: string;
  autoDataFlag: number | string;
  assertFlag?: number | string;
  key?: string;
}

export interface TableListParams {
  current?: number;
  pageSize?: number;
  autoDataFlag?: number | null;
  caseAuthor?: string;
  caseGroup?: string;
  keyword?: string;
  txNo?: string;
  [key: string]: any;
}

export interface ResponseData<T> {
  code: number;
  total: number;
  pageSize?: number;
  data: T;
  msg?: string;
}

// 保存案例参数接口
export interface SaveCaseParams {
  uniqueKey: string;
  pcsPost: string;
  // 可以根据实际需要添加其他必要参数
  caseGroup?: string;
  caseAuthor?: string;
  caseName?: string;
  caseNo?: string;
  txNo?: string;
  autoDataFlag?: string | number;
  assertFlag?: string | number;
}

// API前缀，根据实际情况修改
const API_PREFIX = "/api/testtool/testTaskData";

/**
 * 保存案例数据
 * @param params 保存参数，包含uniqueKey和pcsPost
 * @returns 保存结果
 */
export async function saveCase(params: SaveCaseParams) {
  return request("/api/testtool/savePcsSendRecord", {
    method: "POST",
    data: params,
  });
}

// 获取表格数据
export async function getTableList(params: TableListParams) {
  try {
    console.log("[DEBUG] service.getTableList 开始请求, 参数:", params);

    // 尝试从API获取数据
    const response = await request<ResponseData<TableListItem[]>>(
      `${API_PREFIX}/getlist`,
      {
        method: "GET",
        params,
      },
    );

    console.log("[DEBUG] API请求成功:", response);
    return response;
  } catch (error) {
    console.error("[DEBUG] API请求失败:", error);

    // 生成模拟数据作为备用
    console.log("[DEBUG] 返回模拟数据");
    return {
      code: 200,
      msg: "模拟数据",
      total: 5,
      data: [
        {
          caseGroup: "0327",
          caseAuthor: "张三",
          caseName: "测试任务1",
          caseNo: "TEST001",
          txNo: "API001",
          autoDataFlag: 1,
          assertFlag: 1,
          pcsPost: JSON.stringify({ test: "data1" }),
        },
        {
          caseGroup: "0327",
          caseAuthor: "李四",
          caseName: "测试任务2",
          caseNo: "TEST002",
          txNo: "API002",
          autoDataFlag: 0,
          assertFlag: 0,
          pcsPost: JSON.stringify({ test: "data2" }),
        },
        {
          caseGroup: "0328",
          caseAuthor: "王五",
          caseName: "测试任务3",
          caseNo: "TEST003",
          txNo: "API003",
          autoDataFlag: 1,
          assertFlag: 0,
          pcsPost: JSON.stringify({ test: "data3" }),
        },
        {
          caseGroup: "0329",
          caseAuthor: "赵六",
          caseName: "测试任务4",
          caseNo: "TEST004",
          txNo: "API004",
          autoDataFlag: 0,
          assertFlag: 1,
          pcsPost: null,
        },
        {
          caseGroup: "0330",
          caseAuthor: "钱七",
          caseName: "测试任务5",
          caseNo: "TEST005",
          txNo: "API005",
          autoDataFlag: 1,
          assertFlag: 1,
          pcsPost: '{"invalidJson": true',
        },
      ],
    };
  }
}
