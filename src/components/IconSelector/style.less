.anticonsList {
  margin: 6px 0;
  overflow: hidden;
  direction: ltr;
  list-style: none;
  li {
    position: relative;
    float: left;
    width: 16.66%;
    height: 60px;
    margin: 30px 0;
    padding: 4px 0 0;
    overflow: hidden;
    color: #555;
    text-align: center;
    list-style: none;
    background-color: inherit;
    border-radius: 3px;
    cursor: pointer;
    transition: color 0.3s ease-in-out, background-color 0.3s ease-in-out;
    .rtl & {
      margin: 2px 0;
      padding: 3px 0 0;
    }
    .anticon {
      margin: 2px 0 3px;
      font-size: 26px;
      transition: transform 0.3s ease-in-out;
      will-change: transform;
    }

    .anticonTitle {
      display: block;
      font-family: '<PERSON><PERSON>', Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
      white-space: nowrap;
      text-align: center;
      transform: scale(0.83);
      .ant-badge {
        transition: color 0.3s ease-in-out;
      }
    }

    &:hover {
      color: #fff;
      background-color: @primary-color;
      .anticon {
        transform: scale(1.4);
      }
      .ant-badge {
        color: #fff;
      }
    }

    &.TwoTone:hover {
      background-color: #8ecafe;
    }

    &.copied:hover {
      color: rgba(255, 255, 255, 0.2);
    }

    &::after {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      color: #fff;
      line-height: 110px;
      text-align: center;
      opacity: 0;
      transition: all 0.3s cubic-bezier(0.18, 0.89, 0.32, 1.28);
      content: 'Copied!';
    }

    &.copied::after {
      top: -10px;
      opacity: 1;
    }
  }
}
