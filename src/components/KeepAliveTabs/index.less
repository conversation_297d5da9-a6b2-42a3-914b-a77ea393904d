@import '~antd/es/style/themes/default.less';

.tabs {
  position: fixed;
  top: @layout-trigger-height;
  width: 100%;
  z-index: 2;
  background: #fff;
  height: 38px;
  // margin-left: -24px;
  // width: calc(100% - 256px);
  // width: 100%;
  // margin-top: 5px;
  display: flex;
  justify-content: space-between;
  border-bottom: solid;
  border-width: 1px;
  border-color: @border-color-base;

  .tabList {
    display: flex;
    margin-top: 2px;
    flex: 1;
    overflow: hidden;

    .linkTabs {
      display: flex;
      flex: 1;
      // transform: translate(-50px, 0);
      transition: all 0.5s;
    }
  }

  .tabLeftMenu {
    margin-top: 5px;
    padding: 0 20px 0 5px;
    display: flex;
    align-items: center;
    height: 30px;
    z-index: 12;
    width: 100px;

    .tabMore {
      color: @primary-color;
      font-weight: bold;
    }
  }

  .boxShadow {
    box-shadow: -7px 0 7px -7px #5e5e5e; //@tab-left-menu;
  }

  &:first-child {
    &::before {
      display: none;
    }
  }

  :global {
    .ant-dropdown-link {
      line-height: 30px;
    }
  }
}