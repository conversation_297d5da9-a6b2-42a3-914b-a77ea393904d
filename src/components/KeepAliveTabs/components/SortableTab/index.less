@import '~antd/es/style/themes/default.less';
.tabItem {
  z-index: 20;
  padding: 0 20px;
  white-space: nowrap;
  text-overflow: ellipsis;
  background-color: #fff;
  border-style: solid;
  border-width: 1px;
  border-color: @border-color-base;
  border-radius: @border-radius-base;
  // border-top-left-radius: @border-radius-base;
  // border-top-right-radius: @border-radius-base;
  overflow: hidden;
  height: 30px;
  line-height: 30px;
  text-align: center;
  margin-left: 5px;
  margin-top: 2px;
  width: 120px;
  position: relative;
  cursor: pointer;
  font-size: @font-size-sm;
  color: @text-color;

  &:hover {
    // background: @layout-body-background;
    color: @primary-color-hover;
    font-weight: bolder;

    &::before {
      display: none;
    }

    // display: block;
    & + .tabItem {
      &::before {
        display: none;
      }
    }
  }


  // &:before {
  //     content: '';
  //     display: block;
  //     position: absolute;
  //     top: 5px;
  //     bottom: 5px;
  //     left: 0;
  //     width: 1px;
  //     border-left: 1px solid #d9d9d9;
  //     z-index: 10;
  // }

  .closeIcon {
    // display: none;
    position: absolute;
    right: 5px;
    top: 5px;
    width: 15px;
    height: 15px;
    border-radius: 50%;
    color: rgba(0, 0, 0, 0.45);
    transform: scale(0.8);
    transition: 300ms;
    display: flex;
    justify-content: center;
    align-items: center;

    &:hover {
      background: #ccc; //@close-icon-hover;
    }
  }
}

.active {
  color: @primary-color;
  font-weight: bolder;

  &::before {
    display: none;
  }

  & + .tabItem {
    &::before {
      display: none;
    }
  }
}