@import '~antd/es/style/themes/default.less';

@pro-header-hover-bg: rgba(0, 0, 0, 0.025);

.menu {
  :global(.anticon) {
    margin-right: 8px;
  }
  :global(.ant-dropdown-menu-item) {
    min-width: 160px;
  }
  :global(.ant-dropdown-menu) {
    background-color: #f0f0f0;
  }
}

.right {
  display: flex;
  float: right;
  height: 48px;
  margin-left: auto;
  overflow: hidden;
  .action {
    display: flex;
    align-items: center;
    height: 48px;
    padding: 0 12px;
    cursor: pointer;
    transition: all 0.3s;
    > span {
      vertical-align: middle;
    }
    &:hover {
      background: @pro-header-hover-bg;
    }
    &:global(.opened) {
      background: @pro-header-hover-bg;
    }
  }
  .search {
    padding: 0 12px;
    &:hover {
      background: transparent;
    }
  }
  .account {
    .avatar {
      margin-right: 8px;
      color: @primary-color;
      vertical-align: top;
      // background: rgba(255, 255, 255, 0.85);
      background: rgba(66, 173, 45, 0.85);
    }
  }
}

.dark {
  .action {
    &:hover {
      background: #252a3d;
    }
    &:global(.opened) {
      background: #252a3d;
    }
  }
}

@media only screen and (max-width: @screen-md) {
  :global(.ant-divider-vertical) {
    vertical-align: unset;
  }
  .name {
    display: none;
  }
  .right {
    position: absolute;
    top: 0;
    right: 12px;
    .account {
      .avatar {
        margin-right: 0;
      }
    }
    .search {
      display: none;
    }
  }
}


// 齿轮css
.action {
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  padding: 8px;
  text-decoration: none;
  color: #1b191b;
  min-width: 80px; // 设置最小宽度，防止挤压
}

.envText {
  font-size: 13px;
  font-weight: bold;
  transition: all 0.3s ease;
  color: #000000;
  margin-right: 8px; // 添加固定的右侧间距
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0; // 防止文字被压缩
}

.iconWrapper {
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  transition: transform 0.3s ease;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0; // 防止图标被压缩
}

.iconWrapper :global(.anticon) {
  font-size: 20px;
}
