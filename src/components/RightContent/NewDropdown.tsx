import React, { useState } from 'react';
import { SettingOutlined } from '@ant-design/icons';
import { Dropdown, Menu, MenuProps } from 'antd';
import styles from './index.less';


const environments = ['DEV1', 'TEST','DEVS', 'DEV2', 'DEV5', 'SITA', 'SITB', 'T1', 'T2', 'T3', 'T4', 'ET', 'PREPROD'];

const NewDropdown: React.FC = () => {
  const [currentEnv, setCurrentEnv] = useState(localStorage.getItem('currentEnv') || 'DEV1');
  const [rotation, setRotation] = useState(0);

  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    // 只更新齿轮的旋转角度
    setRotation(prev => prev + 45);
  };

  const handleMenuClick: MenuProps['onClick'] = (e) => {
    setCurrentEnv(e.key);
    localStorage.setItem('currentEnv', e.key);
  };

  const menu = (
    <Menu onClick={handleMenuClick}>
      {environments.map(env => (
        <Menu.Item key={env}>{env}</Menu.Item>
      ))}
    </Menu>
  );

  return (
    <Dropdown overlay={menu} trigger={['click']} placement="bottomLeft">
      <a
        className={styles.action}
        onClick={handleClick}
        href="#"
      >
        <span className={styles.envText}>{currentEnv}</span>
        <span
          className={styles.iconWrapper}
          style={{ transform: `rotate(${rotation}deg)` }}
        >
          <SettingOutlined />
        </span>
      </a>
    </Dropdown>
  );
};

export default NewDropdown;
