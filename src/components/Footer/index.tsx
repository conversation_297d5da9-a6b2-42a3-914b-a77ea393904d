import { useIntl } from 'umi';
import { EnvironmentOutlined } from '@ant-design/icons';
import { DefaultFooter } from '@ant-design/pro-layout';

export default () => {
  const intl = useIntl();
  const defaultMessage = intl.formatMessage({
    id: 'app.copyright.produced',
    defaultMessage: '集成测试组深度研发',
  });

  const currentYear = new Date().getFullYear();

  return (
    <DefaultFooter
      copyright={`${currentYear} 集成测试组基于Antd深度研发`}
      links={[
        {
          key: 'Ant Design Pro',
          title: '个人存款',
          // href: 'https://pro.ant.design',
          // blankTarget: false,
          href: 'javascript:void(0)',  // 使用 javascript:void(0) 阻止跳转
        },
        {
          key: 'environment',
          title: <EnvironmentOutlined />,
          // href: 'https://github.com/ant-design/ant-design-pro',
          // blankTarget: true,
          href: 'javascript:void(0)',
        },
        {
          key: 'Ant Design',
          title: '集成测试组',
          // href: 'https://ant.design',
          // blankTarget: true,
          href: 'javascript:void(0)',
        },
      ]}
    />
  );
};
