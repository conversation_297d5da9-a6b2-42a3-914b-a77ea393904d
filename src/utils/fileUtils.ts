/**
 * 文件操作工具类
 * 提供文件分片、哈希计算等功能
 */

/**
 * 文件分片信息
 */
export interface FileChunk {
  chunk: Blob;
  index: number;
}

/**
 * 文件分片配置
 */
export interface ChunkOptions {
  chunkSize?: number; // 分片大小，默认 1MB
  hashCalculation?: boolean; // 是否计算文件哈希值
}

/**
 * 分片文件结果
 */
export interface SliceFileResult {
  chunks: FileChunk[];
  fileHash: string;
  fileName: string;
  fileSize: number;
  totalChunks: number;
}

/**
 * 计算文件哈希
 * @param file 文件对象
 * @returns 文件哈希值
 */
export const calculateFileHash = (file: File): Promise<string> => {
  return new Promise((resolve) => {
    try {
      // 使用文件名+大小+最后修改时间组合生成唯一性较强的哈希值
      const lastModified = file.lastModified ? new Date(file.lastModified).toISOString() : '';
      const hash = `${file.name}-${file.size}-${lastModified}`;
      resolve(hash);
    } catch (error) {
      console.error('计算文件哈希失败:', error);
      // 如果哈希计算失败，使用备用方式
      const fallbackHash = `${file.name}-${file.size}-${Date.now()}`;
      resolve(fallbackHash);
    }
  });
};

/**
 * 将文件分片并计算哈希值
 * @param file 文件对象
 * @param options 分片配置选项
 * @returns 分片结果
 */
export const sliceFile = async (
  file: File,
  options?: ChunkOptions
): Promise<SliceFileResult> => {
  const chunkSize = options?.chunkSize || 1024 * 1024; // 默认1MB
  const chunks: FileChunk[] = [];
  const fileSize = file.size;
  const totalChunks = Math.ceil(fileSize / chunkSize);

  // 生成分片
  for (let i = 0; i < totalChunks; i++) {
    const start = i * chunkSize;
    const end = Math.min(fileSize, start + chunkSize);
    const chunk = file.slice(start, end);
    chunks.push({ chunk, index: i });
  }

  // 计算文件哈希
  const fileHash = await calculateFileHash(file);

  return {
    chunks,
    fileHash,
    fileName: file.name,  // 确保返回文件名
    fileSize,
    totalChunks,
  };
};

/**
 * 判断是否为大文件（需要分片上传）
 * @param fileSize 文件大小（字节）
 * @param threshold 大小阈值（字节），默认 10MB
 * @returns 是否为大文件
 */
export const isLargeFile = (fileSize: number, threshold = 10 * 1024 * 1024): boolean => {
  return fileSize > threshold;
};

/**
 * 格式化文件大小
 * @param size 文件大小（字节）
 * @returns 格式化后的文件大小
 */
export const formatFileSize = (size?: number): string => {
  if (!size) return '-';
  
  const units = ['B', 'KB', 'MB', 'GB', 'TB'];
  let formattedSize = size;
  let unitIndex = 0;
  
  while (formattedSize >= 1024 && unitIndex < units.length - 1) {
    formattedSize /= 1024;
    unitIndex++;
  }
  
  return `${formattedSize.toFixed(2)} ${units[unitIndex]}`;
};

/**
 * 安全解码文件名（处理中文等非ASCII字符）
 * @param fileName 文件名
 * @returns 解码后的文件名
 */
export const safeDecodeFileName = (fileName: string): string => {
  if (!fileName) return '';
  
  try {
    // 尝试 UTF-8 解码
    return decodeURIComponent(fileName);
  } catch (error) {
    try {
      // 尝试 escape/unescape 解码（适用于某些特殊编码）
      return decodeURIComponent(escape(fileName));
    } catch (innerError) {
      console.error('文件名解码失败:', fileName, innerError);
      // 解码失败时返回原始文件名
      return fileName;
    }
  }
}; 