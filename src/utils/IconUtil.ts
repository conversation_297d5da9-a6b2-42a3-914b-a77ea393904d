import * as AntdIcons from '@ant-design/icons';
import React from 'react';

/**
 * 处理图标名称，尝试不同的后缀
 */
const getIconWithSuffix = (name: string) => {
  // 尝试原始名称
  if (AntdIcons[name]) {
    return AntdIcons[name];
  }

  // 尝试添加 Outlined 后缀
  const outlinedName = `${name}Outlined`;
  if (AntdIcons[outlinedName]) {
    return AntdIcons[outlinedName];
  }

  // 尝试添加 Filled 后缀
  const filledName = `${name}Filled`;
  if (AntdIcons[filledName]) {
    return AntdIcons[filledName];
  }

  // 尝试添加 TwoTone 后缀
  const twoToneName = `${name}TwoTone`;
  if (AntdIcons[twoToneName]) {
    return AntdIcons[twoToneName];
  }

  return null;
};

export function getIcon(name: string): React.ReactNode | string {
  if (!name) {
    return '';
  }

  const IconComponent = getIconWithSuffix(name);
  return IconComponent || '';
}

export function createIcon(icon: string | any): React.ReactNode | string {
  if (!icon) {
    return '';
  }

  if (typeof icon === 'object') {
    return icon;
  }

  const IconComponent = getIconWithSuffix(icon);
  if (IconComponent) {
    return React.createElement(IconComponent);
  }

  return '';
}
