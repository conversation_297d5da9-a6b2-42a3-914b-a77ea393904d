export const GENERAL_FLAP_MAP: Record<string, string> = {
  '0': '否',
  '1': '是',
}

export const MEDIUM_TP_CD_MAP: Record<string, string> = {
  '0201': '绿卡',
  '0202': '绿卡通',
  '0203': '绿卡通副卡',
  '0204': '小额支付卡',
  '0301': '活期存折',
  '0302': '定期零整存折',
  '0303': '定期整零存折',
  '0304': '存本取息存折',
  '0305': '本外币活期一本通',
  '0306': '本外币定期一本通',
  '0309': '本币定期一本通',
  '0401': '整存整取存单',
  '0402': '定活两便存单',
  '0403': '通知存款存单',
  '0404': '整存整取特种存单'
};

export const CATE_FLAG_CD_MAP: Record<string, string> = {
  '01': '活期',
  '02': '定期',
}

export const CARD_MEDIUM_CODE_MAP: Record<string, string> = {
  '1': '磁条卡',
  '2': '复合卡',
  '3': '单芯片卡',
  '4': '虚拟卡',
}

export const CARD_KIND_CD_MAP: Record<string, string> = {
  '01': '储蓄卡',
  '02': '联名卡(储蓄卡)',
  '03': '联名卡(绿卡通)',
  '04': '认同卡(绿卡通)',
  '05': '认同卡(储蓄卡)',
  '06': '绿卡通卡',
  '07': '绿卡通副卡',
  '08': '小额支付卡',
  '09': '万事达卡',
  '13': '13-绿卡通(万事网联)',
}

export const SAV_TYPE_MCLASS_CODE_MAP: Record<string, string> = {
  '010100': '储蓄活期',
  '010200': '结算活期',
  '020100': '保值储蓄',
  '020200': '整存整取',
  '020300': '整存零取',
  '020400': '零存整取',
  '020500': '存本取息',
  '020600': '定额定期',
  '020800': '大额存单',
  '020700': '定期协议利率存款',
  '030100': '不固定定活两便',
  '030200': '固定定活两便',
  '040100': '老通知',
  '040200': '新通知',
  '050100': '一本通',
  '070100': '结构性存款',
  '990100': '小额支付账户',
  '990200': '行业应用子账户（计息）',
  '990300': '行业应用子账户（不计息）',
  '990400': '行业应用子账户（分段）',
  '999900': '其它',
}

export const CURR_CODE_MAP: Record<string, string> = {
  '036': '澳大利亚元',
  '124': '加元',
  '344': '香港元',
  '392': '日元',
  '826': '英镑',
  '840': '美元',
  '978': '欧元（EUR）',
  '156': '人民币元',
}

export const SIGN_CHN_KIND_CD_MAP: Record<string, string> = {
  '01': '网点柜面',
  '10': '网上银行',
  '12': '个人网银',
  '13': '电视银行',
  '14': '电话银行',
  '15': '手机银行',
  '16': '企业网银',
  '17': '自助设备',
  '18': 'POS',
  '20': '超级网银',
  '21': '大小额支付',
  '22': '银联前置',
  '24': '管理端',
  '25': '交易端',
  '26': '商易通',
  '27': '助农通',
  '29': '外部系统',
  '30': '系统自动',
  '31': '电子汇兑系统',
  '32': '理财规划终端',
  '34': '网汇通',
  '35': '同城支付',
  '36': '移动终端-TSM（可信服务管理）',
  '37': '移动终端-移动展业',
  '38': '直销银行',
  '39': '短信',
  '40': '专属APP',
  '41': '第三方线上渠道',
  '43': '智能柜员机（ITM）',
  '42': '国际支付前置',
  '44': '邮储经营',
  '45': '银银前置系统',
  '46': 'U链供应链',
  '47': '油料保障结算系统',
  '48': '银企直联',
  '91': '微信银行',
  '99': '其他',
}

export const DP_CONTR_TP_CD_MAP: Record<string, string> = {
  '1001': '人民币活期储蓄合约',
  '1002': '人民币活期结算合约',
  '1003': '外币活期储蓄合约',
  '1004': '外币活期结算合约',
  '1005': '本外币合一结算合约',
  '2001': '整存整取储蓄存款合约',
  '2002': '整存整取协议存款合约',
  '2003': '提前付息定期存款合约',
  '2004': '定活两便储蓄存款合约',
  '2005': '整存零取储蓄存款合约',
  '2006': '存本取息储蓄存款合约',
  '2007': '零存整取储蓄存款合约',
  '2008': '通知存款合约',
  '2009': '结构性存款合约',
  '2010': '递增计息合约',
  '2011': '梦想加邮站合约',
  '2012': '大额存单合约',
  '2013': '礼仪存单合约',
  '2014': '邮智存合约',
  '2015': '月月存账户',
  '3001': '行业应用子账户合约',
  '3002': '电子现金账户合约',
  '4001': '副卡合约',
  '4002': '映射卡合约',
  '4003': '本外币定期一本通合约',
}

export const PERS_DEP_ACC_KIND_CD_MAP: Record<string, string> = {
  '0': '无关',
  '1': 'Ⅰ类户',
  '2': 'Ⅱ类户',
  '3': 'Ⅲ类户',
}

export const SPEC_CONTR_TP_CD_MAP: Record<string, string> = {
  '0000': '其他',
  '9001': '医保账户合约',
  '9002': '居家养老账户合约（一家通）',
  '9003': '医保子账户合约',
  '9004': '居家养老账户合约（金晖卡）',
  '9005': '保证金账户合约',
  '9999': '电子现金账户合约',
}

export const MAIN_CONTR_FLAP_MAP: Record<string, string> = {
  '0': '否',
  '1': '是',
}

export const PERS_DEP_ACC_TP_CD_MAP: Record<string, string> = {
  '1001': '人民币活期储蓄合约',
  '1002': '人民币活期结算合约',
  '1003': '外币活期储蓄合约',
  '1004': '外币活期结算合约',
  '1005': '本外币合一结算合约',
  '2001': '整存整取储蓄存款合约',
  '2002': '整存整取协议存款合约',
  '2003': '提前付息定期存款合约',
  '2004': '定活两便储蓄存款合约',
  '2005': '整存零取储蓄存款合约',
  '2006': '存本取息储蓄存款合约',
  '2007': '零存整取储蓄存款合约',
  '2008': '通知存款合约',
  '2009': '结构性存款合约',
  '2010': '递增计息合约',
  '2011': '梦想加邮站合约',
  '2012': '大额存单合约',
  '2013': '礼仪存单合约',
  '2014': '邮智存合约',
  '3001': '行业应用子账户合约',
  '3002': '电子现金账户合约',
  '4001': '副卡合约',
  '4002': '映射卡合约',
  '4003': '本外币定期一本通合约',
}

export const CASH_EXG_VATG_CD_MAP: Record<string, string> = {
  '2': '钞',
  '3': '汇',
}

export const TERM_UNIT_CODE_MAP: Record<string, string> = {
  '1': '日',
  '2': '周',
  '3': '月',
  '4': '季',
  '5': '年',
  '6': '单次',
}

export const EXEC_CYC_VAL_MAP: Record<string, string> = {
  '1': '日',
  '2': '周',
  '3': '月',
  '4': '季',
  '5': '年',
}

export const REL_CATEG_FG_CD_MAP: Record<string, string> = {
  '01': '活期',
  '02': '定期',
}

export const PER_CERT_TP_CD_MAP: Record<string, string> = {
  '1010': '居民身份证',
  '1011': '临时居民身份证',
  '1020': '军人身份证件',
  '1021': '士兵证',
  '1022': '军官证',
  '1023': '文职干部证',
  '1024': '军官退休证',
  '1025': '文职干部退休证',
  '1030': '武警身份证件',
  '1031': '武警士兵证',
  '1032': '警官证',
  '1033': '武警文职干部证',
  '1034': '武警军官退休证',
  '1035': '武警文职干部退休证',
  '1040': '户口簿',
  '1050': '中国护照',
  '1051': '外国护照',
  '1060': '学生证',
  '1070': '港澳居民来往内地通行证',
  '1071': '往来港澳通行证',
  '1080': '台湾居民来往大陆通行证',
  '1090': '执行公务证',
  '1100': '机动车驾驶证',
  '1110': '社会保障卡',
  '1120': '外国人居留证',
  '1121': '外国人永久居留证',
  '1130': '旅行证件',
  '1140': '香港居民身份证',
  '1150': '澳门居民身份证',
  '1160': '台湾居民身份证',
  '1170': '边民证',
  '1180': '港澳台居民居住证',
  '1181': '港澳居民居住证',
  '1182': '台湾居民居住证',
  '1190': '外国身份证',
  '1998': '其他（原98类）',
  '1999': '其他证件（个人）',
}

export const NINE_ITEM_INFO_INCOMP_GLG_MAP: Record<string, string> = {
  '0': '不全',
  '1': '全',
}


// 获取介质类型显示文本
export const getMediumTpCdText = (code: string): string => {
  return code ? `${code}-${MEDIUM_TP_CD_MAP[code] || '未知类型'}` : '';
};

// 获取类别标识代码
export const getCateFlagCdText = (code: string): string => {
  return code ? `${code}-${CATE_FLAG_CD_MAP[code] || '未知类型'}` : '';
};

// 获取卡介质代码
export const getCardMediumCodeText = (code: string): string => {
  return code ? `${code}-${CARD_MEDIUM_CODE_MAP[code] || '未知类型'}` : '';
};

// 获取卡介质代码
export const getCardKinCdText = (code: string): string => {
  return code ? `${code}-${CARD_KIND_CD_MAP[code] || '未知类型'}` : '';
};

// 获取储种中类代码
export const getSavTypeMclassCodeText = (code: string): string => {
  return code ? `${code}-${SAV_TYPE_MCLASS_CODE_MAP[code] || '未知类型'}` : '';
};

// 获取币种代码
export const getCurrCodeText = (code: string): string => {
  return code ? `${code}-${CURR_CODE_MAP[code] || code }` : '';
};

// 获取签约渠道种类代码
export const getSignChnKindCdText = (code: string): string => {
  return code ? `${code}-${SIGN_CHN_KIND_CD_MAP[code] || '未知类型'}` : '';
};

// 获取个人存款合约类型代码
export const getDpContrTpCdText = (code: string): string => {
  return code ? `${code}-${DP_CONTR_TP_CD_MAP[code] || '未知类型'}` : '';
};

// 获取个人存款账户种类代码
export const getPersDepAccKindCdText = (code: string): string => {
  return code ? `${code}-${PERS_DEP_ACC_KIND_CD_MAP[code] || code }` : '';
};

// 获取特殊合约类型代码
export const getSpecContrTpCdText = (code: string): string => {
  return code ? `${code}-${SPEC_CONTR_TP_CD_MAP[code] || '未知类型'}` : '';
};

// 获取主合约标志
export const getMainContrFlagText = (code: string): string => {
  return code ? `${code}-${MAIN_CONTR_FLAP_MAP[code] || '未知类型'}` : '';
};

// 获取个人存款账户类型代码
export const getPersDepAccTpCdText = (code: string): string => {
  return code ? `${code}-${PERS_DEP_ACC_TP_CD_MAP[code] || '未知类型'}` : '';
}

// 获取钞汇类别代码
export const getCashExgVatgCdText = (code: string): string => {
  return code ? `${code}-${CASH_EXG_VATG_CD_MAP[code] || '未知类型'}` : '';
}

// 获取期限单位代码
export const getTermUnitCodeText = (code: string): string => {
  return code ? `${code}-${TERM_UNIT_CODE_MAP[code] || '未知类型'}` : '';
}

// 获取执行周期值
export const getExecCycValText = (code: string): string => {
  return code ? `${code}-${EXEC_CYC_VAL_MAP[code] || '未知类型'}` : '';
}

// 获取关联类别标识代码
export const getRelCategFgCdText = (code: string): string => {
  return code ? `${code}-${REL_CATEG_FG_CD_MAP[code] || '未知类型'}` : '';
}

// 获取证件类型
export const getPerCertTpCdText = (code: string): string => {
  return code ? `${code}::${PER_CERT_TP_CD_MAP[code] || '未知类型'}` : '';
}

// 获取九项信息不全标识
export const getNineItemInfoIncompGlagText = (code: string | null | undefined): string => {
  return code ? `${code}-${NINE_ITEM_INFO_INCOMP_GLG_MAP[code] || '未知类型'}` : '';
}

// 通用标识位
export const getGeneralFlagText = (code: string | null | undefined): string => {
  return code ? `${code}-${GENERAL_FLAP_MAP[code] || '未知类型'}` : '';
}

