/**
 * 上传存储工具类
 * 用于记录上传进度和支持断点续传
 */

import { UploadProgress } from '@/pages/autotest/autotestsvndata/data';

// 存储键前缀
const STORAGE_KEY_PREFIX = 'svn_upload_';

/**
 * 保存上传进度信息
 * @param fileHash 文件哈希值
 * @param progress 上传进度信息
 */
export const saveUploadProgress = (fileHash: string, progress: UploadProgress): void => {
  try {
    // 带有过期时间的进度信息
    const dataWithExpiry = {
      data: progress,
      expiry: Date.now() + 24 * 60 * 60 * 1000, // 24小时后过期
    };
    
    localStorage.setItem(`${STORAGE_KEY_PREFIX}${fileHash}`, JSON.stringify(dataWithExpiry));
  } catch (error) {
    console.error('保存上传进度失败:', error);
  }
};

/**
 * 获取上传进度信息
 * @param fileHash 文件哈希值
 * @returns 上传进度信息，不存在或已过期则返回null
 */
export const getUploadProgress = (fileHash: string): UploadProgress | null => {
  try {
    const storedData = localStorage.getItem(`${STORAGE_KEY_PREFIX}${fileHash}`);
    
    if (!storedData) {
      return null;
    }
    
    const { data, expiry } = JSON.parse(storedData);
    
    // 检查是否过期
    if (expiry && Date.now() > expiry) {
      // 已过期，删除该条目
      removeUploadProgress(fileHash);
      return null;
    }
    
    return data as UploadProgress;
  } catch (error) {
    console.error('获取上传进度失败:', error);
    return null;
  }
};

/**
 * 更新分片进度
 * @param fileHash 文件哈希值
 * @param chunkIndex 分片索引
 * @param percentage 完成百分比
 */
export const updateChunkProgress = (fileHash: string, chunkIndex: number, percentage: number): void => {
  try {
    const progress = getUploadProgress(fileHash);
    
    if (!progress) {
      return;
    }
    
    // 更新分片进度
    progress.chunkProgress[chunkIndex] = percentage;
    
    // 如果分片完成，添加到已上传分片列表
    if (percentage === 100 && !progress.uploadedChunks.includes(chunkIndex)) {
      progress.uploadedChunks.push(chunkIndex);
    }
    
    // 保存更新后的进度
    saveUploadProgress(fileHash, progress);
  } catch (error) {
    console.error('更新分片进度失败:', error);
  }
};

/**
 * 添加已上传分片
 * @param fileHash 文件哈希值
 * @param chunkIndex 分片索引
 */
export const addUploadedChunk = (fileHash: string, chunkIndex: number): void => {
  try {
    const progress = getUploadProgress(fileHash);
    
    if (!progress) {
      return;
    }
    
    // 添加到已上传分片列表
    if (!progress.uploadedChunks.includes(chunkIndex)) {
      progress.uploadedChunks.push(chunkIndex);
      progress.chunkProgress[chunkIndex] = 100;
    }
    
    // 保存更新后的进度
    saveUploadProgress(fileHash, progress);
  } catch (error) {
    console.error('添加已上传分片失败:', error);
  }
};

/**
 * 创建新的上传进度记录
 * @param fileHash 文件哈希值
 * @param fileName 文件名
 * @param uploadedChunks 已上传的分片索引数组
 */
export const createUploadProgress = (fileHash: string, fileName: string, uploadedChunks: number[] = []): UploadProgress => {
  const progress: UploadProgress = {
    fileHash,
    fileName,
    chunkProgress: {},
    uploadedChunks: [...uploadedChunks],
  };
  
  // 初始化已上传分片进度为100%
  uploadedChunks.forEach(index => {
    progress.chunkProgress[index] = 100;
  });
  
  // 保存进度
  saveUploadProgress(fileHash, progress);
  
  return progress;
};

/**
 * 移除上传进度信息
 * @param fileHash 文件哈希值
 */
export const removeUploadProgress = (fileHash: string): void => {
  try {
    localStorage.removeItem(`${STORAGE_KEY_PREFIX}${fileHash}`);
  } catch (error) {
    console.error('移除上传进度失败:', error);
  }
};

/**
 * 清理过期的上传进度
 */
export const cleanExpiredProgress = (): void => {
  try {
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      
      if (key && key.startsWith(STORAGE_KEY_PREFIX)) {
        const storedData = localStorage.getItem(key);
        
        if (storedData) {
          const { expiry } = JSON.parse(storedData);
          
          if (expiry && Date.now() > expiry) {
            localStorage.removeItem(key);
          }
        }
      }
    }
  } catch (error) {
    console.error('清理过期上传进度失败:', error);
  }
};

// 定期清理过期的上传进度
const cleanup = () => {
  cleanExpiredProgress();
  // 每小时清理一次
  setTimeout(cleanup, 60 * 60 * 1000);
};

// 启动清理任务
cleanup(); 