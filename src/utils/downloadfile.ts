import request from '@/utils/request';

const mimeMap = {
  xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  zip: 'application/zip',
};

export function downLoadZip(url: string) {
  request(url, {
    method: 'GET',
    responseType: 'blob',
    getResponse: true,
  }).then((res) => {
    resolveBlob(res, mimeMap.zip);
  });
}

/**
 * 解析blob响应内容并下载
 * @param {*} res blob响应内容
 * @param {String} mimeType MIME类型
 */
export function resolveBlob(res: any, mimeType: string) {
  const aLink = document.createElement('a');
  const blob = new Blob([res.data], { type: mimeType });
  // //从response的headers中获取filename, 后端response.setHeader("Content-disposition", "attachment; filename=xxxx.docx") 设置的文件名;
  const patt = new RegExp('filename=([^;]+\\.[^\\.;]+);*');
  const contentDisposition = decodeURI(res.response.headers.get('content-disposition'));
  const result = patt.exec(contentDisposition);
  let fileName = result ? result[1] : 'file';
  fileName = fileName.replace(/"/g, '');
  aLink.style.display = 'none';
  aLink.href = URL.createObjectURL(blob);
  aLink.setAttribute('download', fileName); // 设置下载文件名称
  document.body.appendChild(aLink);
  aLink.click();
  URL.revokeObjectURL(aLink.href); // 清除引用
  document.body.removeChild(aLink);
}

export async function downLoadXlsx(url: string, params: any, fileName: string) {
  return request(url, {
    ...params,
    method: 'POST',
    responseType: 'blob',
    getResponse: true,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
      'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    },
  }).then((res) => {
    const blob = new Blob([res.data], { type: mimeMap.xlsx });
    const aLink = document.createElement('a');
    aLink.style.display = 'none';
    aLink.href = URL.createObjectURL(blob);
    aLink.setAttribute('download', fileName);
    document.body.appendChild(aLink);
    aLink.click();
    URL.revokeObjectURL(aLink.href);
    document.body.removeChild(aLink);
  });
}
