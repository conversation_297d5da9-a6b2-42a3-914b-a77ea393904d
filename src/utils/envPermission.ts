export function matchEnvPermission (permissions: string[]|undefined, value: string): boolean {
    if(permissions === undefined)
      return false;
    return matchPerm(permissions, value);
  }
  
  // /**
  //  * 字符权限校验
  //  * @param {Array} value 校验值
  //  * @returns {Boolean}
  //  */
  export function matchPerms (permissions: string[], value: string[]) {
    if (value && value instanceof Array && value.length > 0) {
      const permissionDatas = value;
      const all_permission = '*';
      const hasPermission = permissions.some((permission) => {
        return all_permission === permission || permissionDatas.includes(permission);
      });
      if (!hasPermission) {
        return false;
      }
      return true;
    }
    console.error(`need env permissions! Like checkPermi="['T1','T2]"`);
    return false;
  }
  
  export function matchPerm (permissions: string[], value: string) {
    if (value && value.length > 0) {
      const permissionDatas = value;
      const all_permission = '*';
      const hasPermission = permissions.some((permission) => {
        return all_permission === permission || permissionDatas === permission;
      });
      if (!hasPermission) {
        return false;
      }
      return true;
    }
    console.error(`need env permissions! Like checkPermi="['T1','T2]"`);
    return false;
  }
  