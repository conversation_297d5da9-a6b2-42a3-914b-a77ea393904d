/* eslint-disable @typescript-eslint/dot-notation */
/** Request 网络请求工具 更详细的 api 文档: https://github.com/umijs/umi-request */
import { extend, RequestOptionsInit } from 'umi-request';
import { history } from 'umi';
import { message, notification } from 'antd';
import { clearSessionToken, getAccessToken, getRefreshToken, getTokenExpireTime } from '../access';
import { LoginPageUrl } from './utils';
import defaultSettings from '../../config/defaultSettings';

// 修改自定义响应类型
interface ResponseWithExtra extends Response {
  data?: any;
  clone(): ResponseWithExtra;
  request?: {
    url: string;
    options: RequestOptionsInit;
  };
}

const codeMessage: Record<number, string> = {
  10000: '系统未知错误，请反馈给管理员',
  200: '服务器成功返回请求的数据。',
  201: '新建或修改数据成功。',
  202: '一个请求已经进入后台排队（异步任务）。',
  204: '删除数据成功。',
  400: '发出的请求有错误，服务器没有进行新建或修改数据的操作。',
  401: '用户没有权限（令牌、用户名、密码错误）。',
  403: '用户得到授权，但是访问是被禁止的。',
  404: '发出的请求针对的是不存在的记录，服务器没有进行操作。',
  406: '请求的格式不可得。',
  410: '请求的资源被永久删除，且不会再得到的。',
  422: '当创建一个对象时，发生一个验证错误。',
  500: '服务器发生错误，请检查服务器。',
  502: '网关错误。',
  503: '服务不可用，服务器暂时过载或维护。',
  504: '网关超时。',
};

/** 异常处理程序 */
// const errorHandler = (error: { response: Response }): Response => {
//   const { response } = error;
//   if (response && response.status) {
//     const errorText = codeMessage[response.status] || response.statusText;
//     const { status, url } = response;

//     notification.error({
//       message: `请求错误 ${status}: ${url}`,
//       description: errorText,
//     });
//   } else if (!response) {
//     notification.error({
//       description: '您的网络发生异常，无法连接服务器',
//       message: '网络异常',
//     });
//   }
//   return response;
// };

const errorHandler = (error: { response: Response; name: string; message: string }): Response => {
  const { response, name, message } = error;

  // 处理请求超时
  if (name === 'RequestError' && message.includes('timeout')) {
    notification.error({
      message: '请求超时',
      description: '服务器响应时间过长，请稍后重试',
    });
    // 直接抛出错误，中断后续处理
    throw error;
  }

  if (response && response.status) {
    const errorText = codeMessage[response.status] || response.statusText;
    const { status, url } = response;

    notification.error({
      message: `请求错误 ${status}: ${url}`,
      description: errorText,
    });
  } else if (!response) {
    notification.error({
      description: '您的网络发生异常，无法连接服务器',
      message: '网络异常',
    });
  }
  return response;
};

function createClient () {
  /** 配置request请求时的默认参数 */
  return extend({
    errorHandler, // 默认错误处理
    credentials: 'include', // 默认请求是否带上cookie
    prefix: defaultSettings.apiBasePath,
    timeout: 30000, // 设置超时时间为30秒
    timeoutMessage: '请求超时，请重试', // 添加超时提示信息
    throwErrIfParseFail: true, // 解析失败时抛出错误
    errorConfig: {
      adaptor: (resData: any) => {
        return {
          ...resData,
          success: resData.code === 200,
          errorMessage: resData.msg,
        };
      },
    },
  });
}

const request = createClient();

// 更换令牌的时间区间
const checkRegion = 5 * 60 * 1000;

// 添加刷新token的函数
// 添加一个标志来追踪是否正在刷新token
let isRefreshing = false;
// 添加一个队列来存储等待token刷新的请求
let waitingQueue: ((token: string) => void)[] = [];

// 执行等待队列中的请求
const executeQueue = (token: string) => {
  waitingQueue.forEach((callback) => callback(token));
  waitingQueue = [];
};

// 修改刷新token的函数
async function refreshToken() {
  const refreshTokenStr = getRefreshToken();
  if (!refreshTokenStr) {
    return null;
  }
  // 如果已经在刷新，则返回一个Promise
  if (isRefreshing) {
    return new Promise<string | null>((resolve) => {
      waitingQueue.push((token: string) => {
        resolve(token);
      });
    });
  }
  try {
    isRefreshing = true;
    console.log('开始refreshToken');
    const response = await fetch(`${defaultSettings.apiBasePath}/refreshToken`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ refreshToken: refreshTokenStr }),
    });

    const result = await response.json();
    if (result.code === 200) {
      const current = new Date();
      const expireTime = current.setTime(current.getTime() + 1000 * 12 * 60 * 60);
      localStorage.setItem('access_token', result.token);
      // localStorage.setItem('refresh_token', result.refresh_token);
      localStorage.setItem('expireTime', String(expireTime));
      console.log('refreshToken成功, 新的token为:', result.token);

      // 执行队列中的请求
      executeQueue(result.token);
      return result.token;
    }
    return null;
  } catch (error) {
    console.error('刷新token失败:', error);
    return null;
  } finally {
    isRefreshing = false;
  }
}

// 修改请求拦截器
request.interceptors.request.use((url: string, options: RequestOptionsInit) => {
  const headers: Record<string, string> = {};

  // 复制现有的 headers
  if (options.headers) {
    if (options.headers instanceof Headers) {
      // 使用 Headers 的 forEach 方法来遍历
      options.headers.forEach((value, key) => {
        headers[key] = value;
      });
    } else if (typeof options.headers === 'object') {
      Object.assign(headers, options.headers);
    }
  }

  const currentEnv = localStorage.getItem('currentEnv') || 'DEV1';
  headers['env'] = currentEnv;
  // 创建一个同步的处理函数
  const processRequest = () => {

    if (headers['Authorization'] === '' || headers['Authorization'] == null) {
      const expireTime = getTokenExpireTime();
      if (expireTime) {
        const left = Number(expireTime) - new Date().getTime();
        const refreshTokenStr = getRefreshToken();

        if (left < checkRegion && refreshTokenStr) {
          // Token即将过期，直接使用当前token，让响应拦截器处理刷新
          const accessToken = getAccessToken();
          if (accessToken) {
            headers['Authorization'] = `Bearer ${accessToken}`;

          }
        } else {
          const accessToken = getAccessToken();
          if (accessToken) {
            headers['Authorization'] = `Bearer ${accessToken}`;
          }
        }
      } else {
        clearSessionToken();
        history.push(LoginPageUrl);
      }
    }
    return {
      url,
      options: { ...options, headers },
    };
  };

  return processRequest();
});
// 修改响应拦截器，在这里处理token刷新
// 修改响应拦截器
request.interceptors.response.use(async (response: ResponseWithExtra, options: RequestOptionsInit) => {

  try {
    // 克隆响应以获取响应数据
    const clonedResponse = response.clone();
    const res = await clonedResponse.json();

    // 检查业务状态码
    if (res.code === 401) {
      console.log('检测到业务状态码401，准备刷新token');

      const newToken = await refreshToken();
      if (newToken) {
        const url = response.url;
        console.log('原始请求URL:', url);

        // 从完整URL中正确提取相对路径和查询参数
        const urlObj = new URL(url);

        // 获取原始请求的完整配置

        const requestMethod = options.method;
        const originalOptions = options;

        console.log('使用的请求方法:', requestMethod);

        // 完全重写路径提取逻辑
        let requestPath: string;

        // 获取原始路径（去除域名和协议）
        const originalPath = urlObj.pathname;

        // 直接从URL中提取实际功能路径（不含api前缀）
        const functionPathMatch = originalPath.match(/\/(?:api\/?)?(.+)/);
        if (functionPathMatch && functionPathMatch[1]) {
          // 找到了实际功能路径部分，确保开头有斜杠
          requestPath = functionPathMatch[1];

          // 确保路径以斜杠开头，这样与apiBasePath拼接时才会有斜杠分隔
          if (!requestPath.startsWith('/')) {
            requestPath = '/' + requestPath;
          }

          console.log('提取的功能路径:', requestPath);
        } else {
          // 备选方案：移除api前缀但保留开头斜杠
          requestPath = originalPath.replace(/^\/api\/?/, '/');
          console.log('备选功能路径:', requestPath);
        }

        // 添加查询参数
        if (urlObj.search) {
          requestPath += urlObj.search;
        }

        console.log('最终重试请求路径:', requestPath);

        // 创建新的请求选项，保持原有所有配置
        const newOptions: RequestOptionsInit = {
          ...originalOptions,
          headers: {
            ...originalOptions.headers, // 保留所有原始请求头
            'Authorization': `Bearer ${newToken}`
          },
          data: originalOptions.data, // 保留原始请求体数据
          params: originalOptions.params, // 保留原始params
          method: requestMethod, // 使用推断的请求方法
        };

        // 确保请求头中的Content-Type和Accept保持不变
        if (typeof originalOptions.headers === 'object') {
          const headers = originalOptions.headers as Record<string, string>;
          if (!headers['Content-Type']) {
            (newOptions.headers as Record<string, string>)['Content-Type'] = 'application/json;charset=UTF-8';
          }
          if (!headers['Accept']) {
            (newOptions.headers as Record<string, string>)['Accept'] = 'application/json';
          }
        }

        // 使用处理后的路径发起请求
        return request(requestPath, newOptions);
      }

      // 如果刷新失败，清除信息并跳转登录
      console.log('token刷新失败');
      const msg = '用户未登录或token已失效，请重新登录';
      message.warn(msg);
      clearSessionToken();
      history.push(LoginPageUrl);
    }

    // 处理其他状态码
    if (res.code === 403) {
      const msg = codeMessage[403] || codeMessage[10000];
      message.warn(`403 ${msg}`);
    }
    return response;
  } catch (error) {
    console.error('响应拦截器处理失败:', error);
    return response;
  }
});
export default request;