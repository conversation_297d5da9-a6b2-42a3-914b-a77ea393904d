import CryptoJS from 'crypto-js';

// 加密密钥，建议从环境变量获取
const SECRET_KEY = process.env.REACT_APP_SECRET_KEY || 'default-secret-key';

interface StorageData {
  value: string;
  expires: number;
}

export const storage = {
  /**
   * 设置存储项
   * @param key 存储键
   * @param value 存储值
   * @param expires 过期时间（秒）
   */
  set(key: string, value: any, expires: number): void {
    try {
      // console.log(`[Storage] 设置缓存: ${key}`, {
      //   value,
      //   stack: new Error().stack // 添加调用栈信息
      // });
      const data: StorageData = {
        value: CryptoJS.AES.encrypt(JSON.stringify(value), SECRET_KEY).toString(),
        expires: Date.now() + expires * 1000 // 转换为毫秒
      };
      localStorage.setItem(key, JSON.stringify(data));
    } catch (error) {
      console.error(`[Storage] 设置缓存失败: ${key}`, error);
    }
    
  },

  /**
   * 获取存储项
   * @param key 存储键
   * @returns 存储值或null
   */
  get(key: string): any | null {
    try {
      const dataStr = localStorage.getItem(key);
      if (!dataStr) return null;

      const data: StorageData = JSON.parse(dataStr);

      // 检查是否过期
      if (Date.now() >= data.expires) {
        console.log('数据已过期');
        localStorage.removeItem(key);
        return null;
      }

      try {
        const bytes = CryptoJS.AES.decrypt(data.value, SECRET_KEY);
        return JSON.parse(bytes.toString(CryptoJS.enc.Utf8));
      } catch (error) {
        console.error('解密失败:', error);
        return null;
      }
    } catch (error) {
      console.error(`[Storage] 获取缓存失败: ${key}`, error);
      return null;
    }
  },

  /**
   * 删除存储项
   * @param key 存储键
   */
  remove(key: string): void {
    try {
      localStorage.removeItem(key);
    } catch (error) {
      console.error(`[Storage] 删除缓存失败: ${key}`, error);
    }
  }
};