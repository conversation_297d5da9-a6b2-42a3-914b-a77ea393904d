{"commands": [{"name": "启动开发服务器", "command": "npm run start:dev", "shortcut": "ctrl+shift+d"}, {"name": "构建项目", "command": "npm run build", "shortcut": "ctrl+shift+b"}, {"name": "代码格式化", "command": "npm run prettier", "shortcut": "ctrl+shift+f"}, {"name": "代码检查", "command": "npm run lint", "shortcut": "ctrl+shift+l"}, {"name": "运行测试", "command": "npm run test", "shortcut": "ctrl+shift+t"}, {"name": "添加依赖", "command": "npm install ${input:package} --save", "shortcut": "ctrl+shift+i"}, {"name": "添加开发依赖", "command": "npm install ${input:package} --save-dev", "shortcut": "ctrl+shift+e"}, {"name": "创建组件文件", "command": "mkdir -p src/components/${input:componentName} && touch src/components/${input:componentName}/index.tsx src/components/${input:componentName}/${input:componentName}.tsx src/components/${input:componentName}/${input:componentName}.less", "shortcut": "ctrl+shift+c"}, {"name": "创建页面文件", "command": "mkdir -p src/pages/${input:pageName} && touch src/pages/${input:pageName}/index.tsx src/pages/${input:pageName}/${input:pageName}.tsx src/pages/${input:pageName}/${input:pageName}.less", "shortcut": "ctrl+shift+p"}]}