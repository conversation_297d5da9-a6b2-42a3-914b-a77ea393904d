{"组件代码片段": {"prefix": "rfc", "body": ["import React from 'react';", "import styles from './${TM_FILENAME_BASE}.less';", "", "type ${TM_FILENAME_BASE}Props = {", "  $1", "};", "", "const ${TM_FILENAME_BASE}: React.FC<${TM_FILENAME_BASE}Props> = (props) => {", "  const { $2 } = props;", "  ", "  return (", "    <div className={styles.container}>", "      $0", "    </div>", "  );", "};", "", "export default ${TM_FILENAME_BASE};"], "description": "React函数组件", "scope": "typescriptreact"}, "页面组件": {"prefix": "rpage", "body": ["import React from 'react';", "import { PageContainer } from '@ant-design/pro-layout';", "import { Card } from 'antd';", "import styles from './${TM_FILENAME_BASE}.less';", "", "const ${TM_FILENAME_BASE}: React.FC = () => {", "  return (", "    <PageContainer>", "      <Card>", "        $0", "      </Card>", "    </PageContainer>", "  );", "};", "", "export default ${TM_FILENAME_BASE};"], "description": "React页面组件", "scope": "typescriptreact"}, "获取数据Hook": {"prefix": "usequery", "body": ["import { useState, useEffect } from 'react';", "import { message } from 'antd';", "", "export function use${1:Data}Query(params: any) {", "  const [loading, setLoading] = useState<boolean>(false);", "  const [data, setData] = useState<any>(null);", "", "  useEffect(() => {", "    const fetchData = async () => {", "      setLoading(true);", "      try {", "        // 替换为实际API调用", "        const response = await fetch${1:Data}(params);", "        setData(response.data);", "      } catch (error) {", "        message.error('获取数据失败');", "        console.error(error);", "      } finally {", "        setLoading(false);", "      }", "    };", "", "    fetchData();", "  }, [params]);", "", "  return { loading, data };", "}"], "description": "创建数据获取Hook", "scope": "typescript,typescriptreact"}, "TS接口定义": {"prefix": "interface", "body": ["/**", " * $1接口", " */", "export interface ${1:Interface}Type {", "  /** $2 */", "  ${2:property}: ${3:string};", "  $0", "}"], "description": "TypeScript接口定义", "scope": "typescript,typescriptreact"}, "Ant Design表格": {"prefix": "protable", "body": ["import React from 'react';", "import { PageContainer } from '@ant-design/pro-layout';", "import ProTable, { ProColumns } from '@ant-design/pro-table';", "import { ${1:DataType} } from '@/services/${2:api}';", "", "const ${TM_FILENAME_BASE}: React.FC = () => {", "  const columns: ProColumns<${1:DataType}>[] = [", "    {", "      title: '${3:标题}',", "      dataIndex: '${4:key}',", "      valueType: 'text',", "    },", "    $0", "    {", "      title: '操作',", "      valueType: 'option',", "      render: (_, record) => [", "        <a key=\"edit\" onClick={() => handleEdit(record)}>编辑</a>,", "        <a key=\"delete\" onClick={() => handleDelete(record.id)}>删除</a>,", "      ],", "    },", "  ];", "", "  const handleEdit = (record: ${1:DataType}) => {", "    // 处理编辑", "  };", "", "  const handleDelete = (id: string) => {", "    // 处理删除", "  };", "", "  return (", "    <PageContainer>", "      <ProTable<${1:DataType}>", "        columns={columns}", "        request={async (params, sorter, filter) => {", "          // 这里需要替换为实际的API调用", "          const { data, success } = await query${1:DataType}List({", "            ...params,", "            sorter,", "            filter,", "          });", "          return {", "            data: data || [],", "            success,", "          };", "        }}", "        rowKey=\"id\"", "        pagination={{", "          showQuickJumper: true,", "        }}", "        search={{", "          labelWidth: 120,", "        }}", "        dateFormatter=\"string\"", "        toolBarRender={() => [", "          <Button key=\"button\" type=\"primary\">", "            新建", "          </Button>,", "        ]}", "      />", "    </PageContainer>", "  );", "};", "", "export default ${TM_FILENAME_BASE};"], "description": "Ant Design Pro表格", "scope": "typescriptreact"}, "Model文件": {"prefix": "model", "body": ["import { Effect, Reducer } from 'umi';", "import { ${1:fetch}${2:Data} } from '@/services/${3:api}';", "", "export interface ${4:State}ModelState {", "  ${5:data}: any[];", "  ${6:total}: number;", "}", "", "export interface ${4:State}ModelType {", "  namespace: '${7:namespace}';", "  state: ${4:State}ModelState;", "  effects: {", "    fetch${2:Data}: Effect;", "    $0", "  };", "  reducers: {", "    save${2:Data}: Reducer<${4:State}ModelState>;", "  };", "}", "", "const ${4:State}Model: ${4:State}ModelType = {", "  namespace: '${7:namespace}',", "", "  state: {", "    ${5:data}: [],", "    ${6:total}: 0,", "  },", "", "  effects: {", "    *fetch${2:Data}({ payload }, { call, put }) {", "      const response = yield call(${1:fetch}${2:Data}, payload);", "      yield put({", "        type: 'save${2:Data}',", "        payload: response,", "      });", "    },", "  },", "", "  reducers: {", "    save${2:Data}(state, { payload }) {", "      return {", "        ...state,", "        ${5:data}: payload.data || [],", "        ${6:total}: payload.total || 0,", "      };", "    },", "  },", "};", "", "export default ${4:State}Model;"], "description": "<PERSON><PERSON> Model文件", "scope": "typescript"}, "Service函数": {"prefix": "service", "body": ["import { request } from 'umi';", "", "/**", " * ${1:描述}", " * @param params 请求参数", " */", "export async function ${2:functionName}(params?: any) {", "  return request<API.${3:ResponseType}>('${4:/api/path}', {", "    method: '${5:GET}',", "    params,", "  });", "}"], "description": "请求服务函数", "scope": "typescript"}}