{"version": "1.0.0", "defaults": {"linter": {"enabled": true, "pauseBetweenRuns": 1000, "run": "onType"}, "autofix": {"enabled": true, "suggestionsEnabled": true, "onSave": true}, "formatter": {"enabled": true, "indentStyle": "space", "indentWidth": 2, "lineWidth": 100}}, "typescript": {"useTabs": false, "lineWidth": 100, "indentWidth": 2, "semiColons": true, "trailingCommas": "es5", "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "always", "experimentalDecorators": true}, "javascript": {"useTabs": false, "lineWidth": 100, "indentWidth": 2, "semiColons": true, "trailingCommas": "es5", "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "always"}, "css": {"useTabs": false, "lineWidth": 100, "indentWidth": 2}, "less": {"useTabs": false, "lineWidth": 100, "indentWidth": 2}, "html": {"useTabs": false, "lineWidth": 100, "indentWidth": 2, "bracketSameLine": false}, "json": {"useTabs": false, "lineWidth": 100, "indentWidth": 2}, "ignore": [".git", "node_modules", "dist", "build", ".umi", ".umi-production", "**/*.min.js", "**/*.min.css"], "editor": {"formatOnSave": true, "autoSave": "onWindowChange", "tabSize": 2, "wordWrap": false, "showWhitespace": "boundary", "cursorBlinking": "smooth", "fontFamily": "JetBrains Mono, Menlo, Monaco, Consolas, 'Courier New', monospace", "fontLigatures": true, "fontSize": 14, "lineHeight": 1.5, "minimap": {"enabled": true, "showSlider": "always"}, "guides": {"indentation": true, "bracketPairs": true}}, "ai": {"language": "cn", "autoComplete": true, "snippets": true, "inlineCompletion": true, "chatPrefixes": ["// @AI", "/* @AI", "<!-- @AI"]}}