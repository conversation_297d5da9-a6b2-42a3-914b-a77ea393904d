const express = require('express');
const app = express();
app.use(express.json());

app.post('/testtool/subContQuery', (req, res) => {
    console.log("收到请求",req)
  const mockData = {
    code: 200,
    data: JSON.stringify([{
      mediumNo: '6217994032100098800',
      saccnoSeqNo: '1',
      mainContrNo: 'DP20210825010000670844',
      mainPersInnerAccno: '0303011010007045663',
      subProdtContractNo: 'DP20210825010000670844',
      subPersInnerAccno: '0303011010007045663',
      custNo: '00000090635097',
      custName: '测试客户',
      perCertTpCd: '1010',
      personalCertNo: '110101199003077832',
      prodtContractName: '活期储蓄合约LALA拉历程',
      accBal: '10000.00',
      accAvalBal: '9800.00',
      subInstNo: '11010266',
      currCode: '156',
      persDepAccKindCd: '1'
    }])
  };
  res.json(mockData);
});

const PORT = 3002;
app.listen(PORT, () => {
  console.log(`Mock server running at http://localhost:${PORT}`);
});