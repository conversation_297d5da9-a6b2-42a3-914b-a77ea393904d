# Enterprise Management System

基于 Ant Design Pro 5.0 的企业级管理系统，提供完整的企业应用解决方案。

## 项目简介

本项目是一个功能丰富的企业级管理系统，集成了用户管理、权限控制、系统监控等多个核心功能模块，采用现代化的技术栈和最佳实践，为企业级应用开发提供完整的解决方案。

## 核心特性

- 🚀 基于 Ant Design Pro 5.0，提供现代化的 UI 界面
- 📦 开箱即用的高质量组件
- 🔐 完整的权限管理和用户控制
- 🌐 支持国际化
- 📊 丰富的数据可视化功能
- 🛠 完善的开发工具链
- 📱 响应式设计，支持多端适配

## 功能模块

### 系统管理
- 用户管理
- 角色管理
- 菜单管理
- 部门管理
- 岗位管理
- 字典管理
- 参数管理

### 系统监控
- 操作日志
- 登录日志
- 在线用户
- 服务监控
- 缓存监控
- Druid监控

### 开发工具
- 代码生成
- 表单构建
- Swagger接口文档

### 测试工具集
- 文件上传
- SQL操作
- 报文格式化
- 数据查询
- 限额管理

## 技术栈

- 核心框架：React 17.x + UmiJS 3.x
- UI 框架：Ant Design 4.x
- 状态管理：DVA
- 图表库：Ant Design Charts、BizCharts
- 工具库：Lodash、Moment、Axios
- 开发工具：TypeScript、ESLint、Prettier
- 国际化：React-Intl
- 代码编辑：CodeMirror
- 数据可视化：D3.js

## 快速开始

### 环境准备
- Node.js >= 14
- npm >= 6.14.0
- Git

### 安装依赖
```bash
npm install
# 或
yarn
```

### 开发环境运行
```bash
# 正常开发模式
pnpm run dev

# Mock数据模式
pnpm start

# 无UI开发模式
pnpm run start:no-ui
```

### 生产环境构建
```bash
pnpm run build
```

### Docker部署
```bash
# 开发环境
pnpm run docker:dev

# 生产环境
pnpm run docker-prod:build
pnpm run docker-prod:dev
```

## 开发指南

### 代码规范
项目内置了 ESLint、Prettier、StyleLint 代码规范工具，确保代码风格统一：

```bash
# 代码检查
pnpm run lint

# 自动修复
pnpm run lint:fix
```

### 测试
```bash
# 运行所有测试
pnpm run test:all

# 运行组件测试
pnpm run test:component
```

## 项目结构
